{"env": {"commonjs": true, "node": true, "es6": true}, "extends": "eslint:recommended", "parserOptions": {"ecmaVersion": 2021}, "plugins": [], "rules": {"brace-style": [2, "1tbs"], "indent": ["error", 2, {"SwitchCase": 1}], "array-bracket-spacing": ["error", "always", {"objectsInArrays": false}], "object-curly-spacing": ["error", "always"], "curly": "error", "space-in-parens": ["error", "never"], "no-multiple-empty-lines": ["error", {"max": 1}], "max-lines-per-function": ["error", {"max": 400, "skipBlankLines": true}], "space-before-blocks": ["error"], "no-multi-spaces": ["error"], "semi": ["error", "always"], "quotes": ["error", "single"], "prefer-const": ["error"], "no-undef": ["error"], "no-unused-vars": ["error"], "no-console": ["warn"], "no-useless-escape": ["off"], "keyword-spacing": ["error"], "space-before-function-paren": ["error", "always"], "key-spacing": ["error", {"afterColon": true}], "max-lines": ["error", {"max": 1000, "skipBlankLines": true, "skipComments": true}], "max-depth": ["error", 5], "newline-after-var": ["error", "always"], "no-unreachable": "error", "space-infix-ops": "error", "eqeqeq": "error", "no-eq-null": "error"}, "globals": {"moment": true, "zE": true, "_": true, "__basedir": true, "Schema": true}}