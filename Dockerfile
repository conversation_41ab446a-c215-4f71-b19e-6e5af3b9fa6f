FROM --platform=linux/amd64 node:20-alpine

# Install build tools and necessary dependencies
RUN apk add --no-cache python3 make g++ \
  && apk add --no-cache bash

# Set the working directory
WORKDIR /usr/src/app

# Copy package files first for caching layers
COPY package*.json ./

# Install app dependencies
RUN npm ci --only=production

# Copy the rest of your application code
COPY . .

# Rebuild bcrypt to avoid native mismatch issues
RUN npm rebuild bcrypt --build-from-source

# Expose port (optional if you're not defining it already)
EXPOSE 3000

# Start the app
CMD ["npm", "start"]
