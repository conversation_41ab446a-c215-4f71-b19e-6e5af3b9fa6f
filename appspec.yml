version: 0.0
os: linux
files:
  - source: /
    destination: /home/<USER>/development/one-god-node
    owner: ubuntu
    group: ubuntu
    mode: 755
hooks:
    ApplicationStop:
        - location: shells/stop_server.sh
          timeout: 300
          runas: ubuntu
    AfterInstall:
        - location: shells/install_dependencies.sh
          timeout: 300
          runas: ubuntu
    ApplicationStart:
        - location: shells/start_server.sh
          timeout: 900
          runas: ubuntu
