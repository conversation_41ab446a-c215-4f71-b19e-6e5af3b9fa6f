image: atlassian/default-image:2

pipelines:
  branches:
      dev:
        - step:
            name: Build
            script:
              - zip -r develop.zip *
            artifacts:
              - develop.zip
        - step:
            name: Upload to S3
            services:
              - docker
            script:
              - pipe: atlassian/aws-code-deploy:0.3.2
                variables:
                  AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                  AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                  AWS_DEFAULT_REGION: $AWS_REGION
                  S3_BUCKET: $AWS_S3_BUCKET
                  COMMAND: "upload"
                  APPLICATION_NAME: 'one-god-dev-app'
                  ZIP_FILE: "develop.zip"
        - step:
            name: Deploy with CodeDeploy
            services:
              - docker
            script:
              - echo "Environment ${REPOSITORY_VARIABLE}"
              - pipe: atlassian/aws-code-deploy:0.3.2
                variables:
                  AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                  AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                  AWS_DEFAULT_REGION: $AWS_REGION
                  S3_BUCKET: $AWS_S3_BUCKET
                  COMMAND: "deploy"
                  APPLICATION_NAME: 'one-god-dev-app'
                  DEPLOYMENT_GROUP: 'one-god-dev-deployment-group'
                  IGNORE_APPLICATION_STOP_FAILURES: "true"
                  FILE_EXISTS_BEHAVIOR: "OVERWRITE"
                  WAIT: "true"
      uat:
        - step:
            name: Build
            script:
              - zip -r develop.zip *
            artifacts:
              - develop.zip
        - step:
            name: Upload to S3
            services:
              - docker
            script:
              - pipe: atlassian/aws-code-deploy:0.3.2
                variables:
                  AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                  AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                  AWS_DEFAULT_REGION: $AWS_REGION
                  S3_BUCKET: $AWS_S3_BUCKET_UAT
                  COMMAND: "upload"
                  APPLICATION_NAME: 'one-god-uat-app'
                  ZIP_FILE: "develop.zip"
        - step:
            name: Deploy with CodeDeploy
            services:
              - docker
            script:
              - echo "Environment ${REPOSITORY_VARIABLE}"
              - pipe: atlassian/aws-code-deploy:0.3.2
                variables:
                  AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                  AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                  AWS_DEFAULT_REGION: $AWS_REGION
                  S3_BUCKET: $AWS_S3_BUCKET_UAT
                  COMMAND: "deploy"
                  APPLICATION_NAME: 'one-god-uat-app'
                  DEPLOYMENT_GROUP: 'one-god-uat-deployment-group'
                  IGNORE_APPLICATION_STOP_FAILURES: "true"
                  FILE_EXISTS_BEHAVIOR: "OVERWRITE"
                  WAIT: "true"

