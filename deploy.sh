#!/bin/bash

set -e  # Exit immediately if a command exits with a non-zero status

echo "🔄 Pulling latest code..."
git pull

echo "🔐 Logging into AWS ECR..."
aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 113304815637.dkr.ecr.ap-south-1.amazonaws.com

echo "🐳 Building Docker image..."
docker build -t ek-ishwar .

echo "🏷️ Tagging Docker image..."
docker tag ek-ishwar:latest 113304815637.dkr.ecr.ap-south-1.amazonaws.com/ek-ishwar:latest

echo "📤 Pushing image to AWS ECR..."
docker push 113304815637.dkr.ecr.ap-south-1.amazonaws.com/ek-ishwar:latest

echo "☸️ Updating kubeconfig for EKS..."
aws eks update-kubeconfig --name ek-ishwar --region ap-south-1

echo "⚙️ Updating ConfigMap..."
kubectl create configmap ek-ishwar-config --from-env-file=.env --dry-run=client -o yaml | kubectl apply -f -

echo "🔁 Restarting deployment..."
kubectl rollout restart deployment deployment-ek-ishwar -n ek-ishwar

echo "📦 Getting deployments..."
kubectl get deployments -n ek-ishwar

echo "📦 Getting pods..."
kubectl get pods -n ek-ishwar

echo "🔌 Getting services..."
kubectl get svc -n ek-ishwar

echo "🌐 Getting ingress..."
kubectl get ingress -n ek-ishwar

echo "📈 Getting HPA..."
kubectl get hpa -n ek-ishwar

echo "🔍 Describing HPA..."
kubectl describe hpa hpa-ek-ishwar -n ek-ishwar

echo "✅ Deployment script completed!"