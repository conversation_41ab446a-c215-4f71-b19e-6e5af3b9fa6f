apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: ek-ishwar
  name: deployment-ek-ishwar
spec:
  selector:
    matchLabels:
      app: ek-ishwar
  template:
    metadata:
      labels:
        app: ek-ishwar
    spec:
      containers:
      - name: ek-ishwar
        image: 113304815637.dkr.ecr.ap-south-1.amazonaws.com/ek-ishwar:latest
        envFrom:
          - configMapRef:
              name: ek-ishwar-config
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
        resources:  # resources will be used in auto scaling
          limits:  # indicates the higher limit
            cpu: 8000m
            memory: 8192Mi
          requests: # indicates the lower limit
            cpu: 1000m
            memory: 1024Mi
