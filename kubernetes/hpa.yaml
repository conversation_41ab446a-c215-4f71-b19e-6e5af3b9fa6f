apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
 name: hpa-ek-ishwar # name of HPA
 namespace: ek-ishwar
spec:
 scaleTargetRef:
   apiVersion: apps/v1
   kind: Deployment
   name: deployment-ek-ishwar # autoscales the app we deployed previously
 minReplicas: 1 # minimum number of replicas
 maxReplicas: 10 # maximum number of replicas
 metrics:
   - type: Resource
     resource:
       name: cpu
       target:
         type: Utilization
         averageUtilization: 50