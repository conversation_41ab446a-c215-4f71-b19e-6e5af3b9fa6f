apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: ek-ishwar
  name: ingress-ek-ishwar
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    nginx.ingress.kubernetes.io/use-forwarded-headers: "true"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}]'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:ap-south-1:113304815637:certificate/b9a43fff-fd4e-438c-a621-33680b85e87f
    alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=300
spec:
  ingressClassName: alb
  rules:
    - host: api.ekishwar.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: service-ek-ishwar
                port:
                  number: 80