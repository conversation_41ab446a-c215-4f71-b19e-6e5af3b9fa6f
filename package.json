{"name": "user-auth-api", "version": "1.0.0", "description": "User authentication and CRUD API", "main": "src/index.js", "engines": {"node": "20.16.0"}, "scripts": {"start": "node -r newrelic src/index.js", "dev": "nodemon src/index.js", "test": "jest"}, "dependencies": {"@google-cloud/translate": "^9.1.0", "@sentry/node": "^10.1.0", "@sentry/profiling-node": "^10.1.0", "aws-sdk": "^2.1692.0", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "eslint": "^8.51.0", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "firebase-admin": "^13.3.0", "handlebars": "^4.7.8", "html-pdf-node": "^1.0.8", "joi": "^17.11.0", "json-2-csv": "^5.5.9", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.12.9", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.2", "newrelic": "^12.25.0", "node-cron": "^3.0.3", "qrcode": "^1.5.4", "razorpay": "^2.9.6", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}}