/**
 * Constant file for db enums
 */
const userTypeValue = {
  USER: 'USER',
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',
  VENDOR: 'VENDOR',
  KIOSK: 'KIOSK',
  TEMPLE_ADMIN: 'TEMPLE_ADMIN',
};

const userStatusValue = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  BLOCKED: 'BLOCKED',
  PENDING: 'PENDING'
};

const otpTypeValue = {
  EMAIL: 'EMAIL',
  PHONE_NUMBER: 'PHONE',
};

const templeServiceTypes = {
  PHYSICAL_POOJA: 'PHYSICAL_POOJA',
  PHYSICAL_DARSHAN: 'PHYSICAL_DARSHAN',
  VIRTUAL_POOJA: 'VIRTUAL_POOJA'
};

const paymentStatus = {
  CAPTURED: 'CAPTURED',
  FAILED: 'FAILED',
  REFUNDED: 'REFUNDED',
  PENDING: 'PENDING'
};

const paymentMethod = {
  UPI: 'UPI',
  CARD: 'CARD',
  NETBANKING: 'NETBANKING',
  WALLET: 'WALLET'
};

const transactionStatus = {
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  PENDING: 'PENDING'
};

const transactionType = {
  PAYMENT: 'PAYMENT',
  REFUND: 'REFUND',
  CANCELLATION: 'CANCELLATION'
};

const type = {
  PHYSICAL_DARSHAN: 'PHYSICAL_DARSHAN',
  PHYSICAL_POOJA: 'PHYSICAL_POOJA',
  VIRTUAL_POOJA: 'VIRTUAL_POOJA',
  EVENT: 'EVENT'
};

const status = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

const bookingType = {
  UPCOMING: 'UPCOMING',
  PAST: 'PAST'
};

const darshanStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

const extension = {
  // image formats
  JPG: 'jpg',
  JPEG: 'jpeg',
  PNG: 'png',
  HEIC: 'heic',
  HEIF: 'heif',
  PDF: 'pdf',
  WEBP: 'webp',

  // video Formats
  MP4: 'mp4',
  MOV: 'mov',
  HEVC: 'hevc',
  FLV: 'flv',
  WEBM: 'webm',
  MPEG: 'mpeg',
};

const productStatusValue = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
  REJECTED: 'REJECTED',
  OUT_OF_STOCK: 'OUT_OF_STOCK'
};

const stockUpdateStatusValue = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

const orderStatusValue = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  PARTIALLY_SHIPPED: 'PARTIALLY_SHIPPED',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  RETURNED: 'RETURNED',
  REFUNDED: 'REFUNDED'
};

const paymentStatusValue = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  REFUNDED: 'REFUNDED',
  PARTIALLY_REFUNDED: 'PARTIALLY_REFUNDED'
};

const transactionTypeValue = {
  PAYMENT: 'PAYMENT',
  REFUND: 'REFUND'
};

const transactionStatusValue = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED'
};

const shippingStatusValue = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  PICKUP_SCHEDULED: 'PICKUP_SCHEDULED',
  PICKUP_GENERATED: 'PICKUP_GENERATED',
  PICKUP_COMPLETED: 'PICKUP_COMPLETED',
  IN_TRANSIT: 'IN_TRANSIT',
  OUT_FOR_DELIVERY: 'OUT_FOR_DELIVERY',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  RTO_INITIATED: 'RTO_INITIATED',
  RTO_DELIVERED: 'RTO_DELIVERED',
  FAILED_DELIVERY: 'FAILED_DELIVERY'
};

const shipmentStatusValue = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  DISPATCHED: 'DISPATCHED',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  RETURNED: 'RETURNED'
};

const settlementStatusValue = {
  PENDING: 'PENDING',
  SETTLED: 'SETTLED'
};

const applicabilityType = {
  SPECIFIC_TEMPLES: 'SPECIFIC_TEMPLES',
  SPECIFIC_PRODUCTS: 'SPECIFIC_PRODUCTS',
  GENERAL: 'GENERAL'
};

const discountType = {
  FIXED_AMOUNT: 'FIXED_AMOUNT',
  PERCENTAGE: 'PERCENTAGE'
};

const discountStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

module.exports = {
  userTypeValue,
  userStatusValue,
  otpTypeValue,
  templeServiceTypes,
  paymentStatus,
  paymentMethod,
  transactionStatus,
  transactionType,
  type,
  status,
  darshanStatus,
  bookingType,
  extension,
  productStatusValue,
  stockUpdateStatusValue,
  orderStatusValue,
  paymentStatusValue,
  transactionTypeValue,
  transactionStatusValue,
  shippingStatusValue,
  shipmentStatusValue,
  settlementStatusValue,
  applicabilityType,
  discountType,
  discountStatus
};