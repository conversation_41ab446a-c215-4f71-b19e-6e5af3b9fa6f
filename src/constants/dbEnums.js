/**
 * Constant file for db enums
 */
const userTypeValue = {
  USER: 'USER',
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',
  VENDOR: 'VENDOR',
  KIOSK: 'KIOSK',
  TEMPLE_ADMIN: 'TEMPLE_ADMIN',
};

const userStatusValue = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  BLOCKED: 'BLOCKED',
  PENDING: 'PENDING'
};

const otpTypeValue = {
  EMAIL: 'EMAIL',
  PHONE_NUMBER: 'PHONE',
};

const templeServiceTypes = {
  PHYSICAL_POOJA: 'PHYSICAL_POOJA',
  PHYSICAL_DARSHAN: 'PHYSICAL_DARSHAN',
  VIRTUAL_POOJA: 'VIRTUAL_POOJA'
};

const paymentStatus = {
  CAPTURED: 'CAPTURED',
  FAILED: 'FAILED',
  REFUNDED: 'REFUNDED',
  PENDING: 'PENDING'
};

const paymentMethod = {
  UPI: 'UPI',
  CARD: 'CARD',
  NETBANKING: 'NETBANKING',
  WALLET: 'WALLET'
};

const transactionStatus = {
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  PENDING: 'PENDING'
};

const transactionType = {
  PAYMENT: 'PAYMENT',
  REFUND: 'REFUND',
  CANCELLATION: 'CANCELLATION'
};

const type = {
  PHYSICAL_DARSHAN: 'PHYSICAL_DARSHAN',
  PHYSICAL_POOJA: 'PHYSICAL_POOJA',
  VIRTUAL_POOJA: 'VIRTUAL_POOJA',
  EVENT: 'EVENT',
  HELICOPTER_BOOKING: 'HELICOPTER_BOOKING'
};

const status = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  SUBMITTED: 'SUBMITTED'
};

const bookingType = {
  UPCOMING: 'UPCOMING',
  PAST: 'PAST'
};

const darshanStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

const extension = {
  // image formats
  JPG: 'jpg',
  JPEG: 'jpeg',
  PNG: 'png',
  HEIC: 'heic',
  HEIF: 'heif',
  PDF: 'pdf',
  WEBP: 'webp',

  // video Formats
  MP4: 'mp4',
  MOV: 'mov',
  HEVC: 'hevc',
  FLV: 'flv',
  WEBM: 'webm',
  MPEG: 'mpeg',
};

const productStatusValue = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
  REJECTED: 'REJECTED',
  OUT_OF_STOCK: 'OUT_OF_STOCK'
};

const stockUpdateStatusValue = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

const orderStatusValue = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  PARTIALLY_SHIPPED: 'PARTIALLY_SHIPPED',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  RETURNED: 'RETURNED',
  REFUNDED: 'REFUNDED'
};

const paymentStatusValue = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  REFUNDED: 'REFUNDED',
  PARTIALLY_REFUNDED: 'PARTIALLY_REFUNDED'
};

const transactionTypeValue = {
  PAYMENT: 'PAYMENT',
  REFUND: 'REFUND'
};

const transactionStatusValue = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED'
};

const shippingStatusValue = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  PICKUP_SCHEDULED: 'PICKUP_SCHEDULED',
  PICKUP_GENERATED: 'PICKUP_GENERATED',
  PICKUP_COMPLETED: 'PICKUP_COMPLETED',
  IN_TRANSIT: 'IN_TRANSIT',
  OUT_FOR_DELIVERY: 'OUT_FOR_DELIVERY',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  RTO_INITIATED: 'RTO_INITIATED',
  RTO_DELIVERED: 'RTO_DELIVERED',
  FAILED_DELIVERY: 'FAILED_DELIVERY'
};

const shipmentStatusValue = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  DISPATCHED: 'DISPATCHED',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  RETURNED: 'RETURNED',
  IN_TRANSIT: 'IN_TRANSIT',
  OUT_FOR_DELIVERY: 'OUT_FOR_DELIVERY'
};

const settlementStatusValue = {
  PENDING: 'PENDING',
  SETTLED: 'SETTLED'
};

const applicabilityType = {
  SPECIFIC_TEMPLES: 'SPECIFIC_TEMPLES',
  SPECIFIC_PRODUCTS: 'SPECIFIC_PRODUCTS',
  GENERAL: 'GENERAL'
};

const discountType = {
  FIXED_AMOUNT: 'FIXED_AMOUNT',
  PERCENTAGE: 'PERCENTAGE'
};

const discountStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

const auditLogAction = {
  LOGIN: 'Logged in',
  LOGOUT: 'Logged out',
  BANNER_CREATED: 'Banner created',
  BANNER_UPDATED: 'Banner updated',
  BANNER_DELETED: 'Banner deleted',
  DARSHAN_SCHEDULE_CREATED: 'Darshan schedule created',
  DARSHAN_SCHEDULE_UPDATED: 'Darshan schedule updated',
  DARSHAN_SCHEDULE_DELETED: 'Darshan schedule deleted',
  DISCOUNT_CREATED: 'Discount created',
  DISCOUNT_UPDATED: 'Discount updated',
  DISCOUNT_DELETED: 'Discount deleted',
  OFFERING_CREATED: 'Offering created',
  OFFERING_UPDATED: 'Offering updated',
  OFFERING_DELETED: 'Offering deleted',
  RECORDING_STATUS_UPDATED: 'Recording status updated',
  POOJA_SCHEDULE_CREATED: 'Puja schedule created',
  POOJA_SCHEDULE_UPDATED: 'Puja schedule updated',
  POOJA_SCHEDULE_DELETED: 'Puja schedule deleted',
  ATTRIBUTE_CREATED: 'Attribute created',
  ATTRIBUTE_UPDATED: 'Attribute updated',
  ATTRIBUTE_DELETED: 'Attribute deleted',
  CATEGORY_CREATED: 'Category created',
  CATEGORY_UPDATED: 'Category updated',
  CATEGORY_DELETED: 'Category deleted',
  COLLECTION_CREATED: 'Collection created',
  COLLECTION_UPDATED: 'Collection updated',
  COLLECTION_DELETED: 'Collection deleted',
  SUBCATEGORY_CREATED: 'Subcategory created',
  SUBCATEGORY_UPDATED: 'Subcategory updated',
  SUBCATEGORY_DELETED: 'Subcategory deleted',
  PRODUCT_CREATED: 'Product created',
  PRODUCT_UPDATED: 'Product updated',
  PRODUCT_DELETED: 'Product deleted',
  STOCK_UPDATE_REQUEST_UPDATED: 'Stock update request updated',
  ORDER_STATUS_UPDATED: 'Order status updated',
  ORDER_REFUNDED: 'Order refunded',
  ORDER_SHIPPED: 'Order shipped',
  SHIPMENT_SETTLED: 'Shipment settled',
  INVOICE_GENERATED: 'Invoice generated',
  SHIPMENT_CREATED: 'Shipment created',
  AWB_GENERATED: 'AWB generated',
  PICKUP_REQUESTED: 'Pickup requested',
  PICKUP_LOCATION_UPDATED: 'Pickup location updated',
  SHOP_BANNER_CREATED: 'Shop banner created',
  SHOP_BANNER_UPDATED: 'Shop banner updated',
  SHOP_BANNER_DELETED: 'Shop banner deleted',
  BOOKING_SETTLED: 'Booking settled',
  EVENT_CREATED: 'Event created',
  EVENT_UPDATED: 'Event updated',
  EVENT_DELETED: 'Event deleted',
  ATTENDANCE_MARKED: 'Attendance marked',
  INVENTORY_SENT: 'Inventory sent',
  KIOSK_USER_CREATED: 'Kiosk user created',
  KIOSK_USER_UPDATED: 'Kiosk user updated',
  KIOSK_USER_DELETED: 'Kiosk user deleted',
  KIOSK_CREATED: 'Kiosk created',
  KIOSK_UPDATED: 'Kiosk updated',
  KIOSK_DELETED: 'Kiosk deleted',
  PUJARI_CREATED: 'Pujari created',
  PUJARI_UPDATED: 'Pujari updated',
  PUJARI_DELETED: 'Pujari deleted',
  TEMPLE_CREATED: 'Temple created',
  TEMPLE_UPDATED: 'Temple updated',
  TEMPLE_DELETED: 'Temple deleted',
  TEMPLE_ADMIN_CREATED: 'Temple admin created',
  TEMPLE_ADMIN_UPDATED: 'Temple admin updated',
  TEMPLE_ADMIN_DELETED: 'Temple admin deleted',
  TEMPLE_CATEGORY_CREATED: 'Temple category created',
  TEMPLE_CATEGORY_UPDATED: 'Temple category updated',
  TEMPLE_CATEGORY_DELETED: 'Temple category deleted',
  VENDOR_CREATED: 'Vendor created',
  VENDOR_UPDATED: 'Vendor updated',
  VENDOR_DELETED: 'Vendor deleted',
  HELICOPTER_CREATED: 'Helicopter created',
  HELICOPTER_UPDATED: 'Helicopter updated',
  HELICOPTER_DELETED: 'Helicopter deleted',
  HELICOPTER_STATUS_UPDATED: 'Helicopter status updated',
  HELICOPTER_PUJA_CREATED: 'Helicopter puja created',
  HELICOPTER_PUJA_UPDATED: 'Helicopter puja updated',
  HELICOPTER_PUJA_DELETED: 'Helicopter puja deleted',
  USER_ACCOUNT_UPDATED: 'User account updated',
  KIOSK_USER_ACCOUNT_UPDATED: 'Kiosk user account updated',
  OFFERING_STATUS_UPDATED: 'Offering status updated',
  NOTIFICATION_BROADCASTED: 'Notification broadcasted'
};

const assignmentMode = {
  AUTOMATIC: 'AUTOMATIC',
  MANUAL: 'MANUAL'
};

const notificationType = {
  TEMPLE_APPROVAL: 'TEMPLE_APPROVAL',
  LOW_STOCK_ALERT: 'LOW_STOCK_ALERT',
  UNASSIGNED_PUJA: 'UNASSIGNED_PUJA',
  UNASSIGNED_DARSHAN: 'UNASSIGNED_DARSHAN',
  LIVE_DARSHAN_APPROVAL: 'LIVE_DARSHAN_APPROVAL',
  PUJARI_APPROVAL: 'PUJARI_APPROVAL',
  PUJA_RECORDING_APPROVAL: 'PUJA_RECORDING_APPROVAL',
  PRODUCT_APPROVAL: 'PRODUCT_APPROVAL',
  STOCK_UPDATE_APPROVAL: 'STOCK_UPDATE_APPROVAL',
  ORDER_CONFIRMED: 'ORDER_CONFIRMED',
  ORDER_DELIVERED: 'ORDER_DELIVERED',
  ORDER_SHIPPED: 'ORDER_SHIPPED',
  BROADCAST_NOTIFICATION: 'BROADCAST_NOTIFICATION'
};

const broadcastNotificationType = {
  RELIGIOUS_DAY_MORNING: 'RELIGIOUS_DAY_MORNING',
  FESTIVAL_DAY_MORNING: 'FESTIVAL_DAY_MORNING',
  FESTIVAL_WEEK: 'FESTIVAL_WEEK',
  BEFORE_FESTIVAL_DAY: 'BEFORE_FESTIVAL_DAY',
  THREE_DAYS_REMINDER: 'THREE_DAYS_REMINDER'
};

const appPlatforms = {
  ANDROID: 'ANDROID',
  IOS: 'IOS'
};

module.exports = {
  userTypeValue,
  userStatusValue,
  otpTypeValue,
  templeServiceTypes,
  paymentStatus,
  paymentMethod,
  transactionStatus,
  transactionType,
  type,
  status,
  darshanStatus,
  bookingType,
  extension,
  productStatusValue,
  stockUpdateStatusValue,
  broadcastNotificationType,
  orderStatusValue,
  paymentStatusValue,
  transactionTypeValue,
  transactionStatusValue,
  shippingStatusValue,
  shipmentStatusValue,
  settlementStatusValue,
  applicabilityType,
  discountType,
  discountStatus,
  auditLogAction,
  assignmentMode,
  notificationType,
  appPlatforms
};