const moment = require('moment');
const User = require('../models/User');
const { sendWhatsAppMessage } = require('../utils/whatsappService');
const { sendEmail } = require('../utils/sesEmailService');

const sendRegistrationBookingReminder = async () => {
  try {

    const previousHour = moment().utc().subtract(1, 'hour').toDate();
    const oneHourBeforePreviousHour = moment().utc().subtract(2, 'hour').toDate();

    const targetUsers = await User.aggregate([
      {
        $match: {
          createdAt: { $lte: previousHour, $gte: oneHourBeforePreviousHour },
          $or: [
            { isRegistrationReminderSent: false },
            { isRegistrationReminderSent: null },
            { isRegistrationReminderSent: { $exists: false } }
          ]
        }
      },
      {
        $lookup: {
          from: 'bookings',
          localField: '_id',
          foreignField: 'user',
          as: 'bookings'
        }
      },
      {
        $match: {
          $or: [
            { bookings: { $exists: false } },
            { bookings: { $in: [ null, [] ] } },
          ]
        }
      }
    ]);

    const phoneNumbers = new Set();

    for (const user of targetUsers) {
      const userPhoneNumber = `${user.countryCode.replace('+', '') || '91'}${user.phoneNumber}`;
      
      phoneNumbers.add(userPhoneNumber);
    }

    if (phoneNumbers.size === 0) {
      return;
    }

    await Promise.all([
      User.updateMany({
        _id: { $in: targetUsers.map(user => user._id) }
      }, {
        isRegistrationReminderSent: true
      }),
      sendWhatsAppMessage([ ...phoneNumbers ], process.env.DAY_AFTER_REGISTRATION_TEMPLATE)
    ]);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error in pooja booking started reminder cron job:', error);
    await sendEmail(process.env.AWS_SES_SENDER_EMAIL, process.env.DEBUG_EMAIL, `<div>${error.message}<div>`, 'Error in EkIshwar app cron!');
  }
};

module.exports = {
  sendRegistrationBookingReminder
};