const Booking = require('../models/Booking');
const { sendFirebasePushMulticast } = require('../utils/firebase_cm');
const Token = require('../models/Token');
const Notification = require('../models/Notification');
const moment = require('moment');
const { userTypeValue, type } = require('../constants/dbEnums');
const { translateDataForStore } = require('../utils/translateInput');
const { sendWhatsAppMessage } = require('../utils/whatsappService');
const Kiosk = require('../models/Kiosk');

const sendBookingReminders = async () => {
  try {

    const tomorrowStart = moment().utc().add(1, 'days').startOf('day').toDate();
    const tomorrowEnd = moment().utc().add(1, 'days').endOf('day').toDate();
    
    const bookings = await Booking.find({
      date: { $gte: tomorrowStart, $lte: tomorrowEnd },
      status: 'COMPLETED'
    }).populate([
      { path: 'temple' },
      { path: 'user' },
      { path: 'darshanSchedule' },
      { path: 'poojaSchedule' },
      { path: 'event' }
    ]).lean();

    const promises = [], templeIds = [];

    for (const booking of bookings) {
      const tokens = await Token.find({ 
        userId: booking.user._id,
        fcmToken: { $ne: null, $exists: true }
      }).select('fcmToken');
      
      const fcmTokens = [ ...new Set(tokens.map(t => t.fcmToken)) ];

      let scheduleName = '';

      if (booking.darshanSchedule) {
        scheduleName = booking.darshanSchedule.name.en || booking.darshanSchedule.name;
      } else if (booking.poojaSchedule) {
        scheduleName = booking.poojaSchedule.name.en || booking.poojaSchedule.name;
      } else if (booking.event) {
        scheduleName = booking.event.name.en || booking.event.name;
      }
      
      const templeName = booking.temple?.name.en || booking.temple?.name || '';
      const bookingDate = moment(booking.date).format('MMMM D, YYYY');
      const bookingTime = booking.timeSlot?.startTime || '';
      let bookingType = '';

      if (booking.type === type.PHYSICAL_DARSHAN) {
        bookingType = 'Darshan';
      } else if (booking.type === type.PHYSICAL_POOJA) {
        bookingType = 'Physical Puja';
      } else if (booking.type === type.VIRTUAL_POOJA) {
        bookingType = 'Virtual Puja';
      } else if (booking.type === type.EVENT) {
        bookingType = 'Event';
      }
      
      let title = `Reminder: Your ${scheduleName} Booking Tomorrow!`;
      let message = `Just a reminder that you have a booking for ${scheduleName} at ${templeName} tomorrow, ${bookingDate}, at ${bookingTime}. Please arrive 30 minutes before. See you there!`;

      const data = {
        title, message
      };

      // Translate the title and body
      const translatedFields = [ 'title', 'message' ];
      const translatedData = await translateDataForStore(translatedFields, data);

      if ((booking.user.preferredLanguage).toLowerCase() === 'hindi') {
        title = translatedData.title.hi;
        message = translatedData.message.hi;
      } else {
        title = translatedData.title.en;
        message = translatedData.message.en;
      }

      promises.push(Notification.create({
        userId: booking.user._id,
        title,
        body: message,
        booking: booking._id,
        type: userTypeValue.USER
      }));

      if (fcmTokens.length > 0) {
        promises.push(sendFirebasePushMulticast(message, title, fcmTokens, '', { bookingId: booking._id.toString(), type: bookingType }));
      }
      const userPhoneNumber = `${booking.user.countryCode.replace('+', '') || '91'}${booking.user.phoneNumber}`;

      promises.push(sendWhatsAppMessage([ userPhoneNumber ], process.env.BOOKING_REMINDER_TEMPLATE, {
        body_1: {
          type: 'text',
          value: (booking?.user?.firstName?.en || booking?.user?.firstName) + ' ' + (booking?.user?.lastName?.en || booking?.user?.lastName)
        },
        body_2: {
          type: 'text',
          value: scheduleName + ' ' + bookingType
        },
        body_3: {
          type: 'text',
          value: bookingDate
        },
        body_4: {
          type: 'text',
          value: bookingTime
        },
        body_5: {
          type: 'text',
          value: templeName
        }
      }));

      templeIds.push(booking.temple._id);
    }

    const kioskUserMessagePromises = [];
    const kiosks = await Kiosk.find({ temple: { $in: templeIds } }).populate('kioskUsers');

    for (const kiosk of kiosks) {
      for (const kioskUser of kiosk.kioskUsers) {
        const kioskUserPhoneNumber = `${kioskUser.countryCode.replace('+', '') || '91'}${kioskUser.phoneNumber}`;

        kioskUserMessagePromises.push(sendWhatsAppMessage([ kioskUserPhoneNumber ], process.env.BOOKING_REMINDER_KIOSK_TEMPLATE, {
          body_1: {
            type: 'text',
            value: kioskUser?.name?.en || kioskUser?.name
          },
          body_2: {
            type: 'text',
            value: moment().utc().add(1, 'days').format('MMMM D, YYYY')
          },
        }));
      }
    }

    await Promise.all([ ...promises, ...kioskUserMessagePromises ]);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error in booking reminder cron job:', error);
  }
};

module.exports = {
  sendBookingReminders
};