const Booking = require('../models/Booking');
const { sendFirebasePushMulticast } = require('../utils/firebase_cm');
const Token = require('../models/Token');
const Notification = require('../models/Notification');
const moment = require('moment');
const { userTypeValue } = require('../constants/dbEnums');
const { translateDataForStore } = require('../utils/translateInput');

const sendBookingReminders = async () => {
  try {

    const tomorrowStart = moment().utc().add(1, 'days').startOf('day').toDate();
    const tomorrowEnd = moment().utc().add(1, 'days').endOf('day').toDate();
    
    const bookings = await Booking.find({
      date: { $gte: tomorrowStart, $lte: tomorrowEnd },
      status: 'COMPLETED'
    }).populate([
      { path: 'temple' },
      { path: 'user' },
      { path: 'darshanSchedule' },
      { path: 'poojaSchedule' },
      { path: 'event' }
    ]);

    const promises = [];

    for (const booking of bookings) {
      const tokens = await Token.find({ 
        userId: booking.user._id,
        fcmToken: { $ne: null, $exists: true }
      }).select('fcmToken');
      
      const fcmTokens = [ ...new Set(tokens.map(t => t.fcmToken)) ];

      let scheduleName = '';

      if (booking.darshanSchedule) {
        scheduleName = booking.darshanSchedule.name;
      } else if (booking.poojaSchedule) {
        scheduleName = booking.poojaSchedule.name;
      } else if (booking.event) {
        scheduleName = booking.event.name;
      }
      
      const templeName = booking.temple?.name || '';
      const bookingDate = moment(booking.date).format('MMMM D, YYYY');
      const bookingTime = booking.timeSlot?.startTime || '';
      
      let title = `Reminder: Your ${scheduleName} Booking Tomorrow!`;
      let message = `Just a reminder that you have a booking for ${scheduleName} at ${templeName} tomorrow, ${bookingDate}, at ${bookingTime}. Please arrive 30 minutes before. See you there!`;

      const data = {
        title, message
      };

      // Translate the title and body
      const translatedFields = ['title', 'message'];
      const translatedData = await translateDataForStore(translatedFields, data);
    
      title = translatedData.title;
      message = translatedData.message;

      promises.push(Notification.create({
        userId: booking.user._id,
        title,
        body: message,
        booking: booking._id,
        type: userTypeValue.USER
      }));

      if (fcmTokens.length > 0) {
        promises.push(sendFirebasePushMulticast(message, title, fcmTokens));
      }
    }
    await Promise.all(promises);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error in booking reminder cron job:', error);
  }
};

module.exports = {
  sendBookingReminders
};