const Booking = require('../models/Booking');
const { sendFirebasePushMulticast } = require('../utils/firebase_cm');
const Token = require('../models/Token');
const Notification = require('../models/Notification');
const moment = require('moment');
const { userTypeValue, type } = require('../constants/dbEnums');
const { translateDataForStore } = require('../utils/translateInput');

const sendBookingStartedReminders = async () => {
  try {
    
    const yesterdayStart = moment().utc().subtract(1, 'day').startOf('day').toDate();
    const tomorrowStart = moment().utc().add(1, 'day').startOf('day').toDate();

    const reminderBookings = await Booking.aggregate([
      {
        $match: {
          status: 'COMPLETED',
          $or: [
            { 'primaryDevoteeDetails.verification.verified': { $exists: false } },
            { 'primaryDevoteeDetails.verification.verified': false }
          ],
          date: { $gte: yesterdayStart, $lt: tomorrowStart },
          type: type.PHYSICAL_POOJA,
          oneHourReminderSent: false
        }
      },
      {
        $match: {
          $expr: {
            $eq: [
              {
                $size: {
                  $filter: {
                    input: '$otherDevotees',
                    as: 'devotee',
                    cond: { $eq: [ '$$devotee.verification.verified', true ] }
                  }
                }
              },
              0
            ]
          }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $lookup: {
          from: 'poojaschedules',
          localField: 'poojaSchedule',
          foreignField: '_id',
          as: 'poojaSchedule'
        }
      },
      { $unwind: '$poojaSchedule' },
      {
        $lookup: {
          from: 'temples',
          localField: 'temple',
          foreignField: '_id',
          as: 'temple'
        }
      },
      { $unwind: '$temple' }
    ]);

    const promises = [];
    let oneHourReminderTitle, oneHourReminderMessage, translationInput, bookingDateTime;
    const currentTime = moment().utc();

    for (const booking of reminderBookings) {

      bookingDateTime = moment(`${new Date(booking.date).toISOString()} ${booking.timeSlot.startTime}`, 'YYYY-MM-DD HH:mm A').utc();

      if (currentTime.diff(bookingDateTime, 'minutes') > 60) {

        const tokens = await Token.find({ 
          userId: booking.user._id,
          fcmToken: { $ne: null, $exists: true }
        }).select('fcmToken');

        const fcmTokens = [ ...new Set(tokens.map(t => t.fcmToken)) ];

        oneHourReminderTitle = 'Reminder: Your puja has started!';
        oneHourReminderMessage = `Just a reminder, your '${booking?.poojaSchedule?.name.en || booking?.poojaSchedule?.name}' puja at '${booking?.temple?.name.en || booking?.temple?.name}' has started an hour ago.`;
        translationInput = {
          title: oneHourReminderTitle,
          message: oneHourReminderMessage
        };
        
        const translatedFields = [ 'title', 'message' ];
        const translatedData = await translateDataForStore(translatedFields, translationInput);

        if ((booking.user.preferredLanguage).toLowerCase() === 'hindi') {
          oneHourReminderTitle = translatedData.title.hi;
          oneHourReminderMessage = translatedData.message.hi;
        } else {
          oneHourReminderTitle = translatedData.title.en;
          oneHourReminderMessage = translatedData.message.en;
        }

        promises.push(Notification.create({
          userId: booking.user._id,
          title: oneHourReminderTitle,
          body: oneHourReminderMessage,
          booking: booking._id,
          type: userTypeValue.USER
        }));

        if (fcmTokens.length > 0) {
          promises.push(sendFirebasePushMulticast(oneHourReminderMessage, oneHourReminderTitle, fcmTokens, '', { bookingId: booking._id.toString(), type: type.PHYSICAL_POOJA }));
        }

        promises.push(Booking.updateOne({ _id: booking._id }, { oneHourReminderSent: true }));
      }

    }

    const ratingReminderBookings = await Booking.aggregate([
      {
        $match: {
          status: 'COMPLETED',
          date: { $gte: yesterdayStart, $lt: tomorrowStart },
          type: type.PHYSICAL_POOJA,
          fourHourReminderSent: false
        }
      },
      {
        $match: {
          $or: [
            { 'primaryDevoteeDetails.verification.verified': true },
            {
              $expr: {
                $gt: [
                  {
                    $size: {
                      $filter: {
                        input: '$otherDevotees',
                        as: 'devotee',
                        cond: { $eq: [ '$$devotee.verification.verified', true ] }
                      }
                    }
                  },
                  0
                ]
              }
            }
          ]
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $lookup: {
          from: 'poojaschedules',
          localField: 'poojaSchedule',
          foreignField: '_id',
          as: 'poojaSchedule'
        }
      },
      { $unwind: '$poojaSchedule' },
      {
        $lookup: {
          from: 'temples',
          localField: 'temple',
          foreignField: '_id',
          as: 'temple'
        }
      },
      { $unwind: '$temple' }
    ]);

    for (const booking of ratingReminderBookings) {
      bookingDateTime = moment(`${new Date(booking.date).toISOString()} ${booking.timeSlot.startTime}`, 'YYYY-MM-DD HH:mm A').utc();
      
      if (currentTime.diff(bookingDateTime, 'minutes') > 240) {

        const tokens = await Token.find({ 
          userId: booking.user._id,
          fcmToken: { $ne: null, $exists: true }
        }).select('fcmToken');

        const fcmTokens = [ ...new Set(tokens.map(t => t.fcmToken)) ];

        oneHourReminderTitle = 'Rate your puja experience!';
        oneHourReminderMessage = `Rate your experience for '${booking?.poojaSchedule?.name?.en || booking?.poojaSchedule?.name}' puja at '${booking?.temple?.name?.en || booking?.temple?.name}'. Your feedback will help us improve our services.`;
        translationInput = {
          title: oneHourReminderTitle,
          message: oneHourReminderMessage
        };
        
        const translatedFields = [ 'title', 'message' ];
        const translatedData = await translateDataForStore(translatedFields, translationInput);

        if ((booking.user.preferredLanguage).toLowerCase() === 'hindi') {
          oneHourReminderTitle = translatedData.title.hi;
          oneHourReminderMessage = translatedData.message.hi;
        } else {
          oneHourReminderTitle = translatedData.title.en;
          oneHourReminderMessage = translatedData.message.en;
        }
        promises.push(Notification.create({
          userId: booking.user._id,
          title: oneHourReminderTitle,
          body: oneHourReminderMessage,
          booking: booking._id,
          type: userTypeValue.USER
        }));

        if (fcmTokens.length > 0) {
          promises.push(sendFirebasePushMulticast(oneHourReminderMessage, oneHourReminderTitle, fcmTokens, '', { bookingId: booking._id.toString(), type: type.PHYSICAL_POOJA }));
        }

        promises.push(Booking.updateOne({ _id: booking._id }, { fourHourReminderSent: true }));
      }
    }

    await Promise.all(promises);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error in pooja booking started reminder cron job:', error);
  }
};

module.exports = {
  sendBookingStartedReminders
};