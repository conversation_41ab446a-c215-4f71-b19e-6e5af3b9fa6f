const Token = require('../models/Token');
const { sendFirebasePushMulticast } = require('../utils/firebase_cm');
const moment = require('moment-timezone');
const { broadcastNotificationType } = require('../constants/dbEnums');

const User = require('../models/User');
const Booking = require('../models/Booking');

const sendBroadcastReminders = async () => {
  try {
    
    // Get the 3 days ago date :
    const threeDaysAgo = moment().utc().subtract(3, 'days').startOf('day').toDate();
    
    // Get all bookings of type Virtual or Physical Pooja :
    const bookings = await Booking.find({
      type: { $in: [ 'VIRTUAL_POOJA', 'PHYSICAL_POOJA' ] },
      status: 'COMPLETED',
    });

    if (bookings.length === 0) {
      return;
    }

    // Get unique users with only 1 booking :
    const uniqueBookingsMap = new Map();

    bookings.forEach(booking => {
      const userId = booking?.user?.toString();

      if (!uniqueBookingsMap.has(userId)) {
        uniqueBookingsMap.set(userId, 1);
      } else {
        uniqueBookingsMap.set(userId, uniqueBookingsMap.get(userId) + 1);
      }
    });

    const userIds = [ ...uniqueBookingsMap.keys() ].filter(userId => uniqueBookingsMap.get(userId) === 1);
    
    if (userIds.length === 0) {
      return;
    }

    // Filter bookings that are 3 days old or more and extract user ids :
    const recentBookings = bookings.filter(booking => {
      const userId = booking?.user?.toString();

      const date = moment(booking.date).format('YYYY-MM-DD');
      const time = booking?.timeSlot?.startTime ?? '12:00 AM'; 
    
      const combined = moment.tz(`${date} ${time}`, 'YYYY-MM-DD hh:mm A', 'Asia/Kolkata');
      const bookingDateTime = combined.utc().toISOString();
    
      return userIds.includes(userId) && new Date(bookingDateTime) <= threeDaysAgo;
    });

    const recentUserIds = recentBookings.map(booking => booking.user.toString());

    if (recentUserIds.length === 0) {
      return;
    }

    // Get users who have not received the reminder yet :
    const users = await User.find({
      _id: { $in: recentUserIds },
      deletedAt: null,
      $or: [
        { isThreeDaysReminderSent: false },
        { isThreeDaysReminderSent: null },
        { isThreeDaysReminderSent: { $exists: false } }
      ]
    }).select('_id');

    const finalUserIds = users.map(user => user._id.toString());

    // Get FCM tokens for these users :
    const tokens = await Token.find({
      userId: { $in: finalUserIds },
      fcmToken: { $ne: null, $exists: true }
    }).select('fcmToken');

    const fcmTokens = [ ...new Set(tokens.map(t => t.fcmToken)) ];

    if (fcmTokens.length === 0) {
      return;
    }

    // Send push notification :
    await sendFirebasePushMulticast('🙏 Hope your pooja brought you peace. Explore temple darshans or book for your loved ones.', 'Ek Ishwar', fcmTokens, '', { type: broadcastNotificationType.THREE_DAYS_REMINDER });

    // Mark users as reminded :
    await User.updateMany({
      _id: { $in: finalUserIds }
    }, {
      isThreeDaysReminderSent: true
    });

  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error in broadcast reminder cron job:', error);
  }
};

module.exports = {
  sendBroadcastReminders
};