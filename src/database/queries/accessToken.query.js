const Token = require('../../models/Token');

const findToken = async (query) => Token.findOne(query).lean();

const createToken = async (obj) => new Token(obj).save();

const updateToken = async (query, updateFields, options) => Token.updateOne(query, updateFields, options);

const findAndUpdateToken = async (query, updateFields, options) => Token.findOneAndUpdate(query, updateFields, options);

const deleteToken = async (query) => Token.deleteOne(query);

const deleteTokens = async (query) => Token.deleteMany(query);

module.exports = {
  findToken,
  createToken,
  updateToken,
  findAndUpdateToken,
  deleteToken,
  deleteTokens,
};

