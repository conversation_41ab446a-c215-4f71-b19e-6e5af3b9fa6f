const OneTimePasswords = require('../../models/OneTimePassword');

const findOtp = async (query) => OneTimePasswords.findOne(query).lean();

const createOtp = async (obj) => new OneTimePasswords(obj).save();

const updateOtp = async (query, updateFields, options) => OneTimePasswords.updateOne(query, updateFields, options);

const findAndUpdateOtp = async (query, updateFields, options) => OneTimePasswords.findOneAndUpdate(query, updateFields, options);

const deleteOtp = async (query) => OneTimePasswords.deleteOne(query);

const deleteOtps = async (query) => OneTimePasswords.deleteMany(query);

module.exports = {
  findOtp,
  createOtp,
  updateOtp,
  findAndUpdateOtp,
  deleteOtp,
  deleteOtps,
};

