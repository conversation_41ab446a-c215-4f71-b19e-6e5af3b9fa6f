const User = require('../../models/User');

const findUser = async (query) => User.findOne(query).lean();

const createUser = async (obj) => new User(obj).save();

const updateUser = async (query, updateFields, options) => User.updateOne(query, updateFields, options);

const findOneAndUpdateUser = async (query, updateFields, options) => User.findOneAndUpdate(query, updateFields, options);

const deleteUser = async (query) => User.deleteOne(query);

const deleteUsers = async (query) => User.deleteMany(query);

module.exports = {
  findUser,
  createUser,
  updateUser,
  findOneAndUpdateUser,
  deleteUser,
  deleteUsers,
};

