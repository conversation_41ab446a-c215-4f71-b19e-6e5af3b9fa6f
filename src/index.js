// require('newrelic');
global.__basedir = __dirname;
const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const mongoose = require('mongoose');
const connectDB = require('./config/database');
const modules = require('./modules');
const { addSuperAdminData } = require('./modules/admin/auth/service');
const { initCronJobs } = require('./services/cronService');

dotenv.config();

const app = express();

app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  methods: [ 'GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH' ],
  allowedHeaders: [ 'Content-Type', 'Authorization' ]
}));

app.use(express.json());

// Health check route for Kubernetes
app.get('/health', (req, res) => {
  const dbState = mongoose.connection.readyState;

  if (dbState === 1) {
    return res.status(200).send('OK');
  } else {
    return res.status(500).send('MongoDB not connected');
  }
});

// Initialize all modules
modules(app);

// Connect to MongoDB
connectDB()
  .then(() => {
    process.stdout.write('MongoDB connected...');

    // Graceful shutdown handler
    const gracefulShutdown = async () => {
      process.stdout.write('🛑 Shutdown signal received. Disconnecting MongoDB...');

      // Set a timeout in case disconnect hangs
      const timeout = setTimeout(() => {
        process.stderr.write('❌ Force exiting due to timeout.');
        process.exit(1);
      }, 10000); // 10 seconds

      try {
        await mongoose.disconnect();
        process.stdout.write('✅ MongoDB disconnected.');
      } catch (err) {
        process.stderr.write('❌ Error disconnecting MongoDB:', err);
      }

      clearTimeout(timeout);
      process.exit(0);
    };

    process.on('SIGINT', gracefulShutdown); // For Ctrl+C (local dev)
    process.on('SIGTERM', gracefulShutdown); // For Kubernetes pod shutdown

    addSuperAdminData();

    // Initialize cron jobs after database connection is established
    initCronJobs();
  })
  .catch(err => process.stderr.write('Error: ' + err));

// Global error handling
process.on('unhandledRejection', (reason) => {
  process.stderr.write('❌ Unhandled Rejection:', reason);
  process.exit(1);
});

process.on('uncaughtException', (err) => {
  process.stderr.write('❌ Uncaught Exception:', err);
  process.exit(1);
});

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  process.stdout.write(`Server is running on port ${PORT}`);
});
