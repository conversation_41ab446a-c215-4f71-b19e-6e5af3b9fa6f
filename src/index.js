global.__basedir = __dirname;
const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const connectDB = require('./config/database');
const modules = require('./modules');
const { addSuperAdminData } = require('./modules/admin/auth/service');
const { initCronJobs } = require('./services/cronService');

dotenv.config();

const app = express();

app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  methods: [ 'GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH' ],
  allowedHeaders: [ 'Content-Type', 'Authorization' ]
}));

app.use(express.json());

// Initialize all modules
modules(app);

// Connect to MongoDB
connectDB()
  .then(() => {
    process.stdout.write('MongoDB connected...');
    addSuperAdminData();

    // Initialize cron jobs after database connection is established
    initCronJobs();
  })
  .catch(err => process.stderr.write('Error: ' + err));

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  process.stdout.write(`Server is running on port ${PORT}`);
});
