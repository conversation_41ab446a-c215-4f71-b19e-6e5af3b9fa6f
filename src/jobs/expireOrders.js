const Order = require('../models/Order');
const Payment = require('../models/Payment');
const { orderStatusValue, paymentStatusValue } = require('../constants/dbEnums');
const { logOrderEvent } = require('../utils/logger');
const ProductShipment = require('../models/ProductShipment');
const Transaction = require('../models/Transaction');

/**
 * Job to expire pending orders after a certain time
 * This should be run as a scheduled task (e.g., every hour)
 */
const expirePendingOrders = async () => {
  try {
    // Find orders that are pending for more than 30 minutes
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

    const pendingOrders = await Order.find({
      orderStatus: orderStatusValue.PENDING,
      paymentStatus: paymentStatusValue.PENDING,
      createdAt: { $lt: thirtyMinutesAgo }
    });

    logOrderEvent('EXPIRE_ORDERS_JOB', {
      count: pendingOrders.length,
      timestamp: new Date()
    });

    // Delete Order, ProductShipment, Payment, Transaction by pendingOrders ids
    await Payment.deleteMany({
      order: { $in: pendingOrders.map(order => order._id) }
    });
    await ProductShipment.deleteMany({
      order: { $in: pendingOrders.map(order => order._id) }
    });
    await Transaction.deleteMany({
      order: { $in: pendingOrders.map(order => order._id) }
    });
    await Order.deleteMany({
      _id: { $in: pendingOrders.map(order => order._id) }
    });

    return {
      success: true,
      expiredCount: pendingOrders.length
    };
  } catch (error) {
    logOrderEvent('EXPIRE_ORDERS_JOB_ERROR', {
      error: error.message
    });

    throw error;
  }
};

module.exports = {
  expirePendingOrders
};
