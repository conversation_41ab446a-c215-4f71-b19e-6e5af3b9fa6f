/**
 * File to represent the api response messages
 */
const messages = {
  // errors
  SERVER_ERROR: 'Something went wrong',
  SERVER_ERROR_HI: 'कुछ गलत हो गया',

  TOO_MANY_REQUESTS: 'Too many requests. Please try again later.',
  TOO_MANY_REQUESTS_HI: 'बहुत सारे अनुरोध। कृपया बाद में पुनः प्रयास करें।',

  ACCESS_DENIED: 'Access denied',
  ACCESS_DENIED_HI: 'पहुंच निरस्त की गई',

  NOT_FOUND: 'Not found',
  NOT_FOUND_HI: 'नहीं मिला',

  INVALID_OTP: 'Please enter the correct OTP & try again',
  INVALID_OTP_HI: 'कृपया सही OTP दर्ज करें और पुनः प्रयास करें',

  OTP_EXPIRED: 'OTP expired',
  OTP_EXPIRED_HI: 'OTP की अवधि समाप्त हो चुकी है',

  USER_NOT_FOUND: 'User not found',
  USER_NOT_FOUND_HI: 'उपयोगकर्ता नहीं मिला',

  USER_INACTIVE: 'Your account has been deactivated. Please contact the admin for further details',
  USER_INACTIVE_HI: 'आपका खाता निष्क्रिय कर दिया गया है। कृपया अधिक जानकारी के लिए व्यवस्थापक से संपर्क करें',

  ALREADY_EXISTS: 'Already Exists',
  ALREADY_EXISTS_HI: 'पहले से मौजूद है',

  ERROR: 'API executed with errors',
  ERROR_HI: 'API त्रुटियों के साथ निष्पादित हुई',

  NOT_AUTHORIZED: 'Your session has expired. Please login again',
  NOT_AUTHORIZED_HI: 'आपका सत्र समाप्त हो गया है। कृपया फिर से लॉगिन करें',

  INVALID_TOKEN: 'Invalid token',
  INVALID_TOKEN_HI: 'अमान्य टोकन',

  INVALID_MONGO_ID: 'Invalid ID format',
  INVALID_MONGO_ID_HI: 'अमान्य आईडी प्रारूप',

  FCM_TOKEN_REQUIRED: 'FCM token is required',
  FCM_TOKEN_REQUIRED_HI: 'FCM टोकन आवश्यक है',

  // success
  SUCCESS: 'API executed successfully',
  SUCCESS_HI: 'API सफलतापूर्वक निष्पादित हुई',

  SUCCESS_MESSAGE: 'Success',
  SUCCESS_MESSAGE_HI: 'सफलता',

  OTP_SENT: 'An OTP has been sent on this phone number',
  OTP_SENT_HI: 'OTP इस फोन नंबर पर भेजा गया है',

  // users
  USER_ALREADY_EXISTS: 'User already exists!',
  USER_ALREADY_EXISTS_HI: 'उपयोगकर्ता पहले से मौजूद है!',

  USER_ALREADY_REGISTERED: 'This user is already registered on PurDriven',
  USER_ALREADY_REGISTERED_HI: 'यह उपयोगकर्ता पहले से PurDriven पर पंजीकृत है',

  VERIFY_EMAIL_ACCOUNT: 'Please verify your email account',
  VERIFY_EMAIL_ACCOUNT_HI: 'कृपया अपना ईमेल खाता सत्यापित करें',

  EMAIL_ACCOUNT_VERIFIED: 'Email verified successfully',
  EMAIL_ACCOUNT_VERIFIED_HI: 'ईमेल सफलतापूर्वक सत्यापित हो गया',

  PHONE_NUMBER_VERIFIED: 'Phone number verified successfully',
  PHONE_NUMBER_VERIFIED_HI: 'फोन नंबर सफलतापूर्वक सत्यापित हो गया',

  VERIFY_PHONE_NUMBER: 'Please verify your phone number first',
  VERIFY_PHONE_NUMBER_HI: 'कृपया पहले अपना फोन नंबर सत्यापित करें',

  USER_REGISTERED_SUCCESSFULLY: 'User registered successfully',
  USER_REGISTERED_SUCCESSFULLY_HI: 'उपयोगकर्ता सफलतापूर्वक पंजीकृत हुआ',

  VERIFY_OLD_EMAIL_FIRST: 'Please verify your old email first to complete this action',
  VERIFY_OLD_EMAIL_FIRST_HI: 'इस कार्रवाई को पूरा करने के लिए कृपया पहले अपना पुराना ईमेल सत्यापित करें',

  NEW_EMAIL_CANNOT_BE_SAME: 'New email can\'t be the same as old email',
  NEW_EMAIL_CANNOT_BE_SAME_HI: 'नया ईमेल पुराने ईमेल के समान नहीं हो सकता',

  EMAIL_ALREADY_EXISTS: 'Email already exists',
  EMAIL_ALREADY_EXISTS_HI: 'ईमेल पहले से मौजूद है',

  PHONE_NUMBER_ALREADY_EXISTS: 'Phone number already exists',
  PHONE_NUMBER_ALREADY_EXISTS_HI: 'फोन नंबर पहले से मौजूद है',

  ADDRESS_NOT_FOUND: 'Address not found',
  ADDRESS_NOT_FOUND_HI: 'पता नहीं मिला',

  TEMPLE_ADMIN_NOT_FOUND: 'Temple admin not found',
  TEMPLE_ADMIN_NOT_FOUND_HI: 'मंदिर प्रशासक नहीं मिला',

  LIVE_DARSHAN_SCHEDULED: 'Live Darshan has already been started',
  LIVE_DARSHAN_SCHEDULED_HI: 'लाइव दर्शन पहले से शुरू हो चुका है',

  INVALID_USER_ID: 'Invalid user ID',
  INVALID_USER_ID_HI: 'अमान्य उपयोगकर्ता आईडी',

  NOTIFICATION_NOT_FOUND: 'Notification not found',
  NOTIFICATION_NOT_FOUND_HI: 'सूचना नहीं मिली',

  // koisk
  KIOSK_ALREADY_EXISTS: 'Kiosk already exists for this temple',
  KIOSK_ALREADY_EXISTS_HI: 'इस मंदिर के लिए कियोस्क पहले से मौजूद है',

  // validation
  CONFIRM_PASSWORD_NOT_MATCHED: 'Confirm Password doesn\'t match',
  CONFIRM_PASSWORD_NOT_MATCHED_HI: 'पासवर्ड और कन्फर्म पासवर्ड मेल नहीं खाते',

  PASSWORD_NOT_VALID: 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  PASSWORD_NOT_VALID_HI: 'पासवर्ड में कम से कम एक अपरकेस अक्षर, एक लोअरकेस अक्षर और एक संख्या होनी चाहिए',

  CURRENT_PASSWORD_NOT_VALID: 'Current Password must contain at least one uppercase letter, one lowercase letter, and one number',
  CURRENT_PASSWORD_NOT_VALID_HI: 'वर्तमान पासवर्ड में कम से कम एक अपरकेस अक्षर, एक लोअरकेस अक्षर और एक संख्या होनी चाहिए',

  INVALID_CREDENTIALS: 'Invalid credentials',
  INVALID_CREDENTIALS_HI: 'अमान्य प्रमाण-पत्र',

  LOGOUT_ERROR: 'User logout error',
  LOGOUT_ERROR_HI: 'उपयोगकर्ता लॉगआउट में त्रुटि',

  LOGOUT_SUCCESS: 'User logout successfully',
  LOGOUT_SUCCESS_HI: 'उपयोगकर्ता सफलतापूर्वक लॉगआउट हुआ',

  // favorites - temples
  TEMPLE_ADDED_TO_FAVORITES: 'Temple added to favorites successfully',
  TEMPLE_ADDED_TO_FAVORITES_HI: 'मंदिर को पसंदीदा में सफलतापूर्वक जोड़ा गया',

  TEMPLE_REMOVED_FROM_FAVORITES: 'Temple removed from favorites successfully',
  TEMPLE_REMOVED_FROM_FAVORITES_HI: 'मंदिर को पसंदीदा से सफलतापूर्वक हटाया गया',

  TEMPLE_NOT_IN_FAVORITES: 'Temple is not in favorites',
  TEMPLE_NOT_IN_FAVORITES_HI: 'मंदिर पसंदीदा में नहीं है',

  TEMPLE_ALREADY_IN_FAVORITES: 'Temple is already in favorites',
  TEMPLE_ALREADY_IN_FAVORITES_HI: 'मंदिर पहले से पसंदीदा में है',

  TEMPLE_NOT_FOUND: 'Temple not found',
  TEMPLE_NOT_FOUND_HI: 'मंदिर नहीं मिला',

  // favorites - products
  PRODUCT_ADDED_TO_FAVORITES: 'Product added to favorites successfully',
  PRODUCT_ADDED_TO_FAVORITES_HI: 'उत्पाद को पसंदीदा में सफलतापूर्वक जोड़ा गया',

  PRODUCT_REMOVED_FROM_FAVORITES: 'Product removed from favorites successfully',
  PRODUCT_REMOVED_FROM_FAVORITES_HI: 'उत्पाद को पसंदीदा से सफलतापूर्वक हटाया गया',

  PRODUCT_NOT_IN_FAVORITES: 'Product is not in favorites',
  PRODUCT_NOT_IN_FAVORITES_HI: 'उत्पाद पसंदीदा में नहीं है',

  PRODUCT_ALREADY_IN_FAVORITES: 'Product is already in favorites',
  PRODUCT_ALREADY_IN_FAVORITES_HI: 'उत्पाद पहले से पसंदीदा में है',

  VARIANT_ADDED_TO_FAVORITES: 'Product variant added to favorites successfully',
  VARIANT_ADDED_TO_FAVORITES_HI: 'उत्पाद वेरिएंट को पसंदीदा में सफलतापूर्वक जोड़ा गया',

  VARIANT_REMOVED_FROM_FAVORITES: 'Product variant removed from favorites successfully',
  VARIANT_REMOVED_FROM_FAVORITES_HI: 'उत्पाद वेरिएंट को पसंदीदा से सफलतापूर्वक हटाया गया',

  VARIANT_NOT_IN_FAVORITES: 'Product variant is not in favorites',
  VARIANT_NOT_IN_FAVORITES_HI: 'उत्पाद वेरिएंट पसंदीदा में नहीं है',

  VARIANT_ALREADY_IN_FAVORITES: 'Product variant is already in favorites',
  VARIANT_ALREADY_IN_FAVORITES_HI: 'उत्पाद वेरिएंट पहले से पसंदीदा में है',

  // live-darshan
  LIVE_DARSHAN_CREATED: 'Live Darshan created successfully',
  LIVE_DARSHAN_CREATED_HI: 'लाइव दर्शन सफलतापूर्वक बनाया गया',

  LIVE_DARSHAN_CREATION_FAILED: 'Live Darshan creation failed',
  LIVE_DARSHAN_CREATION_FAILED_HI: 'लाइव दर्शन बनाना विफल हुआ',

  LIVE_DARSHAN_NOT_FOUND: 'Live Darshan not found',
  LIVE_DARSHAN_NOT_FOUND_HI: 'लाइव दर्शन नहीं मिला',

  LIVE_DARSHAN_UPDATED: 'Live Darshan updated successfully',
  LIVE_DARSHAN_UPDATED_HI: 'लाइव दर्शन सफलतापूर्वक अपडेट हुआ',

  LIVE_DARSHAN_DELETED: 'Live Darshan deleted successfully',
  LIVE_DARSHAN_DELETED_HI: 'लाइव दर्शन सफलतापूर्वक हटाया गया',

  RECORDING_NOT_FOUND: 'Recording not found',
  RECORDING_NOT_FOUND_HI: 'रिकॉर्डिंग नहीं मिली',

  // booking
  DARSHAN_BOOKING_CREATED: 'Darshan booking created successfully',
  DARSHAN_BOOKING_CREATED_HI: 'दर्शन बुकिंग सफलतापूर्वक बनाई गई',

  POOJA_BOOKING_CREATED: 'Puja booking created successfully',
  POOJA_BOOKING_CREATED_HI: 'पूजा बुकिंग सफलतापूर्वक बनाई गई',

  BOOKING_NOT_FOUND: 'Booking not found',
  BOOKING_NOT_FOUND_HI: 'बुकिंग नहीं मिली',

  INVALID_PHONE_NUMBER: 'The phone number entered is not valid.',
  INVALID_PHONE_NUMBER_HI: 'दर्ज किया गया फ़ोन नंबर मान्य नहीं है',
  VALID_PHONE_NUMBER: 'The phone number entered is valid',
  VALID_PHONE_NUMBER_HI: 'दर्ज किया गया फ़ोन नंबर मान्य है',
  WHATSAPP_NUMBER_RETRIEVED: 'WhatsApp numbers retrieved successfully',
  WHATSAPP_NUMBER_RETRIEVED_HI: 'व्हाट्सएप नंबर सफलतापूर्वक प्राप्त किए गए हैं',

  // Events
  EVENT_CREATED_HI: 'इवेंट सफलतापूर्वक बनाया गया',
  EVENT_UPDATED_HI: 'इवेंट सफलतापूर्वक अपडेट किया गया',
  EVENT_DELETED_HI: 'इवेंट सफलतापूर्वक हटाया गया',
  EVENT_NOT_FOUND_HI: 'इवेंट नहीं मिला',
  EVENT_RETRIEVED_HI: 'इवेंट सफलतापूर्वक प्राप्त हुआ',
  EVENTS_RETRIEVED_HI: 'इवेंट्स सफलतापूर्वक प्राप्त हुए',
  HOMEPAGE_EVENTS_RETRIEVED_HI: 'होमपेज इवेंट्स सफलतापूर्वक प्राप्त हुए',
  EVENT_IMAGE_DELETED_HI: 'इवेंट की छवि सफलतापूर्वक हटाई गई',
  UPLOAD_URL_GENERATED_HI: 'अपलोड यूआरएल सफलतापूर्वक जेनरेट हुआ',
  EVENT_BOOKING_CREATED_HI: 'इवेंट बुकिंग सफलतापूर्वक बनाई गई',

  // Verification
  DEVOTEE_ALREADY_VERIFIED_HI: 'भक्त पहले ही चेक-इन कर चुके हैं',
  DEVOTEE_VERIFIED_SUCCESS_HI: 'भक्त सफलतापूर्वक चेक-इन हुए',
  DEVOTEE_ALREADY_VERIFIED_DATE_HI: 'भक्त इस तारीख के लिए पहले ही चेक-इन कर चुके हैं',
  DEVOTEE_VERIFIED_SUCCESS_DATE_HI: 'भक्त इस तारीख के लिए सफलतापूर्वक चेक-इन हुए',
  DEVOTEE_NOT_FOUND_HI: 'इस बुकिंग में भक्त नहीं मिला',
  INVALID_EVENT_DATE_HI: 'इस इवेंट बुकिंग के लिए अमान्य तिथि',
  NO_EVENT_DATES_HI: 'इस इवेंट बुकिंग के लिए कोई तिथियाँ उपलब्ध नहीं हैं',

  // Temple Category
  TEMPLE_CATEGORY_CREATED_HI: 'मंदिर श्रेणी सफलतापूर्वक बनाई गई',
  TEMPLE_CATEGORIES_RETRIEVED_HI: 'मंदिर श्रेणियाँ सफलतापूर्वक प्राप्त हुईं',
  TEMPLE_CATEGORY_RETRIEVED_HI: 'मंदिर श्रेणी सफलतापूर्वक प्राप्त हुई',
  TEMPLE_CATEGORY_UPDATED_HI: 'मंदिर श्रेणी सफलतापूर्वक अपडेट की गई',
  TEMPLE_CATEGORY_DELETED_HI: 'मंदिर श्रेणी सफलतापूर्वक हटाई गई',

  // Inventory
  INVENTORY_SENT_HI: 'इन्वेंटरी सफलतापूर्वक भेजी गई',
  INVENTORY_RECEIVED_HI: 'इन्वेंटरी सफलतापूर्वक प्राप्त हुई',
  INVENTORIES_RETRIEVED_HI: 'इन्वेंटरीज़ सफलतापूर्वक प्राप्त हुईं',
  INVENTORY_RETRIEVED_HI: 'इन्वेंटरी सफलतापूर्वक प्राप्त हुई',
  INVENTORY_UPDATED_HI: 'इन्वेंटरी सफलतापूर्वक अपडेट हुई',

  // Vendor Management
  VENDOR_CREATED_HI: 'वेंडर सफलतापूर्वक बनाया गया',
  VENDOR_UPDATED_HI: 'वेंडर सफलतापूर्वक अपडेट किया गया',
  VENDOR_RETRIEVED_HI: 'वेंडर सफलतापूर्वक प्राप्त हुआ',
  VENDORS_RETRIEVED_HI: 'वेंडर्स सफलतापूर्वक प्राप्त हुए',
  VENDOR_DOCUMENT_DELETED_HI: 'वेंडर दस्तावेज़ सफलतापूर्वक हटाया गया',

  // Products
  PRODUCT_CREATED_HI: 'उत्पाद सफलतापूर्वक बनाया गया',
  PRODUCT_CREATED_PENDING_APPROVAL_HI: 'उत्पाद सफलतापूर्वक बनाया गया और अनुमोदन लंबित है',
  PRODUCT_UPDATED_HI: 'उत्पाद सफलतापूर्वक अपडेट किया गया',
  PRODUCT_DELETED_HI: 'उत्पाद सफलतापूर्वक हटाया गया',
  PRODUCT_NOT_FOUND_HI: 'उत्पाद नहीं मिला',
  PRODUCT_APPROVED_HI: 'उत्पाद सफलतापूर्वक अनुमोदित हुआ',
  PRODUCT_REJECTED_HI: 'उत्पाद अस्वीकृत किया गया',
  PRODUCT_FEATURED_HI: 'उत्पाद को फीचर्ड किया गया',
  PRODUCT_UNFEATURED_HI: 'उत्पाद को अनफीचर्ड किया गया',
  PRODUCT_NOT_PENDING_HI: 'उत्पाद पेंडिंग स्थिति में नहीं है',
  INVALID_PRODUCT_ID_HI: 'अमान्य उत्पाद आईडी',
  SKU_ALREADY_EXISTS_HI: 'SKU पहले से मौजूद है',
  ONLY_ACTIVE_PRODUCTS_CAN_BE_FEATURED_HI: 'केवल सक्रिय उत्पादों को ही फीचर्ड किया जा सकता है',
  REJECTION_REASON_REQUIRED_HI: 'अस्वीकृति का कारण आवश्यक है',
  INSUFFICIENT_STOCK_HI: 'पर्याप्त स्टॉक नहीं है',
  REVIEW_ADDED_SUCCESSFULLY_HI: 'समीक्षा सफलतापूर्वक जोड़ी गई',
  PRODUCTS_RETRIEVED_HI: 'उत्पाद सफलतापूर्वक प्राप्त हुए',
  PRODUCT_RETRIEVED_HI: 'उत्पाद सफलतापूर्वक प्राप्त हुआ',
  UNAUTHORIZED_ACCESS_HI: 'आपको इस संसाधन तक पहुँचने की अनुमति नहीं है',
  PRODUCT_REVIEW_RETRIEVED_HI: 'उत्पाद समीक्षा विवरण सफलतापूर्वक प्राप्त हुआ',
  PRODUCT_NOTIFICATION_SENT_HI: 'उत्पाद सूचना सफलतापूर्वक भेजी गई',

  // events
  EVENT_CREATED: 'Event created successfully',
  EVENT_UPDATED: 'Event updated successfully',
  EVENT_DELETED: 'Event deleted successfully',
  EVENT_NOT_FOUND: 'Event not found',
  EVENT_RETRIEVED: 'Event retrieved successfully',
  EVENTS_RETRIEVED: 'Events retrieved successfully',
  HOMEPAGE_EVENTS_RETRIEVED: 'Homepage events retrieved successfully',
  EVENT_IMAGE_DELETED: 'Event image deleted successfully',
  UPLOAD_URL_GENERATED: 'Upload URL generated successfully',
  EVENT_BOOKING_CREATED: 'Event booking created successfully',

  // verification
  DEVOTEE_ALREADY_VERIFIED: 'Devotee already checked in',
  DEVOTEE_VERIFIED_SUCCESS: 'Devotee successfully checked in',
  DEVOTEE_ALREADY_VERIFIED_DATE: 'Devotee already checked in for this date',
  DEVOTEE_VERIFIED_SUCCESS_DATE: 'Devotee successfully checked in for this date',
  DEVOTEE_NOT_FOUND: 'Devotee not found in this booking',
  INVALID_EVENT_DATE: 'Invalid date for this event booking',
  NO_EVENT_DATES: 'No dates available for this event booking',

  // temple category
  TEMPLE_CATEGORY_CREATED: 'Temple category created successfully',
  TEMPLE_CATEGORIES_RETRIEVED: 'Temple categories retrieved successfully',
  TEMPLE_CATEGORY_RETRIEVED: 'Temple category retrieved successfully',
  TEMPLE_CATEGORY_UPDATED: 'Temple category updated successfully',
  TEMPLE_CATEGORY_DELETED: 'Temple category deleted successfully',

  // inventory
  INVENTORY_SENT: 'Inventory sent successfully',
  INVENTORY_RECEIVED: 'Inventory received successfully',
  INVENTORIES_RETRIEVED: 'Inventories retrieved successfully',
  INVENTORY_RETRIEVED: 'Inventory retrieved successfully',
  INVENTORY_UPDATED: 'Inventory updated successfully',
  INVENTORY_STATUS_FETCHED: 'Inventory status fetched successfully',
  INVENTORY_STATUS_FETCHED_HI: 'इन्वेंटरी की स्थिति सफलतापूर्वक प्राप्त की गई',
  INVENTORY_FOUND: 'Active inventory found',
  NO_INVENTORY_FOUND: 'No active inventory found',
  INVENTORY_FOUND_HI: 'सक्रिय इन्वेंटरी पाई गई है',
  NO_INVENTORY_FOUND_HI: 'कोई सक्रिय इन्वेंटरी उपलब्ध नहीं है',

  // vendor management
  VENDOR_CREATED: 'Vendor created successfully',
  VENDOR_UPDATED: 'Vendor updated successfully',
  VENDOR_RETRIEVED: 'Vendor retrieved successfully',
  VENDORS_RETRIEVED: 'Vendors retrieved successfully',
  VENDOR_DOCUMENT_DELETED: 'Vendor document deleted successfully',

  // shop - products
  PRODUCT_CREATED: 'Product created successfully',
  PRODUCT_CREATED_PENDING_APPROVAL: 'Product created successfully and pending approval',
  PRODUCT_UPDATED: 'Product updated successfully',
  PRODUCT_DELETED: 'Product deleted successfully',
  PRODUCT_NOT_FOUND: 'Product not found',
  PRODUCT_APPROVED: 'Product approved successfully',
  PRODUCT_REJECTED: 'Product rejected successfully',
  PRODUCT_FEATURED: 'Product featured successfully',
  PRODUCT_UNFEATURED: 'Product unfeatured successfully',
  PRODUCT_NOT_PENDING: 'Product is not in pending state',
  INVALID_PRODUCT_ID: 'Invalid product ID',
  SKU_ALREADY_EXISTS: 'SKU already exists',
  ONLY_ACTIVE_PRODUCTS_CAN_BE_FEATURED: 'Only active products can be featured',
  REJECTION_REASON_REQUIRED: 'Rejection reason is required',
  INSUFFICIENT_STOCK: 'Insufficient stock',
  REVIEW_ADDED_SUCCESSFULLY: 'Review added successfully',
  PRODUCTS_RETRIEVED: 'Products retrieved successfully',
  PRODUCT_RETRIEVED: 'Product retrieved successfully',
  UNAUTHORIZED_ACCESS: 'You are not authorized to access this resource',
  PRODUCT_REVIEW_RETRIEVED: 'Product review details retrieved successfully',
  PRODUCT_NOTIFICATION_SENT: 'Product notification sent successfully',

  CATEGORY_CREATED: 'Category created successfully',
  CATEGORY_CREATED_HI: 'श्रेणी सफलतापूर्वक बनाई गई',
  CATEGORY_UPDATED: 'Category updated successfully',
  CATEGORY_UPDATED_HI: 'श्रेणी सफलतापूर्वक अपडेट की गई',
  CATEGORY_DELETED: 'Category deleted successfully',
  CATEGORY_DELETED_HI: 'श्रेणी सफलतापूर्वक हटाई गई',
  CATEGORY_NOT_FOUND: 'Category not found',
  CATEGORY_NOT_FOUND_HI: 'श्रेणी नहीं मिली',
  CATEGORY_ALREADY_EXISTS: 'Category already exists',
  CATEGORY_ALREADY_EXISTS_HI: 'श्रेणी पहले से मौजूद है',
  CATEGORY_NAME_ALREADY_EXISTS: 'Category name already exists',
  CATEGORY_NAME_ALREADY_EXISTS_HI: 'श्रेणी नाम पहले से मौजूद है',
  CATEGORY_HAS_CHILD_CATEGORIES: 'Category has child categories',
  CATEGORY_HAS_CHILD_CATEGORIES_HI: 'श्रेणी में उप-श्रेणियाँ हैं',
  CATEGORY_HAS_PRODUCTS: 'Category has products',
  CATEGORY_HAS_PRODUCTS_HI: 'श्रेणी में उत्पाद हैं',
  INVALID_CATEGORY_ID: 'Invalid category ID',
  INVALID_CATEGORY_ID_HI: 'अमान्य श्रेणी आईडी',
  PARENT_CATEGORY_NOT_FOUND: 'Parent category not found',
  PARENT_CATEGORY_NOT_FOUND_HI: 'मूल श्रेणी नहीं मिली',

  // shop - subcategories
  SUBCATEGORY_CREATED: 'Subcategory created successfully',
  SUBCATEGORY_CREATED_HI: 'उप-श्रेणी सफलतापूर्वक बनाई गई',
  SUBCATEGORY_UPDATED: 'Subcategory updated successfully',
  SUBCATEGORY_UPDATED_HI: 'उप-श्रेणी सफलतापूर्वक अपडेट की गई',
  SUBCATEGORY_DELETED: 'Subcategory deleted successfully',
  SUBCATEGORY_DELETED_HI: 'उप-श्रेणी सफलतापूर्वक हटाई गई',
  SUBCATEGORY_NOT_FOUND: 'Subcategory not found',
  SUBCATEGORY_NOT_FOUND_HI: 'उप-श्रेणी नहीं मिली',
  SUBCATEGORY_ALREADY_EXISTS: 'Subcategory already exists',
  SUBCATEGORY_ALREADY_EXISTS_HI: 'उप-श्रेणी पहले से मौजूद है',
  SUBCATEGORY_NAME_ALREADY_EXISTS: 'Subcategory name already exists',
  SUBCATEGORY_NAME_ALREADY_EXISTS_HI: 'उप-श्रेणी नाम पहले से मौजूद है',
  SUBCATEGORY_HAS_PRODUCTS: 'Subcategory has products',
  SUBCATEGORY_HAS_PRODUCTS_HI: 'उप-श्रेणी में उत्पाद हैं',
  INVALID_SUBCATEGORY_ID: 'Invalid subcategory ID',
  INVALID_SUBCATEGORY_ID_HI: 'अमान्य उप-श्रेणी आईडी',
  SUBCATEGORY_NOT_BELONG_TO_CATEGORY: 'Subcategory does not belong to the selected category',
  SUBCATEGORY_NOT_BELONG_TO_CATEGORY_HI: 'उप-श्रेणी चयनित श्रेणी से संबंधित नहीं है',

  // shop - collections
  COLLECTION_CREATED: 'Collection created successfully',
  COLLECTION_CREATED_HI: 'संग्रह सफलतापूर्वक बनाया गया',
  COLLECTION_UPDATED: 'Collection updated successfully',
  COLLECTION_UPDATED_HI: 'संग्रह सफलतापूर्वक अपडेट किया गया',
  COLLECTION_DELETED: 'Collection deleted successfully',
  COLLECTION_DELETED_HI: 'संग्रह सफलतापूर्वक हटाया गया',
  COLLECTION_NOT_FOUND: 'Collection not found',
  COLLECTION_NOT_FOUND_HI: 'संग्रह नहीं मिला',
  COLLECTION_ALREADY_EXISTS: 'Collection already exists',
  COLLECTION_ALREADY_EXISTS_HI: 'संग्रह पहले से मौजूद है',
  COLLECTION_NAME_ALREADY_EXISTS: 'Collection name already exists',
  COLLECTION_NAME_ALREADY_EXISTS_HI: 'संग्रह नाम पहले से मौजूद है',
  INVALID_COLLECTION_ID: 'Invalid collection ID',
  INVALID_COLLECTION_ID_HI: 'अमान्य संग्रह आईडी',

  // shop - cart
  ITEM_ADDED_TO_CART: 'Item added to cart successfully',
  ITEM_ADDED_TO_CART_HI: 'आइटम को कार्ट में सफलतापूर्वक जोड़ा गया',
  ITEM_REMOVED_FROM_CART: 'Item removed from cart successfully',
  ITEM_REMOVED_FROM_CART_HI: 'आइटम को कार्ट से सफलतापूर्वक हटाया गया',
  CART_UPDATED: 'Cart updated successfully',
  CART_UPDATED_HI: 'कार्ट सफलतापूर्वक अपडेट किया गया',
  CART_CLEARED: 'Cart cleared successfully',
  CART_CLEARED_HI: 'कार्ट सफलतापूर्वक साफ़ किया गया',
  CART_NOT_FOUND: 'Cart not found',
  CART_NOT_FOUND_HI: 'कार्ट नहीं मिला',
  ITEM_NOT_IN_CART: 'Item not in cart',
  ITEM_NOT_IN_CART_HI: 'आइटम कार्ट में नहीं है',
  CART_EMPTY: 'Cart is empty',
  CART_EMPTY_HI: 'कार्ट खाली है',

  // shop - orders
  ORDER_CREATED: 'Order created successfully',
  ORDER_CREATED_HI: 'ऑर्डर सफलतापूर्वक बनाया गया',
  ORDER_UPDATED: 'Order updated successfully',
  ORDER_UPDATED_HI: 'ऑर्डर सफलतापूर्वक अपडेट किया गया',
  ORDER_CANCELLED: 'Order cancelled successfully',
  ORDER_CANCELLED_HI: 'ऑर्डर सफलतापूर्वक रद्द किया गया',
  ORDER_NOT_FOUND: 'Order not found',
  ORDER_NOT_FOUND_HI: 'ऑर्डर नहीं मिला',
  ORDER_ALREADY_CANCELLED: 'Order is already cancelled',
  ORDER_ALREADY_CANCELLED_HI: 'ऑर्डर पहले ही रद्द किया जा चुका है',
  ORDER_CANNOT_BE_CANCELLED: 'Order cannot be cancelled',
  ORDER_CANNOT_BE_CANCELLED_HI: 'ऑर्डर रद्द नहीं किया जा सकता',
  ORDER_STATUS_UPDATED: 'Order status updated successfully',
  ORDER_STATUS_UPDATED_HI: 'ऑर्डर की स्थिति सफलतापूर्वक अपडेट की गई',
  ORDER_REFUNDED: 'Order refunded successfully',
  ORDER_REFUNDED_HI: 'ऑर्डर का रिफंड सफलतापूर्वक किया गया',
  ORDER_SHIPPED: 'Order shipped successfully',
  ORDER_SHIPPED_HI: 'ऑर्डर सफलतापूर्वक भेजा गया',
  ORDER_CANNOT_BE_REFUNDED: 'Order cannot be refunded',
  ORDER_CANNOT_BE_REFUNDED_HI: 'ऑर्डर को रिफंड नहीं किया जा सकता',
  ORDER_CANNOT_BE_SHIPPED: 'Order cannot be shipped',
  ORDER_CANNOT_BE_SHIPPED_HI: 'ऑर्डर को भेजा नहीं जा सकता',
  ORDER_NOT_PENDING: 'Order is not in pending state',
  ORDER_NOT_PENDING_HI: 'ऑर्डर लंबित स्थिति में नहीं है',
  INVALID_ORDER_ID: 'Invalid order ID',
  INVALID_ORDER_ID_HI: 'अमान्य ऑर्डर आईडी',
  REFUND_AMOUNT_EXCEEDS_ORDER_TOTAL: 'Refund amount exceeds order total',
  REFUND_AMOUNT_EXCEEDS_ORDER_TOTAL_HI: 'रिफंड राशि ऑर्डर कुल से अधिक है',

  // shipping
  SHIPROCKET_ORDER_CREATED: 'ShipRocket order created successfully',
  SHIPROCKET_ORDER_CREATED_HI: 'शिपरॉकेट ऑर्डर सफलतापूर्वक बनाया गया',
  SHIPMENT_UPDATED: 'Shipment updated successfully',
  SHIPMENT_UPDATED_HI: 'शिपमेंट सफलतापूर्वक अपडेट किया गया',
  AWB_GENERATED: 'AWB generated successfully',
  AWB_GENERATED_HI: 'AWB सफलतापूर्वक जनरेट किया गया',
  PICKUP_REQUESTED: 'Pickup requested successfully',
  PICKUP_REQUESTED_HI: 'पिकअप सफलतापूर्वक अनुरोधित किया गया',
  SHIPMENT_CANCELLED: 'Shipment cancelled successfully',
  SHIPMENT_CANCELLED_HI: 'शिपमेंट सफलतापूर्वक रद्द किया गया',

  // shop - payments
  PAYMENT_SUCCESSFUL: 'Payment successful',
  PAYMENT_SUCCESSFUL_HI: 'भुगतान सफलतापूर्वक हुआ',
  PAYMENT_FAILED: 'Payment failed',
  PAYMENT_FAILED_HI: 'भुगतान विफल हुआ',
  PAYMENT_ALREADY_PROCESSED: 'Payment already processed',
  PAYMENT_ALREADY_PROCESSED_HI: 'भुगतान पहले ही किया जा चुका है',
  PAYMENT_RECORD_NOT_FOUND: 'Payment record not found',
  PAYMENT_RECORD_NOT_FOUND_HI: 'भुगतान रिकॉर्ड नहीं मिला',
  RAZORPAY_ORDER_CREATED: 'Razorpay order created successfully',
  RAZORPAY_ORDER_CREATED_HI: 'रेज़रपे ऑर्डर सफलतापूर्वक बनाया गया',
  RAZORPAY_ORDER_NOT_CREATED: 'Razorpay order not created',
  RAZORPAY_ORDER_NOT_CREATED_HI: 'रेज़रपे ऑर्डर नहीं बनाया गया',
  INVALID_PAYMENT_SIGNATURE: 'Invalid payment signature',
  INVALID_PAYMENT_SIGNATURE_HI: 'अमान्य भुगतान हस्ताक्षर',
  PAYMENT_GATEWAY_ERROR: 'Error communicating with payment gateway',
  PAYMENT_GATEWAY_ERROR_HI: 'भुगतान गेटवे से संवाद में त्रुटि',
  INVALID_WEBHOOK_SIGNATURE: 'Invalid webhook signature',
  INVALID_WEBHOOK_SIGNATURE_HI: 'अमान्य वेबहुक हस्ताक्षर',
  WEBHOOK_PROCESSED: 'Webhook processed successfully',
  WEBHOOK_PROCESSED_HI: 'वेबहुक सफलतापूर्वक संसाधित किया गया',

  // vendors
  VENDOR_REGISTERED: 'Vendor registered successfully',
  VENDOR_REGISTERED_HI: 'विक्रेता सफलतापूर्वक पंजीकृत हुआ',
  VENDOR_REGISTRATION_PENDING_APPROVAL: 'Vendor registration successful. Your account is pending approval.',
  VENDOR_REGISTRATION_PENDING_APPROVAL_HI: 'विक्रेता पंजीकरण सफल। आपका खाता अनुमोदन लंबित है।',
  VENDOR_APPROVED: 'Vendor approved successfully',
  VENDOR_APPROVED_HI: 'विक्रेता को सफलतापूर्वक अनुमोदित किया गया',
  VENDOR_REJECTED: 'Vendor rejected successfully',
  VENDOR_REJECTED_HI: 'विक्रेता को सफलतापूर्वक अस्वीकृत किया गया',
  VENDOR_DELETED: 'Vendor deleted successfully',
  VENDOR_DELETED_HI: 'विक्रेता सफलतापूर्वक हटाया गया',
  VENDOR_NOT_FOUND: 'Vendor not found',
  VENDOR_NOT_FOUND_HI: 'विक्रेता नहीं मिला',
  VENDOR_ACCOUNT_NOT_ACTIVE: 'Your vendor account is not active',
  VENDOR_ACCOUNT_NOT_ACTIVE_HI: 'आपका विक्रेता खाता सक्रिय नहीं है',
  VENDOR_HAS_PRODUCTS: 'Vendor has products',
  VENDOR_HAS_PRODUCTS_HI: 'विक्रेता के पास उत्पाद हैं',
  INVALID_VENDOR_ID: 'Invalid vendor ID',
  INVALID_VENDOR_ID_HI: 'अमान्य विक्रेता आईडी',
  LOGIN_SUCCESS: 'Login successful',
  LOGIN_SUCCESS_HI: 'लॉगिन सफल हुआ',
  PASSWORD_RESET_EMAIL_SENT: 'Password reset email sent',
  PASSWORD_RESET_EMAIL_SENT_HI: 'पासवर्ड रीसेट ईमेल भेजा गया',
  PASSWORD_RESET_SUCCESS: 'Password reset successful',
  PASSWORD_RESET_SUCCESS_HI: 'पासवर्ड सफलतापूर्वक रीसेट हुआ',
  PASSWORD_CHANGED: 'Password changed successfully',
  PASSWORD_CHANGED_HI: 'पासवर्ड सफलतापूर्वक बदला गया',
  CURRENT_PASSWORD_INCORRECT: 'Current password is incorrect',
  CURRENT_PASSWORD_INCORRECT_HI: 'वर्तमान पासवर्ड गलत है',
  INVALID_OR_EXPIRED_TOKEN: 'Invalid or expired token',
  INVALID_OR_EXPIRED_TOKEN_HI: 'अमान्य या समाप्त टोकन',
  PROFILE_UPDATED: 'Profile updated successfully',
  PROFILE_UPDATED_HI: 'प्रोफ़ाइल सफलतापूर्वक अपडेट हुई',
  INVALID_TEMPLE_ID: 'Invalid temple ID',
  INVALID_TEMPLE_ID_HI: 'अमान्य मंदिर आईडी',

  VARIANT_ATTRIBUTES_REQUIRED: 'Variant attributes are required for products with variants',
  VARIANT_ATTRIBUTES_REQUIRED_HI: 'वेरिएंट वाले उत्पादों के लिए वेरिएंट विशेषताएँ आवश्यक हैं',
  VARIANTS_REQUIRED: 'At least one variant is required for products with variants',
  VARIANTS_REQUIRED_HI: 'वेरिएंट वाले उत्पादों के लिए कम से कम एक वेरिएंट आवश्यक है',
  ATTRIBUTE_NOT_FOUND: 'Attribute not found',
  ATTRIBUTE_NOT_FOUND_HI: 'गुण नहीं मिला',
  MISSING_VARIANT_ATTRIBUTE: 'Missing required variant attribute',
  MISSING_VARIANT_ATTRIBUTE_HI: 'आवश्यक वेरिएंट गुण अनुपस्थित है',
  INVALID_VARIANT_ATTRIBUTE: 'Invalid variant attribute',
  INVALID_VARIANT_ATTRIBUTE_HI: 'अमान्य वेरिएंट गुण',
  DEFAULT_VARIANT_REQUIRED: 'At least one variant must be marked as default',
  DEFAULT_VARIANT_REQUIRED_HI: 'कम से कम एक वेरिएंट को डिफ़ॉल्ट के रूप में चिह्नित करना आवश्यक है',
  INVALID_VARIANT_ID: 'Invalid variant ID',
  INVALID_VARIANT_ID_HI: 'अमान्य वेरिएंट आईडी',
  VARIANT_NOT_FOUND: 'Variant not found',
  VARIANT_NOT_FOUND_HI: 'वेरिएंट नहीं मिला',
  DEFAULT_VARIANT_NOT_FOUND: 'Default variant not found',
  DEFAULT_VARIANT_NOT_FOUND_HI: 'डिफ़ॉल्ट वेरिएंट नहीं मिला',
  CANNOT_REMOVE_ALL_VARIANTS: 'Cannot remove all variants from a product',
  CANNOT_REMOVE_ALL_VARIANTS_HI: 'किसी उत्पाद से सभी वेरिएंट को हटाया नहीं जा सकता',
  CANNOT_REMOVE_ALL_DEFAULT_VARIANTS: 'Cannot remove all default variants',
  CANNOT_REMOVE_ALL_DEFAULT_VARIANTS_HI: 'सभी डिफ़ॉल्ट वेरिएंट को हटाया नहीं जा सकता',

  // Attributes
  ATTRIBUTE_CREATED: 'Attribute created successfully',
  ATTRIBUTE_CREATED_HI: 'गुण सफलतापूर्वक बनाया गया',
  ATTRIBUTE_UPDATED: 'Attribute updated successfully',
  ATTRIBUTE_UPDATED_HI: 'गुण सफलतापूर्वक अपडेट किया गया',
  ATTRIBUTE_DELETED: 'Attribute deleted successfully',
  ATTRIBUTE_DELETED_HI: 'गुण सफलतापूर्वक हटाया गया',
  ATTRIBUTE_ALREADY_EXISTS: 'Attribute already exists',
  ATTRIBUTE_ALREADY_EXISTS_HI: 'गुण पहले से मौजूद है',
  ATTRIBUTE_NAME_ALREADY_EXISTS: 'Attribute name already exists',
  ATTRIBUTE_NAME_ALREADY_EXISTS_HI: 'गुण का नाम पहले से मौजूद है',
  ATTRIBUTE_IN_USE: 'Attribute is in use by category/products',
  ATTRIBUTE_IN_USE_HI: 'गुण श्रेणी/उत्पादों द्वारा उपयोग में है',

  // Settlements
  SETTLEMENT_CREATED: 'Settlement batch created successfully',
  SETTLEMENT_CREATED_HI: 'सेटलमेंट बैच सफलतापूर्वक बनाया गया',
  SETTLEMENT_UPDATED: 'Settlement batch updated successfully',
  SETTLEMENT_UPDATED_HI: 'सेटलमेंट बैच सफलतापूर्वक अपडेट किया गया',
  SETTLEMENT_APPROVED: 'Settlement batch approved successfully',
  SETTLEMENT_APPROVED_HI: 'सेटलमेंट बैच को सफलतापूर्वक स्वीकृत किया गया',
  SETTLEMENT_PAID: 'Settlement batch marked as paid successfully',
  SETTLEMENT_PAID_HI: 'सेटलमेंट बैच को भुगतान किया गया के रूप में चिह्नित किया गया',
  SETTLEMENT_REJECTED: 'Settlement batch rejected successfully',
  SETTLEMENT_REJECTED_HI: 'सेटलमेंट बैच सफलतापूर्वक अस्वीकृत किया गया',
  SETTLEMENT_NOT_FOUND: 'Settlement batch not found',
  SETTLEMENT_NOT_FOUND_HI: 'सेटलमेंट बैच नहीं मिला',
  NO_ORDERS_TO_SETTLE: 'No delivered orders found in the selected date range',
  NO_ORDERS_TO_SETTLE_HI: 'चयनित दिनांक सीमा में कोई वितरित ऑर्डर नहीं मिला',
  SETTLEMENT_ALREADY_PAID: 'Settlement batch is already paid',
  SETTLEMENT_ALREADY_PAID_HI: 'सेटलमेंट बैच पहले ही भुगतान किया जा चुका है',
  SETTLEMENT_ALREADY_REJECTED: 'Settlement batch is already rejected',
  SETTLEMENT_ALREADY_REJECTED_HI: 'सेटलमेंट बैच पहले ही अस्वीकृत किया जा चुका है',
  BANK_TRANSACTION_ID_REQUIRED: 'Bank transaction ID is required for paid settlements',
  BANK_TRANSACTION_ID_REQUIRED_HI: 'भुगतान किए गए सेटलमेंट के लिए बैंक लेन-देन आईडी आवश्यक है',

  DISCOUNT_CREATED: 'Discount created successfully',
  DISCOUNT_CREATED_HI: 'छूट सफलतापूर्वक बनाई गई',
  DISCOUNT_UPDATED: 'Discount updated successfully',
  DISCOUNT_UPDATED_HI: 'छूट सफलतापूर्वक अपडेट की गई',
  DISCOUNTS_RETRIEVED: 'Discounts retrieved successfully',
  DISCOUNTS_RETRIEVED_HI: 'छूट सफलतापूर्वक प्राप्त की गईं',
  DISCOUNT_RETRIEVED: 'Discount retrieved successfully',
  DISCOUNT_RETRIEVED_HI: 'छूट सफलतापूर्वक प्राप्त की गई',
  DISCOUNT_DELETED: 'Discount deleted successfully',
  DISCOUNT_DELETED_HI: 'छूट सफलतापूर्वक हटाई गई',
  INVOICE_GENERATED: 'Commission invoice generated successfully',
  INVOICE_GENERATED_HI: 'कमीशन इनवॉइस सफलतापूर्वक जेनरेट की गई',
  DISCOUNT_APPLIED: 'Discount applied successfully',
  DISCOUNT_APPLIED_HI: 'छूट सफलतापूर्वक लागू की गई',

  // Shop Banners 
  BANNER_NOT_FOUND: 'Banner not found',
  BANNER_NOT_FOUND_HI: 'बैनर नहीं मिला',

  // Offerings
  OFFERING_CREATED: 'Offering created successfully',
  OFFERING_CREATED_HI: 'ऑफ़रिंग सफलतापूर्वक बनाई गई',
  OFFERINGS_RETRIEVED: 'Offerings retrieved successfully',
  OFFERINGS_RETRIEVED_HI: 'ऑफ़रिंग्स सफलतापूर्वक प्राप्त की गईं',
  OFFERING_RETRIEVED: 'Offering retrieved successfully',
  OFFERING_RETRIEVED_HI: 'ऑफ़रिंग सफलतापूर्वक प्राप्त हुई',
  OFFERING_UPDATED: 'Offering updated successfully',
  OFFERING_UPDATED_HI: 'ऑफ़रिंग सफलतापूर्वक अपडेट की गई',
  OFFERING_DELETED: 'Offering deleted successfully',
  OFFERING_DELETED_HI: 'ऑफ़रिंग सफलतापूर्वक हटाई गई',

  // Centralized Approval Center
  COUNT_RETRIEVED: 'Counts retrieved successfully',
  COUNT_RETRIEVED_HI: 'गिनती सफलतापूर्वक प्राप्त हुई',

  // Temple 
  TEMPLE_DELETED: 'Temple deleted successfully',
  TEMPLE_DELETED_HI: 'मंदिर सफलतापूर्वक हटाया गया',

  // Helicopter
  HELICOPTER_CREATED: 'Helicopter created successfully',
  HELICOPTER_CREATED_HI: 'हेलीकॉप्टर सफलतापूर्वक बनाया गया',
  HELICOPTER_UPDATED: 'Helicopter updated successfully',
  HELICOPTER_UPDATED_HI: 'हेलीकॉप्टर सफलतापूर्वक अपडेट किया गया',
  HELICOPTER_DELETED: 'Helicopter deleted successfully',
  HELICOPTER_DELETED_HI: 'हेलीकॉप्टर सफलतापूर्वक हटाया गया',
  HELICOPTERS_RETRIEVED: 'Helicopters retrieved successfully',
  HELICOPTERS_RETRIEVED_HI: 'हेलीकॉप्टर्स सफलतापूर्वक प्राप्त किए गए',
  HELICOPTER_RETRIEVED: 'Helicopter retrieved successfully',
  HELICOPTER_RETRIEVED_HI: 'हेलीकॉप्टर सफलतापूर्वक प्राप्त हुआ',

  // Helicopter Puja
  HELICOPTER_PUJA_CREATED: 'Helicopter puja created successfully',
  HELICOPTER_PUJA_CREATED_HI: 'हेलीकॉप्टर पूजा सफलतापूर्वक बनाई गई',
  HELICOPTER_PUJAS_RETRIEVED: 'Helicopter pujas retrieved successfully',
  HELICOPTER_PUJAS_RETRIEVED_HI: 'हेलीकॉप्टर पूजाएं सफलतापूर्वक प्राप्त की गईं',
  HELICOPTER_PUJA_RETRIEVED: 'Helicopter puja retrieved successfully',
  HELICOPTER_PUJA_RETRIEVED_HI: 'हेलीकॉप्टर पूजा सफलतापूर्वक प्राप्त हुई',
  HELICOPTER_PUJA_UPDATED: 'Helicopter puja updated successfully',
  HELICOPTER_PUJA_UPDATED_HI: 'हेलीकॉप्टर पूजा सफलतापूर्वक अपडेट की गई',
  HELICOPTER_PUJA_DELETED: 'Helicopter puja deleted successfully',
  HELICOPTER_PUJA_DELETED_HI: 'हेलीकॉप्टर पूजा सफलतापूर्वक हटाई गई',

  // Helicopter Booking
  HELICOPTER_BOOKING_CREATED: 'Helicopter booking created successfully',
  HELICOPTER_BOOKING_CREATED_HI: 'हेलीकॉप्टर बुकिंग सफलतापूर्वक की गई',

  // Notifiction 
  NOTIFICATIONS_RETRIEVED: 'Notifications retrieved successfully',
  NOTIFICATIONS_RETRIEVED_HI: 'सूचनाएं सफलतापूर्वक प्राप्त हुईं',
  NOTIFICATION_MARKED_AS_READ: 'Notification marked as read successfully',
  NOTIFICATION_MARKED_AS_READ_HI: 'सूचना को सफलतापूर्वक पढ़ा गया के रूप में चिह्नित किया गया',
  ALL_NOTIFICATIONS_MARKED_AS_READ: 'All notifications marked as read successfully',
  ALL_NOTIFICATIONS_MARKED_AS_READ_HI: 'सभी सूचनाओं को सफलतापूर्वक पढ़ा गया के रूप में चिह्नित किया गया',

  // Panchang
  MUHURAT_RETRIEVED_SUCCESSFULLY: 'Muhurat retrieved successfully',
  MUHURAT_RETRIEVED_SUCCESSFULLY_HI: 'मुहूर्त सफलतापूर्वक प्राप्त हुआ',
  DURMUHURAT_RETRIEVED_SUCCESSFULLY: 'Durmuhurat retrieved successfully',
  DURMUHURAT_RETRIEVED_SUCCESSFULLY_HI: 'दुर्मुहूर्त सफलतापूर्वक प्राप्त हुआ',
  HORA_RETRIEVED_SUCCESSFULLY: 'Hora retrieved successfully',
  HORA_RETRIEVED_SUCCESSFULLY_HI: 'होरा सफलतापूर्वक प्राप्त हुआ',

  // APP Version
  APP_VERSION_UPDATED: 'App version updated successfully',
  APP_VERSION_UPDATED_HI: 'ऐप संस्करण सफलतापूर्वक अपडेट किया गया',
  APP_VERSIONS_RETRIEVED: 'App versions retrieved successfully',
  APP_VERSIONS_RETRIEVED_HI: 'ऐप संस्करण सफलतापूर्वक प्राप्त किए गए',
  APP_VERSION_NOT_FOUND: 'App version not found',
  APP_VERSION_NOT_FOUND_HI: 'ऐप संस्करण नहीं मिला',
  PLATFORM_AND_TYPE_REQUIRED: 'Platform and type are required',
  PLATFORM_AND_TYPE_REQUIRED_HI: 'प्लेटफॉर्म और प्रकार आवश्यक हैं',

  DASHBOARD_DATA_RETRIEVED: 'Dashboard data retrieved successfully',

  // Gotra 
  GOTRA_CREATED: 'Gotra created successfully',
  GOTRA_CREATED_HI: 'गोत्र सफलतापूर्वक बनाया गया',
  GOTRA_ALREADY_EXISTS: 'A gotra with this name already exists',
  GOTRA_ALREADY_EXISTS_HI: 'इस नाम का गोत्र पहले से मौजूद है',
  GOTRA_RETRIEVED: 'Gotra list retrieved successfully',
  GOTRA_RETRIEVED_HI: 'गोत्र सूची सफलतापूर्वक प्राप्त हुई',
  GOTRA_UPDATED: 'Gotra updated successfully',
  GOTRA_UPDATED_HI: 'गोत्र सफलतापूर्वक अपडेट किया गया',
  GOTRA_DELETED: 'Gotra deleted successfully',
  GOTRA_DELETED_HI: 'गोत्र सफलतापूर्वक हटाया गया',
  GOTRA_NOT_FOUND: 'Gotra not found',
  GOTRA_NOT_FOUND_HI: 'गोत्र नहीं मिला'

};

module.exports = {
  messages,
};
