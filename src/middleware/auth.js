const jwt = require('jsonwebtoken');
const Token = require('../models/Token');
const User = require('../models/User');
const KioskUser = require('../models/KioskUser');
const TempleAdmin = require('../models/TempleAdmin');
const { userTypeValue } = require('../constants/dbEnums');

const auth = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    const token = authHeader ? authHeader.replace('Bearer ', '') : undefined;

    if (!token) {
      return res.status(401).json({ message: 'No token, authorization denied' });
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // First check if token exists in database
    const tokenDoc = await Token.findOne({ 
      token,
      userId: decoded.id || decoded._id,
    });

    if (!tokenDoc) {
      return res.status(401).json({ message: 'Token is not valid' });
    }

    // Then check if token has expired
    if (tokenDoc.expiresAt < new Date()) {
      // Delete token only if it exists and has expired
      await Token.deleteOne({ _id: tokenDoc._id });
      return res.status(401).json({ message: 'Token has expired' });
    }

    let user;

    if (decoded.userType === userTypeValue.USER) {
      // Check if user is not deleted 
      user = await User.findOne({
        _id: decoded.id || decoded._id,
        deletedAt: null
      });

      if (!user) {
        return res.status(403).json({ message: 'Access denied. User does not exist or has been deleted.' });
      }
    }

    if (decoded.userType === userTypeValue.KIOSK) {
      // Check if kiosk user is not deleted
      const kioskUser = await KioskUser.findOne({
        _id: decoded.id || decoded._id,
        deletedAt: null
      });

      if (!kioskUser) {
        return res.status(403).json({ message: 'Access denied. Kiosk User does not exist or has been deleted.' });
      }
    }

    if (decoded.userType === userTypeValue.TEMPLE_ADMIN) {
      // Check if temple admin is not deleted
      const templeAdmin = await TempleAdmin.findOne({
        _id: decoded.id || decoded._id,
        deletedAt: null
      });

      if (!templeAdmin) {
        return res.status(403).json({ message: 'Access denied. Temple Admin does not exist or has been deleted.' });
      }
    }

    req.user = {
      id: decoded.id || decoded._id,
      userType: decoded.userType,
      tokenId: tokenDoc._id,
      preferredLanguage: user?.preferredLanguage || 'English'
    };
    
    next();
  } catch (err) {
    res.status(401).json({ message: 'Token is not valid' });
  }
};

module.exports = auth;
