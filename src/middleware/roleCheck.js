const User = require('../models/User');
const { userTypeValue, userStatusValue } = require('../constants/dbEnums');
const Admin = require('../models/Admin');
const TempleAdmin = require('../models/TempleAdmin');
const KioskUser = require('../models/KioskUser');
const Vendor = require('../models/Vendor');
const Kiosk = require('../models/Kiosk');

// Check if user is a regular user
const isUser = async (req, res, next) => {
  try {
    const user = await User.findOne({
      _id: req.user.id,
      deletedAt: null
    });

    if (!user) {
      return res.status(403).json({ message: 'Access denied. User role required' });
    }

    next();
  } catch (err) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Check if user is an admin
const isAdmin = async (req, res, next) => {
  try {
    const user = await Admin.findOne({
      _id: req.user.id,
    });

    if (!user || user.userType !== userTypeValue.ADMIN) {
      return res.status(403).json({ message: 'Access denied. Admin role required' });
    }

    next();
  } catch (err) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Check if user is a super admin
const isSuperAdmin = async (req, res, next) => {
  try {
    const user = await Admin.findOne({
      _id: req.user.id,
    });

    if (!user || user.userType !== userTypeValue.SUPER_ADMIN) {
      return res.status(403).json({ message: 'Access denied. Super Admin role required' });
    }

    next();
  } catch (err) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Check if user is either admin or super admin
const isAdminOrSuperAdmin = async (req, res, next) => {
  try {
    const user = await Admin.findOne({
      _id: req.user.id,
    });

    if (!user || (user.userType !== userTypeValue.ADMIN && user.userType !== userTypeValue.SUPER_ADMIN)) {
      return res.status(403).json({ message: 'Access denied. Admin or Super Admin role required' });
    }

    next();
  } catch (err) {
    res.status(500).json({ message: 'Server error' });
  }
};

const isTempleAdmin = async (req, res, next) => {
  try {
    const user = await TempleAdmin.findOne({
      _id: req.user.id,
      deletedAt: null
    });

    if (!user) {
      return res.status(403).json({ message: 'Access denied. Temple Admin role required' });
    }

    next();
  } catch (err) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Check if user is a kiosk user
const isKioskUser = async (req, res, next) => {
  try {
    const user = await KioskUser.findOne({
      _id: req.user.id,
      status: userStatusValue.ACTIVE,
      deletedAt: null
    });

    if (!user || !user.kiosk) {
      return res.status(403).json({ message: 'Access denied. Kiosk User role required' });
    }

    const kiosk = await Kiosk.findById(user.kiosk);

    if (!kiosk || kiosk.status !== userStatusValue.ACTIVE) {
      return res.status(403).json({ message: 'Access denied. Kiosk User role required' });
    }

    req.user.kiosk = kiosk;

    next();
  } catch (err) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Check if user is a vendor
const isVendor = async (req, res, next) => {
  try {
    const user = await Vendor.findOne({
      _id: req.user.id,
      status: 'ACTIVE'
    });

    if (!user) {
      return res.status(403).json({ message: 'Access denied. Vendor role required' });
    }

    next();
  } catch (err) {
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  isUser,
  isAdmin,
  isTempleAdmin,
  isSuperAdmin,
  isAdminOrSuperAdmin,
  isKioskUser,
  isVendor
};