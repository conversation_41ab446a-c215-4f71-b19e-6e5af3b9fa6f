const mongoose = require('mongoose');

const addressSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  addressLine1: JSON,
  addressLine2: JSON,
  city: JSON,
  state: JSON,
  country: {
    type: JSON,
    default: {
      en: 'India',
      hi: 'भारत'
    }
  },
  postalCode: {
    type: String,
    required: true,
    trim: true
  },
  latitude: {
    type: Number,
    required: true
  },
  longitude: {
    type: Number,
    required: true
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, { timestamps: true });

const Address = mongoose.model('Address', addressSchema);

module.exports = Address;