const mongoose = require('mongoose');
const { userTypeValue } = require('../constants/dbEnums');

const adminSchema = new mongoose.Schema({ // OTP will be stored in the same table "OTP"
  name: {
    type: String,
    required: false,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  phoneNumber: {
    type: String,
    required: false,
    unique: true,
    sparse: true,
    trim: true
  },
  countryCode: {
    type: String,
    required: false,
    trim: true
  },
  password: {
    type: String,
    required: true
  },
  userType: {
    type: String,
    enum: Object.values(userTypeValue),
    default: userTypeValue.SUPER_ADMIN
  },
  shipRocketPickupLocationName: {
    type: String,
    trim: true,
    default: 'AdminLocation'
  }
}, { timestamps: true });

const Admin = mongoose.model('Admin', adminSchema);

module.exports = Admin;
