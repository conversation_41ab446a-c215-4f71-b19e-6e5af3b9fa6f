const mongoose = require('mongoose');
const { appPlatforms, userTypeValue } = require('../constants/dbEnums');

const appVersionSchema = new mongoose.Schema({ // OTP will be stored in the same table "OTP"
  platform: {
    type: String,
    enum: Object.values(appPlatforms),
    required: true
  },
  latestVersion: {
    type: String,
    required: true
  },
  forceUpdate: {
    type: Boolean,
    default: false
  },
  message: {
    type: String,
    default: null
  },
  type: {
    type: String,
    enum: [ userTypeValue.USER, userTypeValue.KIOSK ],
    required: true
  }
}, { timestamps: true });

const AppVersion = mongoose.model('AppVersion', appVersionSchema);

module.exports = AppVersion;
