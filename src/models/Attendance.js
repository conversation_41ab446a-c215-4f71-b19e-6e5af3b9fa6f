const mongoose = require('mongoose');

const attendanceSchema = new mongoose.Schema({
  kioskUser: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'KioskUser',
    required: true
  },
  date: {
    type: String,
    required: true
  },
  checkInTime: {
    type: String,
    default: null
  },
  checkInTimezone: {
    type: String,
    default: null
  },
  checkOutTime: {
    type: String,
    default: null
  },
  checkOutTimezone: {
    type: String,
    default: null
  },
  status: {
    type: String,
    enum: [ 'PRESENT', 'ABSENT' ],
    default: 'ABSENT'
  },
  notes: {
    type: String,
    default: ''
  }
}, { timestamps: true });

// Create a compound index on kioskUser and date to ensure uniqueness
attendanceSchema.index({ kioskUser: 1, date: 1 }, { unique: true });

const Attendance = mongoose.model('Attendance', attendanceSchema);

module.exports = Attendance;
