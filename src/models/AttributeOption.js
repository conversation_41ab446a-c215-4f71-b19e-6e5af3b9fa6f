const mongoose = require('mongoose');

const attributeOptionSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  values: [{
    type: String,
    required: true,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  }
}, { timestamps: true });

// Add indexes for better query performance
attributeOptionSchema.index({ name: 1 }, { unique: true });
attributeOptionSchema.index({ isActive: 1 });

const AttributeOption = mongoose.model('AttributeOption', attributeOptionSchema);

module.exports = AttributeOption;
