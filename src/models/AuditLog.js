const mongoose = require('mongoose');
const { auditLogAction } = require('../constants/dbEnums');

const auditLogSchema = new mongoose.Schema({
  admin: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  action: {
    type: String,
    enum: Object.values(auditLogAction),
    required: true
  },
  detail: {
    type: String,
    required: true
  },
  ipAddress: {
    type: String,
    required: true
  },
  model: {
    type: String,
    required: true
  },
  metaData: {
    type: Object,
    required: false
  }
}, { timestamps: true });

auditLogSchema.index({ admin: 1 });
auditLogSchema.index({ action: 1 });
auditLogSchema.index({ model: 1 });
auditLogSchema.index({ ipAddress: 1 });
auditLogSchema.index({ detail: 1 });
auditLogSchema.index({ createdAt: 1 });

const AuditLog = mongoose.model('AuditLog', auditLogSchema);

module.exports = AuditLog;