const mongoose = require('mongoose');
const { type, status, settlementStatusValue, paymentGateway } = require('../constants/dbEnums');

const bookingSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: function () {
      return this.type !== 'HELICOPTER_BOOKING';
    }
  },
  helicopter: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Helicopter',
    required: function () {
      return this.type === 'HELICOPTER_BOOKING';
    }
  },
  discount: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Discount',
    default: null
  },
  discountAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  promotionalKitCount: {
    type: Number,
    default: null,
    min: 0
  },
  promotionalKitCost: {
    type: Number,
    default: null,
    min: 0
  },
  promotionalKitTotalAmount: {
    type: Number,
    default: null,
    min: 0
  },
  offerings: [{
    offering: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Offering',
      default: null
    },
    quantity: {
      type: Number,
      min: 1,
      default: null
    },
    amount: {
      type: Number,
      min: 0,
      default: null
    },
    subtotal: {
      type: Number,
      min: 0,
      default: null
    }
  }],
  offeringsTotal: {
    type: Number,
    default: null,
    min: 0
  },
  bookingNumber: {
    type: String,
    unique: true,
    sparse: true,
    trim: true,
    uppercase: true
  },
  invoiceNumber: {
    type: String, 
    trim: true,
    unique: true,
    sparse: true    
  },
  bookingInvoice: {
    type: String,
    trim: true
  },
  ticketPdfUrl: {
    type: String,
    trim: true,
    default: ''
  },
  darshanSchedule: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DarshanSchedule',
    default: null
  },
  poojaSchedule: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PoojaSchedule',
    default: null
  },
  event: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Event',
    default: null
  },
  type: {
    type: String,
    required: true,
    trim: true,
    enum: Object.values(type)
  },
  date: {
    type: Date,
    required: function () {
      return !this.event; // Only required for non-event bookings
    }
  },
  eventDateType: {
    type: String,
    enum: [ 'SPECIFIC_DATE', 'DATE_RANGE' ],
    required: function () {
      return this.event;
    }
  },
  eventDates: {
    type: [{
      date: {
        type: Date,
        required: true
      },
      pricing: {
        individual: {
          quantity: {
            type: Number,
            default: 0,
            min: 0,
            validate: {
              validator: Number.isInteger,
              message: 'Individual quantity must be a whole number'
            }
          },
          price: {
            type: Number,
            required: true,
            min: 1
          }
        },
        couple: {
          quantity: {
            type: Number,
            default: 0,
            min: 0,
            validate: {
              validator: Number.isInteger,
              message: 'Couple quantity must be a whole number'
            }
          },
          price: {
            type: Number,
            required: true,
            min: 1
          }
        },
        family: {
          quantity: {
            type: Number,
            default: 0,
            min: 0,
            validate: {
              validator: Number.isInteger,
              message: 'Family quantity must be a whole number'
            }
          },
          price: {
            type: Number,
            required: true,
            min: 1
          }
        }
      },
      subtotal: {
        type: Number,
        required: true,
        min: 0
      }
    }],
    validate: {
      validator: function (dates) {
        if (!this.event) {
          return true;
        }

        // For SPECIFIC_DATE, only one date is allowed
        if (this.eventDateType === 'SPECIFIC_DATE' && dates.length !== 1) {
          return false;
        }

        // For DATE_RANGE, at least one date is required
        if (this.eventDateType === 'DATE_RANGE' && dates.length < 1) {
          return false;
        }

        return true;
      },
      message: 'Invalid number of dates for the event type'
    }
  },
  timeSlot: {
    startTime: {
      type: String,
      required: function () {
        return this.type !== 'HELICOPTER_BOOKING';
      },
      validate: {
        validator: function (v) {
          return /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/.test(v);
        },
        message: 'Invalid time format. Use HH:MM AM/PM format'
      }
    },
    endTime: {
      type: String,
      required: function () {
        return this.type !== 'HELICOPTER_BOOKING';
      },
      validate: {
        validator: function (v) {
          return /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/.test(v);
        },
        message: 'Invalid time format. Use HH:MM AM/PM format'
      }
    }
  },
  individual: {
    type: Number,
    default: 0,
    min: 0,
    validate: {
      validator: function (v) {
        return Number.isInteger(v);
      },
      message: 'Individual count must be a whole number'
    }
  },
  couple: {
    type: Number,
    default: 0,
    min: 0,
    validate: {
      validator: function (v) {
        return Number.isInteger(v);
      },
      message: 'Couple count must be a whole number'
    }
  },
  family: {
    type: Number,
    default: 0,
    min: 0,
    validate: {
      validator: function (v) {
        return Number.isInteger(v);
      },
      message: 'Family count must be a whole number'
    }
  },
  primaryDevoteeDetails: {
    _id: {
      type: mongoose.Schema.Types.ObjectId,
      auto: true
    },
    fullName: JSON,
    fatherOrHusbandName: JSON,
    gender: {
      type: String,
      enum: [ 'Male', 'Female', 'Other' ],
      required: true
    },
    aadharNumber: {
      type: String,
      trim: true
    },
    aadharImage: {
      type: String,
      required: false,
      trim: true
    },
    gotra: JSON,
    sankalp: JSON,
    countryCode: {
      type: String,
      required: true,
      trim: true,
    },
    phoneNumber: {
      type: String,
      required: true,
      trim: true,
      // validate: {
      //   validator: function (v) {
      //     return /^[6-9]\d{9}$/.test(v);
      //   },
      //   message: 'Please enter a valid phone number'
      // }
    },
    countryCodeForWhatsapp: {
      type: String, 
      required: false, 
      trim: true
    },
    whatsappNumber: {
      type: String,
      required: false,
      trim: true,
      // validate: {
      //   validator: function (v) {
      //     if (!v) {
      //       return true;
      //     } // Allow empty
      //     return /^[6-9]\d{9}$/.test(v);
      //   },
      //   message: 'Please enter a valid WhatsApp number'
      // }
    },
    verification: {
      type: Map,
      of: {
        verified: {
          type: Boolean,
          default: false
        },
        verifiedAt: Date,
        verifiedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'KioskUser'
        },
        kioskId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Kiosk'
        }
      },
      default: new Map()
    }
  },
  otherDevotees: [{
    _id: {
      type: mongoose.Schema.Types.ObjectId,
      auto: true
    },
    fullName: JSON,
    gotra: JSON,
    gender: {
      type: String,
      enum: [ 'Male', 'Female', 'Other' ],
      required: true
    },
    aadharNumber: {
      type: String,
      trim: true
    },
    aadharImage: {
      type: String,
      required: false,
      trim: true
    },
    verification: {
      type: Map,
      of: {
        verified: {
          type: Boolean,
          default: false
        },
        verifiedAt: Date,
        verifiedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'KioskUser'
        },
        kioskId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Kiosk'
        }
      },
      default: new Map()
    }
  }],
  status: {
    type: String,
    enum: Object.values(status),
    default: status.PENDING
  },
  paymentGateway: {
    type: String,
    enum: Object.values(paymentGateway),
    default: paymentGateway.RAZORPAY
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  assignedPujari: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Pujari',
    default: null
  },
  address: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Address',
    default: null
  },
  settlementStatus: {
    type: String,
    enum: Object.values(settlementStatusValue),
    default: settlementStatusValue.PENDING
  },
  settledAt: {
    type: Date
  },
  settlementNotes: {
    type: String,
    trim: true
  },
  settledBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  isCompleted: {
    type: Boolean,
    default: false
  },
  oneHourReminderSent: {
    type: Boolean,
    default: false
  },
  fourHourReminderSent: {
    type: Boolean,
    default: false
  },
  templePayoutDetails: {
    gstPercentage: {
      type: Number,
      required: false,
      min: 0
    },

    individual: {
      baseAmount: {
        type: Number,
        min: 0,
        default: 0,
      },
      gstAmount: {
        type: Number,
        min: 0,
        default: 0,
      },
      totalAmount: {
        type: Number,
        min: 0,
        default: 0,
      }
    },
    
    couple: {
      baseAmount: {
        type: Number,
        min: 0,
        default: 0,
      },
      gstAmount: {
        type: Number,
        min: 0,
        default: 0,
      },
      totalAmount: {
        type: Number,
        min: 0,
        default: 0,
      }
    },

    family: {
      baseAmount: {
        type: Number,
        min: 0,
        default: 0,
      },
      gstAmount: {
        type: Number,
        min: 0,
        default: 0,
      },
      totalAmount: {
        type: Number,
        min: 0,
        default: 0,
      }
    }, 

    totalPayoutSummary: {
      totalBaseAmount: { 
        type: Number, 
        min: 0, 
        default: 0 
      },
      totalGstAmount: { 
        type: Number, 
        min: 0, 
        default: 0 
      },
      totalAmount: { 
        type: Number, 
        min: 0, 
        default: 0 
      },
    }
  }
}, { timestamps: true });

// Indexes
bookingSchema.index({ date: 1 });
bookingSchema.index({ 'primaryDevoteeDetails.phoneNumber': 1 });

// For time-based queries
bookingSchema.index({
  'timeSlot.startTime': 1,
  date: 1,
});

const Booking = mongoose.model('Booking', bookingSchema);

module.exports = Booking;
