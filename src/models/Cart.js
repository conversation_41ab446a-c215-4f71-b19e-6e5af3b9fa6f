const mongoose = require('mongoose');

const cartSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  items: [{
    id: {
      type: String,
      auto: false
    },
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true
    },
    variant: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ProductVariant'
    },
    quantity: {
      type: Number,
      required: true,
      min: 1,
      default: 1
    },
    price: {
      type: Number,
      required: true,
      min: 0
    },
    variantAttributes: {
      type: Map,
      of: String
    }
  }],
  subtotal: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  lastActive: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

// Add indexes for better query performance
cartSchema.index({ user: 1 });
cartSchema.index({ lastActive: 1 });
cartSchema.set('id', false);

const Cart = mongoose.model('Cart', cartSchema);

module.exports = Cart;
