const mongoose = require('mongoose');

const categorySchema = new mongoose.Schema({
  name: <PERSON><PERSON><PERSON>,
  description: JSO<PERSON>,
  image: {
    type: String,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  // Variant types associated with this category
  variantTypes: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AttributeOption'
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  }
}, { timestamps: true });

// Add indexes for better query performance
categorySchema.index({ name: 1 });
categorySchema.index({ isActive: 1 });

const Category = mongoose.model('Category', categorySchema);

module.exports = Category;
