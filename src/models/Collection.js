const mongoose = require('mongoose');

const collectionSchema = new mongoose.Schema({
  name: <PERSON><PERSON><PERSON>,
  description: JSO<PERSON>,
  image: {
    type: String,
    trim: true
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true
  },
  products: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product'
  }],
  subCategories: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SubCategory'
  }],
  collectionType: {
    type: String,
    enum: [ 'PRODUCTS', 'SUBCATEGORIES' ],
    required: true,
    default: 'PRODUCTS'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  }
}, { timestamps: true });

// Add validation to ensure a collection can have either products or subCategories, but not both
collectionSchema.pre('validate', function (next) {
  if (this.collectionType === 'PRODUCTS' && this.subCategories && this.subCategories.length > 0) {
    this.subCategories = [];
  } else if (this.collectionType === 'SUBCATEGORIES' && this.products && this.products.length > 0) {
    this.products = [];
  }
  next();
});

// Add indexes for better query performance
collectionSchema.index({ name: 1 });
collectionSchema.index({ category: 1 });
collectionSchema.index({ isActive: 1 });
collectionSchema.index({ collectionType: 1 });

const Collection = mongoose.model('Collection', collectionSchema);

module.exports = Collection;
