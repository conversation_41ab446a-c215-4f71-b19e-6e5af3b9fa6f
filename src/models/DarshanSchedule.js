const mongoose = require('mongoose');

const timeSlotSchema = new mongoose.Schema({
  startTime: {
    type: String,
    required: true,
    validate: {
      validator: function (v) {
        return /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/.test(v);
      },
      message: 'Invalid time format. Use HH:MM AM/PM format'
    }
  },
  endTime: {
    type: String,
    required: true,
    validate: {
      validator: function (v) {
        return /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/.test(v);
      },
      message: 'Invalid time format. Use HH:MM AM/PM format'
    }
  }
});

const darshanScheduleSchema = new mongoose.Schema({
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: true
  },
  name: JSON,
  description: JSON,
  dateType: {
    type: String,
    enum: [ 'SPECIFIC_DATE', 'DATE_RANGE' ],
    required: true
  },
  specificDate: {
    type: Date,
    required: function () {
      return this.dateType === 'SPECIFIC_DATE';
    }
  },
  dateRange: {
    startDate: {
      type: Date,
      required: function () {
        return this.dateType === 'DATE_RANGE';
      },
      validate: {
        validator: function (startDate) {
          if (this.dateType !== 'DATE_RANGE') {
            return true;
          }
          return startDate >= new Date();
        },
        message: 'Start date must be greater than or equal to current date'
      }
    },
    endDate: {
      type: Date,
      required: function () {
        return this.dateType === 'DATE_RANGE';
      },
      validate: {
        validator: function (endDate) {
          if (this.dateType !== 'DATE_RANGE') {
            return true;
          }
          return endDate > this.dateRange.startDate;
        },
        message: 'End date must be greater than start date'
      }
    }
  },
  timeSlots: [ timeSlotSchema ],
  pricing: {
    gstPercentage: {
      type: Number,
      required: false,
      min: 0
    },
    individual: {
      type: Number,
      required: true,
      min: 0
    },
    individualTaxablePrice: {
      type: Number,
      required: false,
      min: 0
    },
    individualGstPrice: {
      type: Number,
      required: false,
      min: 0
    },
    couple: {
      type: Number,
      required: true,
      min: 0,
    },
    coupleTaxablePrice: {
      type: Number,
      required: false,
      min: 0
    },
    coupleGstPrice: {
      type: Number,
      required: false,
      min: 0
    },
    family: {
      type: Number,
      required: true,
      min: 0,
    },
    familyTaxablePrice: {
      type: Number,
      required: false,
      min: 0
    },
    familyGstPrice: {
      type: Number,
      required: false,
      min: 0
    },
  },
  serviceCode: {
    type: String,
    required: true,
    trim: true,
  },
  promotionalKit: {
    price: {
      type: Number,
      min: 0,
      required: false
    },
    gstPercentage: {
      type: Number,
      min: 0,
      required: false
    },
    taxablePrice: {
      type: Number,
      min: 0,
      required: false
    },
    gstPrice: {
      type: Number,
      min: 0,
      required: false
    },
    productCode: {
      type: String,
      required: false,
      trim: true,
    }
  },
  occupancyPerSlot: {
    type: Number,
    required: true,
    min: 1,
    validate: {
      validator: Number.isInteger,
      message: 'Occupancy per slot must be an integer'
    }
  },
  status: {
    type: String,
    enum: [ 'ACTIVE', 'INACTIVE' ],
    default: 'ACTIVE'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, { timestamps: true });

const DarshanSchedule = mongoose.model('DarshanSchedule', darshanScheduleSchema);

module.exports = DarshanSchedule;
