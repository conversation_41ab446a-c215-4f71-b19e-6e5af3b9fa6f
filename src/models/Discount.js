const mongoose = require('mongoose');
const { applicabilityType, discountType, discountStatus } = require('../constants/dbEnums');

const discountSchema = new mongoose.Schema({
  code: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  applicabilityType: {
    type: String,
    enum: Object.values(applicabilityType),
    required: true
  },
  maxDiscount: {
    type: Number,
    default: null,
    min: 0
  },
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple'
  },
  poojaSchedules: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'PoojaSchedule'
  }],
  darshanSchedules: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DarshanSchedule'
  }],
  products: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product'
  }],
  discountType: {
    type: String,
    enum: Object.values(discountType),
    required: true
  },
  discountValue: {
    type: Number,
    required: true,
    min: 0
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  startTime: {
    type: String,
    required: true,
    validate: {
      validator: function (v) {
        return /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/.test(v);
      },
      message: 'Invalid time format. Use HH:MM AM/PM format'
    }
  },
  endTime: {
    type: String,
    required: true,
    validate: {
      validator: function (v) {
        return /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/.test(v);
      },
      message: 'Invalid time format. Use HH:MM AM/PM format'
    }
  },
  minimumPurchaseAmount: {
    type: Number,
    min: 0,
    default: 0
  },
  usageLimitPerUser: {
    type: Number,
    required: true,
    min: 1
  },
  totalUsageLimit: {
    type: Number,
    required: true,
    min: 1
  },
  totalUsageCount: {
    type: Number,
    default: 0
  },
  discountStatus: {
    type: String,
    enum: Object.values(discountStatus),
    default: discountStatus.ACTIVE
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  }
}, { timestamps: true });

const Discount = mongoose.model('Discount', discountSchema);

module.exports = Discount;