const mongoose = require('mongoose');

const favoriteProductSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  variant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ProductVariant',
    default: null
  }
}, { timestamps: true });

// Compound index to prevent duplicate favorites
// A user can favorite a product once, or a specific variant of a product
favoriteProductSchema.index(
  { user: 1, product: 1, variant: 1 }, 
  { unique: true }
);

const FavoriteProduct = mongoose.model('FavoriteProduct', favoriteProductSchema);

module.exports = FavoriteProduct;
