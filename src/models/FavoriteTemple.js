const mongoose = require('mongoose');

const favoriteTempleSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: true
  }
}, { timestamps: true });

// Compound index to prevent duplicate favorites
favoriteTempleSchema.index({ user: 1, temple: 1 }, { unique: true });

const FavoriteTemple = mongoose.model('FavoriteTemple', favoriteTempleSchema);

module.exports = FavoriteTemple;