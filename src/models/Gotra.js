const mongoose = require('mongoose');

const gotraSchema = new mongoose.Schema({
  gotra: JSON,
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, { timestamps: true });

const Gotra = mongoose.model('Gotra', gotraSchema);

module.exports = Gotra;
