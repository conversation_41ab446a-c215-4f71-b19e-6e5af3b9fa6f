const mongoose = require('mongoose');

const helicopterSchema = new mongoose.Schema({
  name: JSO<PERSON>,
  shortDescription: JSON,
  detailedDescription: JSON,
  image: {
    type: String,
    required: false,
    trim: true
  },
  video: {
    type: String,
    required: false,
    trim: true
  },
  link: {
    type: String,
    required: true,
    trim: true
  },
  itinerary: {
    type: String,
    required: false,
    trim: true
  },
  basePrice: {
    type: Number,
    required: false,
    min: 0
  },
  currency: {
    type: String,
    required: false,
    trim: true,
    default: 'INR'
  },
  maximumCapacityPerBooking: {
    type: Number,
    required: false,
    min: 0
  },
  dates: [{
    type: Date
  }],
  availability: [{
    date: {
      type: Date,
      required: true
    },
    availableSeats: {
      type: Number,
      required: true,
      min: 0
    }
  }],
  status: {
    type: String,
    enum: [ 'ACTIVE', 'INACTIVE' ],
    default: 'ACTIVE'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, { timestamps: true });

const Helicopter = mongoose.model('Helicopter', helicopterSchema);

module.exports = Helicopter;