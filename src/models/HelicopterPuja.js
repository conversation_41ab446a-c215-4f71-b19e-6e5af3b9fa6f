const mongoose = require('mongoose');

const helicopterPujaSchema = new mongoose.Schema({
  helicopter: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Helicopter',
    required: true
  },
  name: <PERSON><PERSON><PERSON>,
  description: JSON,
  duration: {
    value: {
      type: Number,
      min: 1
    },
    unit: {
      type: String,
      enum: [ 'MINS', 'HOURS' ]
    }
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  status: {
    type: String,
    enum: [ 'ACTIVE', 'INACTIVE' ],
    default: 'ACTIVE'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, { timestamps: true });

const HelicopterPuja = mongoose.model('HelicopterPuja', helicopterPujaSchema);

module.exports = HelicopterPuja;