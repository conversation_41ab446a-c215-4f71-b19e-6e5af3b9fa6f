const mongoose = require('mongoose');

const helicopterQuerySchema = new mongoose.Schema({
  helicopter: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Helicopter',
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  numberOfPeople: {
    type: Number,
    required: true,
    min: 1,
    max: 6
  },
  primaryDevoteeDetails: {
    fullName: JSON,
    gender: {
      type: String,
      enum: [ 'Male', 'Female', 'Other' ],
      required: true
    },
    aadharNumber: {
      type: String,
      trim: true
    },
    aadharImage: {
      type: String,
      required: false,
      trim: true
    },
    weight: {
      type: Number,
      min: 1
    },
    phoneNumber: {
      type: String,
      required: true,
      trim: true,
      validate: {
        validator: function (v) {
          return /^[6-9]\d{9}$/.test(v);
        },
        message: 'Please enter a valid phone number'
      }
    },
    whatsappNumber: {
      type: String,
      required: false,
      trim: true,
      validate: {
        validator: function (v) {
          if (!v) {
            return true;
          } // Allow empty
          return /^[6-9]\d{9}$/.test(v);
        },
        message: 'Please enter a valid WhatsApp number'
      }
    },
  },
  otherDevotees: [{
    _id: {
      type: mongoose.Schema.Types.ObjectId,
      auto: true
    },
    fullName: JSON,
    gender: {
      type: String,
      enum: [ 'Male', 'Female', 'Other' ],
      required: true
    },
    aadharNumber: {
      type: String,
      trim: true
    },
    aadharImage: {
      type: String,
      required: false,
      trim: true
    },
    weight: {
      type: Number,
      min: 1
    }
  }],
  status: {
    type: String,
    enum: [ 'SUBMITTED', 'APPROVED', 'REJECTED', 'CANCELLED', 'COMPLETED' ],
    default: 'SUBMITTED'
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
}, { timestamps: true });

const HelicopterQuery = mongoose.model('HelicopterQuery', helicopterQuerySchema);

module.exports = HelicopterQuery;