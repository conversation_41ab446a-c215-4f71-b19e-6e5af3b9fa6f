const mongoose = require('mongoose');

const inventorySchema = new mongoose.Schema({
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: true
  },
  kiosk: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Kiosk',
    required: true
  },
  admin: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  sentQuantity: {
    type: Number,
    required: true,
    min: 1
  },
  receivedQuantity: {
    type: Number,
    default: 0,
    min: 0
  },
  sentAt: {
    type: Date,
    required: true
  },
  receivedAt: {
    type: Date,
    default: null
  },
  receivedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'KioskUser',
    default: null
  }
}, { timestamps: true });

const Inventory = mongoose.model('Inventory', inventorySchema);

module.exports = Inventory;