const mongoose = require('mongoose');

/**
 * Invoice model for storing vendor commission invoices
 */
const invoiceSchema = new mongoose.Schema({
  // Invoice details
  invoiceNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  invoiceDate: {
    type: Date,
    required: true,
    default: Date.now
  },

  // Vendor details
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor',
    required: true
  },
  vendorDetails: {
    businessName: {
      type: String,
      required: true,
      trim: true
    },
    address: {
      type: String,
      required: true,
      trim: true
    },
    gstNumber: {
      type: String,
      required: true,
      trim: true
    }
  },

  // Platform/Admin details
  platformDetails: {
    businessName: {
      type: String,
      required: true,
      trim: true
    },
    address: {
      type: String,
      required: true,
      trim: true
    },
    gstNumber: {
      type: String,
      required: true,
      trim: true
    }
  },

  // Settlement details
  settlementPeriod: {
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    }
  },

  // Financial details
  totalTaxablePrice: {
    type: Number,
    required: true,
    min: 0
  },
  totalGstPrice: {
    type: Number,
    required: true,
    min: 0
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },

  // Related shipments
  shipment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ProductShipment'
  },

  // Order items
  orderItems: [{
    name: {
      type: String,
      required: true
    },
    quantity: {
      type: Number,
      required: true
    },
    price: {
      type: String,
      required: true
    },
    subtotal: {
      type: String,
      required: true
    }
  }],

  // Invoice file
  invoiceFile: {
    type: String,
    required: true,
    trim: true
  },

  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  }
}, { timestamps: true });

// Add indexes for better query performance
invoiceSchema.index({ invoiceNumber: 1 }, { unique: true });
invoiceSchema.index({ vendor: 1 });
invoiceSchema.index({ 'settlementPeriod.startDate': 1, 'settlementPeriod.endDate': 1 });
invoiceSchema.index({ createdAt: 1 });

const Invoice = mongoose.model('Invoice', invoiceSchema);

module.exports = Invoice;
