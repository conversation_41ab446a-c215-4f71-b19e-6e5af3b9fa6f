const mongoose = require('mongoose');
const { userStatusValue } = require('../constants/dbEnums');

const kioskSchema = new mongoose.Schema({
  name: {
    type: String,
    required: false,
    trim: true
  },
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: true
  },
  location: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: Object.values(userStatusValue),
    default: userStatusValue.ACTIVE
  },
  inventoryRemaining: {
    type: Number,
    default: 0,
    min: 0
  },
  isNotificationTriggered: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, { timestamps: true });

// Virtual for kiosk users
kioskSchema.virtual('kioskUsers', {
  ref: 'KioskUser',
  localField: '_id',
  foreignField: 'kiosk'
});

kioskSchema.set('toObject', { virtuals: true });
kioskSchema.set('toJSON', { virtuals: true });

const Kiosk = mongoose.model('Kiosk', kioskSchema);

module.exports = Kiosk;
