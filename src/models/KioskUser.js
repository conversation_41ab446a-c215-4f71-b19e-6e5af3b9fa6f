const mongoose = require('mongoose');
const { userStatusValue, userTypeValue } = require('../constants/dbEnums');

const kioskUserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
  },
  phoneNumber: {
    type: String,
    required: true,
    trim: true,
  },
  kiosk: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Kiosk',
    required: true
  },
  password: {
    type: String,
    required: true
  },
  userType: {
    type: String,
    enum: [ userTypeValue.KIOSK ],
    default: userTypeValue.KIOSK
  },
  status: {
    type: String,
    enum: Object.values(userStatusValue),
    default: userStatusValue.ACTIVE
  },
  passwordChangeRequired: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, { timestamps: true });

const KioskUser = mongoose.model('KioskUser', kioskUserSchema);

module.exports = KioskUser;
