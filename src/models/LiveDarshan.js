const mongoose = require('mongoose');
const { darshanStatus } = require('../constants/dbEnums');

const liveDarshanSchema = new mongoose.Schema({
  url: {
    type: String,
    required: true,
    trim: true
  },
  name: {
    type: String,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: true
  },
  templeAdmin: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TempleAdmin',
    required: true
  },
  status: {
    type: String,
    enum: Object.values(darshanStatus),
    default: darshanStatus.PENDING
  },
  scheduledAt: {
    type: Date,
    default: null
  },
  reviewedAt: {
    type: Date,
    default: null
  },
  rejectionReason: {
    type: String,
    trim: true,
    default: null
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, { timestamps: true });

liveDarshanSchema.index({ temple: 1 });
liveDarshanSchema.index({ templeAdmin: 1 });

const LiveDarshan = mongoose.model('LiveDarshan', liveDarshanSchema);

module.exports = LiveDarshan;