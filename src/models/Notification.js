const mongoose = require('mongoose');
const { userTypeValue, notificationType } = require('../constants/dbEnums');

const notificationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  title: JSO<PERSON>,
  body: JSON,
  type: {
    type: String,
    enum: Object.values(userTypeValue),
    required: true
  },
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    default: null
  },
  isRead: {
    type: Boolean,
    default: false
  },
  redirectPath: {
    type: String,
    default: null
  },
  metaData: {
    type: Object,
    default: null
  },
  notificationType: {
    type: String,
    enum: [ ...Object.values(notificationType), null ],
    default: null
  }
}, { timestamps: true });

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;