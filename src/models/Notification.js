const mongoose = require('mongoose');
const { userTypeValue } = require('../constants/dbEnums');

const notificationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  body: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    enum: Object.values(userTypeValue),
    required: true
  },
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    default: null
  },
  isRead: {
    type: Boolean,
    default: false
  }

}, { timestamps: true });

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;