const mongoose = require('mongoose');
const { otpTypeValue, userTypeValue } = require('../constants/dbEnums');

const oneTimePasswordSchema = new mongoose.Schema({
  phoneNumber: {
    type: String,
    required: false,
    trim: true,
    // match: [ /^[6-9]\d{9}$/, 'Please use a valid phone number format' ],
  },
  countryCode: {
    type: String,
    required: false,
    trim: true,
    match: [ /^\+\d{1,3}$/, 'Please use a valid country code format' ],
  },
  email: {
    type: String,
    required: false,
    default: null,
    trim: true,
    lowercase: true,
    match: [ /^\S+@\S+\.\S+$/, 'Please use a valid email address' ]
  },
  type: {
    type: String,
    enum: [ otpTypeValue.EMAIL, otpTypeValue.PHONE_NUMBER ],
    default: otpTypeValue.EMAIL,
    required: true,
  },
  userType: {
    type: String,
    enum: Object.values(userTypeValue), // Super Admin , User , <PERSON> Admin , <PERSON><PERSON><PERSON> , Vendor
    default: userTypeValue.SUPER_ADMIN,
    required: true,
  },
  otp: {
    type: String,
    required: true,
    minlength: 6,
    maxlength: 6,
  },
  expiresAt: {
    type: Date,
    required: false,
    default: null,
  },
  deletedAt: {
    type: Date,
    required: false,
    default: null,
  },
  isVerified: {
    type: Boolean,
    required: false,
    default: false,
  }
}, { timestamps: true });

const OneTimePassword = mongoose.model('OneTimePassword', oneTimePasswordSchema);

module.exports = OneTimePassword;
