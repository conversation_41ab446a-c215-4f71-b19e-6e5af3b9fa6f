const mongoose = require('mongoose');
const { otpTypeValue, userTypeValue } = require('../constants/dbEnums');

const oneTimePasswordSchema = new mongoose.Schema({
  phoneNumber: {
    type: String,
    required: false,
    trim: true,
    // match: [ /^[6-9]\d{9}$/, 'Please use a valid phone number format' ],
  },
  countryCode: {
    type: String,
    required: false,
    trim: true,
    match: [ /^\+\d{1,3}$/, 'Please use a valid country code format' ],
  },
  email: {
    type: String,
    required: false,
    default: null,
    trim: true,
    lowercase: true,
    match: [ /^\S+@\S+\.\S+$/, 'Please use a valid email address' ]
  },
  type: {
    type: String,
    enum: [ otpTypeValue.EMAIL, otpTypeValue.PHONE_NUMBER ],
    default: otpTypeValue.EMAIL,
    required: true,
  },
  userType: {
    type: String,
    enum: Object.values(userTypeValue), // Super Admin , User , <PERSON>min , <PERSON><PERSON><PERSON> , Vendor
    default: userTypeValue.SUPER_ADMIN,
    required: true,
  },
  otp: {
    type: String,
    required: true,
    minlength: 6,
    maxlength: 6,
  },
  expiresAt: {
    type: Date,
    required: false,
    default: null,
  },
  deletedAt: {
    type: Date,
    required: false,
    default: null,
  },
  isVerified: {
    type: Boolean,
    required: false,
    default: false,
  }
}, { timestamps: true });

oneTimePasswordSchema.index({
  phoneNumber: 1,
  countryCode: 1,
  type: 1,
  userType: 1,
  deletedAt: 1,
  isVerified: 1
});

/** ✅ TTL index for automatic cleanup of expired OTPs */
oneTimePasswordSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

const OneTimePassword = mongoose.model('OneTimePassword', oneTimePasswordSchema);

module.exports = OneTimePassword;
