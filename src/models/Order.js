const mongoose = require('mongoose');
const { orderStatusValue, paymentStatusValue, paymentGateway } = require('../constants/dbEnums');

const orderItemSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  variant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ProductVariant'
  },
  name: JSON,
  price: {
    type: Number,
    required: true,
    min: 0
  },
  gstPercentage: {
    type: Number,
    min: 0,
    required: false
  },
  taxablePrice: {
    type: Number,
    min: 0,
    required: false
  },
  gstPrice: {
    type: Number,
    min: 0,
    required: false
  },
  vendorPrice: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  vendorTaxablePrice: {
    type: Number,
    min: 0,
    required: false
  },
  vendorGstPrice: {
    type: Number,
    min: 0,
    required: false
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  subTotalTaxablePrice: {
    type: Number,
    min: 0,
    required: false
  },
  subTotalGstPrice: {
    type: Number,
    min: 0,
    required: false
  },
  vendorSubtotal: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  vendorSubTotalTaxablePrice: {
    type: Number,
    min: 0,
    required: false
  },
  vendorSubTotalGstPrice: {
    type: Number,
    min: 0,
    required: false
  },
  commissionType: {
    type: String,
    enum: [ 'FIXED', 'PERCENTAGE' ]
  },
  commissionPrice: {
    type: Number,
    min: 0,
    required: false
  },
  isCommissionBasedProduct: {
    type: Boolean
  },
  isOneGodProduct: {
    type: Boolean
  },
  variantAttributes: {
    type: Map,
    of: String
  },
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'vendorModel',
    required: true
  },
  vendorModel: {
    type: String,
    required: true,
    enum: [ 'Admin', 'Vendor' ]
  },
  pickupAddress: {
    addressLine1: {
      type: String,
      trim: true
    },
    addressLine2: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      trim: true
    },
    state: {
      type: String,
      trim: true
    },
    postalCode: {
      type: String,
      trim: true
    },
    country: {
      type: String,
      trim: true,
      default: 'India'
    },
    phone: {
      type: String,
      trim: true
    }
  },
  shipRocketPickupLocationName: {
    type: String,
    trim: true,
    default: 'AdminLocation'
  },
  shipmentStatus: {
    type: String,
    enum: [ 'PENDING', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED' ],
    default: 'PENDING'
  },
  estimatedDelivery: {
    type: Date
  }
});

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  items: [ orderItemSchema ],
  shipments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ProductShipment'
  }],
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  subTotalTaxablePrice: {
    type: Number,
    min: 0,
    required: false
  },
  subTotalGstPrice: {
    type: Number,
    min: 0,
    required: false
  },
  shippingCost: {
    totalWeight: {
      type: Number,
      min: 0
    },
    baseShipping: {
      type: Number,
      min: 0
    },
    gst: {
      type: Number,
      min: 0
    },
    totalShipping: {
      type: Number,
      min: 0
    }
  },
  discountAmount: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  total: {
    type: Number,
    required: true,
    min: 0
  },
  originalTotal: {
    type: Number,
    min: 0
  },
  discount: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Discount',
    default: null
  },
  shippingAddress: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    addressLine1: {
      type: String,
      required: true,
      trim: true
    },
    addressLine2: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      required: true,
      trim: true
    },
    state: {
      type: String,
      required: true,
      trim: true
    },
    postalCode: {
      type: String,
      required: true,
      trim: true
    },
    country: {
      type: String,
      required: true,
      trim: true,
      default: 'India'
    },
    phone: {
      type: String,
      required: true,
      trim: true
    },
    email: {
      type: String,
      trim: true
    }
  },
  billingAddress: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    addressLine1: {
      type: String,
      required: true,
      trim: true
    },
    addressLine2: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      required: true,
      trim: true
    },
    state: {
      type: String,
      required: true,
      trim: true
    },
    postalCode: {
      type: String,
      required: true,
      trim: true
    },
    country: {
      type: String,
      required: true,
      trim: true,
      default: 'India'
    },
    phone: {
      type: String,
      required: true,
      trim: true
    },
    email: {
      type: String,
      trim: true
    }
  },
  paymentMethod: {
    type: String,
    required: true,
    enum: [ 'RAZORPAY' ],
    default: 'RAZORPAY'
  },
  paymentGateway: {
    type: String,
    enum: Object.values(paymentGateway),
    default: paymentGateway.RAZORPAY
  },
  paymentStatus: {
    type: String,
    required: true,
    enum: Object.values(paymentStatusValue),
    default: paymentStatusValue.PENDING
  },
  orderStatus: {
    type: String,
    required: true,
    enum: Object.values(orderStatusValue),
    default: orderStatusValue.PENDING
  },
  notes: JSON,
  trackingNumber: {
    type: String,
    trim: true
  },
  shippingProvider: {
    type: String,
    trim: true
  },
  estimatedDelivery: {
    type: Date
  },
  cancelReason: JSON,
  cancelledAt: {
    type: Date
  },
  deliveredAt: {
    type: Date
  },
  stockUpdates: {
    type: Array,
    default: []
  },
  variantStockUpdates: {
    type: Array,
    default: []
  },
  stockReduced: {
    type: Boolean,
    default: false
  },
  userInvoice: {
    type: String,
    trim: true
  },
  isBuyNow: {
    type: Boolean,
    default: false
  }
}, { timestamps: true });

// Add indexes for better query performance
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ user: 1 });
orderSchema.index({ orderStatus: 1 });
orderSchema.index({ paymentStatus: 1 });
orderSchema.index({ createdAt: -1 });

const Order = mongoose.model('Order', orderSchema);

module.exports = Order;
