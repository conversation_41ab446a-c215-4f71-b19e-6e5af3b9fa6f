const mongoose = require('mongoose');
const { orderStatusValue, paymentStatusValue } = require('../constants/dbEnums');

const orderItemSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  variant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ProductVariant'
  },
  name: JSON,
  price: {
    type: Number,
    required: true,
    min: 0
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  variantAttributes: {
    type: Map,
    of: String
  },
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'vendorModel',
    required: true
  },
  vendorModel: {
    type: String,
    required: true,
    enum: [ 'Admin', 'Vendor' ]
  },
  shipmentStatus: {
    type: String,
    enum: [ 'PENDING', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED' ],
    default: 'PENDING'
  },
  estimatedDelivery: {
    type: Date
  }
});

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  items: [ orderItemSchema ],
  shipments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ProductShipment'
  }],
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  tax: {
    type: Number,
    required: true,
    min: 0
  },
  shippingCost: {
    type: Number,
    required: true,
    min: 0
  },
  discountAmount: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  total: {
    type: Number,
    required: true,
    min: 0
  },
  originalTotal: {
    type: Number,
    min: 0
  },
  discount: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Discount',
    default: null
  },
  shippingAddress: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    addressLine1: {
      type: String,
      required: true,
      trim: true
    },
    addressLine2: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      required: true,
      trim: true
    },
    state: {
      type: String,
      required: true,
      trim: true
    },
    postalCode: {
      type: String,
      required: true,
      trim: true
    },
    country: {
      type: String,
      required: true,
      trim: true,
      default: 'India'
    },
    phone: {
      type: String,
      required: true,
      trim: true
    },
    email: {
      type: String,
      trim: true
    }
  },
  billingAddress: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    addressLine1: {
      type: String,
      required: true,
      trim: true
    },
    addressLine2: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      required: true,
      trim: true
    },
    state: {
      type: String,
      required: true,
      trim: true
    },
    postalCode: {
      type: String,
      required: true,
      trim: true
    },
    country: {
      type: String,
      required: true,
      trim: true,
      default: 'India'
    },
    phone: {
      type: String,
      required: true,
      trim: true
    },
    email: {
      type: String,
      trim: true
    }
  },
  paymentMethod: {
    type: String,
    required: true,
    enum: [ 'RAZORPAY' ],
    default: 'RAZORPAY'
  },
  paymentStatus: {
    type: String,
    required: true,
    enum: Object.values(paymentStatusValue),
    default: paymentStatusValue.PENDING
  },
  orderStatus: {
    type: String,
    required: true,
    enum: Object.values(orderStatusValue),
    default: orderStatusValue.PENDING
  },
  notes: JSON,
  trackingNumber: {
    type: String,
    trim: true
  },
  shippingProvider: {
    type: String,
    trim: true
  },
  estimatedDelivery: {
    type: Date
  },
  cancelReason: JSON,
  cancelledAt: {
    type: Date
  },
  deliveredAt: {
    type: Date
  },
  stockUpdates: {
    type: Array,
    default: []
  },
  variantStockUpdates: {
    type: Array,
    default: []
  },
  stockReduced: {
    type: Boolean,
    default: false
  },
  userInvoice: {
    type: String,
    trim: true
  },
  isBuyNow: {
    type: Boolean,
    default: false
  }
}, { timestamps: true });

// Add indexes for better query performance
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ user: 1 });
orderSchema.index({ orderStatus: 1 });
orderSchema.index({ paymentStatus: 1 });
orderSchema.index({ createdAt: -1 });

const Order = mongoose.model('Order', orderSchema);

module.exports = Order;
