const mongoose = require('mongoose');
const { paymentStatusValue } = require('../constants/dbEnums');

const paymentSchema = new mongoose.Schema({
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    required: true,
    default: 'INR',
    enum: [ 'INR' ]
  },
  paymentMethod: {
    type: String,
    required: true,
    enum: [ 'RAZORPAY' ],
    default: 'RAZORPAY'
  },
  razorpayOrderId: {
    type: String,
    trim: true
  },
  razorpayPaymentId: {
    type: String,
    trim: true
  },
  razorpaySignature: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    required: true,
    enum: Object.values(paymentStatusValue),
    default: paymentStatusValue.PENDING
  },
  notes: {
    type: String,
    trim: true
  },
  failureReason: {
    type: String,
    trim: true
  },
  refundAmount: {
    type: Number,
    min: 0,
    default: 0
  },
  refundReason: {
    type: String,
    trim: true
  },
  refundedAt: {
    type: Date
  }
}, { timestamps: true });

// Add indexes for better query performance
paymentSchema.index({ order: 1 });
paymentSchema.index({ user: 1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ razorpayOrderId: 1 });
paymentSchema.index({ razorpayPaymentId: 1 });

const Payment = mongoose.model('Payment', paymentSchema);

module.exports = Payment;
