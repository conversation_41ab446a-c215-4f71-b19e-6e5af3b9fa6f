const mongoose = require('mongoose');
const { paymentStatus, paymentMethod, type } = require('../constants/dbEnums');

const paymentBookingSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  razorpayOrderId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  razorpayPaymentId: {
    type: String,
    trim: true,
    sparse: true,
    unique: true
  },
  razorpaySignature: {
    type: String,
    trim: true
  },
  type: {
    type: String,
    enum: Object.values(type),
    required: true
  },
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking'
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    default: 'INR',
    uppercase: true,
    trim: true
  },
  status: {
    type: String,
    enum: Object.values(paymentStatus),
    default: paymentStatus.PENDING
  },
  paymentMethod: {
    type: String,
    enum: Object.values(paymentMethod)
  },
  upiVpa: {
    type: String,
    trim: true
  },
  bankName: {
    type: String,
    trim: true
  },
  cardId: {
    type: String,
    trim: true
  },
  paymentResponse: {
    type: mongoose.Schema.Types.Mixed
  },
  failureReason: {
    type: String,
    trim: true
  },
  refundId: {
    type: String,
    trim: true
  },
  refundAmount: {
    type: Number,
    min: 0
  },
  refundReason: {
    type: String,
    trim: true
  },
  refundAt: {
    type: Date
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed
  }
}, { timestamps: true });

// Indexes
paymentBookingSchema.index({ userId: 1 });
paymentBookingSchema.index({ darshanBookingId: 1 });
paymentBookingSchema.index({ poojaBookingId: 1 });
paymentBookingSchema.index({ status: 1 });
paymentBookingSchema.index({ createdAt: 1 });

const PaymentBooking = mongoose.model('PaymentBooking', paymentBookingSchema);

module.exports = PaymentBooking;