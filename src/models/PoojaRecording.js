const mongoose = require('mongoose');

const poojaRecordingSchema = new mongoose.Schema({
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    required: true
  },
  kiosk: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Kiosk',
    required: true
  },
  kioskUser: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'KioskUser',
    required: true
  },
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: true
  },
  recordingUrl: {
    type: String,
    required: true,
    trim: true
  },
  recordingKey: {
    type: String,
    required: true,
    trim: true
  },
  updatedKey: {
    type: String,
    trim: true,
    default: null
  },
  updatedUrl: {
    type: String,
    trim: true,
    default: null
  },
  status: {
    type: String,
    enum: [ 'APPROVED', 'REJECTED', 'PENDING' ],
    default: 'PENDING'
  },
  rejectionReason: {
    type: String,
    trim: true,
    default: null
  },
  reviewedAt: {
    type: Date,
    default: null
  }
}, { timestamps: true });

const PoojaRecording = mongoose.model('PoojaRecording', poojaRecordingSchema);

module.exports = PoojaRecording;