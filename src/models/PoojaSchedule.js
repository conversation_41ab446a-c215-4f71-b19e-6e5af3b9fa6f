const mongoose = require('mongoose');

const timeSlotSchema = new mongoose.Schema({
  startTime: {
    type: String,
    required: true,
    validate: {
      validator: function (v) {
        return /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/.test(v);
      },
      message: 'Invalid time format. Use HH:MM AM/PM format'
    }
  },
  endTime: {
    type: String,
    required: true,
    validate: {
      validator: function (v) {
        return /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/.test(v);
      },
      message: 'Invalid time format. Use HH:MM AM/PM format'
    }
  }
});

const poojaScheduleSchema = new mongoose.Schema({
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: true
  },
  type: {
    type: String,
    enum: [ 'PHYSICAL', 'VIRTUAL' ],
    required: true
  },
  name: <PERSON><PERSON><PERSON>,
  description: JSON,
  duration: {
    value: {
      type: Number,
      min: 1
    },
    unit: {
      type: String,
      enum: [ 'MINS', 'HOURS', 'ALL_NIGHT' ]
    }
  },
  dateType: {
    type: String,
    enum: [ 'SPECIFIC_DATE', 'DATE_RANGE' ],
    required: true
  },
  specificDate: {
    type: Date,
    required: function () {
      return this.dateType === 'SPECIFIC_DATE';
    }
  },
  dateRange: {
    startDate: {
      type: Date,
      required: function () {
        return this.dateType === 'DATE_RANGE';
      },
      validate: {
        validator: function (startDate) {
          if (this.dateType !== 'DATE_RANGE') {
            return true;
          }
          return startDate >= new Date();
        },
        message: 'Start date must be greater than or equal to current date'
      }
    },
    endDate: {
      type: Date,
      required: function () {
        return this.dateType === 'DATE_RANGE';
      },
      validate: {
        validator: function (endDate) {
          if (this.dateType !== 'DATE_RANGE') {
            return true;
          }
          return endDate > this.dateRange.startDate;
        },
        message: 'End date must be greater than start date'
      }
    }
  },
  timeSlots: [ timeSlotSchema ],
  pricing: {
    individual: {
      type: Number,
      required: true,
      min: 0
    },
    couple: {
      type: Number,
      required: true,
      min: 0,
      validate: {
        validator: function (v) {
          return v >= this.pricing.individual;
        },
        message: 'Couple price must be greater than or equal to individual price'
      }
    },
    family: {
      type: Number,
      required: true,
      min: 0,
      validate: {
        validator: function (v) {
          return v >= this.pricing.couple;
        },
        message: 'Family price must be greater than or equal to couple price'
      }
    }
  },
  occupancyPerSlot: {
    type: Number,
    required: false,
    min: 1,
    validate: {
      validator: Number.isInteger,
      message: 'Occupancy per slot must be an integer'
    }
  },
  status: {
    type: String,
    enum: [ 'ACTIVE', 'INACTIVE' ],
    default: 'ACTIVE'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, { timestamps: true });

const PoojaSchedule = mongoose.model('PoojaSchedule', poojaScheduleSchema);

module.exports = PoojaSchedule;
