const mongoose = require('mongoose');
const { productStatusValue } = require('../constants/dbEnums');

const productSchema = new mongoose.Schema({
  name: JSON,
  gstPercentage: {
    type: Number,
    min: 0,
    required: false
  },
  productCode: {
    type: String,
    required: false,
    trim: true,
  },
  isCommissionBasedProduct: {
    type: Boolean,
  },
  isOneGodProduct: {
    type: Boolean,
  },
  vendorPrice: {
    type: Number,
    required: false,
    min: 0
  },
  vendorTaxablePrice: {
    type: Number,
    min: 0,
    required: false
  },
  vendorGstPrice: {
    type: Number,
    min: 0,
    required: false
  },
  commissionType: {
    type: String,
    enum: [ 'FIXED', 'PERCENTAGE' ]
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true
  },
  subCategory: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SubCategory',
    required: true
  },
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: false
  },
  lowStockThreshold: {
    type: Number,
    min: 0,
    default: 50
  },
  stockUpdateStatus: {
    type: String,
    enum: [ 'NONE', 'PENDING' ],
    default: 'NONE'
  },

  // weight: {
  //   type: Number,
  //   required: true,
  //   min: 0
  // },
  // dimensions: {
  //   length: {
  //     type: Number,
  //     required: true,
  //     min: 0
  //   },
  //   width: {
  //     type: Number,
  //     required: true,
  //     min: 0
  //   },
  //   height: {
  //     type: Number,
  //     required: true,
  //     min: 0
  //   }
  // },
  featured: {
    type: Boolean,
    default: false
  },
  showOnHomepage: {
    type: Boolean,
    default: false,
    index: true
  },
  status: {
    type: String,
    enum: Object.values(productStatusValue),
    default: productStatusValue.PENDING
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'createdByModel',
    required: true
  },
  createdByModel: {
    type: String,
    required: true,
    enum: [ 'Admin', 'Vendor' ]
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'updatedByModel'
  },
  updatedByModel: {
    type: String,
    enum: [ 'Admin', 'Vendor' ]
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  approvedAt: {
    type: Date
  },
  rejectionReason: {
    type: String,
    trim: true
  },
  // Variant configuration
  hasVariants: {
    type: Boolean,
    default: false
  },
  variantAttributes: {
    type: [ String ],
    default: []
  },
  pickupAddress: {
    addressLine1: {
      type: String,
      trim: true
    },
    addressLine2: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      trim: true
    },
    state: {
      type: String,
      trim: true
    },
    postalCode: {
      type: String,
      trim: true
    },
    country: {
      type: String,
      trim: true,
      default: 'India'
    },
    phone: {
      type: String,
      trim: true
    }
  },
  deletedAt: {
    type: Date,
    default: null
  },
}, { timestamps: true, id: false });

// Add virtual field for variants
productSchema.virtual('variants', {
  ref: 'ProductVariant',
  localField: '_id',
  foreignField: 'product'
});

// Configure toJSON and toObject to include virtuals
productSchema.set('toJSON', { virtuals: true });
productSchema.set('toObject', { virtuals: true });

// Add indexes for better query performance
productSchema.index({ 'name.en': 'text' });
productSchema.index({ 'name.hi': 'text' });
productSchema.index({ category: 1 });
productSchema.index({ subCategory: 1 });
productSchema.index({ temple: 1 });
productSchema.index({ status: 1 });
productSchema.index({ featured: 1 });
productSchema.index({ createdBy: 1 });

const Product = mongoose.model('Product', productSchema);

module.exports = Product;
