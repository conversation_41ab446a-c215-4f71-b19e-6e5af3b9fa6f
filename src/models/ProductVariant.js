const mongoose = require('mongoose');

const productVariantSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  // Variant attributes (e.g., color, size)
  attributes: {
    type: Map,
    of: String,
    required: true
  },
  // Variant-specific details
  price: {
    type: Number,
    required: true,
    min: 0
  },
  vendorPrice: {
    type: Number,
    required: false,
    min: 0,
    default: 0
  },
  discountPrice: {
    type: Number,
    min: 0,
    validate: {
      validator: function (v) {
        return !v || v < this.price;
      },
      message: 'Discount price must be less than regular price'
    }
  },
  stock: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  lowStockThreshold: {
    type: Number,
    min: 0,
    default: 50
  },
  stockUpdateStatus: {
    type: String,
    enum: [ 'NONE', 'PENDING' ],
    default: 'NONE'
  },
  sku: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  images: [{
    type: String,
    trim: true
  }],
  isDefault: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, { timestamps: true });

// Add indexes for better query performance
productVariantSchema.index({ product: 1 });
productVariantSchema.index({ sku: 1 });
productVariantSchema.index({ 'attributes.color': 1 });
productVariantSchema.index({ 'attributes.size': 1 });

const ProductVariant = mongoose.model('ProductVariant', productVariantSchema);

module.exports = ProductVariant;
