const mongoose = require('mongoose');

const productVariantSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  description: JSON,
  // Variant attributes (e.g., color, size)
  attributes: {
    type: Map,
    of: String,
    required: true
  },
  unit: {
    type: Number,
    required: false,
  },
  hsn: {
    type: String,
    required: false,
    trim: true,
  },
  // Variant-specific details
  price: {
    type: Number,
    required: true,
    min: 0
  },
  gstPercentage: {
    type: Number,
    min: 0,
    required: false
  },
  taxablePrice: {
    type: Number,
    min: 0,
    required: false
  },
  gstPrice: {
    type: Number,
    min: 0,
    required: false
  },
  productCode: {
    type: String,
    required: false,
    trim: true,
  },
  vendorPrice: {
    type: Number,
    required: false,
    min: 0,
    default: 0
  },
  vendorTaxablePrice: {
    type: Number,
    min: 0,
    required: false
  },
  vendorGstPrice: {
    type: Number,
    min: 0,
    required: false
  },
  commissionType: {
    type: String,
    enum: [ 'FIXED', 'PERCENTAGE' ]
  },
  commissionPrice: {
    type: Number,
    required: false,
    min: 0
  },
  discountPrice: {
    type: Number,
    min: 0,
    validate: {
      validator: function (v) {
        return !v || v < this.price;
      },
      message: 'Discount price must be less than regular price'
    }
  },
  stock: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  lowStockThreshold: {
    type: Number,
    min: 0,
    default: 50
  },
  stockUpdateStatus: {
    type: String,
    enum: [ 'NONE', 'PENDING' ],
    default: 'NONE'
  },
  sku: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  images: [{
    type: String,
    trim: true
  }],
  isDefault: {
    type: Boolean,
    default: false
  },
  pickupAddress: {
    addressLine1: {
      type: String,
      trim: true
    },
    addressLine2: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      trim: true
    },
    state: {
      type: String,
      trim: true
    },
    postalCode: {
      type: String,
      trim: true
    },
    country: {
      type: String,
      trim: true,
      default: 'India'
    },
    phone: {
      type: String,
      trim: true
    }
  },
  shipRocketPickupLocationName: {
    type: String,
    trim: true,
    default: 'AdminLocation'
  },
  gstIn: {
    type: String,
    required: false,
    trim: true,
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, { timestamps: true });

// Add indexes for better query performance
productVariantSchema.index({ product: 1 });
productVariantSchema.index({ sku: 1 });
productVariantSchema.index({ 'attributes.color': 1 });
productVariantSchema.index({ 'attributes.size': 1 });

const ProductVariant = mongoose.model('ProductVariant', productVariantSchema);

module.exports = ProductVariant;
