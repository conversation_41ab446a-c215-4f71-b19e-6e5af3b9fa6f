const mongoose = require('mongoose');

const pujariSchema = new mongoose.Schema({
  name: JSO<PERSON>,
  email: {
    type: String,
    required: false,
    trim: true,
    lowercase: true
  },
  phoneNumber: {
    type: String,
    required: true,
    trim: true
  },
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: true
  },
  status: {
    type: String,
    enum: [ 'ACTIVE', 'INACTIVE' ],
    default: 'ACTIVE'
  },
  approvalStatus: {
    type: String,
    enum: [ 'PENDING_APPROVAL', 'APPROVED', 'REJECTED' ],
    default: 'PENDING_APPROVAL'
  },
  templeSequenceNumber: {
    type: Number,
    default: null
  },
  rejectionReason: JSON,
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    default: null
  },
  approvedAt: {
    type: Date,
    default: null
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, { timestamps: true });

// Add indexes for better query performance
pujariSchema.index({ temple: 1 });
pujariSchema.index({ email: 1 });
pujariSchema.index({ phoneNumber: 1 });
pujariSchema.index({ temple: 1, approvalStatus: 1 });
pujariSchema.index({ temple: 1, templeSequenceNumber: 1 });

const Pujari = mongoose.model('Pujari', pujariSchema);

module.exports = Pujari;
