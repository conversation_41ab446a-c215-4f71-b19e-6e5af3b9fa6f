const mongoose = require('mongoose');

const shopBannerSchema = new mongoose.Schema({
  url: {
    type: String,
    required: true,
    trim: true
  },
  key: {
    type: String,
    required: true,
    trim: true
  },
  title: {
    type: String,
    trim: true,
    default: null,
    maxLength: 100
  },
  description: {
    type: String,
    trim: true,
    default: null,
    maxLength: 200
  },
  order: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
}, { timestamps: true });

const EShopBanner = mongoose.model('ShopBanner', shopBannerSchema);

module.exports = EShopBanner;