const mongoose = require('mongoose');
const { stockUpdateStatusValue } = require('../constants/dbEnums');

const stockUpdateRequestSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  variant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ProductVariant'
  },
  currentStock: {
    type: Number,
    required: true,
    min: 0
  },
  requestedStock: {
    type: Number,
    required: true,
    min: 0
  },
  status: {
    type: String,
    enum: Object.values(stockUpdateStatusValue),
    default: stockUpdateStatusValue.PENDING
  },
  requestedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor',
    required: true
  },
  requestedAt: {
    type: Date,
    default: Date.now
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  reviewedAt: {
    type: Date
  },
  rejectionReason: {
    type: String,
    trim: true
  }
}, { timestamps: true });

// Add indexes for better query performance
stockUpdateRequestSchema.index({ product: 1 });
stockUpdateRequestSchema.index({ variant: 1 });
stockUpdateRequestSchema.index({ requestedBy: 1 });
stockUpdateRequestSchema.index({ status: 1 });

const StockUpdateRequest = mongoose.model('StockUpdateRequest', stockUpdateRequestSchema);

module.exports = StockUpdateRequest;
