const mongoose = require('mongoose');
const { assignmentMode } = require('../constants/dbEnums');

// Define the payout component schema
const payoutComponentSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  }
}, { _id: true });

const templeSchema = new mongoose.Schema({
  name: JSON,
  pujariBookingAssignmentMode: {
    type: String,
    enum: Object.values(assignmentMode),
    default: assignmentMode.AUTOMATIC
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TempleCategory',
    required: false
  },
  tagLine: JSON,
  description: JSON,
  history: JSON,
  significanceAndArchitecture: JSON,
  state: JSON,
  city: JSON,
  locationUrl: {
    type: String,
    validate: {
      validator: function (v) {
        return !v || /^https:\/\/www\.google\.com\/maps/.test(v);
      },
      message: props => `${props.value} is not a valid Google Maps URL`
    }
  },
  deity: JSON,
  images: [{
    type: String,
    required: true,
    trim: true,
  }],
  video: {
    type: String, 
    required: false, 
    trim: true, 
    default: null
  },
  guidelines: JSON,
  tags: [{
    type: String,
    trim: true
  }],
  showOnHomepage: {
    type: Boolean,
    default: false,
    index: true
  },
  services: [{
    type: String,
    enum: [ 'PHYSICAL_POOJA', 'PHYSICAL_DARSHAN', 'VIRTUAL_POOJA' ]
  }],
  operatingHours: {
    openTime: {
      type: String,
      required: false,
    },
    closeTime: {
      type: String,
      required: false,
    }
  },
  aartiTimings: {
    type: String,
    required: false,
  },
  configurableField: {
    name: {
      type: String,
      required: false
    },
    description: {
      type: String,
      required: false
    }
  },
  deletedAt: {
    type: Date,
    default: null
  },
  payoutConfiguration: [ payoutComponentSchema ],
  templeCode: {
    type: String,
  },
  lastServiceCode: {
    type: String,
  },
  lastKitCode: {
    type: String,
  }
}, { timestamps: true });

const Temple = mongoose.model('Temple', templeSchema);

module.exports = Temple;
