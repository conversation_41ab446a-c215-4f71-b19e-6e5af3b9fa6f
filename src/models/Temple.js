const mongoose = require('mongoose');

// Define the payout component schema
const payoutComponentSchema = new mongoose.Schema({
  name: JSO<PERSON>,
  amount: {
    type: Number,
    required: true,
    min: 0
  }
}, { _id: true });

const templeSchema = new mongoose.Schema({
  name: JSO<PERSON>,
  promotionalKitCost: {
    type: Number, 
    min: 0,
    default: null,
    required: false
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TempleCategory',
    required: false
  },
  tagLine: JSON,
  description: JSON,
  history: JSON,
  significanceAndArchitecture: JSON,
  state: JSON,
  city: JSON,
  locationUrl: {
    type: String,
    validate: {
      validator: function (v) {
        return !v || /^https:\/\/www\.google\.com\/maps/.test(v);
      },
      message: props => `${props.value} is not a valid Google Maps URL`
    }
  },
  deity: JSON,
  images: [{
    type: String,
    required: true,
    trim: true,
  }],
  guidelines: JSON,
  tags: [{
    type: String,
    trim: true
  }],
  showOnHomepage: {
    type: Boolean,
    default: false,
    index: true
  },
  services: [{
    type: String,
    enum: [ 'PHYSICAL_POOJA', 'PHYSICAL_DARSHAN', 'VIRTUAL_POOJA' ]
  }],
  operatingHours: {
    openTime: {
      type: String,
      required: false,
    },
    closeTime: {
      type: String,
      required: false,
    }
  },
  aartiTimings: {
    type: String,
    required: false,
  },
  configurableField: {
    name: {
      type: String,
      required: false
    },
    description: {
      type: String,
      required: false
    }
  },
  deletedAt: {
    type: Date,
    default: null
  },
  payoutConfiguration: [ payoutComponentSchema ]
}, { timestamps: true });

const Temple = mongoose.model('Temple', templeSchema);

module.exports = Temple;
