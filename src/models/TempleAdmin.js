const mongoose = require('mongoose');
const { userTypeValue, userStatusValue } = require('../constants/dbEnums');

const templeAdminSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  phoneNumber: {
    type: String,
    required: false,
    sparse: true,
    trim: true
  },
  whatsappEnabled: {
    type: Boolean,
    default: false
  },
  password: {
    type: String,
    required: true
  },
  type: {
    type: String,
    default: userTypeValue.TEMPLE_ADMIN
  },
  temple: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Temple',
    required: true
  },
  status: {
    type: String,
    enum: Object.values(userStatusValue),
    default: userStatusValue.ACTIVE
  },
  passwordChangeRequired: {
    type: Boolean,
    default: true
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for full name
templeAdminSchema.virtual('fullName').get(function () {
  return `${this.firstName} ${this.lastName}`;
});

// Add indexes
templeAdminSchema.index({ email: 1 }, { unique: true });
templeAdminSchema.index({ phoneNumber: 1 }, { unique: true, sparse: true });
// Remove unique constraint from temple index
templeAdminSchema.index({ temple: 1 });

const TempleAdmin = mongoose.model('TempleAdmin', templeAdminSchema);

module.exports = TempleAdmin;
