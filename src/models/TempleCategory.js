const mongoose = require('mongoose');

const templeCategorySchema = new mongoose.Schema({
  name: {
    type: String, 
    trim: true, 
    required: true, 
    unique: true
  },
  image: {
    type: String,
    trim: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  }
}, { timestamps: true });

const TempleCategory = mongoose.model('TempleCategory', templeCategorySchema);

module.exports = TempleCategory;