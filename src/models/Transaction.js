const mongoose = require('mongoose');
const { transactionTypeValue, transactionStatusValue } = require('../constants/dbEnums');

const transactionSchema = new mongoose.Schema({
  transactionId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: true
  },
  payment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Payment',
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    required: true,
    default: 'INR',
    enum: [ 'INR' ]
  },
  type: {
    type: String,
    required: true,
    enum: Object.values(transactionTypeValue)
  },
  status: {
    type: String,
    required: true,
    enum: Object.values(transactionStatusValue),
    default: transactionStatusValue.PENDING
  },
  gatewayResponse: {
    type: Object
  },
  notes: {
    type: String,
    trim: true
  }
}, { timestamps: true });

// Add indexes for better query performance
transactionSchema.index({ transactionId: 1 });
transactionSchema.index({ order: 1 });
transactionSchema.index({ payment: 1 });
transactionSchema.index({ user: 1 });
transactionSchema.index({ type: 1 });
transactionSchema.index({ status: 1 });
transactionSchema.index({ createdAt: -1 });

const Transaction = mongoose.model('Transaction', transactionSchema);

module.exports = Transaction;
