const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  firstName: JSON,
  lastName: JSON,
  phoneNumber: {
    type: String,
    required: true,
    trim: true,
  },
  countryCode: {
    type: String,
    required: true,
    trim: true,
  },
  whatsappNumbers: {
    type: [{
      countryCode: {
        type: String,
        required: true,
        trim: true,
      },
      phoneNumber: {
        type: String,
        required: true,
        trim: true,
      },
      booking: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Booking',
        required: true
      },
      isPrimaryWhatsappNumber: {
        type: Boolean,
        default: false
      }
    }],
    default: [],
  },
  email: {
    type: String,
    required: false,
    default: null,
    trim: true,
    lowercase: true,
  },
  ageGroup: {
    type: String,
    enum: [ 'Under 18', '18-35', '36-55', '55+', null ],
    default: null
  },
  gender: {
    type: String,
    enum: [ 'Male', 'Female', null ],
    default: null
  },
  // locationPermission: {
  //   type: Number,
  //   enum: [ 1, 2, 3, 4 ], // Will be finalized based on (allow once , never ask again , deny , allow always)
  //   required: true
  // },
  latitude: {
    type: Number,
    default: null
  },
  longitude: {
    type: Number,
    default: null
  },
  preferredLanguage: {
    type: String,
    enum: [ 'English', 'Hindi', null ],
    default: null
  },
  isGuestUser: {
    type: Boolean,
    default: false
  },
  isThreeDaysReminderSent: {
    type: Boolean,
    default: false
  },
  isRegistrationReminderSent: {
    type: Boolean,
    default: false
  },
  deletedAt: {
    type: Date,
    default: null
  }
}, { timestamps: true });

userSchema.virtual('userAddresses', {
  ref: 'Address',
  localField: '_id',
  foreignField: 'userId', // field in Address model that stores the user reference
});

userSchema.set('toObject', { virtuals: true });
userSchema.set('toJSON', { virtuals: true });

const User = mongoose.model('User', userSchema);

module.exports = User;
