const mongoose = require('mongoose');

const userDiscountSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  discount: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Discount',
    required: true
  },
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    default: null
  },
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    default: null
  }
}, { timestamps: true });

// Compound index to efficiently query user's discount usage
userDiscountSchema.index({ user: 1, discount: 1 });
userDiscountSchema.index({ booking: 1 });
userDiscountSchema.index({ order: 1 });

const UserDiscount = mongoose.model('UserDiscount', userDiscountSchema);

module.exports = UserDiscount;