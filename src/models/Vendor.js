const mongoose = require('mongoose');
const { userTypeValue, userStatusValue } = require('../constants/dbEnums');

const vendorSchema = new mongoose.Schema({
  // Business Information
  name: {
    type: String,
    trim: true
  },
  businessName: {
    type: String,
    required: true,
    trim: true
  },
  address: {
    fullAddress: {
      type: String,
      required: true,
      trim: true
    },
    pincode: {
      type: String,
      required: true,
      trim: true
    }
  },

  // Contact Information
  contactPersonName: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [ /^\S+@\S+\.\S+$/, 'Please use a valid email address' ]
  },
  phoneNumber: {
    type: String,
    required: true,
    trim: true
  },

  // Tax Information & Documents
  gstNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  gstCertificate: {
    type: String,
    required: true,
    trim: true
  },
  panNumber: {
    type: String,
    trim: true,
    uppercase: true
  },
  panCard: {
    type: String,
    trim: true
  },
  otherDocuments: {
    type: [{
      name: {
        type: String,
        required: true,
        trim: true
      },
      image: {
        type: String,
        required: true,
        trim: true
      }
    }],
    default: []
  },

  // Bank Details
  bankDetails: {
    accountHolderName: {
      type: String,
      required: true,
      trim: true
    },
    accountNumber: {
      type: String,
      required: true,
      trim: true
    },
    ifscCode: {
      type: String,
      required: true,
      trim: true,
      uppercase: true
    },
    bankName: {
      type: String,
      required: true,
      trim: true
    },
    branchName: {
      type: String,
      required: true,
      trim: true
    }
  },

  // Account Credentials & Status
  password: {
    type: String,
    required: true
  },
  userType: {
    type: String,
    enum: [ userTypeValue.VENDOR ],
    default: userTypeValue.VENDOR
  },
  status: {
    type: String,
    enum: Object.values(userStatusValue),
    default: userStatusValue.ACTIVE
  },
  passwordChangeRequired: {
    type: Boolean,
    default: true
  },
  shipRocketPickupLocationName: {
    type: String,
    trim: true,
    default: 'AdminLocation'
  },

  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  }
}, { timestamps: true });

// Add indexes
vendorSchema.index({ email: 1 }, { unique: true });
vendorSchema.index({ gstNumber: 1 }, { unique: true });
vendorSchema.index({ panNumber: 1 }, { unique: true, sparse: true });
vendorSchema.index({ status: 1 });

const Vendor = mongoose.model('Vendor', vendorSchema);

module.exports = Vendor;
