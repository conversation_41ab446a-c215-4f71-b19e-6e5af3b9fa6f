const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { getAuditLogsSchema } = require('./validation');
const auditLogService = require('./service');
const converter = require('json-2-csv');
const moment = require('moment');

//* 1. Function to get audit logs
const getAuditLogs = async (req, res) => {
  try {
    const { error } = getAuditLogsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await auditLogService.getAuditLogs(req.query);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Audit logs retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 2. Function to export audit logs
const exportAuditLogs = async (req, res) => {
  try {
    const { error } = getAuditLogsSchema.validate(req.query);
    
    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }
    
    const { logs } = await auditLogService.getAuditLogs(req.query); // Get the filtered logs

    const data = logs.map(log => ({
      'Date & Time': moment(log.createdAt).format('MMM DD, YYYY HH:mm:ss'),
      User: log.admin.email,
      Action: log.action,
      Details: log.detail,
      'IP Address': log.ipAddress
    }));
    
    // Convert logs to CSV
    const csv = await converter.json2csv(data);
  
    // Convert CSV to base64
    const base64Csv = Buffer.from(csv).toString('base64');
        
    // Send the base64-encoded CSV data in the response
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Audit logs exported successfully',
      status: true,
      data: base64Csv
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getAuditLogs,
  exportAuditLogs
};