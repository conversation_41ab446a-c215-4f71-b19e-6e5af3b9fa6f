const express = require('express');
const router = express.Router();
const auditLogController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

router.get('/', auth, isAdminOrSuperAdmin, auditLogController.getAuditLogs);
router.get('/export', auth, isAdminOrSuperAdmin, auditLogController.exportAuditLogs);

module.exports = router;