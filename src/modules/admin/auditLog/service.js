const AuditLog = require('../../../models/AuditLog');
const moment = require('moment');

const getAuditLogs = async (query) => {
  const { page = 1, limit = 10, startDate, endDate, sortBy = 'createdAt', sortOrder = -1, search, admin, model, ipAddress, action } = query;

  const skip = (parseInt(page) - 1) * parseInt(limit);
  const sort = {};

  sort[sortBy] = parseInt(sortOrder);

  const filter = {};

  if (search) {
    filter.$or = [
      { detail: { $regex: search, $options: 'i' } },
      { action: { $regex: search, $options: 'i' } },
      { model: { $regex: search, $options: 'i' } },
      { ipAddress: { $regex: search, $options: 'i' } }
    ];
  }

  if (admin) {
    filter.admin = admin;
  }

  if (model) {
    filter.model = model;
  }

  if (ipAddress) {
    filter.ipAddress = ipAddress;
  }

  if (action) {
    filter.action = action;
  }

  if (startDate) {
    filter.createdAt = { $gte: new Date(startDate) };
  }

  if (endDate) {
    filter.createdAt = { $lte: new Date(new Date(endDate).setUTCHours(23, 59, 59, 999)) };
  }

  let logs = await AuditLog.find(filter)
    .populate('admin', 'name email')
    .sort(sort).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit));

  logs = logs.map(log => ({
    ...log.toObject(),
    date: moment(log.createdAt).format('MMM DD, YYYY'),
    time: moment(log.createdAt).format('HH:mm:ss')
  }));

  const total = await AuditLog.countDocuments(filter);

  return {
    logs,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

module.exports = {
  getAuditLogs
};