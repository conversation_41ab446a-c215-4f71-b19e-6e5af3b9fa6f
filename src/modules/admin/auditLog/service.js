const AuditLog = require('../../../models/AuditLog');
const moment = require('moment');

const getAuditLogs = async (query) => {
  const { page, limit, startDate, endDate, sortBy = 'createdAt', sortOrder = -1, search, admin, model, ipAddress, action } = query;

  const skip = (parseInt(page) - 1) * parseInt(limit);
  const sort = {};

  sort[sortBy] = parseInt(sortOrder);

  const filter = {};

  if (search) {
    filter.$or = [
      { detail: { $regex: search, $options: 'i' } },
      { action: { $regex: search, $options: 'i' } },
      { model: { $regex: search, $options: 'i' } },
      { ipAddress: { $regex: search, $options: 'i' } }
    ];
  }

  if (admin) {
    filter.admin = admin;
  }

  if (model) {
    filter.model = model;
  }

  if (ipAddress) {
    filter.ipAddress = ipAddress;
  }

  if (action) {
    filter.action = action;
  }

  if (startDate || endDate) {
    filter.createdAt = {};

    if (startDate) {
      const start = moment.utc(startDate).startOf('day'); // 00:00:00 UTC

      filter.createdAt.$gte = start.toDate();
    }

    if (endDate) {
      const end = moment.utc(endDate).endOf('day'); // 23:59:59.999 UTC

      filter.createdAt.$lte = end.toDate();
    }
  }

  let logs = await AuditLog.find(filter)
    .populate('admin', 'name email')
    .sort(sort).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit));

  logs = logs.map(log => ({
    ...log.toObject(),
    date: moment(log.createdAt).format('MMM DD, YYYY'),
    time: moment(log.createdAt).format('HH:mm:ss')
  }));

  const total = await AuditLog.countDocuments(filter);

  return {
    logs,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

module.exports = {
  getAuditLogs
};