const joi = require('joi');
const { auditLogAction } = require('../../../constants/dbEnums');
const mongoose = require('mongoose');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const getAuditLogsSchema = joi.object({
  page: joi.number().integer().min(1).default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
  limit: joi.number().integer().min(1).max(100).default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
  search: joi.string().allow('').optional()
    .messages({
      'string.base': 'Search query must be a string'
    }),
  admin: joi.string().custom(validateObjectId).allow('').optional()
    .messages({
      'any.invalid': 'Invalid admin ID format'
    }),
  model: joi.string().allow('').optional()
    .messages({
      'string.base': 'Model must be a string'
    }),
  ipAddress: joi.string().allow('').optional()
    .messages({
      'string.base': 'IP address must be a string'
    }),
  action: joi.string().valid(...Object.values(auditLogAction)).allow('').optional()
    .messages({
      'any.only': `Action must be one of: ${Object.values(auditLogAction).join(', ')}`
    }),
  startDate: joi.date().iso().allow('').optional()
    .messages({
      'date.base': 'Start date must be a valid date',
      'date.format': 'Start date must be in ISO format (YYYY-MM-DD)'
    }),
  endDate: joi.date().iso().allow('').optional()
    .when('startDate', {
      is: joi.date().required(),
      then: joi.date().min(joi.ref('startDate')),
      otherwise: joi.optional()
    })
    .messages({
      'date.base': 'End date must be a valid date',
      'date.format': 'End date must be in ISO format (YYYY-MM-DD)',
      'date.min': 'End date must be greater than or equal to start date',
      'any.required': 'End date is required when start date is provided'
    }),
  sortBy: joi.string().valid('createdAt', 'action', 'detail', 'ipAddress', 'model').default('createdAt')
    .messages({ 
      'any.only': 'Sort by field must be one of: createdAt, action, detail, ipAddress, model'
    }),
  sortOrder: joi.number().valid(1, -1).default(-1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    }),
});

module.exports = {
  getAuditLogsSchema
};