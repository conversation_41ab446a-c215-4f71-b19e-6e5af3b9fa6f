const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const authService = require('./service');
const { initiateLoginSchema, verifyOtpSchema } = require('./validation');
const { SUCCESS } = commonConstants;

//* Function to initiate login
const initiateLogin = async (req, res) => {
  try {
    const { value, error } = initiateLoginSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await authService.initiateLogin(value);

    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* Function to verify OTP and complete login
const verifyLoginOtp = async (req, res) => {
  try {
    const { value, error } = verifyOtpSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await authService.verifyLoginOtp(value);

    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  initiateLogin,
  verifyLoginOtp
};
