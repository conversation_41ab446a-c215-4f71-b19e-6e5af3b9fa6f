const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const moment = require('moment');
const Admin = require('../../../models/Admin');
const OneTimePassword = require('../../../models/OneTimePassword');
const Token = require('../../../models/Token');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const { userTypeValue, otpTypeValue } = require('../../../constants/dbEnums');
const { sendMail } = require('../../../utils/sendMail');
const path = require('path');
const { emailSubjects } = require('../../../messages/emailSubjects');

//* Function for initial login step
const initiateLogin = async ({ email, password }) => {
  const admin = await Admin.findOne({ email }).lean();

  if (!admin) {
    throwBadRequestError(messages.INVALID_CREDENTIALS);
  }

  const isMatch = await bcrypt.compare(password, admin.password);

  if (!isMatch) {
    throwBadRequestError(messages.INVALID_CREDENTIALS);
  }

  // Generate 6 digit OTP
  const otp = String(Math.floor(100000 + Math.random() * 900000));
  const expiredAt = moment().add(5, 'minutes').toDate();

  // Delete any existing OTP records
  await OneTimePassword.deleteMany({
    email: admin.email,
    type: otpTypeValue.EMAIL
  });

  // Create new OTP record
  await OneTimePassword.create({
    otp: otp,
    email: admin.email,
    type: otpTypeValue.EMAIL,
    expiresAt: expiredAt,
    userType: userTypeValue.SUPER_ADMIN,
  });

  // Send OTP email
  const subject = emailSubjects.ADMIN_LOGIN_OTP;
  const templatePath = path.join(__dirname, '../../../views/signUp.html');
  const data = {
    name: admin.name || '',
    code: otp
  };
  
  await sendMail(email, subject, templatePath, data);

  return {
    message: 'OTP sent successfully',
    email: admin.email
  };
};

//* Function to verify OTP and complete login
const verifyLoginOtp = async ({ email, otp }) => {
  const admin = await Admin.findOne({ email }).lean();
  
  if (!admin) {
    throwBadRequestError(messages.INVALID_CREDENTIALS);
  }

  const otpRecord = await OneTimePassword.findOne({
    email,
    type: otpTypeValue.EMAIL
  });

  if (!otpRecord) {
    throwBadRequestError(messages.INVALID_OTP);
  }

  if (new Date(otpRecord.expiresAt) < new Date()) {
    await otpRecord.deleteOne();
    throwBadRequestError(messages.OTP_EXPIRED);
  }

  if (process.env.APP_ENV && process.env.APP_ENV === 'development') {
    if (String(otp) !== '797979' && String(otpRecord.otp) !== String(otp)) {
      throwBadRequestError(messages.INVALID_OTP);
    }
  } else {
    if (String(otpRecord.otp) !== String(otp)) {
      throwBadRequestError(messages.INVALID_OTP);
    }
  }

  // Delete OTP record after successful verification
  await otpRecord.deleteOne();

  // Remove password from response
  delete admin.password;

  const token = generateToken(admin._id, userTypeValue.SUPER_ADMIN);

  const expireTime = process.env.ADMIN_TOKEN_EXPIRE_TIME;
  const days = expireTime.toLowerCase().replace('d', '');
  const expiresAt = new Date(Date.now() + (parseInt(days) * 24 * 60 * 60 * 1000));
  
  // Save token in the token table
  await Token.create({
    userId: admin._id,
    token,
    userType: userTypeValue.SUPER_ADMIN,
    expiresAt: expiresAt
  });

  return {
    data: admin,
    token
  };
};

//* 1. Function to add super admin data
const addSuperAdminData = async () => {
  const superAdminUser = {
    name: 'Super Admin',
    email: process.env.SUPER_ADMIN_EMAIL,
    userType: userTypeValue.SUPER_ADMIN,
    password: process.env.SUPER_ADMIN_PASSWORD,
  };

  const isAdminExist = await Admin.findOne({
    $and: [
      { userType: userTypeValue.SUPER_ADMIN },
      {
        $or: [
          { email: superAdminUser.email },
          { phoneNumber: superAdminUser.phoneNumber }
        ]
      }
    ]
  });

  if (!isAdminExist) {
    // Hash password before creating user
    const hashedPassword = await bcrypt.hash(superAdminUser.password, 10);

    await Admin.create({
      ...superAdminUser,
      password: hashedPassword
    });
  }
};

//* Function to generate token 
function generateToken (userId, userType) {
  return jwt.sign({ _id: userId, userType }, process.env.JWT_SECRET, {
    expiresIn: process.env.ADMIN_TOKEN_EXPIRE_TIME
  });
}

module.exports = {
  initiateLogin,
  verifyLoginOtp,
  addSuperAdminData
};
