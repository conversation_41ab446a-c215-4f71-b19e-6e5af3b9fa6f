const express = require('express');
const router = express.Router();
const bannerController = require('./controller');
const auth = require('../../../middleware/auth');
const { isSuperAdmin } = require('../../../middleware/roleCheck');

router.post('/', auth, isSuperAdmin, bannerController.createBanner);

router.put('/:id', auth, isSuperAdmin, bannerController.updateBanner);

router.get('/', auth, isSuperAdmin, bannerController.listBanners);

router.get('/:id', auth, isSuperAdmin, bannerController.getBannerById);

router.delete('/:id', auth, isSuperAdmin, bannerController.deleteBanner);

router.post('/upload-url', auth, isSuperAdmin, bannerController.getUploadUrl);

module.exports = router;
