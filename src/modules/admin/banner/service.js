const Banner = require('../../../models/Banner');
const { throwBadRequestError } = require('../../../errors');
const { deleteFile } = require('../../../utils/s3Service');
const { translateDataForStore } = require('../../../utils/translateInput');

const createBanner = async (bannerData, userId) => {
  // Convert S3 URL to CloudFront URL
  const cloudFrontUrl = `${process.env.MEDIA_URL}/${bannerData.image}`;

  // Translate fields
  const translatedFields = ['name', 'promotionalText'];
  const translatedData = await translateDataForStore(translatedFields, bannerData);

  translatedFields.forEach(field => {
    if (bannerData[field]) {
      delete bannerData[field];
    }
  });

  // Create banner
  const banner = await Banner.create({
    ...bannerData,
    ...translatedData,
    image: cloudFrontUrl,
    createdBy: userId
  });

  return banner;
};

const updateBanner = async (bannerId, updateData, userId) => {
  const banner = await Banner.findById(bannerId);

  if (!banner) {
    throwBadRequestError('Banner not found');
  }

  // Translate fields if provided
  const translatedFields = ['name', 'promotionalText'];
  const hasTranslatableFields = translatedFields.some(field => updateData[field]);

  if (hasTranslatableFields) {
    const translatedData = await translateDataForStore(translatedFields, updateData);

    translatedFields.forEach(field => {
      if (updateData[field]) {
        delete updateData[field];
      }
    });

    updateData = {
      ...updateData,
      ...translatedData
    };
  }

  if (updateData.image) {

    // Convert S3 URL to CloudFront URL
    const cloudFrontUrl = `${process.env.MEDIA_URL}/${updateData.image}`;

    updateData.image = cloudFrontUrl;
  }

  Object.assign(banner, updateData);
  banner.updatedBy = userId;
  await banner.save();

  return banner;
};

const listBanners = async (query) => {
  const { page = 1, limit = 10, status, search } = query;
  
  const filter = {};
  
  if (status) {
    filter.status = status;
  }
  
  if (search) {
    filter.$or = [
      { name: { $regex: search, $options: 'i' } },
      { promotionalText: { $regex: search, $options: 'i' } }
    ];
  }

  const banners = await Banner.find(filter)
    .sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 })
    .skip((page - 1) * limit)
    .limit(limit)
    .populate('createdBy', 'name email')
    .populate('updatedBy', 'name email');

  const total = await Banner.countDocuments(filter);

  return {
    banners,
    pagination: {
      total,
      page: parseInt(page),
      pages: Math.ceil(total / limit)
    }
  };
};

const deleteBanner = async (bannerId) => {
  const banner = await Banner.findByIdAndDelete(bannerId);
  
  if (!banner) {
    throwBadRequestError('Banner not found');
  }

  // Delete image from S3
  // Implement S3 deletion logic here

  if (banner.image) {
    try {
      // The posterImage URL format is: MEDIA_URL/key
      // We need to extract the key part
      const mediaUrl = process.env.MEDIA_URL;

      if (banner.image.startsWith(mediaUrl)) {
        const key = banner.image.substring(mediaUrl.length + 1); // +1 for the '/' character

        // Delete the image file from storage
        await deleteFile(key);
      }
    } catch (error) {
      // Log error but continue with event deletion even if image deletion fails
      // eslint-disable-next-line no-console
      console.error('Error deleting event poster image:', error);
    }
  }

  return banner;
};

const getActiveBanners = async () => {
  const now = new Date();
  
  return Banner.find({
    isActive: true,
    status: 'ACTIVE',
    startDate: { $lte: now },
    endDate: { $gte: now }
  }).sort({ startDate: 1 }).collation({ locale: 'en', strength: 1 });
};

const getBannerById = async (id) => {
  const banner = await Banner.findById(id);
  
  if (!banner) {
    throwBadRequestError('Banner not found');
  }

  return banner;
};

module.exports = {
  createBanner,
  updateBanner,
  listBanners,
  deleteBanner,
  getActiveBanners,
  getBannerById
};
