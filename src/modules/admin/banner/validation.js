const joi = require('joi');
const mongoose = require('mongoose');

const createBannerSchema = joi.object({
  name: joi.string().max(100).required(),
  image: joi.string().max(100).required(),
  promotionalText: joi.string().max(200).allow(''),
  destinationUrl: joi.string().uri().required(),
  startDate: joi.date().iso().required(),
  endDate: joi.date().iso().min(joi.ref('startDate')).required(),
  isActive: joi.boolean().default(false)
});

const updateBannerSchema = joi.object({
  name: joi.string().max(100),
  image: joi.string().max(100).required(),
  promotionalText: joi.string().max(200).allow(''),
  destinationUrl: joi.string().uri(),
  startDate: joi.date().iso(),
  endDate: joi.date().iso().min(joi.ref('startDate')),
  isActive: joi.boolean()
}).min(1);

const uploadUrlSchema = joi.object({
  extension: joi.string()
    .valid('jpeg', 'jpg', 'png')
    .required()
    .messages({
      'any.required': 'File extension is required',
      'any.only': 'Invalid file extension. Only JPEG, JPG and PNG are allowed'
    })
});

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string().custom(validateObjectId).required().messages({
  'any.invalid': 'Invalid ID format',
  'any.required': 'ID is required'
});

module.exports = {
  createBannerSchema,
  updateBannerSchema,
  uploadUrlSchema,
  validateId
};