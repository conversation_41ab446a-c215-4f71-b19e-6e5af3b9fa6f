const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const approvalCenterService = require('./service');
const { SUCCESS } = commonConstants;

//* 1. Function to get centralized approval center counts
const getApprovalCenterCounts = async (req, res) => {
  try {
    const data = await approvalCenterService.getApprovalCenterCounts();

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.COUNT_RETRIEVED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getApprovalCenterCounts
};