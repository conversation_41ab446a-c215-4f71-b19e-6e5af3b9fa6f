const LiveDarshan = require('../../../models/LiveDarshan');
const Pujari = require('../../../models/Pujari');
const PoojaRecording = require('../../../models/PoojaRecording');
const Product = require('../../../models/Product');
const StockUpdateRequest = require('../../../models/StockUpdateRequest');
const { darshanStatus, productStatusValue, stockUpdateStatusValue } = require('../../../constants/dbEnums');

//* Helper function to count status values
const getStatusCounts = (data, statusField, statuses) => {
  const counts = {
    total: data.length,
    pending: 0,
    approved: 0,
    rejected: 0
  };
  
  for (const item of data) {
    const status = item[statusField];
  
    if (status === statuses.PENDING) {
      counts.pending++;
    } else if (status === statuses.APPROVED) {
      counts.approved++;
    } else if (status === statuses.REJECTED) {
      counts.rejected++;
    }
  }
  
  return counts;
};

//* 1. Function to get centralized approval center counts
const getApprovalCenterCounts = async () => {
  const [ liveDarshan, pujari, pujaRecording, product, stockUpdate ] = await Promise.all([
    LiveDarshan.find().select('status').lean(),
    Pujari.find({ approvalStatus: { $exists: true } }).select('approvalStatus').lean(),
    PoojaRecording.find().select('status').lean(),
    Product.find({ createdByModel: 'Vendor', deletedAt: null }).select('status').lean(),
    StockUpdateRequest.find().select('status').lean()
  ]);
  
  const liveDarshanRequest = getStatusCounts(liveDarshan, 'status', darshanStatus);
  const pujariApprovalRequest = getStatusCounts(pujari, 'approvalStatus', {
    PENDING: 'PENDING_APPROVAL',
    APPROVED: 'APPROVED',
    REJECTED: 'REJECTED'
  });
  const virtualPujaVideoRequest = getStatusCounts(pujaRecording, 'status', {
    PENDING: 'PENDING',
    APPROVED: 'APPROVED',
    REJECTED: 'REJECTED'
  });
  const productApprovalRequest = getStatusCounts(product, 'status', {
    PENDING: productStatusValue.PENDING,
    APPROVED: productStatusValue.ACTIVE,
    REJECTED: productStatusValue.REJECTED
  });  
  const stockUpdateRequest = getStatusCounts(stockUpdate, 'status', stockUpdateStatusValue);

  return {
    liveDarshanRequest,
    pujariApprovalRequest,
    virtualPujaVideoRequest,
    productApprovalRequest,
    stockUpdateRequest
  };
};

module.exports = {
  getApprovalCenterCounts
};