const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const darshanService = require('./service');
const { createDarshanScheduleSchema, updateDarshanScheduleSchema } = require('./validation');
const { SUCCESS } = commonConstants;
const { saveAuditLog } = require('../../../utils/auditLogger');
const { auditLogAction } = require('../../../constants/dbEnums');

const createDarshanSchedule = async (req, res) => {
  try {
    const { error } = createDarshanScheduleSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await darshanService.createDarshanSchedule(req.body, req.user.id);

    //* Save audit log 
    const detail = `Darshan schedule ${data.name.en} created successfully`;
    const model = 'DarshanSchedule';

    await saveAuditLog(req, req.user.id, auditLogAction.DARSHAN_SCHEDULE_CREATED, detail, model);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: 'Darshan schedule created successfully', 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateDarshanSchedule = async (req, res) => {
  try {
    const { error } = updateDarshanScheduleSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await darshanService.updateDarshanSchedule(
      req.params.id,
      req.body,
      req.user.id
    );

    //* Save audit log 
    const detail = `Darshan schedule ${data.name.en} updated successfully`;
    const model = 'DarshanSchedule';

    await saveAuditLog(req, req.user.id, auditLogAction.DARSHAN_SCHEDULE_UPDATED, detail, model);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: 'Darshan schedule updated successfully', 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const deleteDarshanSchedule = async (req, res) => {
  try {
    const schedule = await darshanService.deleteDarshanSchedule(req.params.id);

    //* Save audit log 
    const detail = `Darshan schedule ${schedule.name.en} deleted successfully`;
    const model = 'DarshanSchedule';

    await saveAuditLog(req, req.user.id, auditLogAction.DARSHAN_SCHEDULE_DELETED, detail, model);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: 'Darshan schedule deleted successfully', 
      status: true,
      data: schedule
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const listDarshanSchedules = async (req, res) => {
  try {
    const { schedules, pagination } = await darshanService.listDarshanSchedules(req.query);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: 'Darshan schedules retrieved successfully', 
      status: true, 
      data: { schedules, pagination }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getDarshanScheduleById = async (req, res) => {
  try {
    const data = await darshanService.getDarshanScheduleById(req.params.id);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: 'Darshan schedule retrieved successfully', 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createDarshanSchedule,
  updateDarshanSchedule,
  deleteDarshanSchedule,
  listDarshanSchedules,
  getDarshanScheduleById
};
