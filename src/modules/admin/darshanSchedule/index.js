const express = require('express');
const router = express.Router();
const darshanController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

router.get('/', auth, isAdminOrSuperAdmin, darshanController.listDarshanSchedules);
router.get('/:id', auth, isAdminOrSuperAdmin, darshanController.getDarshanScheduleById);
router.post('/', auth, isAdminOrSuperAdmin, darshanController.createDarshanSchedule);
router.put('/:id', auth, isAdminOrSuperAdmin, darshanController.updateDarshanSchedule);
router.delete('/:id', auth, isAdminOrSuperAdmin, darshanController.deleteDarshanSchedule);

module.exports = router;