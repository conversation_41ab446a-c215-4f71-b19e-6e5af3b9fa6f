const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const dashboardService = require('./service');
const { SUCCESS } = commonConstants;

//* 1. Function to get dashboard data 
const getDashboardData = async (req, res) => {
  try {
    const data = await dashboardService.getDashboardData();
  
    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.DASHBOARD_DATA_RETRIEVED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getDashboardData
};