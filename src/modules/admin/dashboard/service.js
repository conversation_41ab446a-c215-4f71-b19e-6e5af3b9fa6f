const Temple = require('../../../models/Temple');
const User = require('../../../models/User');
const Event = require('../../../models/Event');
const moment = require('moment');

//* 1. Function to get dashboard data 
const getDashboardData = async () => {
  //* Get start and end of current month in UTC
  const startOfMonth = moment().startOf('month').toDate(); // 1st day of current month at 00:00
  const endOfMonth = moment().endOf('month').toDate(); // Last day of month at 23:59:59.999

  const [ totalTemples, totalUsers, totalEvents ] = await Promise.all([
    Temple.countDocuments({ deletedAt: null }),
    User.countDocuments({ deletedAt: null }),
    Event.countDocuments({ deletedAt: null })
  ]);

  const [ monthTemples, monthUsers, monthEvents ] = await Promise.all([
    Temple.countDocuments({ createdAt: { $gte: startOfMonth, $lte: endOfMonth }, deletedAt: null }),
    User.countDocuments({ createdAt: { $gte: startOfMonth, $lte: endOfMonth }, deletedAt: null }),
    Event.countDocuments({ createdAt: { $gte: startOfMonth, $lte: endOfMonth }, deletedAt: null })
  ]);

  return {
    temples: {
      total: totalTemples,
      currentMonth: monthTemples
    },
    users: {
      total: totalUsers,
      currentmonth: monthUsers
    },
    events: {
      total: totalEvents,
      currentMonth: monthEvents
    }
  };
};

module.exports = {
  getDashboardData
};