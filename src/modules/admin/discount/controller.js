const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const discountService = require('./service');
const { createDiscountSchema, listDiscountsSchema, validateId, updateDiscountSchema, getSchedulesWithoutDiscountSchema, getProductsWithoutDiscountSchema } = require('./validation');
const { SUCCESS } = commonConstants;

//* 1. Function to create a new discount code
const createDiscount = async (req, res) => {
  try {
    const { error } = createDiscountSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    
    const data = await discountService.createDiscount(adminId, req.body);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.DISCOUNT_CREATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 2. Function to get all discounts with pagination and filters
const listAllDiscounts = async (req, res) => {
  try {
    const { error } = listDiscountsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await discountService.listAllDiscounts(req.query);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.DISCOUNTS_RETRIEVED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 3. Function to get discount by id
const getDiscountById = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await discountService.getDiscountById(req.params.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.DISCOUNT_RETRIEVED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 4. Function to update discount
const updateDiscount = async (req, res) => {
  try {
    const { error: idError } = validateId.validate(req.params.id);

    if (idError) {
      return res.status(400).json({
        message: idError.details[0].message,
        status: false
      });
    }

    const { error } = updateDiscountSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    
    const data = await discountService.updateDiscount(req.params.id, adminId, req.body);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.DISCOUNT_UPDATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 5. Function to delete discount
const deleteDiscount = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await discountService.deleteDiscount(req.params.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.DISCOUNT_DELETED,
      status: true,
      data
    }); 
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 6. Function to get unused pooja and darshan schedules
const getSchedulesWithoutDiscount = async (req, res) => {
  try {
    const temple = req.params.templeId;
    const { error } = getSchedulesWithoutDiscountSchema.validate({ temple });

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await discountService.getSchedulesWithoutDiscount(temple);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 7. Function to get unused products
const getProductsWithoutDiscount = async (req, res) => {
  try {
    const { error } = getProductsWithoutDiscountSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await discountService.getProductsWithoutDiscount(req.query);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createDiscount,
  listAllDiscounts,
  getDiscountById,
  updateDiscount,
  deleteDiscount,
  getSchedulesWithoutDiscount,
  getProductsWithoutDiscount
};
