const express = require('express');
const router = express.Router();
const discountController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

// Create a new discount code
router.post('/create', auth, isAdminOrSuperAdmin, discountController.createDiscount);
router.get('/list', auth, isAdminOrSuperAdmin, discountController.listAllDiscounts);
router.get('/unused-schedules/:templeId', auth, isAdminOrSuperAdmin, discountController.getSchedulesWithoutDiscount);
router.get('/unused-products', auth, isAdminOrSuperAdmin, discountController.getProductsWithoutDiscount);
router.get('/:id', auth, isAdminOrSuperAdmin, discountController.getDiscountById);
router.put('/:id', auth, isAdminOrSuperAdmin, discountController.updateDiscount);
router.delete('/:id', auth, isAdminOrSuperAdmin, discountController.deleteDiscount);

module.exports = router;