const mongoose = require('mongoose');
const Discount = require('../../../models/Discount');
const Temple = require('../../../models/Temple');
const { throwBadRequestError } = require('../../../errors');
const { discountStatus } = require('../../../constants/dbEnums');
const moment = require('moment');
const Product = require('../../../models/Product');
const PoojaSchedule = require('../../../models/PoojaSchedule');
const DarshanSchedule = require('../../../models/DarshanSchedule');

//* 1. Function to create a new discount code
const createDiscount = async (adminId, body) => {
  // 1. Check if discount code already exists
  const discountExists = await Discount.findOne({
    code: body.code.toUpperCase()
  });

  if (discountExists) {
    throwBadRequestError('Discount code already exists');
  }

  if (body.applicabilityType === 'SPECIFIC_TEMPLES') {
    const unusedSchedules = await getSchedulesWithoutDiscount(body.temple);
    const unusedPoojaSchedules = unusedSchedules.poojaSchedules;
    const unusedDarshanSchedules = unusedSchedules.darshanSchedules;

    if (Array.isArray(body.poojaSchedules) && body.poojaSchedules.length > 0) {
      body.poojaSchedules.forEach((id) => {
        const isUnused = unusedPoojaSchedules.some((schedule) => String(schedule._id) === String(id));

        if (!isUnused) {
          throwBadRequestError(`Puja schedule with ID ${id} already has an active discount`);
        }
      });
    }

    if (Array.isArray(body.darshanSchedules) && body.darshanSchedules.length > 0) {
      body.darshanSchedules.forEach((id) => {
        const isUnused = unusedDarshanSchedules.some((schedule) => String(schedule._id) === String(id));

        if (!isUnused) {
          throwBadRequestError(`Darshan schedule with ID ${id} already has an active discount`);
        }
      });
    }
  } 
  
  if (body.applicabilityType === 'SPECIFIC_PRODUCTS') {
    const usedProductIds = await getProductsWithActiveDiscounts();
    
    if (Array.isArray(body.products) && body.products.length > 0) {
      body.products.forEach((id) => {
        if (usedProductIds.some((productId) => String(productId) === String(id))) {
          throwBadRequestError(`Product with ID ${id} already has an active discount`);
        }
      });
    }
  }

  // 2. Validate temple if applicability type is SPECIFIC_TEMPLES
  if (body.applicabilityType === 'SPECIFIC_TEMPLES' && body.temple) {
    if (!mongoose.Types.ObjectId.isValid(body.temple)) {
      throwBadRequestError('Invalid temple ID');
    }
    
    const templeExists = await Temple.findById(body.temple);
    
    if (!templeExists) {
      throwBadRequestError(`Temple with ID ${body.temple} not found`);
    }

    if (Array.isArray(body.poojaSchedules) && body.poojaSchedules.length > 0) {
      for (const id of body.poojaSchedules) {
        if (!mongoose.Types.ObjectId.isValid(id)) {
          throwBadRequestError('Invalid puja schedule ID');
        }
      }
    }
       
    if (Array.isArray(body.darshanSchedules) && body.darshanSchedules.length > 0) {
      for (const id of body.darshanSchedules) {
        if (!mongoose.Types.ObjectId.isValid(id)) {
          throwBadRequestError('Invalid darshan schedule ID');
        }
      }
    }    
  }

  // 3. Validate products if applicability type is SPECIFIC_PRODUCTS
  if (body.applicabilityType === 'SPECIFIC_PRODUCTS' && body.products && body.products.length > 0) {
    for (const productId of body.products) {
      if (!mongoose.Types.ObjectId.isValid(productId)) {
        throwBadRequestError('Invalid product ID');
      }
    }
  }

  // 4. Create discount
  const discount = new Discount({
    code: body.code.toUpperCase(),
    applicabilityType: body.applicabilityType,
    maxDiscount: body.discountType === 'PERCENTAGE' ? body.maxDiscount : null,
    temple: body.applicabilityType === 'SPECIFIC_TEMPLES' ? body.temple : null,
    poojaSchedules: body.applicabilityType === 'SPECIFIC_TEMPLES' ? body.poojaSchedules : [],
    darshanSchedules: body.applicabilityType === 'SPECIFIC_TEMPLES' ? body.darshanSchedules : [],
    products: body.applicabilityType === 'SPECIFIC_PRODUCTS' ? body.products : [],
    discountType: body.discountType,
    discountValue: body.discountValue,
    startDate: body.startDate,
    endDate: body.endDate,
    startTime: body.startTime,
    endTime: body.endTime,
    minimumPurchaseAmount: body.minimumPurchaseAmount || 0,
    usageLimitPerUser: body.usageLimitPerUser,
    totalUsageLimit: body.totalUsageLimit,
    discountStatus: discountStatus.ACTIVE,
    createdBy: adminId
  });

  await discount.save();

  return discount;
};

//* 2. Function to get all discounts with pagination and filters
const listAllDiscounts = async (body) => {
  const { page = 1, limit = 10, search, status, applicabilityType, discountType, startDate, endDate, sortBy = 'createdAt', sortOrder = -1 } = body;

  const skip = (parseInt(page) - 1) * parseInt(limit);
  const query = {};
  const sortOptions = {};
  
  sortOptions[sortBy] = parseInt(sortOrder);
  
  // Add search filter if provided
  if (search) {
    query.code = { $regex: search, $options: 'i' };
  }
  
  // Add status filter if provided
  if (status) {
    query.discountStatus = status;
  }

  // Add applicability type filter if provided
  if (applicabilityType) {
    query.applicabilityType = applicabilityType;
  }

  // Add discount type filter if provided
  if (discountType) {
    query.discountType = discountType;
  }

  // Add date range filters if provided
  if (startDate) {
    query.startDate = { $gte: new Date(startDate) };
  }

  if (endDate) {
    query.endDate = { $lte: new Date(endDate) };
  }
  
  // Get discounts with pagination
  const discounts = await Discount.find(query)
    .populate('temple')
    .populate('poojaSchedules')
    .populate('darshanSchedules')
    .populate('products')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit));
  
  // Get total count for pagination
  const total = await Discount.countDocuments(query);
  
  return {
    discounts,
    pagination: {
      total,
      page: parseInt(page),
      pages: Math.ceil(total / parseInt(limit)),
      limit: parseInt(limit)
    }
  };
};

//* 3. Function to get discount by id
const getDiscountById = async (discountId) => {
  const discount = await Discount.findById(discountId)
    .populate('temple')
    .populate('poojaSchedules')
    .populate('darshanSchedules')
    .populate('products');

  if (!discount) {
    throwBadRequestError('Discount not found');
  }

  return discount;
};

//* 4. Function to update discount
const updateDiscount = async (discountId, adminId, body) => {
  // 1. Check if discount exists
  const discount = await Discount.findById(discountId);

  if (!discount) {
    throwBadRequestError('Discount not found');
  }

  if (body.applicabilityType === 'SPECIFIC_TEMPLES') {
    const unusedSchedules = await getSchedulesWithoutDiscount(body.temple);
    const unusedPoojaSchedules = unusedSchedules.poojaSchedules;
    const unusedDarshanSchedules = unusedSchedules.darshanSchedules;
  
    const currentPoojaScheduleIds = discount.poojaSchedules.map((id) => String(id));
    const currentDarshanScheduleIds = discount.darshanSchedules.map((id) => String(id));
  
    if (Array.isArray(body.poojaSchedules) && body.poojaSchedules.length > 0) {
      body.poojaSchedules.forEach((id) => {
        const isCurrent = currentPoojaScheduleIds.includes(String(id));
        const isUnused = unusedPoojaSchedules.some((schedule) => String(schedule._id) === String(id));
  
        if (!isUnused && !isCurrent) {
          throwBadRequestError(`Puja schedule with ID ${id} already has an active discount`);
        }
      });
    }
  
    if (Array.isArray(body.darshanSchedules) && body.darshanSchedules.length > 0) {
      body.darshanSchedules.forEach((id) => {
        const isCurrent = currentDarshanScheduleIds.includes(String(id));
        const isUnused = unusedDarshanSchedules.some((schedule) => String(schedule._id) === String(id));
  
        if (!isUnused && !isCurrent) {
          throwBadRequestError(`Darshan schedule with ID ${id} already has an active discount`);
        }
      });
    }
  }
  
  if (body.applicabilityType === 'SPECIFIC_PRODUCTS') {
    const usedProductIds = await getProductsWithActiveDiscounts();
    const currentProductIds = discount.products.map((id) => String(id));
  
    if (Array.isArray(body.products) && body.products.length > 0) {
      body.products.forEach((id) => {
        const isCurrent = currentProductIds.includes(String(id));
        const isUsed = usedProductIds.some((productId) => String(productId) === String(id));
  
        if (isUsed && !isCurrent) {
          throwBadRequestError(`Product with ID ${id} already has an active discount`);
        }
      });
    }
  }

  // 2. Validate temple if applicability type is SPECIFIC_TEMPLES
  if (body.applicabilityType === 'SPECIFIC_TEMPLES' && body.temple) {
    if (!mongoose.Types.ObjectId.isValid(body.temple)) {
      throwBadRequestError('Invalid temple ID');
    }
    
    const templeExists = await Temple.findById(body.temple);

    if (!templeExists) {
      throwBadRequestError(`Temple with ID ${body.temple} not found`);
    }

    if (Array.isArray(body.poojaSchedules) && body.poojaSchedules.length > 0) {
      for (const id of body.poojaSchedules) {
        if (!mongoose.Types.ObjectId.isValid(id)) {
          throwBadRequestError('Invalid puja schedule ID');
        }
      }
    }
       
    if (Array.isArray(body.darshanSchedules) && body.darshanSchedules.length > 0) {
      for (const id of body.darshanSchedules) {
        if (!mongoose.Types.ObjectId.isValid(id)) {
          throwBadRequestError('Invalid darshan schedule ID');
        }
      }
    }    
  }

  // 3. Validate products if applicability type is SPECIFIC_PRODUCTS
  if (body.applicabilityType === 'SPECIFIC_PRODUCTS' && body.products && body.products.length > 0) {
    for (const productId of body.products) {
      if (!mongoose.Types.ObjectId.isValid(productId)) {
        throwBadRequestError('Invalid product ID');
      }
    }
  }

  //* Case 1 - Start date is given but end date is not given 
  if (body.startDate && !body.endDate) {
    const date = new Date(body.startDate);

    if (date > discount.endDate) {
      throwBadRequestError(`Start date cannot be after end date ${discount.endDate.toISOString().split('T')[0]}`);
    }
  } else if (!body.startDate && body.endDate) { 
    //* Case 2 - Start date is not given but end date is given
    const date = new Date(body.endDate);

    if (date < discount.startDate) {
      throwBadRequestError(`End date cannot be before start date ${discount.startDate.toISOString().split('T')[0]}`);
    }
  }

  const startDate = body.startDate ? new Date(body.startDate) : discount.startDate;
  const endDate = body.endDate ? new Date(body.endDate) : discount.endDate;
  let todaysDate = moment().format('YYYY-MM-DD') + 'T00:00:00.000Z';

  todaysDate = new Date(todaysDate);

  //* Case 1 - Start time is given but end time is not given 
  if (body.startTime && !body.endTime && todaysDate.toISOString().split('T')[0] === startDate.toISOString().split('T')[0] && todaysDate.toISOString().split('T')[0] === endDate.toISOString().split('T')[0]) {
    const givenStartTime = convertTo24Hour(body.startTime);
    const discountEndTime = convertTo24Hour(discount.endTime);

    if (givenStartTime > discountEndTime) {
      throwBadRequestError(`Start time cannot be after end time ${discount.endTime}`);
    }
  } else if (!body.startTime && body.endTime && todaysDate.toISOString().split('T')[0] === startDate.toISOString().split('T')[0] && todaysDate.toISOString().split('T')[0] === endDate.toISOString().split('T')[0]) { 
    //* Case 2 - Start time is not given but end time is given
    const givenEndTime = convertTo24Hour(body.endTime);
    const discountStartTime = convertTo24Hour(discount.startTime);

    if (givenEndTime < discountStartTime) {
      throwBadRequestError(`End time cannot be before start time ${discount.startTime}`);
    }
  } else if (body.startTime && body.endTime && todaysDate.toISOString().split('T')[0] === startDate.toISOString().split('T')[0] && todaysDate.toISOString().split('T')[0] === endDate.toISOString().split('T')[0]) {
    //* Case 3 - Both start time and end time are given
    const givenStartTime = convertTo24Hour(body.startTime);
    const givenEndTime = convertTo24Hour(body.endTime);

    if (givenStartTime > givenEndTime) {
      throwBadRequestError(`Start time cannot be after end time ${body.endTime}`);
    }
  }

  let maxDiscount;

  if (body.discountType === 'PERCENTAGE' || discount.discountType === 'PERCENTAGE') {
    maxDiscount = body.maxDiscount || discount.maxDiscount;
  } else {
    maxDiscount = null;
  }

  // 4. Update discount
  const updateData = {
    code: body.code ? body.code.toUpperCase() : discount.code,
    applicabilityType: body.applicabilityType || discount.applicabilityType,
    maxDiscount,
    temple: body.applicabilityType === 'SPECIFIC_TEMPLES' ? body.temple : null,
    poojaSchedules: body.applicabilityType === 'SPECIFIC_TEMPLES' ? body.poojaSchedules : [],
    darshanSchedules: body.applicabilityType === 'SPECIFIC_TEMPLES' ? body.darshanSchedules : [],
    products: body.applicabilityType === 'SPECIFIC_PRODUCTS' ? body.products : [],
    discountType: body.discountType || discount.discountType,
    discountValue: body.discountValue || discount.discountValue,
    startDate: body.startDate || discount.startDate,
    endDate: body.endDate || discount.endDate,
    startTime: body.startTime || discount.startTime,
    endTime: body.endTime || discount.endTime,
    minimumPurchaseAmount: body.minimumPurchaseAmount !== undefined ? body.minimumPurchaseAmount : discount.minimumPurchaseAmount,
    usageLimitPerUser: body.usageLimitPerUser !== undefined ? body.usageLimitPerUser : discount.usageLimitPerUser,
    totalUsageLimit: body.totalUsageLimit !== undefined ? body.totalUsageLimit : discount.totalUsageLimit,
    discountStatus: body.discountStatus || discount.discountStatus,
    updatedBy: adminId
  };

  const updatedDiscount = await Discount.findByIdAndUpdate(
    discountId,
    updateData,
    { new: true }
  )
    .populate('createdBy')
    .populate('updatedBy')
    .populate('temple')
    .populate('poojaSchedules')
    .populate('darshanSchedules')
    .populate('products');

  return updatedDiscount;
};

//* 5. Function to delete discount
const deleteDiscount = async (discountId) => {
  const discount = await Discount.findByIdAndUpdate(
    discountId,
    { discountStatus: discountStatus.INACTIVE },
    { new: true }
  );
  
  if (!discount) {
    throwBadRequestError('Discount not found');
  }

  return discount;
};

//* 6. Convert times to 24-hour format for comparison
const convertTo24Hour = (timeStr) => {
  const [ time, period ] = timeStr.split(' ');
  let [ hours, minutes ] = time.split(':').map(Number);

  hours = parseInt(hours);
  minutes = parseInt(minutes);
  if (period === 'PM' && hours < 12) {
    hours += 12;
  }
  if (period === 'AM' && hours === 12) {
    hours = 0;
  }

  return hours * 60 + minutes; // Return minutes since midnight
};

//* 7. Function to get unused puja and darshan schedules
const getSchedulesWithoutDiscount = async (temple) => {
  const discounts = await Discount.find({
    temple,
    applicabilityType: 'SPECIFIC_TEMPLES',
    discountStatus: 'ACTIVE'
  });

  let activeDiscountsForPoojaSchedules = [];
  let activeDiscountsForDarshanSchedules = [];

  let todaysDate = moment().format('YYYY-MM-DD') + 'T00:00:00.000Z';

  todaysDate = new Date(todaysDate);

  //* Find schedules with active discounts
  discounts.forEach((currentDiscount) => {
    if (currentDiscount.endDate > todaysDate) {
      activeDiscountsForPoojaSchedules = [
        ...activeDiscountsForPoojaSchedules,
        ...currentDiscount.poojaSchedules
      ];
      activeDiscountsForDarshanSchedules = [
        ...activeDiscountsForDarshanSchedules,
        ...currentDiscount.darshanSchedules
      ];
    } else if (currentDiscount.endDate.toISOString().split('T')[0] === todaysDate.toISOString().split('T')[0]) {
      const discountEndTime = convertTo24Hour(currentDiscount.endTime);
      const currentTime = convertTo24Hour(moment().format('HH:mm'));

      if (discountEndTime >= currentTime) {
        activeDiscountsForPoojaSchedules = [
          ...activeDiscountsForPoojaSchedules,
          ...currentDiscount.poojaSchedules
        ];
        activeDiscountsForDarshanSchedules = [
          ...activeDiscountsForDarshanSchedules,
          ...currentDiscount.darshanSchedules
        ];
      }
    }
  });

  activeDiscountsForPoojaSchedules = Array.from(new Set(activeDiscountsForPoojaSchedules.map(id => id.toString())));
  activeDiscountsForDarshanSchedules = Array.from(new Set(activeDiscountsForDarshanSchedules.map(id => id.toString())));

  const poojaSchedules = await PoojaSchedule.find({
    temple,
    status: 'ACTIVE'
  }).select('_id name type');

  const darshanSchedules = await DarshanSchedule.find({
    temple,
    status: 'ACTIVE'
  }).select('_id name');

  const unusedPoojaSchedules = poojaSchedules.filter((schedule) => {
    if (activeDiscountsForPoojaSchedules.includes(schedule._id.toString())) {
      return false;
    } else {
      return true;
    }
  });

  const unusedDarshanSchedules = darshanSchedules.filter((schedule) => {
    if (activeDiscountsForDarshanSchedules.includes(schedule._id.toString())) {
      return false;
    } else {
      return true;
    }
  });

  return {
    poojaSchedules: unusedPoojaSchedules,
    darshanSchedules: unusedDarshanSchedules
  };
}; 

//* 8. Function to get unused products
const getProductsWithoutDiscount = async (query) => {
  const { page = 1, limit = 10, search, sortBy = 'name', sortOrder = -1 } = query;
  const skip = (parseInt(page) - 1) * parseInt(limit);
  const sortOptions = { [sortBy]: parseInt(sortOrder) };

  const activeDiscountProductIds = await getProductsWithActiveDiscounts();

  const productFilter = {
    _id: { $nin: activeDiscountProductIds.map(id => new mongoose.Types.ObjectId(id)) },
    deletedAt: null
  };

  if (search) {
    productFilter.name = { $regex: search, $options: 'i' };
  }

  const [ products, total ] = await Promise.all([
    Product.find(productFilter)
      .select('name description price')
      .sort(sortOptions)
      .collation({ locale: 'en', strength: 1 })
      .skip(skip)
      .limit(parseInt(limit)),
    Product.countDocuments(productFilter)
  ]);

  return {
    products,
    pagination: {
      total,
      page: parseInt(page),
      pages: Math.ceil(total / parseInt(limit)),
      limit: parseInt(limit)
    }
  };
};

//* 9. Function to get product ids with active discounts
const getProductsWithActiveDiscounts = async () => {
  const discounts = await Discount.find({
    applicabilityType: 'SPECIFIC_PRODUCTS',
    discountStatus: 'ACTIVE'
  });

  let activeProductIds = [];
  let todaysDate = moment().format('YYYY-MM-DD') + 'T00:00:00.000Z';

  todaysDate = new Date(todaysDate);

  discounts.forEach((currentDiscount) => {
    if (currentDiscount.endDate > todaysDate) {
      activeProductIds = [
        ...activeProductIds,
        ...currentDiscount.products
      ];
    } else if (currentDiscount.endDate.toISOString().split('T')[0] === todaysDate.toISOString().split('T')[0]) {
      const discountEndTime = convertTo24Hour(currentDiscount.endTime);
      const currentTime = convertTo24Hour(moment().format('HH:mm'));

      if (discountEndTime >= currentTime) {
        activeProductIds = [
          ...activeProductIds,
          ...currentDiscount.products
        ];
      }
    }
  });

  const uniqueActiveProductIds = Array.from(new Set(
    activeProductIds.map(id => id.toString())
  ));

  return uniqueActiveProductIds;
};

module.exports = {
  createDiscount,
  listAllDiscounts,
  getDiscountById,
  updateDiscount,
  deleteDiscount,
  getSchedulesWithoutDiscount,
  getProductsWithoutDiscount
};