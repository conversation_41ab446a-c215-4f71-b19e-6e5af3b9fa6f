const joi = require('joi');
const mongoose = require('mongoose');
const { applicabilityType, discountType, discountStatus } = require('../../../constants/dbEnums');
const moment = require('moment');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string()
  .custom(validateObjectId)
  .required()
  .messages({
    'any.invalid': 'Invalid discount ID format',
    'any.required': 'Discount ID is required'
  });

const createDiscountSchema = joi.object({
  code: joi.string()
    .required()
    .trim()
    .uppercase()
    .messages({
      'string.empty': 'Discount code is required',
      'any.required': 'Discount code is required'
    }),
  applicabilityType: joi.string()
    .valid(...Object.values(applicabilityType))
    .required()
    .messages({
      'string.empty': 'Applicability type is required',
      'any.required': 'Applicability type is required',
      'any.only': `Applicability type must be one of: ${Object.values(applicabilityType).join(', ')}`
    }),
  temple: joi.when('applicabilityType', {
    is: applicabilityType.SPECIFIC_TEMPLES,
    then: joi.string()
      .custom(validateObjectId)
      .required()
      .messages({
        'any.invalid': 'Invalid temple ID format',
        'any.required': 'Temple ID is required'
      }),
    otherwise: joi.forbidden()
  }),
  darshanSchedules: joi.when('applicabilityType', {
    is: applicabilityType.SPECIFIC_TEMPLES,
    then: joi.array()
      .items(joi.string().custom(validateObjectId).messages({
        'any.invalid': 'Invalid Darshan Schedule ID format'
      }))
      .messages({
        'array.base': 'Darshan schedules must be an array of valid IDs'
      }),
    otherwise: joi.forbidden()
  }),
  poojaSchedules: joi.when('applicabilityType', {
    is: applicabilityType.SPECIFIC_TEMPLES,
    then: joi.array()
      .items(joi.string().custom(validateObjectId).messages({
        'any.invalid': 'Invalid Puja Schedule ID format'
      }))
      .messages({
        'array.base': 'Puja schedules must be an array of valid IDs'
      }),
    otherwise: joi.forbidden()
  }),
  products: joi.when('applicabilityType', {
    is: applicabilityType.SPECIFIC_PRODUCTS,
    then: joi.array()
      .items(joi.string().custom(validateObjectId).messages({
        'any.invalid': 'Invalid product ID format'
      }))
      .min(1)
      .required()
      .messages({
        'array.min': 'At least one product must be selected',
        'any.required': 'Products are required for product-specific discounts'
      }),
    otherwise: joi.forbidden()
  }),
  discountType: joi.string()
    .valid(...Object.values(discountType))
    .required()
    .messages({
      'string.empty': 'Discount type is required',
      'any.required': 'Discount type is required',
      'any.only': `Discount type must be one of: ${Object.values(discountType).join(', ')}`
    }),
  maxDiscount: joi.when('discountType', {
    is: discountType.PERCENTAGE,
    then: joi.number()
      .min(0)
      .optional()
      .allow(null)
      .messages({
        'number.base': 'Maximum discount amount must be a number',
        'number.min': 'Maximum discount amount cannot be negative'
      }),
    otherwise: joi.forbidden()
      .messages({
        'any.unknown': 'Maximum discount amount is only applicable for percentage discounts'
      })
  }),
  discountValue: joi.number()
    .when('discountType', {
      is: discountType.PERCENTAGE,
      then: joi.number().min(1).max(100).required(),
      otherwise: joi.number().min(1).required()
    })
    .messages({
      'number.base': 'Discount value must be a number',
      'number.min': 'Discount value must be greater than 0',
      'number.max': 'Percentage discount cannot exceed 100%',
      'any.required': 'Discount value is required'
    }),
  startDate: joi.date()
    .iso()
    .min(moment().format('YYYY-MM-DD') + 'T00:00:00.000Z')
    .required()
    .messages({
      'date.base': 'Start date must be a valid date',
      'date.format': 'Start date must be in YYYY-MM-DD format',
      'date.min': 'Start date cannot be in the past',
      'any.required': 'Start date is required'
    }),
  endDate: joi.date()
    .iso()
    .min(joi.ref('startDate'))
    .required()
    .messages({
      'date.base': 'End date must be a valid date',
      'date.format': 'End date must be in YYYY-MM-DD format',
      'date.min': 'End date must be after start date',
      'any.required': 'End date is required'
    }),
  startTime: joi.string()
    .pattern(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/)
    .required()
    .messages({
      'string.pattern.base': 'Start time must be in format HH:MM AM/PM',
      'string.empty': 'Start time is required',
      'any.required': 'Start time is required'
    }),
  endTime: joi.string()
    .pattern(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/)
    .required()
    .messages({
      'string.pattern.base': 'End time must be in format HH:MM AM/PM',
      'string.empty': 'End time is required',
      'any.required': 'End time is required'
    }),
  minimumPurchaseAmount: joi.number()
    .min(0)
    .required()
    .default(0)
    .messages({
      'number.base': 'Minimum purchase amount must be a number',
      'number.min': 'Minimum purchase amount cannot be negative'
    }),
  usageLimitPerUser: joi.number()
    .integer()
    .min(1)
    .required()
    .messages({
      'number.base': 'Usage limit per user must be a number',
      'number.integer': 'Usage limit per user must be an integer',
      'number.min': 'Usage limit per user must be at least 1',
      'any.required': 'Usage limit per user is required'
    }),
  totalUsageLimit: joi.number()
    .integer()
    .min(1)
    .required()
    .messages({
      'number.base': 'Total usage limit must be a number',
      'number.integer': 'Total usage limit must be an integer',
      'number.min': 'Total usage limit must be at least 1',
      'any.required': 'Total usage limit is required'
    })
});

const listDiscountsSchema = joi.object({
  page: joi.number().integer().min(1).default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
  limit: joi.number().integer().min(1).max(100).default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
  search: joi.string().trim().allow('').optional()
    .messages({
      'string.base': 'Search query must be a string'
    }),
  status: joi.string().valid(...Object.values(discountStatus)).allow('').optional()
    .messages({
      'any.only': `Status must be one of: ${Object.values(discountStatus).join(', ')}`
    }),
  applicabilityType: joi.string().valid(...Object.values(applicabilityType)).allow('').optional()
    .messages({
      'any.only': `Applicability type must be one of: ${Object.values(applicabilityType).join(', ')}`
    }),
  discountType: joi.string().valid(...Object.values(discountType)).allow('').optional()
    .messages({
      'any.only': `Discount type must be one of: ${Object.values(discountType).join(', ')}`
    }),
  startDate: joi.date().iso().allow('').optional()
    .messages({
      'date.base': 'Start date must be a valid date',
      'date.format': 'Start date must be in ISO format (YYYY-MM-DD)'
    }),
  endDate: joi.date().iso().allow('').optional()
    .when('startDate', {
      is: joi.date().required(),
      then: joi.date().min(joi.ref('startDate')),
      otherwise: joi.optional()
    })
    .messages({
      'date.base': 'End date must be a valid date',
      'date.format': 'End date must be in ISO format (YYYY-MM-DD)',
      'date.min': 'End date must be greater than or equal to start date',
      'any.required': 'End date is required when start date is provided'
    }),
  sortBy: joi.string().valid('createdAt', 'code', 'discountValue', 'startDate', 'endDate', 'status', 'totalUsageLimit', 'usageLimitPerUser', 'minimumPurchaseAmount', 'totalUsageCount', 'applicabilityType', 'discountType', 'startTime', 'endTime').default('createdAt')
    .messages({
      'any.only': 'Sort by field must be one of: createdAt, code, discountValue, startDate, endDate, status, totalUsageLimit, usageLimitPerUser, minimumPurchaseAmount, totalUsageCount, applicabilityType, discountType, startTime, endTime'
    }),
  sortOrder: joi.number().valid(1, -1).default(-1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    })
});

const updateDiscountSchema = joi.object({
  code: joi.string()
    .optional()
    .trim()
    .uppercase()
    .messages({
      'string.empty': 'Discount code cannot be empty',
    }),
  applicabilityType: joi.string()
    .valid(...Object.values(applicabilityType))
    .optional()
    .messages({
      'string.empty': 'Applicability type cannot be empty',
      'any.only': `Applicability type must be one of: ${Object.values(applicabilityType).join(', ')}`
    }),
  temple: joi.when('applicabilityType', {
    is: applicabilityType.SPECIFIC_TEMPLES,
    then: joi.string()
      .custom(validateObjectId)
      .required()
      .messages({
        'any.invalid': 'Invalid temple ID format',
        'any.required': 'Temple ID is required for temple-specific discounts'
      }),
    otherwise: joi.forbidden()
  }),
  darshanSchedules: joi.when('applicabilityType', {
    is: applicabilityType.SPECIFIC_TEMPLES,
    then: joi.array()
      .items(joi.string().custom(validateObjectId).messages({
        'any.invalid': 'Invalid Darshan Schedule ID format'
      }))
      .messages({
        'array.base': 'Darshan schedules must be an array of valid IDs'
      }),
    otherwise: joi.forbidden()
  }),
  poojaSchedules: joi.when('applicabilityType', {
    is: applicabilityType.SPECIFIC_TEMPLES,
    then: joi.array()
      .items(joi.string().custom(validateObjectId).messages({
        'any.invalid': 'Invalid Puja Schedule ID format'
      }))
      .messages({
        'array.base': 'Puja schedules must be an array of valid IDs'
      }),
    otherwise: joi.forbidden()
  }),
  products: joi.when('applicabilityType', {
    is: applicabilityType.SPECIFIC_PRODUCTS,
    then: joi.array()
      .items(joi.string().custom(validateObjectId).messages({
        'any.invalid': 'Invalid product ID format'
      }))
      .min(1)
      .required()
      .messages({
        'array.min': 'At least one product must be selected',
        'any.required': 'Products are required for product-specific discounts'
      }),
    otherwise: joi.forbidden()
  }),
  discountType: joi.string()
    .valid(...Object.values(discountType))
    .optional()
    .messages({
      'string.empty': 'Discount type cannot be empty',
      'any.only': `Discount type must be one of: ${Object.values(discountType).join(', ')}`
    }),
  maxDiscount: joi.number()
    .min(0)
    .optional()
    .allow(null)
    .messages({
      'number.base': 'Maximum discount amount must be a number',
      'number.min': 'Maximum discount amount cannot be negative'
    }),
  discountValue: joi.number()
    .when('discountType', {
      is: joi.exist(),
      then: joi.number().when('discountType', {
        is: discountType.PERCENTAGE,
        then: joi.number().min(1).max(100).required(),
        otherwise: joi.number().min(1).required()
      }),
      otherwise: joi.number().min(1)
    })
    .messages({
      'number.base': 'Discount value must be a number',
      'number.min': 'Discount value must be greater than 0',
      'number.max': 'Percentage discount cannot exceed 100%',
      'any.required': 'Discount value is required'
    }),
  startDate: joi.date()
    .iso()
    .min(moment().format('YYYY-MM-DD') + 'T00:00:00.000Z')
    .optional()
    .messages({
      'date.base': 'Start date must be a valid date',
      'date.format': 'Start date must be in YYYY-MM-DD format',
      'date.min': 'Start date cannot be in the past',
      'any.required': 'Start date is required'
    }),
  endDate: joi.date()
    .iso()
    .when('startDate', {
      is: joi.exist(),
      then: joi.date().min(joi.ref('startDate')).optional(),
      otherwise: joi.optional()
    })
    .optional()
    .messages({
      'date.base': 'End date must be a valid date',
      'date.format': 'End date must be in YYYY-MM-DD format',
      'date.min': 'End date must be greater than or equal to start date if start date is provided',
      'any.required': 'End date is required when start date is provided'
    }),
  startTime: joi.string()
    .pattern(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/)
    .optional()
    .messages({
      'string.pattern.base': 'Start time must be in format HH:MM AM/PM',
      'string.empty': 'Start time is required'
    }),
  endTime: joi.string()
    .pattern(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/)
    .optional()
    .messages({
      'string.pattern.base': 'End time must be in format HH:MM AM/PM',
      'string.empty': 'End time is required'
    }),
  minimumPurchaseAmount: joi.number()
    .min(0)
    .optional()
    .messages({
      'number.base': 'Minimum purchase amount must be a number',
      'number.min': 'Minimum purchase amount cannot be negative'
    }),
  usageLimitPerUser: joi.number()
    .integer()
    .min(1)
    .optional()
    .messages({
      'number.base': 'Usage limit per user must be a number',
      'number.integer': 'Usage limit per user must be an integer',
      'number.min': 'Usage limit per user must be at least 1'
    }),
  totalUsageLimit: joi.number()
    .integer()
    .min(1)
    .optional()
    .messages({
      'number.base': 'Total usage limit must be a number',
      'number.integer': 'Total usage limit must be an integer',
      'number.min': 'Total usage limit must be at least 1'
    }),
  discountStatus: joi.string()
    .valid(...Object.values(discountStatus))
    .optional()
    .messages({
      'string.empty': 'Discount status cannot be empty',
      'any.only': `Discount status must be one of: ${Object.values(discountStatus).join(', ')}`
    })
}).min(1, 'At least one field is required for update');

const getSchedulesWithoutDiscountSchema = joi.object({
  temple: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Temple ID is required',
      'any.required': 'Temple ID is required',
      'any.invalid': 'Invalid temple ID format'
    })
});

const getProductsWithoutDiscountSchema = joi.object({
  page: joi.number().integer().min(1).default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
  limit: joi.number().integer().min(1).max(100).default(10) 
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
  search: joi.string().trim().allow('').optional()
    .messages({
      'string.base': 'Search query must be a string'
    }),
  sortBy: joi.string().valid('name', 'description', 'price').default('name')
    .messages({
      'string.empty': 'Sort by field cannot be empty',
      'any.only': 'Sort by field must be one of: name, description, price'
    }),
  sortOrder: joi.number().valid(1, -1).default(-1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    })
});

module.exports = {
  validateId,
  createDiscountSchema,
  listDiscountsSchema,
  updateDiscountSchema,
  getSchedulesWithoutDiscountSchema,
  getProductsWithoutDiscountSchema
};
