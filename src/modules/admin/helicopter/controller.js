const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const { createHelicopterSchema, listHelicoptersSchema, updateHelicopterSchema, uploadUrlSchema, validateId, updateHelicopterStatusSchema, listHelicopterBookingsSchema } = require('./validation');
const helicopterService = require('./service');
const { getPresignedUrl } = require('../../../utils/s3Service');
const { saveAuditLog } = require('../../../utils/auditLogger');
const { auditLogAction } = require('../../../constants/dbEnums');

const createHelicopter = async (req, res) => {
  try {
    const { error } = createHelicopterSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }
    
    const data = await helicopterService.createHelicopter(req.body, req.user.id);

    //* Save audit log 
    const detail = 'Helicopter service created successfully';
    const model = 'Helicopter';

    await saveAuditLog(req, req.user.id, auditLogAction.HELICOPTER_CREATED, detail, model);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.HELICOPTER_CREATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const listHelicopters = async (req, res) => {
  try {
    const { error } = listHelicoptersSchema.validate(req.query);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }
    
    const data = await helicopterService.listHelicopters(req.query);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Helicopters retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getHelicopterById = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const data = await helicopterService.getHelicopterById(req.params.id);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Helicopter retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateHelicopter = async (req, res) => {
  try {
    const { error } = updateHelicopterSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }
    
    const data = await helicopterService.updateHelicopter(req.params.id, req.body, req.user.id);

    //* Save audit log 
    const detail = 'Helicopter service updated successfully';
    const model = 'Helicopter';

    await saveAuditLog(req, req.user.id, auditLogAction.HELICOPTER_UPDATED, detail, model);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Helicopter updated successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const deleteHelicopter = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }
    
    const data = await helicopterService.deleteHelicopter(req.params.id);

    //* Save audit log 
    const detail = 'Helicopter service deleted successfully';
    const model = 'Helicopter';

    await saveAuditLog(req, req.user.id, auditLogAction.HELICOPTER_DELETED, detail, model);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Helicopter service deleted successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }
    
    const { error } = uploadUrlSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }
    
    const { extension } = req.body;

    const uploadData = await getPresignedUrl(extension, 'helicopters');

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Upload URL generated successfully',
      status: true,
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateHelicopterStatus = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const { error: statusError } = updateHelicopterStatusSchema.validate(req.body);
    
    if (statusError) {
      return res.status(400).json({ 
        status: false,
        message: statusError.details[0].message 
      });
    }
    
    const data = await helicopterService.updateHelicopterStatus(req.params.id, req.body);

    //* Save audit log 
    const detail = `Helicopter service ${req.body.status === 'ACTIVE' ? 'activated' : 'deactivated'} successfully`;
    const model = 'Helicopter';

    await saveAuditLog(req, req.user.id, auditLogAction.HELICOPTER_STATUS_UPDATED, detail, model);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: `Helicopter service ${req.body.status === 'ACTIVE' ? 'activated' : 'deactivated'} successfully`,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};  

const listHelicopterBookingQueries = async (req, res) => {
  try {
    const { error } = listHelicopterBookingsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await helicopterService.listHelicopterBookings(req.query);
    
    return apiResponse({ 
      res, 
      code: commonConstants.SUCCESS.CODE, 
      message: messages.SUCCESS, 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createHelicopter,
  listHelicopters,
  getHelicopterById,
  updateHelicopter,
  deleteHelicopter,
  getUploadUrl,
  updateHelicopterStatus,
  listHelicopterBookingQueries
};