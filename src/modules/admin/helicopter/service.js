const Helicopter = require('../../../models/Helicopter');
const HelicopterBooking = require('../../../models/HelicopterBooking');
const { throwBadRequestError } = require('../../../errors');
const { translateDataForStore } = require('../../../utils/translateInput');
// const { helicopterBookingStatus } = require('../../../constants/dbEnums');
// const moment = require('moment');

const createHelicopter = async (helicopterData, adminId) => {
  // Process images if provided
  if (helicopterData.image) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = helicopterData.image.startsWith(process.env.MEDIA_URL) ? 
      helicopterData.image : 
      `${process.env.MEDIA_URL}/${helicopterData.image}`;

    helicopterData.image = cloudFrontUrl;
  }

  // Process video if provided
  if (helicopterData.video) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = helicopterData.video.startsWith(process.env.MEDIA_URL) ? 
      helicopterData.video : 
      `${process.env.MEDIA_URL}/${helicopterData.video}`;

    helicopterData.video = cloudFrontUrl;
  }

  // Process itinerary if provided
  if (helicopterData.itinerary) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = helicopterData.itinerary.startsWith(process.env.MEDIA_URL) ? 
      helicopterData.itinerary : 
      `${process.env.MEDIA_URL}/${helicopterData.itinerary}`;

    helicopterData.itinerary = cloudFrontUrl;
  }

  // Translate fields
  const translatedFields = [ 'name', 'shortDescription', 'detailedDescription' ];
  const translatedData = await translateDataForStore(translatedFields, helicopterData);

  translatedFields.forEach(field => {
    if (helicopterData[field]) {
      delete helicopterData[field];
    }
  });

  helicopterData = {
    ...helicopterData,
    ...translatedData
  };

  // Create helicopter
  const helicopter = await Helicopter.create({
    ...helicopterData,
    createdBy: adminId,
    updatedBy: adminId
  });

  return helicopter;
};

const listHelicopters = async (query) => {
  const { page = 1, limit = 10, search, sortBy = 'createdAt', sortOrder = -1, status, startDate, endDate } = query;
  const skip = (page - 1) * limit;

  const filter = {
    deletedAt: null
  };

  if (status) {
    filter.status = status;
  }

  const language = { code: 'en' }; // Assume this comes from request or default

  // Search in localized name
  if (search) {
    filter[`name.${language.code}`] = { $regex: search, $options: 'i' };
  }

  // Date range filtering logic
  if (startDate || endDate) {
    const start = startDate ? new Date(startDate) : null;
    const end = endDate ? new Date(endDate) : null;

    // Ensure that the startDate is not later than the endDate
    if (start && end && start > end) {
      throw new Error('Start date cannot be greater than end date.');
    }

    // Build date filter based on the range
    const dateFilter = {};

    if (start) {
      dateFilter.$gte = start;
    }
    if (end) {
      dateFilter.$lte = end;
    }

    // Filter helicopters where dates overlap with the range
    filter.dates = dateFilter;
  }

  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder);

  const helicopters = await Helicopter.find(filter)
    .skip(skip)
    .limit(limit)
    .sort(sortOptions)
    .collation({ locale: 'en', strength: 1 });

  const total = await Helicopter.countDocuments(filter);

  const response = helicopters.map((helicopter) => ({
    ...helicopter.toObject(),
    name: helicopter.name?.en || '',
    shortDescription: helicopter.shortDescription?.en || '',
    detailedDescription: helicopter.detailedDescription?.en || '',
  }));

  return {
    helicopters: response,
    pagination: {
      total,
      page: parseInt(page),
      pages: Math.ceil(total / limit),
      limit: parseInt(limit)
    }
  };
};

const getHelicopterById = async (helicopterId) => {
  const helicopter = await Helicopter.findById(helicopterId);

  if (!helicopter) {
    throwBadRequestError('Helicopter not found');
  }

  helicopter.name = helicopter.name?.en || '';
  helicopter.shortDescription = helicopter.shortDescription?.en || '';
  helicopter.detailedDescription = helicopter.detailedDescription?.en || '';

  return helicopter;
};

const updateHelicopter = async (helicopterId, updateData, adminId) => {
  const helicopter = await Helicopter.findById(helicopterId);
  
  if (!helicopter) {
    throwBadRequestError('Helicopter not found');
  }

  if (helicopter.deletedAt) {
    throwBadRequestError('Helicopter already deleted');
  }

  // Process images if provided
  if (updateData.image) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = updateData.image.startsWith(process.env.MEDIA_URL) ? 
      updateData.image : 
      `${process.env.MEDIA_URL}/${updateData.image}`;

    updateData.image = cloudFrontUrl;
  }

  // Process video if provided
  if (updateData.video) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = updateData.video.startsWith(process.env.MEDIA_URL) ? 
      updateData.video : 
      `${process.env.MEDIA_URL}/${updateData.video}`;

    updateData.video = cloudFrontUrl;
  }

  // Process itinerary if provided
  if (updateData.itinerary) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = updateData.itinerary.startsWith(process.env.MEDIA_URL) ? 
      updateData.itinerary : 
      `${process.env.MEDIA_URL}/${updateData.itinerary}`;

    updateData.itinerary = cloudFrontUrl;
  }

  // Translate fields if provided
  const translatedFields = [ 'name', 'shortDescription', 'detailedDescription' ];
  const hasTranslatableFields = translatedFields.some(field => updateData[field]);

  if (hasTranslatableFields) {
    const translatedData = await translateDataForStore(translatedFields, updateData);

    translatedFields.forEach(field => {
      if (updateData[field]) {
        delete updateData[field];
      }
    });

    updateData = {
      ...updateData,
      ...translatedData
    };
  }

  const formatDate = d => new Date(d).toISOString().split('T')[0];

  const dates = updateData.dates?.length > 0
    ? updateData.dates.map(d => new Date(d))
    : helicopter.dates.map(d => new Date(d));

  const availability = updateData.availability?.length > 0
    ? updateData.availability.map(a => ({
      date: new Date(a.date),
      availableSeats: a.availableSeats
    }))
    : helicopter.availability.map(a => ({
      date: new Date(a.date),
      availableSeats: a.availableSeats
    }));

  // Convert to strings for easier comparison
  const dateSet = new Set(dates.map(formatDate));
  const availabilitySet = new Set(availability.map(a => formatDate(a.date)));

  //* Check 1: selected dates must not include any blackout dates
  for (const dateStr of dateSet) {
    if (!availabilitySet.has(dateStr)) {
      throwBadRequestError(`Missing availability for selected date ${dateStr}. Please add availability for this date.`);
    }
  }

  //* Check 2: Availability dates must be within selected dates
  for (const dateStr of availabilitySet) {
    if (!dateSet.has(dateStr)) {
      throwBadRequestError(`Availability date ${dateStr} is not selected. Please remove it from availability.`);
    }
  }

  // Update helicopter
  const updatedHelicopter = await Helicopter.findByIdAndUpdate(
    helicopterId,
    {
      ...updateData,
      updatedBy: adminId
    },
    { new: true }
  );

  return updatedHelicopter;
};

const deleteHelicopter = async (helicopterId) => {
  const helicopter = await Helicopter.findById(helicopterId);
  
  if (!helicopter) {
    throwBadRequestError('Helicopter not found');
  }

  if (helicopter.deletedAt) {
    throwBadRequestError('Helicopter already deleted');
  }

  //* Soft delete helicopter 
  helicopter.deletedAt = new Date();
  helicopter.status = 'INACTIVE';
  await helicopter.save();

  return helicopter;
};

const updateHelicopterStatus = async (helicopterId, body) => {
  const { status } = body;

  const helicopter = await Helicopter.findById(helicopterId);
  
  if (!helicopter) {
    throwBadRequestError('Helicopter not found');
  }

  if (helicopter.deletedAt) {
    throwBadRequestError('Helicopter already deleted');
  }

  // Update helicopter status
  helicopter.status = status;
  await helicopter.save();

  return helicopter;
};

const listHelicopterBookings = async (query) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, status, startDate, endDate, search, user, helicopter } = query;

  const filter = {};

  if (user) {
    filter.user = user;
  }

  if (helicopter) {
    filter.helicopter = helicopter;
  }

  if (status) {
    filter.status = status;
  }

  if (startDate && !endDate) {
    filter.date = { $gte: new Date(startDate) };
  } else if (endDate && !startDate) {
    filter.date = { $lte: new Date(endDate) };
  } else if (startDate && endDate) {
    filter.date = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }

  if (search) {
    filter.$or = [
      { 'primaryDevoteeDetails.fullName.en': { $regex: search, $options: 'i' } },
      { 'primaryDevoteeDetails.fullName.hi': { $regex: search, $options: 'i' } },
      { 'otherDevotees.fullName.en': { $regex: search, $options: 'i' } },
      { 'otherDevotees.fullName.hi': { $regex: search, $options: 'i' } }
    ];
  }

  const bookings = await HelicopterBooking.find(filter)
    .populate('helicopter')
    .sort({ [sortBy]: parseInt(sortOrder) })
    .collation({ locale: 'en', strength: 1 })
    .skip((parseInt(page) - 1) * parseInt(limit))
    .limit(parseInt(limit))
    .lean();

  const total = await HelicopterBooking.countDocuments(filter);
  
  return {
    bookings,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

// //* Function to update helicopter booking status 
// const updateHelicopterBookingStatus = async (helicopterBookingId, body) => {
//   const { status } = body;

//   const booking = await HelicopterBooking.findById(helicopterBookingId);

//   if (!booking) {
//     throwNotFoundError('Helicopter booking not found');
//   }

//   const currentStatus = booking.status;

//   if (currentStatus === status) {
//     throwBadRequestError(`Booking is already in ${status}. Please choose a different status to update`);
//   }

//   const today = moment().startOf('day');
//   const bookingDate = moment(booking.date).startOf('day');

//   switch (status) {
//     //? Case 1: When admin wants to request payment for the booking
//     case helicopterBookingStatus.PAYMENT_REQUESTED: 
//       if (
//         currentStatus === helicopterBookingStatus.REJECTED || 
//         currentStatus === helicopterBookingStatus.CANCELLED || 
//         currentStatus === helicopterBookingStatus.COMPLETED || 
//         currentStatus === helicopterBookingStatus.CONFIRMED
//       ) {
//         throwBadRequestError(`Cannot request payment when booking is already ${currentStatus}`);
//       }

//       //* Update status 
//       booking.status = status;

//       //todo: WhatsApp message logic goes from gere >
//       break;
    
//     //? Case 2: When admin wants to reject the booking
//     case helicopterBookingStatus.REJECTED:
//       if (
//         currentStatus === helicopterBookingStatus.COMPLETED || 
//         currentStatus === helicopterBookingStatus.CONFIRMED || 
//         currentStatus === helicopterBookingStatus.CANCELLED
//       ) {
//         throwBadRequestError(`Cannot reject booking when it is already ${currentStatus}`);
//       }

//       //* Update Status   
//       booking.status = status;
//       break;

//     //? Case 3: When admin wants to confirm the booking 
//     case helicopterBookingStatus.CONFIRMED:
//       if (
//         currentStatus === helicopterBookingStatus.SUBMITTED || 
//         currentStatus === helicopterBookingStatus.REJECTED || 
//         currentStatus === helicopterBookingStatus.CANCELLED || 
//         currentStatus === helicopterBookingStatus.COMPLETED
//       ) {
//         throwBadRequestError(`Booking can only be confirmed after payment is requested. Current status: ${currentStatus}`);
//       }

//       //* Update Status 
//       booking.status = status;
//       break;

//     //? Case 4: When admin wants to complete the booking 
//     case helicopterBookingStatus.COMPLETED:
//       if (
//         currentStatus === helicopterBookingStatus.SUBMITTED || 
//         currentStatus === helicopterBookingStatus.REJECTED || 
//         currentStatus === helicopterBookingStatus.PAYMENT_REQUESTED || 
//         currentStatus === helicopterBookingStatus.CANCELLED
//       ) {
//         throwBadRequestError('Cannot complete booking. Only bookings in CONFIRMED status are allowed to be marked as COMPLETED');
//       }

//       if (bookingDate.isAfter(today)) {
//         throwBadRequestError('You can mark the booking as COMPLETED only after the booking date is in the past.');
//       }

//       booking.status = status;
//       break;

//     //? Case 5: When admin wants to cancel the booking
//     case helicopterBookingStatus.CANCELLED:
//       if (
//         currentStatus === helicopterBookingStatus.COMPLETED || 
//         currentStatus === helicopterBookingStatus.CONFIRMED || 
//         currentStatus === helicopterBookingStatus.REJECTED
//       ) {
//         throwBadRequestError(`Cannot cancel booking when it is already ${currentStatus}`);
//       }

//       booking.status = status;
//       break;
    
//     //? Default case: Invalid status 
//     default: 
//       throwBadRequestError('Invalid status');
//   }

//   //* Save the updated booking status 
//   await booking.save();

//   return booking;
// };

module.exports = {
  createHelicopter,
  listHelicopters,
  getHelicopterById,
  updateHelicopter,
  deleteHelicopter,
  updateHelicopterStatus,
  listHelicopterBookings,
  // updateHelicopterBookingStatus
};
