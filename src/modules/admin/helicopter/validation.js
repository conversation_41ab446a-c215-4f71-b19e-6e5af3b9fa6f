const joi = require('joi');
const mongoose = require('mongoose');

const createHelicopterSchema = joi.object({
  name: joi.string().required().messages({
    'any.required': 'Helicopter Name is required'
  }),
  
  shortDescription: joi.string().max(255).messages({
    'string.max': 'Short Description cannot exceed 255 characters'
  }),
  
  detailedDescription: joi.string().max(10000).messages({
    'string.max': 'Detailed Description cannot exceed 10000 characters'
  }),
  
  image: joi.string().messages({
    'string.base': 'Image path must be a string'
  }),
  
  video: joi.string().messages({
    'string.base': 'Video path must be a string'
  }),
  
  link: joi.string().messages({
    'string.base': 'Link must be a string'
  }),
  
  itinerary: joi.string().messages({
    'string.base': 'Itinerary must be a string'
  }),
  
  basePrice: joi.number().min(0).messages({
    'number.min': 'Base Price cannot be negative'
  }),
  
  currency: joi.string().default('INR').messages({
    'string.base': 'Currency must be a string'
  }),
  
  maximumCapacityPerBooking: joi.number().min(0).messages({
    'number.min': 'Maximum Capacity Per Booking cannot be negative'
  }),

  status: joi.string().valid('ACTIVE', 'INACTIVE').messages({
    'any.only': 'Status must be either ACTIVE or INACTIVE'
  }),

  dates: joi.array().items(
    joi.date().iso().min('now').required().messages({
      'date.base': 'Date must be a valid ISO date',
      'date.min': 'Date cannot be in the past',
      'date.format': 'Date must be in ISO format'
    })
  ).default([]).messages({
    'array.base': 'Dates must be an array of valid ISO dates'
  }),
  
  availability: joi.array().items(
    joi.object({
      date: joi.date().iso().min('now').required().messages({
        'date.base': 'Date must be a valid date',
        'date.isoDate': 'Date must be in ISO format',
        'date.min': 'Date must be greater than or equal to current date'
      }),
      availableSeats: joi.number().min(0).required().messages({
        'number.min': 'Available Seats cannot be negative',
        'number.base': 'Available Seats must be a number'
      })
    })
  ).required().custom((availability, helpers) => {
    const { dates = [] } = helpers.state.ancestors[0];

    const dateSet = new Set(
      dates.map(d => new Date(d).toISOString().split('T')[0])
    );
    const availabilitySet = new Set(
      availability.map(item => new Date(item.date).toISOString().split('T')[0])
    );
  
    //* Check 1: all selected dates must have availability
    for (const dateStr of dateSet) {
      if (!availabilitySet.has(dateStr)) {
        return helpers.message(`Missing availability for selected date ${dateStr}. Please add availability for this date.`);
      }
    }

    //* Check 2: availability dates must be within selected dates
    for (const dateStr of availabilitySet) {
      if (!dateSet.has(dateStr)) {
        return helpers.message(`Availability date ${dateStr} is not selected. Please remove it from availability.`);
      }
    }
  
    return availability;
  })
});

const listHelicoptersSchema = joi.object({
  page: joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be greater than or equal to 1'
    }),
  limit: joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be greater than or equal to 1',
      'number.max': 'Limit cannot exceed 100 records per page'
    }),
  search: joi.string()
    .trim()
    .allow('')
    .messages({
      'string.base': 'Search term must be a string'
    }),
  sortBy: joi.string()
    .valid('name', 'createdAt', 'updatedAt', 'maximumCapacityPerBooking')
    .default('createdAt')
    .messages({
      'string.base': 'Sort field must be a string',
      'any.only': 'Sort field must be one of: name, createdAt, updatedAt or maximumCapacityPerBooking'
    }),
  sortOrder: joi.number()
    .valid(1, -1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    }),
  status: joi.string()
    .valid('ACTIVE', 'INACTIVE')
    .messages({
      'any.only': 'Status must be either ACTIVE or INACTIVE'
    }),
  startDate: joi.date()
    .iso()
    .messages({
      'date.base': 'Start date must be a valid date',
      'date.format': 'Start date must be in ISO format (YYYY-MM-DD)'
    }),
  endDate: joi.date()
    .iso()
    .when('startDate', {
      is: joi.date().required(),
      then: joi.date().min(joi.ref('startDate'))
    })
    .messages({
      'date.base': 'End date must be a valid date',
      'date.format': 'End date must be in ISO format (YYYY-MM-DD)',
      'date.min': 'End date must be greater than or equal to start date'
    })
});

const updateHelicopterSchema = joi.object({
  name: joi.string().messages({
    'string.base': 'Helicopter Name must be a string'
  }),

  shortDescription: joi.string().max(255).messages({
    'string.max': 'Short Description cannot exceed 255 characters'
  }),

  detailedDescription: joi.string().max(10000).messages({
    'string.max': 'Detailed Description cannot exceed 10000 characters'
  }),

  image: joi.string().messages({
    'string.base': 'Image path must be a string'
  }),

  video: joi.string().messages({
    'string.base': 'Video path must be a string'
  }),

  link: joi.string().messages({
    'string.base': 'Link must be a string'
  }),

  itinerary: joi.string().messages({
    'string.base': 'Itinerary must be a string'
  }),

  basePrice: joi.number().min(0).messages({
    'number.min': 'Base Price cannot be negative'
  }),

  currency: joi.string().messages({
    'string.base': 'Currency must be a string'
  }),

  maximumCapacityPerBooking: joi.number().min(0).messages({
    'number.min': 'Maximum Capacity Per Booking cannot be negative'
  }),

  status: joi.string().valid('ACTIVE', 'INACTIVE').messages({
    'any.only': 'Status must be either ACTIVE or INACTIVE'
  }),

  dates: joi.array().items(
    joi.date().iso().min('now').messages({
      'date.base': 'Date must be a valid ISO date',
      'date.min': 'Date cannot be in the past',
      'date.format': 'Date must be in ISO format'
    })
  ).messages({
    'array.base': 'Dates must be an array of valid ISO dates'
  }),

  availability: joi.array().items(
    joi.object({
      date: joi.date().iso().min('now').required().messages({
        'date.base': 'Date must be a valid date',
        'date.isoDate': 'Date must be in ISO format',
        'date.min': 'Date must be greater than or equal to current date'
      }),
      availableSeats: joi.number().min(0).required().messages({
        'number.min': 'Available Seats cannot be negative',
        'number.base': 'Available Seats must be a number'
      })
    })
  ).custom((availability, helpers) => {
    const { dates = [] } = helpers.state.ancestors[0];

    // Only run custom logic if dates are present
    if (!Array.isArray(dates) || dates.length === 0) {
      return availability;
    }

    const dateSet = new Set(
      dates.map(d => new Date(d).toISOString().split('T')[0])
    );
    const availabilitySet = new Set(
      availability.map(item => new Date(item.date).toISOString().split('T')[0])
    );

    // Check 1: all selected dates must have availability
    for (const dateStr of dateSet) {
      if (!availabilitySet.has(dateStr)) {
        return helpers.message(`Missing availability for selected date ${dateStr}. Please add availability for this date.`);
      }
    }

    // Check 3: availability dates must be within selected dates
    for (const dateStr of availabilitySet) {
      if (!dateSet.has(dateStr)) {
        return helpers.message(`Availability date ${dateStr} is not selected. Please remove it from availability.`);
      }
    }

    return availability;
  })
}).min(1) // ✅ Ensure at least one field is provided
  .messages({
    'object.min': 'Please provide at least one field to update.'
  });

const uploadUrlSchema = joi.object({
  extension: joi.string()
    .valid('jpeg', 'jpg', 'png', 'heic', 'heif', 'pdf', 'webp', 'mp4', 'mov', 'hevc', 'flv', 'webm', 'mpeg')
    .required()
    .messages({
      'any.required': 'File extension is required',
      'any.only': 'Invalid file extension. Only JPEG, JPG, PNG, HEIC, HEIF, PDF, WEBP, MP4, MOV, HEVC, FLV, WEBM and MPEG are allowed'
    })
});

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string().custom(validateObjectId).required().messages({
  'any.invalid': 'Invalid helicopter ID format',
  'any.required': 'Helicopter ID is required'
});

const updateHelicopterStatusSchema = joi.object({
  status: joi.string().valid('ACTIVE', 'INACTIVE').required().messages({
    'any.required': 'Status is required',
    'any.only': 'Status must be either ACTIVE or INACTIVE'
  })
});

const listHelicopterBookingsSchema = joi.object({
  user: joi.string().custom(validateObjectId).messages({
    'any.invalid': 'Invalid user ID format',
    'any.required': 'User ID is required'
  }),
  helicopter: joi.string().custom(validateObjectId).messages({
    'any.invalid': 'Invalid helicopter ID format',
    'any.required': 'Helicopter ID is required'
  }),
  page: joi.number().integer().min(1).default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
  limit: joi.number().integer().min(1).max(100).default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
  sortBy: joi.string().valid('createdAt', 'date', 'status', 'totalAmount').default('createdAt')
    .messages({
      'string.base': 'Sort field must be a string',
      'any.only': 'Sort field must be one of: createdAt, date, status, totalAmount'
    }),
  sortOrder: joi.number().valid(1, -1).default(-1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    }),
  search: joi.string().trim().allow('').optional()
    .messages({
      'string.base': 'Search query must be a string'
    }),
  status: joi.string().valid('SUBMITTED', 'APPROVED', 'REJECTED', 'CANCELLED', 'COMPLETED').allow('').optional()
    .messages({
      'any.only': 'Status must be one of: SUBMITTED, APPROVED, REJECTED, CANCELLED, or COMPLETED'
    }),
  startDate: joi.date().iso().allow('').optional()
    .messages({
      'date.base': 'Start date must be a valid date',
      'date.format': 'Start date must be in ISO format (YYYY-MM-DD)'
    }),
  endDate: joi.date().iso().allow('').optional()
    .when('startDate', {
      is: joi.date().required(),
      then: joi.date().min(joi.ref('startDate')),
      otherwise: joi.optional()
    })
    .messages({
      'date.base': 'End date must be a valid date',
      'date.format': 'End date must be in ISO format (YYYY-MM-DD)',
      'date.min': 'End date must be greater than or equal to start date',
      'any.required': 'End date is required when start date is provided'
    })
});

module.exports = {
  createHelicopterSchema,
  listHelicoptersSchema,
  updateHelicopterSchema,
  uploadUrlSchema,
  validateId,
  updateHelicopterStatusSchema,
  listHelicopterBookingsSchema
};