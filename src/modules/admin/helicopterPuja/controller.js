const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const { createHelicopterPujaSchema, listHelicopterPujasSchema, updateHelicopterPujaSchema, validateId } = require('./validation');
const helicopterService = require('./service');
const { saveAuditLog } = require('../../../utils/auditLogger');
const { auditLogAction } = require('../../../constants/dbEnums');

const createHelicopterPuja = async (req, res) => {
  try {
    const { error } = createHelicopterPujaSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }
    
    const data = await helicopterService.createHelicopterPuja(req.body, req.user.id);

    //* Save audit log 
    const detail = 'Helicopter puja created successfully';
    const model = 'HelicopterPuja';

    await saveAuditLog(req, req.user.id, auditLogAction.HELICOPTER_PUJA_CREATED, detail, model);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.HELICOPTER_PUJA_CREATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const listHelicopterPujas = async (req, res) => {
  try {
    const { error } = listHelicopterPujasSchema.validate(req.query);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }
    
    const data = await helicopterService.listHelicopterPujas(req.query);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Helicopter pujas retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getHelicopterPujaById = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }
    
    const data = await helicopterService.getHelicopterPujaById(req.params.id);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Helicopter puja retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateHelicopterPuja = async (req, res) => {
  try {
    const { error } = updateHelicopterPujaSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }
    
    const data = await helicopterService.updateHelicopterPuja(req.params.id, req.body, req.user.id);

    //* Save audit log 
    const detail = 'Helicopter puja updated successfully';
    const model = 'HelicopterPuja';

    await saveAuditLog(req, req.user.id, auditLogAction.HELICOPTER_PUJA_UPDATED, detail, model);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Helicopter puja updated successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const deleteHelicopterPuja = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }
    
    const data = await helicopterService.deleteHelicopterPuja(req.params.id);

    //* Save audit log 
    const detail = 'Helicopter puja deleted successfully';
    const model = 'HelicopterPuja';

    await saveAuditLog(req, req.user.id, auditLogAction.HELICOPTER_PUJA_DELETED, detail, model);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Helicopter puja deleted successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createHelicopterPuja,
  listHelicopterPujas,
  getHelicopterPujaById,
  updateHelicopterPuja,
  deleteHelicopterPuja
};