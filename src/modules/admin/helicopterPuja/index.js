const express = require('express');
const router = express.Router();
const helicopterPujaController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

router.post('/create', auth, isAdminOrSuperAdmin, helicopterPujaController.createHelicopterPuja);
router.get('/list', auth, isAdminOrSuperAdmin, helicopterPujaController.listHelicopterPujas);
router.get('/:id', auth, isAdminOrSuperAdmin, helicopterPujaController.getHelicopterPujaById);
router.put('/:id', auth, isAdminOrSuperAdmin, helicopterPujaController.updateHelicopterPuja);
router.delete('/:id', auth, isAdminOrSuperAdmin, helicopterPujaController.deleteHelicopterPuja);

module.exports = router;