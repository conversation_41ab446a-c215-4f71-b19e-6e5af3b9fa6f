const Helicopter = require('../../../models/Helicopter');
const HelicopterPuja = require('../../../models/HelicopterPuja');
const { throwBadRequestError } = require('../../../errors');
const { translateDataForStore } = require('../../../utils/translateInput');
const { transformTranslatedFields } = require('../../../utils/localizer');

const createHelicopterPuja = async (pujaData, adminId) => {
  // Check if helicopter exists
  const helicopter = await Helicopter.findById(pujaData.helicopter);

  if (!helicopter) {
    throwBadRequestError('Helicopter not found');
  }

  if (helicopter.deletedAt || helicopter.status === 'INACTIVE') {
    throwBadRequestError('Helicopter already deleted or inactive');
  }

  // Translate fields
  const translatedFields = [ 'name', 'description' ];
  const translatedData = await translateDataForStore(translatedFields, pujaData);

  translatedFields.forEach(field => {
    if (pujaData[field]) {
      delete pujaData[field];
    }
  });

  pujaData = {
    ...pujaData,
    ...translatedData
  };

  // Create helicopter puja
  const helicopterPuja = await HelicopterPuja.create({
    ...pujaData,
    createdBy: adminId,
    updatedBy: adminId
  });

  return helicopterPuja;
};

const listHelicopterPujas = async (queryParams) => {
  const { page = 1, limit = 10, search, sortBy = 'createdAt', sortOrder = -1, status = 'ACTIVE' } = queryParams;
  const skip = (page - 1) * limit;
  
  const filter = {
    status
  };
  
  const language = { code: 'en' }; // Assume this comes from request or default
  
  // Search in localized name
  if (search) {
    filter[`name.${language.code}`] = { $regex: search, $options: 'i' };
  }
  
  const sortOptions = {};
  
  sortOptions[sortBy] = parseInt(sortOrder);
  
  const pujas = await HelicopterPuja.find(filter)
    .populate('helicopter')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit)).lean();
  
  const total = await HelicopterPuja.countDocuments(filter);
  
  return {
    pujas: await transformTranslatedFields(pujas, language.code),
    pagination: {
      total,
      page: parseInt(page),
      pages: Math.ceil(total / parseInt(limit)),
      limit: parseInt(limit)
    }
  };
};

const getHelicopterPujaById = async (pujaId) => {
  const puja = await HelicopterPuja.findById(pujaId);

  if (!puja) {
    throwBadRequestError('Helicopter puja not found');
  }

  const language = { code: 'en' };

  return await transformTranslatedFields(puja, language.code);
};

const updateHelicopterPuja = async (pujaId, updateData, adminId) => {
  const puja = await HelicopterPuja.findById(pujaId);

  if (!puja) {
    throwBadRequestError('Helicopter puja not found');
  }

  if (puja.deletedAt) {
    throwBadRequestError('Helicopter puja already deleted');
  }

  // Translate fields if provided
  const translatedFields = [ 'name', 'description' ];
  const hasTranslatableFields = translatedFields.some(field => updateData[field]);

  if (hasTranslatableFields) {
    const translatedData = await translateDataForStore(translatedFields, updateData);

    translatedFields.forEach(field => {
      if (updateData[field]) {
        delete updateData[field];
      }
    });

    updateData = {
      ...updateData,
      ...translatedData
    };
  }

  // Update the puja
  const updatedPuja = await HelicopterPuja.findByIdAndUpdate(
    pujaId,
    {
      ...updateData,
      updatedBy: adminId
    },
    { new: true }
  );

  return updatedPuja;
};

const deleteHelicopterPuja = async (pujaId) => {
  const puja = await HelicopterPuja.findById(pujaId);

  if (!puja) {
    throwBadRequestError('Helicopter puja not found');
  }

  if (puja.deletedAt) {
    throwBadRequestError('Helicopter puja already deleted');
  }

  //* Soft delete helicopter puja 
  puja.deletedAt = new Date();
  puja.status = 'INACTIVE';
  await puja.save();

  return puja;
};

module.exports = {
  createHelicopterPuja,
  listHelicopterPujas,
  getHelicopterPujaById,
  updateHelicopterPuja,
  deleteHelicopterPuja
};