const joi = require('joi');
const mongoose = require('mongoose');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const createHelicopterPujaSchema = joi.object({
  helicopter: joi.string().custom(validateObjectId).required().messages({
    'any.invalid': 'Invalid helicopter ID format',
    'any.required': 'Helicopter ID is required'
  }),
  name: joi.string().required().messages({
    'any.required': 'Name is required'
  }),
  description: joi.string().required().messages({
    'any.required': 'Description is required'
  }),
  duration: joi.object({
    value: joi.number().min(1).required().messages({
      'number.min': 'Duration value must be at least 1',
      'any.required': 'Duration value is required'
    }),
    unit: joi.string().valid('MINS', 'HOURS').required().messages({
      'any.only': 'Duration unit must be either MINS or HOURS',
      'any.required': 'Duration unit is required'
    })
  }).required().messages({
    'any.required': 'Duration is required'
  }),
  price: joi.number().min(0).required().messages({
    'number.min': 'Price cannot be negative',
    'any.required': 'Price is required'
  })
});

const listHelicopterPujasSchema = joi.object({
  page: joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be greater than or equal to 1'
    }),
  limit: joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be greater than or equal to 1',
      'number.max': 'Limit cannot exceed 100 records per page'
    }),
  search: joi.string()
    .trim()
    .allow('')
    .messages({
      'string.base': 'Search term must be a string'
    }),
  sortBy: joi.string()
    .valid('name', 'createdAt', 'updatedAt', 'price')
    .default('createdAt')
    .messages({
      'string.base': 'Sort field must be a string',
      'any.only': 'Sort field must be one of: name, createdAt, updatedAt or price'
    }),
  sortOrder: joi.number()
    .valid(1, -1)
    .default(-1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    }),
  status: joi.string()
    .valid('ACTIVE', 'INACTIVE')
    .messages({
      'any.only': 'Status must be either ACTIVE or INACTIVE'
    }),
});

const updateHelicopterPujaSchema = joi.object({
  name: joi.string().messages({
    'string.empty': 'Name is required',
    'any.required': 'Name is required'
  }),
  description: joi.string().messages({
    'string.empty': 'Description is required',
    'any.required': 'Description is required'
  }),
  duration: joi.object({
    value: joi.number().min(1).messages({
      'number.min': 'Duration value must be at least 1'
    }),
    unit: joi.string().valid('MINS', 'HOURS').messages({
      'any.only': 'Duration unit must be either MINS or HOURS'
    })
  }),
  price: joi.number().min(0).messages({
    'number.min': 'Price cannot be negative'
  }),
  status: joi.string().valid('ACTIVE', 'INACTIVE').messages({
    'any.only': 'Status must be either ACTIVE or INACTIVE'
  })
}).min(1);

const validateId = joi.string().custom(validateObjectId).required().messages({
  'any.invalid': 'Invalid helicopter puja ID format',
  'any.required': 'Helicopter puja ID is required'
});

module.exports = {
  createHelicopterPujaSchema,
  listHelicopterPujasSchema,
  updateHelicopterPujaSchema,
  validateId
};