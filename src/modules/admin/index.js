const express = require('express');
const router = express.Router();
const authRoutes = require('./auth');
const bannerRoutes = require('./banner');
const poojaRecordingRoutes = require('./poojaRecording');
const darshanScheduleRoutes = require('./darshanSchedule');
const poojaScheduleRoutes = require('./poojaSchedule');
const shopRoutes = require('./shop');
const liveDarshanController = require('../templeAdmin/liveDarshan/controller');
const { isAdminOrSuperAdmin } = require('../../middleware/roleCheck');
const auth = require('../../middleware/auth');
const discountRoutes = require('./discount');
const templeSettlementRoutes = require('./templeSettlement');
const offeringRoutes = require('./offering');
const approvalCenter = require('./centralizedApprovalCenter');
const auditLogRoutes = require('./auditLog');
const helicopterRoutes = require('./helicopter');
// const helicopterPujaRoutes = require('./helicopterPuja');
const notificationRoutes = require('./notification');
const userAccountManagementRoutes = require('./userAccountManagement');
const kioskUserAccountManagementRoutes = require('./kioskUserAccountManagement');
const dashboardRoutes = require('./dashboard'); 
const pdfRoutes = require('./pdf');

router.use('/auth', authRoutes);
router.use('/banners', bannerRoutes);
router.use('/darshan-schedules', darshanScheduleRoutes);
router.use('/pooja-schedules', poojaScheduleRoutes);
router.use('/pooja-recording', poojaRecordingRoutes);

router.patch('/live-darshan/update/:id',auth, isAdminOrSuperAdmin, liveDarshanController.updateStatusByAdmin);
router.use('/shop', shopRoutes);
router.use('/discount', discountRoutes);
router.use('/temple-settlement', templeSettlementRoutes);
router.use('/offering', offeringRoutes);
router.use('/approval-center', approvalCenter);
router.use('/audit-logs', auditLogRoutes);
router.use('/helicopter', helicopterRoutes);
// router.use('/helicopter-puja', helicopterPujaRoutes);
router.use('/notification', notificationRoutes);
router.use('/user-account', userAccountManagementRoutes);
router.use('/kiosk-user-account', kioskUserAccountManagementRoutes);
router.use('/dashboard', dashboardRoutes);
router.use('/pdf', pdfRoutes);

module.exports = router;
