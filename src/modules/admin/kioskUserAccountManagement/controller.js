const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const kioskUserAccountManagementService = require('./service');
const { updateKioskUserAccountStatusSchema, validateId } = require('./validation');
const { SUCCESS } = commonConstants;
const { saveAuditLog } = require('../../../utils/auditLogger');
const { auditLogAction } = require('../../../constants/dbEnums');

//* 1. Function to update kiosk user account status 
const updateUserAccountStatus = async (req, res) => {
  try {
    const { error } = updateKioskUserAccountStatusSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const { error: idError } = validateId.validate(req.params.id);

    if (idError) {
      return res.status(400).json({ 
        status: false,
        message: idError.details[0].message 
      });
    }

    const user = await kioskUserAccountManagementService.updateKioskUserAccountStatus(req.params.id, req.body.status);

    //* Save audit log 
    const detail = `Kiosk user account ${user.email} ${req.body.status === 'ACTIVATE' ? 'activated' : 'deactivated'} successfully`;
    const model = 'KioskUser';

    await saveAuditLog(req, req.user.id, auditLogAction.KIOSK_USER_ACCOUNT_UPDATED, detail, model);

    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: messages.SUCCESS, 
      status: true, 
      data: user 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  updateUserAccountStatus
};