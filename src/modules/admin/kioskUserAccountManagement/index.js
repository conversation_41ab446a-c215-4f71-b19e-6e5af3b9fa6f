const express = require('express');
const router = express.Router();
const kioskUserAccountManagementController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

router.put('/:id/status', auth, isAdminOrSuperAdmin, kioskUserAccountManagementController.updateUserAccountStatus);

module.exports = router;