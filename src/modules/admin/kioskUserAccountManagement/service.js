const KioskUser = require('../../../models/KioskUser');
const { throwBadRequestError } = require('../../../errors');

//* 1. Function to update kiosk user account status 
const updateKioskUserAccountStatus = async (userId, status) => {
  const kioskUser = await KioskUser.findById(userId);

  if (!kioskUser) {
    throwBadRequestError('Kiosk user not found');
  }

  if (status === 'ACTIVATE') {
    
    //* Check if user is already active
    if (kioskUser.deletedAt === null) {
      throwBadRequestError('Kiosk user is already active');
    }

    kioskUser.deletedAt = null;

  } else if (status === 'DEACTIVATE') {
    
    //* Check if user is already deactivated
    if (kioskUser.deletedAt) {
      throwBadRequestError('Kiosk user is already deactivated');
    }

    kioskUser.deletedAt = new Date();
    kioskUser.status = 'INACTIVE';
  }

  await kioskUser.save();

  return kioskUser;
};

module.exports = {
  updateKioskUserAccountStatus
};