const joi = require('joi');
const mongoose = require('mongoose');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string().custom(validateObjectId).required().messages({
  'any.invalid': 'Invalid kiosk user ID format',
  'any.required': 'Kiosk user ID is required'
});

const updateKioskUserAccountStatusSchema = joi.object({ status: joi.string()
  .valid('ACTIVATE', 'DEACTIVATE')
  .required()
  .messages({
    'any.required': 'Status is required',
    'any.only': 'Status must be either ACTIVATE or DEACTIVATE'
  })
});

module.exports = {
  validateId,
  updateKioskUserAccountStatusSchema
};
