const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const { getNotificationsSchema, notificationIdSchema } = require('./validation');
const notificationService = require('./service');

const getNotifications = async (req, res) => {
  try {
    const { error } = getNotificationsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await notificationService.getNotifications(req.query, req.user.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.NOTIFICATIONS_RETRIEVED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const markNotificationAsRead = async (req, res) => {
  try {
    const { error } = notificationIdSchema.validate(req.params);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await notificationService.markNotificationAsRead(req.params.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.NOTIFICATION_MARKED_AS_READ,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const markAllNotificationsAsRead = async (req, res) => {
  try {
    await notificationService.markAllNotificationsAsRead(req.user.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.ALL_NOTIFICATIONS_MARKED_AS_READ,
      status: true,
      data: {}
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead
};