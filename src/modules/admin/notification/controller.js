const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const { getNotificationsSchema, notificationIdSchema, broadcastNotificationSchema } = require('./validation');
const notificationService = require('./service');
const { saveAuditLog } = require('../../../utils/auditLogger');
const { auditLogAction } = require('../../../constants/dbEnums');

const getNotifications = async (req, res) => {
  try {
    const { error } = getNotificationsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await notificationService.getNotifications(req.query, req.user.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.NOTIFICATIONS_RETRIEVED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const markNotificationAsRead = async (req, res) => {
  try {
    const { error } = notificationIdSchema.validate(req.params);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await notificationService.markNotificationAsRead(req.params.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.NOTIFICATION_MARKED_AS_READ,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const markAllNotificationsAsRead = async (req, res) => {
  try {
    await notificationService.markAllNotificationsAsRead(req.user.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.ALL_NOTIFICATIONS_MARKED_AS_READ,
      status: true,
      data: {}
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const broadcastNotification = async (req, res) => {
  try {
    const { error } = broadcastNotificationSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await notificationService.broadcastNotification(req.body);

    //* Save audit log 
    const detail = 'Broadcast notification created successfully';
    const model = 'Notification';

    await saveAuditLog(req, req.user.id, auditLogAction.NOTIFICATION_BROADCASTED, detail, model);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getNotifications,
  markNotificationAsRead,
  broadcastNotification,
  markAllNotificationsAsRead
};