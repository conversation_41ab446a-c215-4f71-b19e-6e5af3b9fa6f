const express = require('express');
const router = express.Router();
const notificationController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

router.get('/list', auth, isAdminOrSuperAdmin, notificationController.getNotifications);
router.patch('/read/:id', auth, isAdminOrSuperAdmin, notificationController.markNotificationAsRead);
router.post('/mark-all-read', auth, isAdminOrSuperAdmin, notificationController.markAllNotificationsAsRead);

module.exports = router;