const express = require('express');
const router = express.Router();
const notificationController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin, isSuperAdmin } = require('../../../middleware/roleCheck');

router.get('/list', auth, isAdminOrSuperAdmin, notificationController.getNotifications);
router.patch('/read/:id', auth, isAdminOrSuperAdmin, notificationController.markNotificationAsRead);
router.post('/mark-all-read', auth, isAdminOrSuperAdmin, notificationController.markAllNotificationsAsRead);
router.post('/broadcast', auth, isSuperAdmin, notificationController.broadcastNotification);

module.exports = router;