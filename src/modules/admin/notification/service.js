const Notification = require('../../../models/Notification');
const { transformTranslatedFields } = require('../../../utils/localizer');
const { throwBadRequestError } = require('../../../errors');

const getNotifications = async (query, adminId) => {
  const { page = 1, limit = 10, isRead = '', sortBy = 'createdAt', sortOrder = -1, notificationType } = query;

  const skip = (parseInt(page) - 1) * parseInt(limit);
  const sort = {};

  sort[sortBy] = parseInt(sortOrder);

  const filter = {
    userId: adminId
  };

  if (isRead !== '') {
    filter.isRead = isRead;
  }

  if (notificationType) {
    filter.notificationType = notificationType;
  }

  const notifications = await Notification.find(filter)
    .skip(skip)
    .limit(parseInt(limit))
    .sort(sort)
    .collation({ locale: 'en', strength: 1 })
    .lean();

  const language = { code: 'en' };

  const total = await Notification.countDocuments(filter);

  return {
    notifications: await transformTranslatedFields(notifications, language.code),
    pagination: {
      total,
      unreadCount: await Notification.countDocuments({ userId: adminId, isRead: false }),
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

const markNotificationAsRead = async (id) => {
  
  const notification = await Notification.findById(id);

  if (!notification) {
    throwBadRequestError('Notification not found');
  }

  if (notification.type !== 'SUPER_ADMIN' && notification.type !== 'ADMIN') {
    throwBadRequestError('Notification not found');
  }

  if (notification.isRead) {
    throwBadRequestError('Notification already read');
  }

  await Notification.updateOne(
    { _id: id },
    { isRead: true },
    { new: true }
  );

  return notification;
};

const markAllNotificationsAsRead = async () => {
  await Notification.updateMany(
    { type: { $in: [ 'SUPER_ADMIN', 'ADMIN' ] }, isRead: false },
    { isRead: true },
    { new: true }
  );
};

module.exports = {
  getNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead
};