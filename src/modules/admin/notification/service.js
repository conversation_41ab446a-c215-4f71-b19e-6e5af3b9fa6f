const Notification = require('../../../models/Notification');
const { transformTranslatedFields } = require('../../../utils/localizer');
const { throwBadRequestError } = require('../../../errors');
const { sendFirebasePushMulticast } = require('../../../utils/firebase_cm');
const { saveAuditLog } = require('../../../utils/auditLogger');
const Token = require('../../../models/Token');
const User = require('../../../models/User');
const { broadcastNotificationType } = require('../../../constants/dbEnums');
const Booking = require('../../../models/Booking');

const getNotifications = async (query, adminId) => {
  const { page = 1, limit = 10, isRead = '', sortBy = 'createdAt', sortOrder = -1, notificationType } = query;

  const skip = (parseInt(page) - 1) * parseInt(limit);
  const sort = {};

  sort[sortBy] = parseInt(sortOrder);

  const filter = {
    userId: adminId
  };

  if (isRead !== '') {
    filter.isRead = isRead;
  }

  if (notificationType) {
    filter.notificationType = notificationType;
  }

  const notifications = await Notification.find(filter)
    .skip(skip)
    .limit(parseInt(limit))
    .sort(sort)
    .collation({ locale: 'en', strength: 1 })
    .lean();

  const language = { code: 'en' };

  const total = await Notification.countDocuments(filter);

  return {
    notifications: await transformTranslatedFields(notifications, language.code),
    pagination: {
      total,
      unreadCount: await Notification.countDocuments({ userId: adminId, isRead: false }),
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

const markNotificationAsRead = async (id) => {
  
  const notification = await Notification.findById(id);

  if (!notification) {
    throwBadRequestError('Notification not found');
  }

  if (notification.type !== 'SUPER_ADMIN' && notification.type !== 'ADMIN') {
    throwBadRequestError('Notification not found');
  }

  if (notification.isRead) {
    throwBadRequestError('Notification already read');
  }

  await Notification.updateOne(
    { _id: id },
    { isRead: true },
    { new: true }
  );

  return notification;
};

const markAllNotificationsAsRead = async () => {
  await Notification.updateMany(
    { type: { $in: [ 'SUPER_ADMIN', 'ADMIN' ] }, isRead: false },
    { isRead: true },
    { new: true }
  );
};

const broadcastNotification = async (body) => {
  const { title, message, notificationType, push, email, whatsapp, inApp } = body;

  if (push) {

    if (notificationType === broadcastNotificationType.RELIGIOUS_DAY_MORNING) {

      const users = await User.find({}).select('_id');
      const userIds = users.map(user => user._id.toString());

      const bookings = await Booking.find({ user: { $in: userIds } }).select('user');
      const bookingUserIds = bookings.map(booking => booking.user.toString());

      const uniqueBookingUserIds = [ ...new Set(bookingUserIds) ];
      const usersToNotify = userIds.filter(userId => !uniqueBookingUserIds.includes(userId));

      const tokens = await Token.find({ userId: { $in: usersToNotify }, fcmToken: { $ne: null, $exists: true } }).select('fcmToken');
      const fcmTokens = [ ...new Set(tokens.map(t => t.fcmToken)) ];

      await sendFirebasePushMulticast(message, title, fcmTokens, '', { type: notificationType });
    }
  }

  if (inApp) {

    const users = await User.find({}).select('_id');
    
    const notificationPromises = users.map(user => {
      return Notification.create({
        userId: user._id,
        title,
        body: message,
        type: 'USER',
        isRead: false,
        notificationType: 'BROADCAST_NOTIFICATION',
      });
    });

    await Promise.all(notificationPromises);
  }

  if (email) {
    // TODO: Send email
  }

  if (whatsapp) {
    // TODO: Send whatsapp
  }

  return { success: true };
};

module.exports = {
  getNotifications,
  markNotificationAsRead,
  broadcastNotification,
  markAllNotificationsAsRead
};