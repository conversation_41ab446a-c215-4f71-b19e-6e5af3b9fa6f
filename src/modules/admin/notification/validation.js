const joi = require('joi');
const mongoose = require('mongoose');
const { notificationType } = require('../../../constants/dbEnums');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const getNotificationsSchema = joi.object({
  page: joi.number().integer().min(1).default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
  limit: joi.number().integer().min(1).max(100).default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
  isRead: joi.boolean().allow('').optional()
    .messages({
      'boolean.base': 'Is read must be a boolean'
    }),
  sortBy: joi.string().valid('createdAt')
    .messages({
      'any.only': 'Sort by field must be createdAt'
    }),
  sortOrder: joi.number().valid(1, -1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    }),
  notificationType: joi.string().valid(...Object.values(notificationType))
    .messages({
      'any.only': `Notification type must be one of: ${Object.values(notificationType).join(', ')}`
    })
});

const notificationIdSchema = joi.object({
  id: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Notification ID is required',
      'any.required': 'Notification ID is required'
    })
});

module.exports = {
  getNotificationsSchema,
  notificationIdSchema
};