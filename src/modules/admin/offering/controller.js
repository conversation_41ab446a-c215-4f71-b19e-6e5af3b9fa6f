const offeringService = require('./service');
const { createOfferingSchema, listOfferingsSchema, validateId, updateOfferingSchema, uploadUrlSchema } = require('./validation');
const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { SUCCESS } = commonConstants;
const { messages } = require('../../../messages');
const { getPresignedUrl } = require('../../../utils/s3Service');
const { saveAuditLog } = require('../../../utils/auditLogger');
const { auditLogAction } = require('../../../constants/dbEnums');

//* 1. Function to create offering
const createOffering = async (req, res) => {
  try {
    const { error } = createOfferingSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }
    
    const data = await offeringService.createOffering(req.body, req.user.id);

    //* Save audit log 
    const detail = `Offering ${data.offering.name.en} created successfully`;
    const model = 'Offering';

    await saveAuditLog(req, req.user.id, auditLogAction.OFFERING_CREATED, detail, model);
    
    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.OFFERING_CREATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 2. Function to list offerings
const listOfferings = async (req, res) => {
  try {
    const { error } = listOfferingsSchema.validate(req.query);
    
    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }
    
    const data = await offeringService.listOfferings(req.query);
    
    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.OFFERINGS_RETRIEVED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 3. Function to get offering by ID
const getOfferingById = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);
      
    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }
      
    const data = await offeringService.getOfferingById(req.params.id);
      
    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.OFFERING_RETRIEVED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 4. Function to update offering
const updateOffering = async (req, res) => {
  try {
    const { error: idError } = validateId.validate(req.params.id);
        
    if (idError) {
      return res.status(400).json({
        status: false,
        message: idError.details[0].message
      });
    }

    const { error } = updateOfferingSchema.validate(req.body);
        
    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }
        
    const data = await offeringService.updateOffering(req.params.id, req.body, req.user.id);

    //* Save audit log 
    const detail = `Offering ${data.name.en} updated successfully`;
    const model = 'Offering';

    await saveAuditLog(req, req.user.id, auditLogAction.OFFERING_UPDATED, detail, model);
        
    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.OFFERING_UPDATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 5. Function to delete offering
const deleteOffering = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);
      
    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }
      
    const data = await offeringService.deleteOffering(req.params.id);

    //* Save audit log 
    const detail = `Offering ${data.name.en} deleted successfully`;
    const model = 'Offering';

    await saveAuditLog(req, req.user.id, auditLogAction.OFFERING_DELETED, detail, model);
      
    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.OFFERING_DELETED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 6. Get upload URL for offering image
const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }
    
    const { error } = uploadUrlSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }
    
    const { extension } = req.body;

    const uploadData = await getPresignedUrl(extension, 'offerings');

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Upload URL generated successfully',
      status: true,
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createOffering,  
  listOfferings,
  getOfferingById,
  updateOffering,
  deleteOffering,
  getUploadUrl
};