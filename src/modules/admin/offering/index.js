const express = require('express');
const router = express.Router();
const offeringController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

router.post('/create', auth, isAdminOrSuperAdmin, offeringController.createOffering);
router.get('/list', auth, isAdminOrSuperAdmin, offeringController.listOfferings);
router.get('/:id', auth, isAdminOrSuperAdmin, offeringController.getOfferingById);
router.put('/:id', auth, isAdminOrSuperAdmin, offeringController.updateOffering);
router.delete('/:id', auth, isAdminOrSuperAdmin, offeringController.deleteOffering);
router.post('/upload-url', auth, isAdminOrSuperAdmin, offeringController.getUploadUrl);
router.patch('/:id/status', auth, isAdminOrSuperAdmin, offeringController.updateOfferingStatus);

module.exports = router;