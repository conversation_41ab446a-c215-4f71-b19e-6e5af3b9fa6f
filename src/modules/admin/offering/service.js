const Offering = require('../../../models/Offering');
const Temple = require('../../../models/Temple');
const { throwNotFoundError, throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const { translateDataForStore } = require('../../../utils/translateInput');
const { transformTranslatedFields } = require('../../../utils/localizer');

//* 1. Function to create offering
const createOffering = async (offeringData, adminId) => {
  // Check if temple exists
  const temple = await Temple.findById(offeringData.temple);

  if (!temple) {
    throwNotFoundError(messages.TEMPLE_NOT_FOUND || 'Temple not found');
  }
  
  // Check if offering with same name already exists for this temple
  const existingOffering = await Offering.findOne({ 
    'name.en': offeringData.name, 
    temple: offeringData.temple 
  });
  
  if (existingOffering) {
    throwBadRequestError(`An offering with name "${offeringData.name}" already exists for this temple`);
  }
  
  // Process image if provided
  if (offeringData.image) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = `${process.env.MEDIA_URL}/${offeringData.image}`;

    offeringData.image = cloudFrontUrl;
  }

  const translatedData = await translateDataForStore([ 'name', 'description' ], offeringData);

  offeringData.name = translatedData.name;
  offeringData.description = translatedData.description;
  
  // Create new offering
  const offering = await Offering.create({
    ...offeringData,
    createdBy: adminId,
    updatedBy: adminId
  });

  return {
    offering,
    temple
  };
};

//* 2. Function to list offerings
const listOfferings = async (queryParams) => {
  const { page = 1, limit = 10, search, temple, isActive, sortBy, sortOrder } = queryParams;
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder);

  const language = { code: 'en' };

  const query = {
    deletedAt: null
  };

  if (search) {
    query.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`description.${language.code}`]: { $regex: search, $options: 'i' } }
    ];
  }

  if (temple) {
    query.temple = temple;
  }

  if (isActive) {
    query.isActive = isActive;
  }

  const offerings = await Offering.find(query)
    .populate('temple')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  const total = await Offering.countDocuments(query);

  // Localize offerings
  const localizedOfferings = await transformTranslatedFields(offerings, language.code);

  return {
    offerings: localizedOfferings,
    pagination: {
      total,
      page: parseInt(page),
      pages: Math.ceil(total / limit),
      limit: parseInt(limit)
    }
  };
};

//* 3. Function to get offering by ID
const getOfferingById = async (offeringId) => {
  const offering = await Offering.findById(offeringId)
    .populate('temple');

  if (!offering) {
    throwNotFoundError('Offering not found');
  }

  const language = { code: 'en' };

  const localizedOffering = await transformTranslatedFields(offering, language.code);

  return localizedOffering;
};

//* 4. Function to update offering
const updateOffering = async (offeringId, updateData, adminId) => {
  const offering = await Offering.findById(offeringId);
  
  if (!offering) {
    throwNotFoundError('Offering not found');
  }

  // Check if name is being updated and if it already exists for another offering
  if (updateData.name && updateData.name !== offering.name.en) {
    const nameExists = await Offering.findOne({ 
      'name.en': updateData.name, 
      _id: { $ne: offeringId } 
    });

    if (nameExists) {
      throwBadRequestError('An offering with this name already exists');
    }
  }

  // Check if temple is being updated and if it exists
  if (updateData.temple) {
    const temple = await Temple.findById(updateData.temple);

    if (!temple) {
      throwNotFoundError('Temple not found');
    }
  }

  // Process image if provided
  if (updateData.image) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = `${process.env.MEDIA_URL}/${updateData.image}`;

    updateData.image = cloudFrontUrl;
  }

  if (updateData.name) {
    const translatedData = await translateDataForStore([ 'name' ], updateData);

    updateData.name = translatedData.name;
  }

  if (updateData.description) {
    const translatedData = await translateDataForStore([ 'description' ], updateData);

    updateData.description = translatedData.description;
  }

  // Update offering
  const updatedOffering = await Offering.findByIdAndUpdate(
    offeringId,
    {
      ...updateData,
      updatedBy: adminId
    },
    { new: true }
  );

  return updatedOffering;
};

//* 5. Function to delete offering
const deleteOffering = async (offeringId) => {
  const offering = await Offering.findById(offeringId);
  
  if (!offering) {
    throwNotFoundError('Offering not found');
  }

  if (offering.deletedAt || !offering.isActive) {
    throwBadRequestError('Offering already deleted or inactive');
  }

  //* Soft delete offering 
  offering.deletedAt = new Date();
  offering.isActive = false;
  await offering.save();

  return offering;
};

//* 6. Function to update offering status
const updateOfferingStatus = async (offeringId, body) => {
  const { status } = body;

  const offering = await Offering.findById(offeringId);
  
  if (!offering) {
    throwNotFoundError('Offering not found');
  }

  if (status === 'ACTIVATE') {
    if (offering.deletedAt === null) {
      throwBadRequestError('Offering is already active');
    }

    offering.deletedAt = null;
  } else if (status === 'DEACTIVATE') {
    if (offering.deletedAt) {
      throwBadRequestError('Offering is already deactivated');
    }

    offering.deletedAt = new Date();
  }

  await offering.save();

  return offering;
};

module.exports = {
  createOffering,
  listOfferings,
  getOfferingById,
  updateOffering,
  deleteOffering,
  updateOfferingStatus
};

