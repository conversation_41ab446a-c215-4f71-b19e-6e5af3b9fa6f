const joi = require('joi');
const mongoose = require('mongoose');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string()
  .custom(validateObjectId)
  .required()
  .messages({
    'any.invalid': 'Invalid offering ID format',
    'any.required': 'Offering ID is required'
  });

const createOfferingSchema = joi.object({
  name: joi.string()
    .required()
    .trim()
    .max(200)
    .messages({
      'string.base': 'Offering name must be a string',
      'string.empty': 'Offering name is required',
      'string.max': 'Offering name cannot exceed 200 characters',
      'any.required': 'Offering name is required'
    }),
  temple: joi.string()
    .custom(validateObjectId)
    .required()
    .messages({
      'string.empty': 'Temple ID is required',
      'any.required': 'Temple ID is required',
      'any.invalid': 'Invalid temple ID format'
    }),
  description: joi.string()
    .required()
    .trim()
    .max(10000)
    .messages({
      'string.base': 'Description must be a string',
      'string.empty': 'Description is required',
      'string.max': 'Description cannot exceed 10000 characters',
      'any.required': 'Description is required'
    }),
  image: joi.string()
    .required()
    .messages({
      'string.base': 'Image path must be a string'
    }),
  amount: joi.number()
    .required()
    .min(0)
    .messages({
      'number.base': 'Amount must be a number',
      'number.min': 'Amount cannot be negative',
      'any.required': 'Amount is required'
    })
});

const listOfferingsSchema = joi.object({
  page: joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be greater than or equal to 1'
    }),
  limit: joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be greater than or equal to 1',
      'number.max': 'Limit cannot exceed 100 records per page'
    }),
  search: joi.string()
    .trim()
    .allow('')
    .optional()
    .messages({
      'string.base': 'Search term must be a string'
    }),
  temple: joi.string()
    .custom(validateObjectId)
    .allow('')
    .optional()
    .messages({
      'any.invalid': 'Invalid temple ID format'
    }),
  isActive: joi.boolean()
    .allow('')
    .optional()
    .messages({
      'boolean.base': 'Status must be a boolean'
    }),
  sortBy: joi.string()
    .valid('name', 'amount', 'createdAt', 'updatedAt')
    .default('createdAt')
    .messages({
      'string.base': 'Sort field must be a string',
      'any.only': 'Sort field must be one of: name, amount, createdAt, or updatedAt'
    }),
  sortOrder: joi.number()
    .valid(1, -1)
    .default(-1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    })
});

const updateOfferingSchema = joi.object({
  name: joi.string()
    .trim()
    .max(200)
    .messages({
      'string.base': 'Offering name must be a string',
      'string.empty': 'Offering name cannot be empty',
      'string.max': 'Offering name cannot exceed 200 characters'
    }),
  temple: joi.string()
    .custom(validateObjectId)
    .messages({
      'string.empty': 'Temple ID cannot be empty',
      'any.invalid': 'Invalid temple ID format'
    }),
  description: joi.string()
    .trim()
    .max(10000)
    .messages({
      'string.base': 'Description must be a string',
      'string.empty': 'Description cannot be empty',
      'string.max': 'Description cannot exceed 10000 characters'
    }),
  image: joi.string()
    .messages({
      'string.base': 'Image path must be a string',
      'string.empty': 'Image path cannot be empty'
    }),
  amount: joi.number()
    .min(0)
    .messages({
      'number.base': 'Amount must be a number',
      'number.min': 'Amount cannot be negative'
    }),
  isActive: joi.boolean()
    .messages({
      'boolean.base': 'Status must be a boolean value (true or false)'
    })
}).min(1).messages({
  'object.min': 'At least one field is required for updating an offering'
});

const uploadUrlSchema = joi.object({
  extension: joi.string()
    .valid('jpeg', 'jpg', 'png')
    .required()
    .messages({
      'any.required': 'File extension is required',
      'any.only': 'Invalid file extension. Only JPEG, JPG and PNG are allowed'
    })
});

module.exports = {
  validateId,
  createOfferingSchema,
  listOfferingsSchema,
  updateOfferingSchema,
  uploadUrlSchema
};
