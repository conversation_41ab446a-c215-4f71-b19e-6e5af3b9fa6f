const { generateSampleTestPDF, getPDFGenerationStatus } = require('./service');
const { generateSamplePDFSchema } = require('./validation');
const { throwBadRequestError } = require('../../../errors');

/**
 * Generate a sample PDF for testing
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const generateSamplePDF = async (req, res) => {
  try {
    // Validate request body
    const { error, value } = generateSamplePDFSchema.validate(req.body);
    
    if (error) {
      throwBadRequestError(error.details[0].message);
    }

    const { adminEmail, testTitle, testMessage } = value;

    // Generate the sample PDF
    const result = await generateSampleTestPDF({
      adminEmail,
      testTitle,
      testMessage
    });

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.data
    });
  } catch (error) {
    console.error('Error in generateSamplePDF controller:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

/**
 * Get PDF generation system status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getPDFStatus = async (req, res) => {
  try {
    const result = await getPDFGenerationStatus();

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.data
    });
  } catch (error) {
    console.error('Error in getPDFStatus controller:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

module.exports = {
  generateSamplePDF,
  getPDFStatus
};
