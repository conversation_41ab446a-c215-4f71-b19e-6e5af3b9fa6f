const { generateSamplePDF } = require('../../../utils/pdfGenerator');
const { throwBadRequestError } = require('../../../errors');

/**
 * Generate a sample PDF for testing purposes
 * @param {Object} params - Parameters for PDF generation
 * @param {String} params.adminEmail - Admin email for testing
 * @param {String} params.testTitle - Optional title for the test PDF
 * @param {String} params.testMessage - Optional message for the test PDF
 * @returns {Promise<Object>} Generated PDF information
 */
const generateSampleTestPDF = async ({ adminEmail, testTitle, testMessage }) => {
  // Validate admin email
  if (!adminEmail) {
    throwBadRequestError('Admin email is required');
  }

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!emailRegex.test(adminEmail)) {
    throwBadRequestError('Please provide a valid email address');
  }

  try {
    // Prepare sample data
    const sampleData = {
      adminEmail,
      testTitle: testTitle || 'Sample PDF Generation Test',
      testMessage: testMessage || 'This is a test PDF generated using html-pdf-node and handlebars template engine. The PDF generation flow is working correctly!',
      generatedAt: new Date()
    };

    // Generate the PDF
    const s3Key = await generateSamplePDF(sampleData);

    // Construct the full URL
    const pdfUrl = `${process.env.MEDIA_URL}/${s3Key}`;

    return {
      success: true,
      message: 'Sample PDF generated successfully',
      data: {
        pdfUrl,
        s3Key,
        adminEmail,
        generatedAt: sampleData.generatedAt,
        testTitle: sampleData.testTitle,
        testMessage: sampleData.testMessage
      }
    };
  } catch (error) {
    console.error('Error generating sample PDF:', error);
    throw new Error('Failed to generate sample PDF. Please check the server logs for more details.');
  }
};

/**
 * Get PDF generation status and information
 * @returns {Promise<Object>} PDF generation system status
 */
const getPDFGenerationStatus = async () => {
  try {
    return {
      success: true,
      message: 'PDF generation system is operational',
      data: {
        engine: 'html-pdf-node',
        templateEngine: 'handlebars',
        storageProvider: 'AWS S3',
        supportedFormats: [ 'A4', 'Letter', 'Legal' ],
        features: [
          'HTML to PDF conversion',
          'Handlebars template integration',
          'S3 upload functionality',
          'Logo and image embedding',
          'Responsive design support',
          'Dynamic data binding'
        ],
        lastChecked: new Date()
      }
    };
  } catch (error) {
    console.error('Error checking PDF generation status:', error);
    throw new Error('Failed to check PDF generation status');
  }
};

module.exports = {
  generateSampleTestPDF,
  getPDFGenerationStatus
};
