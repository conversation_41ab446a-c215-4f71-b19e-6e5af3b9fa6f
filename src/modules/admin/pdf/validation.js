const joi = require('joi');

/**
 * Validation schema for generating sample PDF
 */
const generateSamplePDFSchema = joi.object({
  adminEmail: joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'string.empty': 'Admin email is required',
      'any.required': 'Admin email is required'
    }),
  testTitle: joi.string()
    .min(3)
    .max(100)
    .optional()
    .messages({
      'string.min': 'Test title must be at least 3 characters long',
      'string.max': 'Test title cannot exceed 100 characters'
    }),
  testMessage: joi.string()
    .min(10)
    .max(500)
    .optional()
    .messages({
      'string.min': 'Test message must be at least 10 characters long',
      'string.max': 'Test message cannot exceed 500 characters'
    })
});

module.exports = {
  generateSamplePDFSchema
};
