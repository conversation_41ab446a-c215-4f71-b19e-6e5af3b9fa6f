const express = require('express');
const router = express.Router();
const poojaRecordingController = require('../../kiosk/poojaRecording/controller');
const auth = require('../../../middleware/auth');
const { isSuperAdmin } = require('../../../middleware/roleCheck');
const kioskController = require('../../kiosk/controller');

router.get('/', auth, isSuperAdmin, poojaRecordingController.listRecordings);
router.get('/:id', auth, isSuperAdmin, poojaRecordingController.getRecordingById);
router.patch('/:id', auth, isSuperAdmin, poojaRecordingController.updateRecordingStatus);

router.post('/upload', auth, isSuperAdmin, kioskController.getUploadUrl);
router.delete('/remove', auth, isSuperAdmin, kioskController.deletePoojaVideo);

module.exports = router;
