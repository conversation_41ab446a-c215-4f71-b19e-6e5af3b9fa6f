const express = require('express');
const router = express.Router();
const poojaController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

router.get('/', auth, isAdminOrSuperAdmin, poojaController.listPoojaSchedules);
router.get('/:id', auth, isAdminOrSuperAdmin, poojaController.getPoojaScheduleById);
router.post('/', auth, isAdminOrSuperAdmin, poojaController.createPoojaSchedule);
router.put('/:id', auth, isAdminOrSuperAdmin, poojaController.updatePoojaSchedule);
router.delete('/:id', auth, isAdminOrSuperAdmin, poojaController.deletePoojaSchedule);

module.exports = router;