const PoojaSchedule = require('../../../models/PoojaSchedule');
const Temple = require('../../../models/Temple');
const { throwBadRequestError } = require('../../../errors');
const { default: mongoose } = require('mongoose');
const { translateDataForStore } = require('../../../utils/translateInput');
const { transformTranslatedFields } = require('../../../utils/localizer');
const states = require('../../../../data/states.json');

const createPoojaSchedule = async (scheduleData, adminId) => {
  // Check if temple exists
  const temple = await Temple.findById(scheduleData.temple);

  if (!temple) {
    throwBadRequestError('Temple not found');
  }

  // Translate fields
  const translatedFields = [ 'name', 'description' ];
  const translatedData = await translateDataForStore(translatedFields, scheduleData);

  translatedFields.forEach(field => {
    if (scheduleData[field]) {
      delete scheduleData[field];
    }
  });

  let code = '0001';

  const state = states.find(state => state.name === temple.state.en);
  const stateCode = state ? state.id : 'XX';

  if (temple.lastServiceCode) {
    code = `${(parseInt(temple.lastServiceCode || 0) + 1).toString().padStart(4, '0')}`;
  }

  const codeType = scheduleData.type === 'PHYSICAL' ? 'PP' : 'VP';

  scheduleData.serviceCode = `S-${stateCode}-${temple.templeCode}-${codeType}-${code}`;

  temple.lastServiceCode = code;
  await temple.save();

  if (scheduleData.promotionalKit) {
    let kitCode = '0001';

    if (temple.lastKitCode) {
      kitCode = `${(parseInt(temple.lastKitCode || 0) + 1).toString().padStart(4, '0')}`;
    }
    temple.lastKitCode = kitCode;
    await temple.save();
    scheduleData.promotionalKit.productCode = `S-${stateCode}-${temple.templeCode}-PB-${kitCode}`;
  }

  scheduleData = {
    ...scheduleData,
    ...translatedData
  };

  // Create pooja schedule
  const poojaSchedule = await PoojaSchedule.create({
    ...scheduleData,
    createdBy: adminId,
    updatedBy: adminId
  });

  return poojaSchedule.populate([
    { path: 'temple', select: 'name location' },
    { path: 'createdBy', select: 'name email' },
    { path: 'updatedBy', select: 'name email' }
  ]);
};

const updatePoojaSchedule = async (scheduleId, updateData, adminId) => {
  const schedule = await PoojaSchedule.findById(scheduleId);
  
  if (!schedule) {
    throwBadRequestError('Puja schedule not found');
  }

  // If temple is being updated, verify it exists
  if (updateData.temple) {
    const temple = await Temple.findById(updateData.temple);

    if (!temple) {
      throwBadRequestError('Temple not found');
    }
  }

  // Translate fields if provided
  const translatedFields = [ 'name', 'description' ];
  const hasTranslatableFields = translatedFields.some(field => updateData[field]);

  if (hasTranslatableFields) {
    const translatedData = await translateDataForStore(translatedFields, updateData);

    translatedFields.forEach(field => {
      if (updateData[field]) {
        delete updateData[field];
      }
    });

    updateData = {
      ...updateData,
      ...translatedData
    };
  }

  // Update the schedule
  const updatedSchedule = await PoojaSchedule.findByIdAndUpdate(
    scheduleId,
    {
      ...updateData,
      updatedBy: adminId
    },
    { new: true }
  ).populate([
    { path: 'temple', select: 'name location' },
    { path: 'createdBy', select: 'name email' },
    { path: 'updatedBy', select: 'name email' }
  ]);

  return updatedSchedule;
};

const deletePoojaSchedule = async (scheduleId) => {
  const schedule = await PoojaSchedule.findById(scheduleId);
  
  if (!schedule) {
    throwBadRequestError('Puja schedule not found');
  }

  if (schedule.deletedAt || schedule.status === 'INACTIVE') {
    throwBadRequestError('Puja schedule already deleted or inactive');
  }

  //* Soft delete puja schedule 
  schedule.deletedAt = new Date();
  schedule.status = 'INACTIVE';
  await schedule.save();

  return schedule;
};

const listPoojaSchedules = async (query = {}) => {
  const { 
    page = 1, 
    limit = 10, 
    search,
    type,
    temple, 
    status,
    dateType,
    startDate,
    endDate,
    specificDate,
    sortBy = 'createdAt', 
    sortOrder = -1,
  } = query;

  const filter = {};

  if (temple) {
    filter.temple = new mongoose.Types.ObjectId(temple);
  }

  if (status) {
    filter.status = status;
  }

  if (type) {
    filter.type = type;
  }

  if (dateType) {
    filter.dateType = dateType;
  }

  if (startDate) {
    filter['dateRange.startDate'] = { $gte: new Date(startDate) };
  }

  if (endDate) {
    filter['dateRange.endDate'] = { $lte: new Date(endDate) };
  }

  if (specificDate) {
    filter.specificDate = new Date(specificDate);
  }

  const aggregatePipeline = [{
    $lookup: {
      from: 'temples',
      localField: 'temple',
      foreignField: '_id',
      as: 'templeDetails'
    }
  },
  {
    $unwind: { path: '$templeDetails', preserveNullAndEmptyArrays: true }
  },
  {
    $lookup: {
      from: 'admins',
      localField: 'createdBy',
      foreignField: '_id',
      as: 'createdBy'
    }
  },
  {
    $unwind: { path: '$createdBy', preserveNullAndEmptyArrays: true }
  },
  {
    $lookup: {
      from: 'admins',
      localField: 'updatedBy',
      foreignField: '_id',
      as: 'updatedBy'
    }
  },
  {
    $unwind: {
      path: '$updatedBy',
      preserveNullAndEmptyArrays: true
    }
  }];

  const matchConditions = {
    $and: [
      { ...filter }
    ]
  };

  const language = { code: 'en' };

  if (search) {
    matchConditions.$and.push({ $or: [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`description.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`templeDetails.name.${language.code}`]: { $regex: search, $options: 'i' } },
    ] });
  }

  aggregatePipeline.push({ $match: matchConditions });

  const sortMongoOrder = parseInt(sortOrder) || -1; // Ensure sortOrder is a number
  
  if (sortBy === 'temple') {
    aggregatePipeline.push({
      $sort: {
        [`templeDetails.name.${language.code}`]: sortMongoOrder
      }
    });
  } else {
    aggregatePipeline.push({
      $sort: {
        [sortBy]: sortMongoOrder
      }
    });
  }

  aggregatePipeline.push({
    $facet: {
      data: [
        { $skip: (parseInt(page) - 1) * parseInt(limit) },
        { $limit: parseInt(limit) },
        {
          $project: {
            _id: 1,
            name: 1,
            description: 1,
            dateType: 1,
            specificDate: 1,
            dateRange: 1,
            type: 1,
            timeSlots: 1,
            pricing: 1,
            occupancyPerSlot: 1,
            status: 1,
            createdAt: 1,
            updatedAt: 1,
            temple: {
              _id: '$templeDetails._id',
              name: '$templeDetails.name',
              location: '$templeDetails.location'
            },
            createdBy: {
              _id: '$createdBy._id',
              name: '$createdBy.name',
              email: '$createdBy.email'
            },
            updatedBy: {
              _id: '$updatedBy._id',
              name: '$updatedBy.name',
              email: '$updatedBy.email'
            }
          }
        }
      ],
      total: [{ $count: 'count' }]
    }
  });

  const [ result ] = await PoojaSchedule.aggregate(aggregatePipeline).collation({ locale: 'en', strength: 1 });

  return {
    schedules: await transformTranslatedFields(result.data, language.code),
    pagination: {
      total: result.total[0]?.count || 0,
      page: parseInt(page),
      pages: Math.ceil((result.total[0]?.count || 0) / limit)
    }
  };
};

const getPoojaScheduleById = async (scheduleId) => {
  const schedule = await PoojaSchedule.findById(scheduleId)
    .populate([
      { path: 'temple', select: 'name location' },
      { path: 'createdBy', select: 'name email' },
      { path: 'updatedBy', select: 'name email' }
    ]).lean();

  if (!schedule) {
    throwBadRequestError('Puja schedule not found');
  }

  const language = { code: 'en' };

  return await transformTranslatedFields(schedule, language.code);
};

module.exports = {
  createPoojaSchedule,
  updatePoojaSchedule,
  deletePoojaSchedule,
  listPoojaSchedules,
  getPoojaScheduleById
};
