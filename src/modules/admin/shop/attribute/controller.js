const { apiResponse, errorApiResponse } = require('../../../../config/responseHandler');
const { commonConstants } = require('../../../../constants/common');
const { messages } = require('../../../../messages');
const attributeService = require('./service');
const { createAttributeSchema, updateAttributeSchema } = require('./validation');
const { SUCCESS } = commonConstants;

/**
 * Create a new attribute
 */
const createAttribute = async (req, res) => {
  try {
    const { error } = createAttributeSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    
    const data = await attributeService.createAttribute({
      adminId,
      ...req.body
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.ATTRIBUTE_CREATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get all attributes
 */
const getAllAttributes = async (req, res) => {
  try {
    const { includeInactive = false } = req.query;
    
    const data = await attributeService.getAllAttributes(includeInactive === 'true');

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get attribute by ID
 */
const getAttributeById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const data = await attributeService.getAttributeById(id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update attribute
 */
const updateAttribute = async (req, res) => {
  try {
    const { error } = updateAttributeSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    const { id } = req.params;
    
    const data = await attributeService.updateAttribute({
      adminId,
      attributeId: id,
      ...req.body
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.ATTRIBUTE_UPDATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete attribute
 */
const deleteAttribute = async (req, res) => {
  try {
    const { id } = req.params;
    
    const data = await attributeService.deleteAttribute(id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.ATTRIBUTE_DELETED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createAttribute,
  getAllAttributes,
  getAttributeById,
  updateAttribute,
  deleteAttribute
};
