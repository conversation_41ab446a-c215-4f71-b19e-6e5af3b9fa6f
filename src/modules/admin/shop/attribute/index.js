const express = require('express');
const router = express.Router();
const attributeController = require('./controller');
const auth = require('../../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../../middleware/roleCheck');

// All routes require admin authentication
router.use(auth, isAdminOrSuperAdmin);

router.post('/', attributeController.createAttribute);
router.get('/', attributeController.getAllAttributes);
router.get('/:id', attributeController.getAttributeById);
router.put('/:id', attributeController.updateAttribute);
router.delete('/:id', attributeController.deleteAttribute);

module.exports = router;
