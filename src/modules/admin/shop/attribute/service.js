const AttributeOption = require('../../../../models/AttributeOption');
const Product = require('../../../../models/Product');
const { throwBadRequestError } = require('../../../../errors');
const { messages } = require('../../../../messages');
const mongoose = require('mongoose');
const Category = require('../../../../models/Category');
const { translateDataForStore } = require('../../../../utils/translateInput');
const { transformTranslatedFields } = require('../../../../utils/localizer');

/**
 * Create a new attribute
 */
const createAttribute = async ({ adminId, name, values }) => {
  // Check if attribute name already exists
  const attributeExists = await AttributeOption.findOne({ 'name.en': name });

  if (attributeExists) {
    throwBadRequestError(messages.ATTRIBUTE_ALREADY_EXISTS);
  }

  // Translate Fields : 

  const translatedData = await translateDataForStore([ 'name' ], { name });

  name = translatedData.name;

  // Create attribute
  const attribute = new AttributeOption({
    name,
    values,
    isActive: true,
    createdBy: adminId
  });

  await attribute.save();

  return attribute;
};

/**
 * Get all attributes
 */
const getAllAttributes = async (includeInactive) => {
  const query = includeInactive ? {} : { isActive: true };
  
  const attributes = await AttributeOption.find(query)
    .sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 }).lean();

  return await transformTranslatedFields(attributes, 'en');
};

/**
 * Get attribute by ID
 */
const getAttributeById = async (attributeId) => {
  // Check if attribute ID is valid
  if (!mongoose.Types.ObjectId.isValid(attributeId)) {
    throwBadRequestError(messages.INVALID_ATTRIBUTE_ID);
  }

  const attribute = await AttributeOption.findById(attributeId)
    .populate('createdBy', 'name email')
    .populate('updatedBy', 'name email').lean();

  if (!attribute) {
    throwBadRequestError(messages.ATTRIBUTE_NOT_FOUND);
  }

  const language = { code: 'en' };

  return await transformTranslatedFields(attribute, language.code);
};

/**
 * Update attribute
 */
const updateAttribute = async ({ adminId, attributeId, name, values, isActive }) => {
  // Check if attribute ID is valid
  if (!mongoose.Types.ObjectId.isValid(attributeId)) {
    throwBadRequestError(messages.INVALID_ATTRIBUTE_ID);
  }

  const attribute = await AttributeOption.findById(attributeId);

  if (!attribute) {
    throwBadRequestError(messages.ATTRIBUTE_NOT_FOUND);
  }

  // Check if name already exists for another attribute
  if (name && name !== attribute.name) {
    const nameExists = await AttributeOption.findOne({ name, _id: { $ne: attributeId } });

    if (nameExists) {
      throwBadRequestError(messages.ATTRIBUTE_NAME_ALREADY_EXISTS);
    }
  }

  if (name) {
    const translatedData = await translateDataForStore([ 'name' ], { name });

    name = translatedData.name;
  }

  // Update attribute fields
  if (name) {
    attribute.name = name;
  }
  if (values) {
    attribute.values = values;
  }
  if (isActive !== undefined) {
    attribute.isActive = isActive;
  }

  // Update metadata
  attribute.updatedBy = adminId;

  await attribute.save();

  return attribute;
};

/**
 * Delete attribute
 */
const deleteAttribute = async (attributeId) => {
  // Check if attribute ID is valid
  if (!mongoose.Types.ObjectId.isValid(attributeId)) {
    throwBadRequestError(messages.INVALID_ATTRIBUTE_ID);
  }

  const attribute = await AttributeOption.findById(attributeId);

  if (!attribute) {
    throwBadRequestError(messages.ATTRIBUTE_NOT_FOUND);
  }

  const categories = await Category.find({ variantTypes: attributeId });

  if (categories.length > 0) {
    throwBadRequestError(messages.ATTRIBUTE_IN_USE);
  }

  // // Check if attribute is used in any products
  const productsWithAttribute = await Product.countDocuments({ 
    variantAttributes: attribute.name.en 
  });
  
  if (productsWithAttribute > 0) {
    throwBadRequestError(messages.ATTRIBUTE_IN_USE);
  }

  // Delete attribute
  await attribute.deleteOne();

  return attribute;
};

module.exports = {
  createAttribute,
  getAllAttributes,
  getAttributeById,
  updateAttribute,
  deleteAttribute
};
