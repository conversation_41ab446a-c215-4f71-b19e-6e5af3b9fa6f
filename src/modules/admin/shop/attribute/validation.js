const joi = require('joi');

const createAttributeSchema = joi.object({
  name: joi.string().max(50).required(),
  values: joi.array().items(joi.string().trim()).min(1).required()
});

const updateAttributeSchema = joi.object({
  name: joi.string().max(50),
  values: joi.array().items(joi.string().trim()).min(1),
  isActive: joi.boolean()
}).min(1);

module.exports = {
  createAttributeSchema,
  updateAttributeSchema
};
