const Category = require('../../../../models/Category');
const Product = require('../../../../models/Product');
const { throwBadRequestError } = require('../../../../errors');
const { messages } = require('../../../../messages');
const mongoose = require('mongoose');
const { processImages } = require('../../../../utils/processImage');
const SubCategory = require('../../../../models/SubCategory');
const { translateDataForStore } = require('../../../../utils/translateInput');
const { transformTranslatedFields } = require('../../../../utils/localizer');

/**
 * Create a new category
 */
const createCategory = async ({ adminId, name, description, image, variantTypes }) => {
  // Check if category name already exists
  const categoryExists = await Category.findOne({ 'name.en': name });

  if (categoryExists) {
    throwBadRequestError(messages.CATEGORY_ALREADY_EXISTS);
  }

  // Process the image if provided
  let processedImage = image;

  if (image) {
    const processedImages = processImages([ image ]);

    processedImage = processedImages[0];
  }

  const translatedData = await translateDataForStore([ 'name', 'description' ], { name, description });

  name = translatedData.name;
  description = translatedData.description;

  // Create category
  const category = new Category({
    name,
    description,
    image: processedImage,
    variantTypes,
    isActive: true,
    createdBy: adminId
  });

  await category.save();

  return category;
};

/**
 * Get all categories
 */
const getAllCategories = async (includeInactive, limit, page, search, sortBy, sortOrder) => {
  const query = includeInactive ? {} : { isActive: true };

  const language = { code: 'en' };

  if (search) {
    query[`name.${language.code}`] = { $regex: search, $options: 'i' };
  }

  const sort = {};

  sort[sortBy] = parseInt(sortOrder) || -1;

  // Calculate skip value for pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);
  
  const categories = await Category.find(query)
    .sort(sort).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit))
    .lean();

  return await transformTranslatedFields(categories, language.code);
};

/**
 * Get category by ID
 */
const getCategoryById = async (categoryId) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID);
  }

  const category = await Category.findById(categoryId)
    .populate('variantTypes', 'name values')
    .populate('createdBy', 'name email')
    .populate('updatedBy', 'name email').lean();

  if (!category) {
    throwBadRequestError(messages.CATEGORY_NOT_FOUND);
  }

  const language = { code: 'en' };

  return await transformTranslatedFields(category, language.code);
};

/**
 * Update category
 */
const updateCategory = async ({ adminId, categoryId, name, description, image, variantTypes, isActive }) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID);
  }

  const category = await Category.findById(categoryId);

  if (!category) {
    throwBadRequestError(messages.CATEGORY_NOT_FOUND);
  }

  // Check if name already exists for another category
  if (name && name !== category.name) {
    const nameExists = await Category.findOne({ 'name.en': name, _id: { $ne: categoryId } });

    if (nameExists) {
      throwBadRequestError(messages.CATEGORY_NAME_ALREADY_EXISTS);
    }
  }

  if (name) {
    const translatedData = await translateDataForStore([ 'name' ], { name });

    name = translatedData.name;
  }

  if (description) {
    const translatedData = await translateDataForStore([ 'description' ], { description });

    description = translatedData.description;
  }

  // Update category fields
  if (name) {
    category.name = name;
  }
  if (description !== undefined) {
    category.description = description;
  }
  if (image) {
    const processedImage = processImages([ image ]);

    category.image = processedImage[0];
  }
  if (variantTypes !== undefined) {
    category.variantTypes = variantTypes;
  }
  if (isActive !== undefined) {
    category.isActive = isActive;
  }

  // Update metadata
  category.updatedBy = adminId;

  await category.save();

  return category;
};

/**
 * Delete category
 */
const deleteCategory = async (categoryId) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID);
  }

  const category = await Category.findById(categoryId);

  if (!category) {
    throwBadRequestError(messages.CATEGORY_NOT_FOUND);
  }

  // Check if category has child categories
  const childCategories = await SubCategory.countDocuments({ category: categoryId });

  if (childCategories > 0) {
    throwBadRequestError(messages.CATEGORY_HAS_CHILD_CATEGORIES);
  }

  // Check if category has products
  const products = await Product.countDocuments({ category: categoryId });

  if (products > 0) {
    throwBadRequestError(messages.CATEGORY_HAS_PRODUCTS);
  }

  // Delete category
  await category.deleteOne();

  return category;
};

module.exports = {
  createCategory,
  getAllCategories,
  getCategoryById,
  updateCategory,
  deleteCategory
};
