const joi = require('joi');

const createCategorySchema = joi.object({
  name: joi.string().max(50).required(),
  description: joi.string().max(200).allow(''),
  image: joi.string().allow(''),
  variantTypes: joi.array().items(joi.string())
});

const updateCategorySchema = joi.object({
  name: joi.string().max(50),
  description: joi.string().max(200).allow(''),
  image: joi.string().allow(''),
  variantTypes: joi.array().items(joi.string()),
  isActive: joi.boolean()
}).min(1);

module.exports = {
  createCategorySchema,
  updateCategorySchema
};
