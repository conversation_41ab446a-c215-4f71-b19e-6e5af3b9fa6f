const express = require('express');
const router = express.Router();
const collectionController = require('./controller');
const auth = require('../../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../../middleware/roleCheck');

// All routes require admin authentication
router.use(auth, isAdminOrSuperAdmin);

router.post('/', collectionController.createCollection);
router.get('/', collectionController.getAllCollections);
router.get('/category/:categoryId', collectionController.getCollectionsByCategory);
router.get('/:id', collectionController.getCollectionById);
router.put('/:id', collectionController.updateCollection);
router.delete('/:id', collectionController.deleteCollection);
router.post('/upload-url', collectionController.getUploadUrl);

module.exports = router;
