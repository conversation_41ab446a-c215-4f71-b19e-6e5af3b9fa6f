const mongoose = require('mongoose');
const Collection = require('../../../../models/Collection');
const Category = require('../../../../models/Category');
const Product = require('../../../../models/Product');
const SubCategory = require('../../../../models/SubCategory');
const { throwBadRequestError } = require('../../../../errors');
const { messages } = require('../../../../messages');
const { processImages } = require('../../../../utils/processImage');

/**
 * Create a new collection
 */
const createCollection = async ({ adminId, name, description, image, category, products, subCategories, collectionType = 'PRODUCTS' }) => {
  // Check if collection name already exists
  const collectionExists = await Collection.findOne({ name });

  if (collectionExists) {
    throwBadRequestError(messages.COLLECTION_ALREADY_EXISTS || 'Collection with this name already exists');
  }

  // Check if category exists
  if (!mongoose.Types.ObjectId.isValid(category)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID || 'Invalid category ID');
  }

  const categoryExists = await Category.findById(category);

  if (!categoryExists) {
    throwBadRequestError(messages.CATEGORY_NOT_FOUND || 'Category not found');
  }

  // Process the image if provided
  let processedImage = image;

  if (image) {
    const processedImages = processImages([ image ]);

    processedImage = processedImages[0];
  }

  // Validate products if provided and collection type is PRODUCTS
  if (collectionType === 'PRODUCTS' && products && products.length > 0) {
    // Validate product IDs
    for (const productId of products) {
      if (!mongoose.Types.ObjectId.isValid(productId)) {
        throwBadRequestError(`Invalid product ID: ${productId}`);
      }

      const productExists = await Product.findById(productId);

      if (!productExists || productExists.deletedAt) {
        throwBadRequestError(`Product not found with ID: ${productId}`);
      }

      // Check if product belongs to the specified category
      if (productExists.category.toString() !== category) {
        throwBadRequestError(`Product ${productId} does not belong to the specified category`);
      }
    }
  }

  // Validate subCategories if provided and collection type is SUBCATEGORIES
  if (collectionType === 'SUBCATEGORIES' && subCategories && subCategories.length > 0) {
    // Validate subCategory IDs
    for (const subCategoryId of subCategories) {
      if (!mongoose.Types.ObjectId.isValid(subCategoryId)) {
        throwBadRequestError(`Invalid subCategory ID: ${subCategoryId}`);
      }

      const subCategoryExists = await SubCategory.findById(subCategoryId);

      if (!subCategoryExists) {
        throwBadRequestError(`SubCategory not found with ID: ${subCategoryId}`);
      }

      // Check if subCategory belongs to the specified category
      if (subCategoryExists.category.toString() !== category) {
        throwBadRequestError(`SubCategory ${subCategoryId} does not belong to the specified category`);
      }
    }
  }

  // Create collection
  const collection = new Collection({
    name,
    description,
    image: processedImage,
    category,
    collectionType,
    products: collectionType === 'PRODUCTS' ? (products || []) : [],
    subCategories: collectionType === 'SUBCATEGORIES' ? (subCategories || []) : [],
    isActive: true,
    createdBy: adminId
  });

  await collection.save();

  return collection;
};

/**
 * Get all collections
 */
const getAllCollections = async (includeInactive = false, limit = 10, page = 1, search, sortBy = 'createdAt', sortOrder = -1, category) => {
  const skip = (page - 1) * limit;
  const query = includeInactive ? {} : { isActive: true };
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder);

  if (search) {
    query.name = { $regex: search, $options: 'i' };
  }

  if (category) {
    query.category = category;
  }

  const collections = await Collection.find(query)
    .populate('category products subCategories')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit));

  const total = await Collection.countDocuments(query);

  return {
    collections,
    pagination: {
      total,
      page: parseInt(page),
      pages: Math.ceil(total / limit),
      limit: parseInt(limit)
    }
  };
};

/**
 * Get collections by category
 */
const getCollectionsByCategory = async (categoryId, includeInactive = false) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID || 'Invalid category ID');
  }

  const query = { category: categoryId };

  if (!includeInactive) {
    query.isActive = true;
  }

  const collections = await Collection.find(query)
    .populate('category')
    .sort({ name: 1 }).collation({ locale: 'en', strength: 1 });

  return collections;
};

/**
 * Get collection by ID
 */
const getCollectionById = async (collectionId) => {
  // Check if collection ID is valid
  if (!mongoose.Types.ObjectId.isValid(collectionId)) {
    throwBadRequestError(messages.INVALID_COLLECTION_ID || 'Invalid collection ID');
  }

  const collection = await Collection.findById(collectionId)
    .populate('category products subCategories');

  if (!collection) {
    throwBadRequestError(messages.COLLECTION_NOT_FOUND || 'Collection not found');
  }

  return collection;
};

/**
 * Update collection
 */
const updateCollection = async ({ adminId, collectionId, name, description, image, category, products, subCategories, collectionType, isActive }) => {
  // Check if collection ID is valid
  if (!mongoose.Types.ObjectId.isValid(collectionId)) {
    throwBadRequestError(messages.INVALID_COLLECTION_ID || 'Invalid collection ID');
  }

  const collection = await Collection.findById(collectionId);

  if (!collection) {
    throwBadRequestError(messages.COLLECTION_NOT_FOUND || 'Collection not found');
  }

  // Check if name already exists for another collection
  if (name && name !== collection.name) {
    const nameExists = await Collection.findOne({ name, _id: { $ne: collectionId } });

    if (nameExists) {
      throwBadRequestError(messages.COLLECTION_NAME_ALREADY_EXISTS || 'Collection with this name already exists');
    }
  }

  // Check if category exists if provided
  if (category) {
    if (!mongoose.Types.ObjectId.isValid(category)) {
      throwBadRequestError(messages.INVALID_CATEGORY_ID || 'Invalid category ID');
    }

    const categoryExists = await Category.findById(category);

    if (!categoryExists) {
      throwBadRequestError(messages.CATEGORY_NOT_FOUND || 'Category not found');
    }
  }

  // Process the image if provided
  let processedImage = image;

  if (image && image !== collection.image) {
    const processedImages = processImages([ image ]);

    processedImage = processedImages[0];
  }

  // Determine the collection type to use for validation
  const updatedCollectionType = collectionType || collection.collectionType;

  // Validate products if provided and collection type is PRODUCTS
  if (updatedCollectionType === 'PRODUCTS' && products && products.length > 0) {
    const categoryId = category || collection.category;

    // Validate product IDs
    for (const productId of products) {
      if (!mongoose.Types.ObjectId.isValid(productId)) {
        throwBadRequestError(`Invalid product ID: ${productId}`);
      }

      const productExists = await Product.findById(productId);

      if (!productExists || productExists.deletedAt) {
        throwBadRequestError(`Product not found with ID: ${productId}`);
      }

      // Check if product belongs to the specified category
      if (productExists.category.toString() !== categoryId.toString()) {
        throwBadRequestError(`Product ${productId} does not belong to the specified category`);
      }
    }
  }

  // Validate subCategories if provided and collection type is SUBCATEGORIES
  if (updatedCollectionType === 'SUBCATEGORIES' && subCategories && subCategories.length > 0) {
    const categoryId = category || collection.category;

    // Validate subCategory IDs
    for (const subCategoryId of subCategories) {
      if (!mongoose.Types.ObjectId.isValid(subCategoryId)) {
        throwBadRequestError(`Invalid subCategory ID: ${subCategoryId}`);
      }

      const subCategoryExists = await SubCategory.findById(subCategoryId);

      if (!subCategoryExists) {
        throwBadRequestError(`SubCategory not found with ID: ${subCategoryId}`);
      }

      // Check if subCategory belongs to the specified category
      if (subCategoryExists.category.toString() !== categoryId.toString()) {
        throwBadRequestError(`SubCategory ${subCategoryId} does not belong to the specified category`);
      }
    }
  }

  // Update collection fields
  if (name) {
    collection.name = name;
  }

  if (description !== undefined) {
    collection.description = description;
  }

  if (image) {
    collection.image = processedImage;
  }

  if (category) {
    collection.category = category;
  }

  if (collectionType) {
    collection.collectionType = collectionType;

    // Reset the appropriate array based on the new collection type
    if (collectionType === 'PRODUCTS') {
      collection.subCategories = [];
    } else if (collectionType === 'SUBCATEGORIES') {
      collection.products = [];
    }
  }

  if (products && updatedCollectionType === 'PRODUCTS') {
    collection.products = products;
  }

  if (subCategories && updatedCollectionType === 'SUBCATEGORIES') {
    collection.subCategories = subCategories;
  }

  if (isActive !== undefined) {
    collection.isActive = isActive;
  }

  collection.updatedBy = adminId;

  await collection.save();

  return collection;
};

/**
 * Delete collection
 */
const deleteCollection = async (collectionId) => {
  // Check if collection ID is valid
  if (!mongoose.Types.ObjectId.isValid(collectionId)) {
    throwBadRequestError(messages.INVALID_COLLECTION_ID || 'Invalid collection ID');
  }

  const collection = await Collection.findById(collectionId);

  if (!collection) {
    throwBadRequestError(messages.COLLECTION_NOT_FOUND || 'Collection not found');
  }

  // Instead of deleting, set isActive to false
  collection.isActive = false;
  await collection.save();

  return { success: true };
};

module.exports = {
  createCollection,
  getAllCollections,
  getCollectionsByCategory,
  getCollectionById,
  updateCollection,
  deleteCollection
};
