const joi = require('joi');

const createCollectionSchema = joi.object({
  name: joi.string().max(50).required(),
  description: joi.string().max(200).allow(''),
  image: joi.string().allow(''),
  category: joi.string().required(),
  collectionType: joi.string().valid('PRODUCTS', 'SUBCATEGORIES').default('PRODUCTS'),
  products: joi.array().items(joi.string()).when('collectionType', {
    is: 'PRODUCTS',
    then: joi.array().items(joi.string()),
    otherwise: joi.array().items(joi.string()).length(0)
  }),
  subCategories: joi.array().items(joi.string()).when('collectionType', {
    is: 'SUBCATEGORIES',
    then: joi.array().items(joi.string()),
    otherwise: joi.array().items(joi.string()).length(0)
  })
});

const updateCollectionSchema = joi.object({
  name: joi.string().max(50),
  description: joi.string().max(200).allow(''),
  image: joi.string().allow(''),
  category: joi.string(),
  collectionType: joi.string().valid('PRODUCTS', 'SUBCATEGORIES'),
  products: joi.array().items(joi.string()).when('collectionType', {
    is: 'PRODUCTS',
    then: joi.array().items(joi.string()),
    otherwise: joi.array().items(joi.string()).length(0)
  }),
  subCategories: joi.array().items(joi.string()).when('collectionType', {
    is: 'SUBCATEGORIES',
    then: joi.array().items(joi.string()),
    otherwise: joi.array().items(joi.string()).length(0)
  }),
  isActive: joi.boolean()
}).min(1);

module.exports = {
  createCollectionSchema,
  updateCollectionSchema
};
