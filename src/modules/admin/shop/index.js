const express = require('express');
const router = express.Router();

// Import sub-modules
const productRoutes = require('./product');
const categoryRoutes = require('./category');
const subCategoryRoutes = require('./subCategory');
const collectionRoutes = require('./collection');
const orderRoutes = require('./order');
const attributeRoutes = require('./attribute');
const shippingRoutes = require('./shipping');
const settlementRoutes = require('./settlement');
const shopBannerRoutes = require('./shopBanner');

// Register sub-modules
router.use('/products', productRoutes);
router.use('/categories', categoryRoutes);
router.use('/subcategories', subCategoryRoutes);
router.use('/collections', collectionRoutes);
router.use('/orders', orderRoutes);
router.use('/attributes', attributeRoutes);
router.use('/shipping', shippingRoutes);
router.use('/settlements', settlementRoutes);
router.use('/banners', shopBannerRoutes);

module.exports = router;
