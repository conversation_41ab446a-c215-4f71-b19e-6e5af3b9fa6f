const express = require('express');
const router = express.Router();
const orderController = require('./controller');
const auth = require('../../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../../middleware/roleCheck');

// All routes require admin authentication
router.use(auth, isAdminOrSuperAdmin);

router.get('/', orderController.getAllOrders);
router.get('/dashboard', orderController.getOrdersDashboard);
router.get('/:id', orderController.getOrderById);
router.put('/:id/status', orderController.updateOrderStatus);
router.post('/:id/refund', orderController.refundOrder);
router.post('/:id/ship', orderController.shipOrder);

module.exports = router;
