const joi = require('joi');
const { orderStatusValue } = require('../../../../constants/dbEnums');

const updateOrderStatusSchema = joi.object({
  status: joi.string().valid(...Object.values(orderStatusValue)).required(),
  notes: joi.string().allow('')
});

const refundOrderSchema = joi.object({
  amount: joi.number().min(0).required(),
  reason: joi.string().required()
});

const shipOrderSchema = joi.object({
  trackingNumber: joi.string().required(),
  shippingProvider: joi.string().required(),
  estimatedDelivery: joi.date().iso().allow(null)
});

module.exports = {
  updateOrderStatusSchema,
  refundOrderSchema,
  shipOrderSchema
};
