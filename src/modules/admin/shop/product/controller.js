const { apiResponse, errorApiResponse } = require('../../../../config/responseHandler');
const { commonConstants } = require('../../../../constants/common');
const { messages } = require('../../../../messages');
const { getPresignedUrl } = require('../../../../utils/s3Service');
const productService = require('./service');
const { createProductSchema, updateProductSchema, updateProductStatusSchema, stockUpdateRequestStatusSchema } = require('./validation');
const { SUCCESS } = commonConstants;
const { saveAuditLog } = require('../../../../utils/auditLogger');
const { auditLogAction } = require('../../../../constants/dbEnums');

/**
 * Create a new product
 */
const createProduct = async (req, res) => {
  try {
    const { error } = createProductSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;

    const data = await productService.createProduct({
      adminId,
      ...req.body
    });

    //* Save audit log 
    const detail = `Product ${data.name.en} created successfully`;
    const model = 'Product';

    await saveAuditLog(req, req.user.id, auditLogAction.PRODUCT_CREATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.PRODUCT_CREATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get all products with pagination
 */
const getAllProducts = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, category, subCategory, search, sortBy = 'createdAt', sortOrder = -1 } = req.query;

    const data = await productService.getAllProducts({
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      category,
      subCategory,
      search,
      sortBy,
      sortOrder,
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get pending products for approval
 */
const getPendingProducts = async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', sortBy = 'createdAt', sortOrder = -1 } = req.query;

    const data = await productService.getPendingProducts({
      page: parseInt(page),
      limit: parseInt(limit),
      search,
      sortBy,
      sortOrder,
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get product by ID
 */
const getProductById = async (req, res) => {
  try {
    const { id } = req.params;

    const data = await productService.getProductById(id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update product
 */
const updateProduct = async (req, res) => {
  try {
    const { error } = updateProductSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    const { id } = req.params;

    const data = await productService.updateProduct({
      adminId,
      productId: id,
      ...req.body
    });

    //* Save audit log 
    const detail = `Product ${data.name.en} updated successfully`;
    const model = 'Product';

    await saveAuditLog(req, req.user.id, auditLogAction.PRODUCT_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.PRODUCT_UPDATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete product
 */
const deleteProduct = async (req, res) => {
  try {
    const { id } = req.params;

    const data = await productService.deleteProduct(id);

    //* Save audit log 
    const detail = `Product ${data.name.en} deleted successfully`;
    const model = 'Product';

    await saveAuditLog(req, req.user.id, auditLogAction.PRODUCT_DELETED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.PRODUCT_DELETED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get upload URL for product images
 */
const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }

    const { extension } = req.body;

    const uploadData = await getPresignedUrl(extension, 'products');

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update product status (approve/reject)
 */
const updateProductStatus = async (req, res) => {
  try {
    const { error } = updateProductStatusSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    const { id } = req.params;
    const { status, rejectionReason, variants } = req.body;

    const data = await productService.updateProductStatus({
      adminId,
      productId: id,
      status,
      rejectionReason,
      variants
    });

    //* Save audit log 
    const detail = `Product ${data.name.en} ${status === 'ACTIVE' ? 'approved' : 'rejected'} successfully`;
    const model = 'Product';

    await saveAuditLog(req, req.user.id, auditLogAction.PRODUCT_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: status === 'ACTIVE' ? messages.PRODUCT_APPROVED : messages.PRODUCT_REJECTED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Toggle featured status
 */
const toggleFeaturedStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { featured } = req.body;

    const data = await productService.toggleFeaturedStatus({
      productId: id,
      featured
    });

    //* Save audit log 
    const detail = `Product ${data.name.en} ${featured ? 'featured' : 'unfeatured'} successfully`;
    const model = 'Product';

    await saveAuditLog(req, req.user.id, auditLogAction.PRODUCT_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: featured ? messages.PRODUCT_FEATURED : messages.PRODUCT_UNFEATURED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Toggle showOnHomepage status
 */
const toggleShowOnHomepageStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { showOnHomepage } = req.body;

    const data = await productService.toggleShowOnHomepageStatus({
      productId: id,
      showOnHomepage
    });

    //* Save audit log 
    const detail = `Product ${data.name.en} ${showOnHomepage ? 'added to homepage' : 'removed from homepage'} successfully`;
    const model = 'Product';

    await saveAuditLog(req, req.user.id, auditLogAction.PRODUCT_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: showOnHomepage ? 'Product added to homepage' : 'Product removed from homepage',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get products for homepage management
 */
const getHomepageProducts = async (req, res) => {
  try {
    const data = await productService.getHomepageProducts();

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Homepage products retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get product for review
 */
const getProductForReview = async (req, res) => {
  try {
    const { id } = req.params;

    const data = await productService.getProductForReview(id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get all stock update requests
 */
const getAllStockUpdateRequests = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search, sortBy = 'createdAt', sortOrder = -1 } = req.query;

    const data = await productService.getAllStockUpdateRequests({
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      search,
      sortBy,
      sortOrder
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Stock update requests retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get stock update request by ID
 */
const getStockUpdateRequestById = async (req, res) => {
  try {
    const { id } = req.params;

    const data = await productService.getStockUpdateRequestById(id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Stock update request retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update stock update request status (approve/reject)
 */
const updateStockUpdateRequestStatus = async (req, res) => {
  try {
    const { error } = stockUpdateRequestStatusSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    const { id } = req.params;
    const { status, rejectionReason } = req.body;

    const data = await productService.updateStockUpdateRequestStatus({
      adminId,
      requestId: id,
      status,
      rejectionReason
    });

    //* Save audit log 
    const detail = `Stock update request ${data.requestId} ${status === 'APPROVED' ? 'approved' : 'rejected'} successfully`;
    const model = 'StockUpdateRequest';

    await saveAuditLog(req, req.user.id, auditLogAction.STOCK_UPDATE_REQUEST_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: status === 'APPROVED' ? 'Stock update request approved' : 'Stock update request rejected',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getProductCode = async (req, res) => {
  try {
    const { state, productCode, variantProductCode } = req.body;

    if (!state && !productCode && !variantProductCode) {
      return res.status(400).json({
        message: 'State, product code or variant product code are required',
        status: false
      });
    }

    const data = await productService.getNextProductCode(state, productCode, variantProductCode);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateProductStatusActiveInactive = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (status !== 'ACTIVE' && status !== 'INACTIVE') {
      return res.status(400).json({
        message: 'Status must be ACTIVE or INACTIVE',
        status: false
      });
    }

    const data = await productService.updateProductStatusActiveInactive(id, status);

    //* Save audit log 
    const detail = `Product ${data.name.en} ${status === 'ACTIVE' ? 'activated' : 'deactivated'} successfully`;
    const model = 'Product';

    await saveAuditLog(req, req.user.id, auditLogAction.PRODUCT_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: status === 'ACTIVE' ? 'Product activated' : 'Product deactivated',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateVariantStatusActiveInactive = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (status !== 'ACTIVE' && status !== 'INACTIVE') {
      return res.status(400).json({
        message: 'Status must be ACTIVE or INACTIVE',
        status: false
      });
    }

    const data = await productService.updateVariantStatusActiveInactive(id, status);

    //* Save audit log
    const detail = `Variant ${data.name.en} ${status === 'ACTIVE' ? 'activated' : 'deactivated'} successfully`;
    const model = 'ProductVariant';

    await saveAuditLog(req, req.user.id, auditLogAction.PRODUCT_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: status === 'ACTIVE' ? 'Variant activated' : 'Variant deactivated',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createProduct,
  getAllProducts,
  getPendingProducts,
  getProductById,
  getProductForReview,
  updateProduct,
  deleteProduct,
  getUploadUrl,
  updateProductStatus,
  toggleFeaturedStatus,
  toggleShowOnHomepageStatus,
  getHomepageProducts,
  getAllStockUpdateRequests,
  getStockUpdateRequestById,
  updateStockUpdateRequestStatus,
  getProductCode,
  updateProductStatusActiveInactive,
  updateVariantStatusActiveInactive
};
