const express = require('express');
const router = express.Router();
const productController = require('./controller');
const auth = require('../../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../../middleware/roleCheck');

// All routes require admin authentication
router.use(auth, isAdminOrSuperAdmin);

router.post('/', productController.createProduct);
router.get('/', productController.getAllProducts);
router.get('/pending', productController.getPendingProducts);
router.get('/:id', productController.getProductById);
router.get('/:id/review', productController.getProductForReview);
router.put('/:id', productController.updateProduct);
router.delete('/:id', productController.deleteProduct);
router.post('/upload-url', productController.getUploadUrl);
router.put('/:id/status', productController.updateProductStatus);
router.put('/:id/featured', productController.toggleFeaturedStatus);
router.put('/:id/homepage', productController.toggleShowOnHomepageStatus);
router.get('/homepage/manage', productController.getHomepageProducts);

// Stock update request routes
router.get('/stock-update/requests', productController.getAllStockUpdateRequests);
router.get('/stock-update/requests/:id', productController.getStockUpdateRequestById);

// Only Super Admin can approve/reject stock update requests
router.put('/stock-update/requests/:id/status', productController.updateStockUpdateRequestStatus);

module.exports = router;
