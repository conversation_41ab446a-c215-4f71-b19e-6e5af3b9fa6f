const Product = require('../../../../models/Product');
const ProductVariant = require('../../../../models/ProductVariant');
const StockUpdateRequest = require('../../../../models/StockUpdateRequest');
const Category = require('../../../../models/Category');
const SubCategory = require('../../../../models/SubCategory');
const AttributeOption = require('../../../../models/AttributeOption');
// const Vendor = require('../../../../models/Vendor');
const { throwBadRequestError, throwNotFoundError } = require('../../../../errors');
const { messages } = require('../../../../messages');
const { productStatusValue, stockUpdateStatusValue } = require('../../../../constants/dbEnums');
const mongoose = require('mongoose');
const { withTransaction, sessionOptions } = require('../../../../utils/transactionHelper');
const { processImages } = require('../../../../utils/processImage');
const { generateSKU } = require('../../../../utils/skuGenerator');
const { translateDataForStore } = require('../../../../utils/translateInput');
const { transformTranslatedFields } = require('../../../../utils/localizer');
// const { sendProductApprovalNotification, sendProductRejectionNotification } = require('../../../../utils/notificationService');
const states = require('../../../../../data/states.json');

/**
 * Create a new product
 */
const createProduct = async ({ adminId, name, gstPercentage, category, subCategory,pickupAddress, temple, weight, dimensions, featured, showOnHomepage, hasVariants, variantAttributes, variants }) => {
  // Check if category exists
  if (!mongoose.Types.ObjectId.isValid(category)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID);
  }

  const categoryExists = await Category.findById(category);

  if (!categoryExists) {
    throwBadRequestError(messages.CATEGORY_NOT_FOUND);
  }

  // Check if subcategory exists
  if (!mongoose.Types.ObjectId.isValid(subCategory)) {
    throwBadRequestError(messages.INVALID_SUBCATEGORY_ID || 'Invalid subcategory ID');
  }

  const subCategoryExists = await SubCategory.findById(subCategory);

  if (!subCategoryExists) {
    throwBadRequestError(messages.SUBCATEGORY_NOT_FOUND || 'Subcategory not found');
  }

  // Check if subcategory belongs to the selected category
  if (subCategoryExists.category.toString() !== category) {
    throwBadRequestError(messages.SUBCATEGORY_NOT_BELONG_TO_CATEGORY || 'Subcategory does not belong to the selected category');
  }

  // Check if temple ID is valid if provided
  if (temple && !mongoose.Types.ObjectId.isValid(temple)) {
    throwBadRequestError(messages.INVALID_TEMPLE_ID);
  }

  // If product has variants, validate variant attributes
  if (hasVariants) {
    // Check if variant attributes exist
    if (!variantAttributes || variantAttributes.length === 0) {
      throwBadRequestError(messages.VARIANT_ATTRIBUTES_REQUIRED);
    }

    // Check if variants are provided
    if (!variants || variants.length === 0) {
      throwBadRequestError(messages.VARIANTS_REQUIRED);
    }

    // Validate each attribute exists in the system
    for (const attrName of variantAttributes) {
      const attributeExists = await AttributeOption.findOne({ ['name.en']: attrName });

      if (!attributeExists) {
        throwBadRequestError(`${messages.ATTRIBUTE_NOT_FOUND}: ${attrName}`);
      }
    }

    // Check if all variants have the required attributes
    for (const variant of variants) {
      const variantAttrs = Object.keys(variant.attributes);

      // Check if variant has all required attributes
      for (const requiredAttr of variantAttributes) {
        if (!variantAttrs.includes(requiredAttr)) {
          throwBadRequestError(`${messages.MISSING_VARIANT_ATTRIBUTE}: ${requiredAttr}`);
        }
      }

      // Check if variant has any extra attributes
      for (const attr of variantAttrs) {
        if (!variantAttributes.includes(attr)) {
          throwBadRequestError(`${messages.INVALID_VARIANT_ATTRIBUTE}: ${attr}`);
        }
      }

      variant.sku = variant.sku || generateSKU('V', `${name}-${Object.values(variant.attributes).join('-')}`);

      // Check if variant SKU is unique
      const variantSkuExists = await ProductVariant.findOne({ sku: variant.sku });

      if (variantSkuExists) {
        throwBadRequestError(`${messages.SKU_ALREADY_EXISTS}: ${variant.sku}`);
      }
    }

    // Check if at least one variant is marked as default
    const hasDefaultVariant = variants.some(v => v.isDefault);

    if (!hasDefaultVariant) {
      throwBadRequestError(messages.DEFAULT_VARIANT_REQUIRED);
    }
  }

  // Translate fields
  const translatedFields = [ 'name' ];
  const productData = { name };
  const translatedData = await translateDataForStore(translatedFields, productData);

  translatedFields.forEach(field => {
    if (productData[field]) {
      delete productData[field];
    }
  });

  const finalProductData = {
    ...productData,
    ...translatedData
  };

  // add product code logic for state be fetching the last product code from the database and incrementing it for the state
  const lastProduct = await Product.findOne({
    'pickupAddress.state': pickupAddress.state
  }).sort({ createdAt: -1 }).lean();

  // add state logic

  let code = '0001';
  
  const state = states.find(state => state.name.toLowerCase() === pickupAddress.state.toLowerCase());
  const stateCode = state ? state.id : 'XX';
  
  if (lastProduct && lastProduct.productCode) {
    code = `${(parseInt(lastProduct.productCode.split('-').pop() || 0) + 1).toString().padStart(4, '0')}`;
  }
  const productCode = `P-${stateCode}-${code}`;

  // Use transaction helper
  return await withTransaction(async (session) => {
    // Create product
    const product = new Product({
      ...finalProductData,
      gstPercentage,
      pickupAddress,
      productCode,
      category,
      subCategory,
      temple,
      weight,
      dimensions,
      featured: featured || false,
      showOnHomepage: showOnHomepage || false,
      status: productStatusValue.ACTIVE, // Admin-created products are active by default
      createdBy: adminId,
      createdByModel: 'Admin',
      hasVariants: hasVariants || false,
      variantAttributes: hasVariants ? variantAttributes : [],
      isOneGodProduct: true
    });

    await product.save(sessionOptions(session));

    // If product has variants, create them
    if (hasVariants && variants && variants.length > 0) {
      let variantCode = 1;

      const variantPromises = variants.map(async variant => {
        // Process variant images if provided
        const variantImages = variant.images && variant.images.length > 0 ?
          processImages(variant.images) : [];

        const variantCodeString = variantCode.toString().padStart(4, '0');
        const variantProductCode = `${productCode}-V-${variantCodeString}`;

        variantCode += 1;

        // Translate variant description if provided
        let translatedDescription = {};

        if (variant.description) {
          const variantTranslatedFields = [ 'description' ];
          const variantData = { description: variant.description };

          translatedDescription = await translateDataForStore(variantTranslatedFields, variantData);
        }

        const productVariant = new ProductVariant({
          product: product._id,
          attributes: new Map(Object.entries(variant.attributes)),
          description: translatedDescription.description || variant.description,
          price: variant.price,
          unit: variant.unit,
          weight: variant.weight,
          dimensions: variant.dimensions,
          faq: variant.faq,
          highlight: variant.highlight,
          barcode: variant.barcode,
          gstIn: variant.gstIn,
          hsn: variant.hsn,
          gstPercentage: variant.gstPercentage,
          taxablePrice: variant.taxablePrice,
          gstPrice: variant.gstPrice,
          pickupAddress: variant.pickupAddress,
          shipRocketPickupLocationName: variant.shipRocketPickupLocationName,
          productCode: variantProductCode,
          discountPrice: variant.discountPrice,
          stock: variant.stock,
          sku: variant.sku,
          images: variantImages,
          isDefault: variant.isDefault || false
        });

        return productVariant.save(sessionOptions(session));
      });

      await Promise.all(variantPromises);
    }

    // Return product with populated variants
    const populatedProduct = await Product.findById(product._id)
      .populate('variants');

    return populatedProduct;
  });
};

/**
 * Get all products with pagination and filters
 */
const getAllProducts = async ({ page, limit, status, category, subCategory, search, sortBy, sortOrder }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const query = {
    deletedAt: null,
    status: productStatusValue.ACTIVE
  };

  if (status) {
    query.status = status;
  }

  if (category) {
    query.category = category;
  }

  if (subCategory) {
    query.subCategory = subCategory;
  }

  const language = { code: 'en' };

  if (search) {
    query.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
    ];
  }

  const products = await Product.find(query)
    .populate('category', 'name')
    .populate('subCategory', 'name')
    .populate('temple', 'name')
    .populate('createdBy', 'name email businessName')
    .populate('variants')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit).lean();

  const total = await Product.countDocuments(query);

  return {
    products: await transformTranslatedFields(products, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get pending products for approval
 */
const getPendingProducts = async ({ page, limit, search, sortBy, sortOrder }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  // Build query
  const query = { status: productStatusValue.PENDING };

  const language = { code: 'en' };

  // Add search filter if provided
  if (search) {
    query.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
    ];
  }

  // Get products with basic info first
  const basicProducts = await Product.find(query, { createdByModel: 1 });

  // Only proceed if there are products
  if (basicProducts.length === 0) {
    return {
      products: [],
      pagination: {
        total: 0,
        page,
        pages: 0,
        limit
      }
    };
  }

  // Now get full product details with proper population
  const products = await Product.find(query)
    .populate('category', 'name')
    .populate('subCategory', 'name')
    .populate('temple', 'name')
    .populate({
      path: 'createdBy',
      select: 'name email businessName phone address',
      model: 'Vendor' // Most pending products will be from vendors
    })
    .populate({
      path: 'variants',
      match: { isActive: true }
    })
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit).lean();

  const total = await Product.countDocuments(query);

  return {
    products: await transformTranslatedFields(products, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get product by ID
 */
const getProductById = async (productId) => {
  // Check if product ID is valid
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  // First fetch the product to determine the model type
  const productBasic = await Product.findById(productId);

  if (!productBasic) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // Now populate with the correct model
  const product = await Product.findById(productId)
    .populate('category', 'name')
    .populate('subCategory', 'name')
    .populate('temple', 'name')
    .populate({
      path: 'createdBy',
      select: 'name email businessName phone address',
      model: productBasic.createdByModel // Use the actual model name from the product
    })
    .populate('approvedBy', 'name email')
    .populate({
      path: 'variants',
      match: { isActive: true }
    }).lean(); // Populate the virtual variants field with active variants only

  if (!product) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  const language = { code: 'en' };

  return await transformTranslatedFields(product, language.code);
};

/**
 * Update product
 */
const updateProduct = async ({ adminId, productId, name, gstPercentage, category, subCategory, pickupAddress, temple, weight, dimensions, featured, showOnHomepage, hasVariants, variantAttributes, addVariants, updateVariants, removeVariants }) => {
  // Check if product ID is valid
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  const product = await Product.findById(productId);

  if (!product || product.deletedAt) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // Check if category exists if provided
  if (category) {
    if (!mongoose.Types.ObjectId.isValid(category)) {
      throwBadRequestError(messages.INVALID_CATEGORY_ID);
    }

    const categoryExists = await Category.findById(category);

    if (!categoryExists) {
      throwBadRequestError(messages.CATEGORY_NOT_FOUND);
    }
  }

  // Check if subcategory exists if provided
  if (subCategory) {
    if (!mongoose.Types.ObjectId.isValid(subCategory)) {
      throwBadRequestError(messages.INVALID_SUBCATEGORY_ID || 'Invalid subcategory ID');
    }

    const subCategoryExists = await SubCategory.findById(subCategory);

    if (!subCategoryExists) {
      throwBadRequestError(messages.SUBCATEGORY_NOT_FOUND || 'Subcategory not found');
    }

    // If both category and subcategory are provided, check if subcategory belongs to the category
    if (category && subCategoryExists.category.toString() !== category) {
      throwBadRequestError(messages.SUBCATEGORY_NOT_BELONG_TO_CATEGORY || 'Subcategory does not belong to the selected category');
    }
  }

  // Check if temple ID is valid if provided
  if (temple && !mongoose.Types.ObjectId.isValid(temple)) {
    throwBadRequestError(messages.INVALID_TEMPLE_ID);
  }

  // Translate fields if provided
  const translatedFields = [ 'name' ];
  const updateData = { name };
  const hasTranslatableFields = translatedFields.some(field => updateData[field]);

  let translatedData = {};

  if (hasTranslatableFields) {
    translatedData = await translateDataForStore(translatedFields, updateData);
  }

  // Use transaction helper
  return await withTransaction(async (session) => {
    // Update product fields
    if (name) {
      product.name = translatedData.name || name;
    }
    if (gstPercentage) {
      product.gstPercentage = gstPercentage;
    }

    if (pickupAddress) {
      product.pickupAddress = pickupAddress;
    }
    if (category) {
      product.category = category;
    }
    if (subCategory) {
      product.subCategory = subCategory;
    }
    if (temple) {
      product.temple = temple;
    }
    if (weight) {
      product.weight = weight;
    }
    if (dimensions) {
      product.dimensions = dimensions;
    }
    if (featured !== undefined) {
      product.featured = featured;
    }

    if (showOnHomepage !== undefined) {
      product.showOnHomepage = showOnHomepage;
    }

    // Handle variant attributes update if provided
    if (variantAttributes) {
      // Validate each attribute exists in the system
      for (const attrName of variantAttributes) {
        const attributeExists = await AttributeOption.findOne({ ['name.en']: attrName });

        if (!attributeExists) {
          throwBadRequestError(`${messages.ATTRIBUTE_NOT_FOUND}: ${attrName}`);
        }
      }

      product.variantAttributes = variantAttributes;
    }

    // Update hasVariants flag if provided
    if (hasVariants !== undefined) {
      product.hasVariants = hasVariants;
    }

    // Update metadata
    product.updatedBy = adminId;
    product.updatedByModel = 'Admin';

    await product.save(sessionOptions(session));

    // Handle variant operations if product has variants
    if (product.hasVariants) {
      // Add new variants
      if (addVariants && addVariants.length > 0) {
        const lastVariant = await ProductVariant.findOne({
          product: product._id
        }).sort({ createdAt: -1 }).lean();

        let variantCode = 1;

        if (lastVariant && lastVariant.productCode) {
          const variantCodeString = lastVariant.productCode.split('-').pop();

          variantCode = parseInt(variantCodeString) + 1;
        }
        
        for (const variant of addVariants) {
          // Check if variant SKU is unique
          const variantSkuExists = await ProductVariant.findOne({ sku: variant.sku });

          if (variantSkuExists) {
            throwBadRequestError(`${messages.SKU_ALREADY_EXISTS}: ${variant.sku}`);
          }

          const variantCodeString = variantCode.toString().padStart(4, '0');
          const variantProductCode = `${product.productCode}-V-${variantCodeString}`;

          variantCode += 1;

          // Process variant images if provided
          const variantImages = variant.images && variant.images.length > 0 ?
            processImages(variant.images) : [];

          // Translate variant description if provided
          let translatedDescription = {};

          if (variant.description) {
            const variantTranslatedFields = [ 'description' ];
            const variantData = { description: variant.description };

            translatedDescription = await translateDataForStore(variantTranslatedFields, variantData);
          }

          // Create new variant
          const productVariant = new ProductVariant({
            product: product._id,
            attributes: new Map(Object.entries(variant.attributes)),
            description: translatedDescription.description || variant.description,
            price: variant.price,
            unit: variant.unit,
            gstIn: variant.gstIn,
            hsn: variant.hsn,
            weight: variant.weight,
            dimensions: variant.dimensions,
            barcode: variant.barcode,
            faq: variant.faq,
            highlight: variant.highlight,
            gstPercentage: variant.gstPercentage,
            taxablePrice: variant.taxablePrice,
            gstPrice: variant.gstPrice,
            pickupAddress: variant.pickupAddress,
            shipRocketPickupLocationName: variant.shipRocketPickupLocationName,
            productCode: variantProductCode,
            discountPrice: variant.discountPrice,
            stock: variant.stock,
            sku: variant.sku,
            images: variantImages,
            isDefault: variant.isDefault || false
          });

          await productVariant.save(sessionOptions(session));
        }
      }

      // Update existing variants
      if (updateVariants && updateVariants.length > 0) {
        for (const variant of updateVariants) {
          if (!mongoose.Types.ObjectId.isValid(variant.id)) {
            throwBadRequestError(messages.INVALID_VARIANT_ID);
          }

          const existingVariant = await ProductVariant.findOne({
            _id: variant.id,
            product: product._id
          });

          if (!existingVariant) {
            throwBadRequestError(messages.VARIANT_NOT_FOUND);
          }

          // Update variant fields
          if (variant.attributes) {
            existingVariant.attributes = new Map(Object.entries(variant.attributes));
          }

          // Update description with translation if provided
          if (variant.description !== undefined) {
            if (variant.description) {
              const variantTranslatedFields = [ 'description' ];
              const variantData = { description: variant.description };
              const translatedDescription = await translateDataForStore(variantTranslatedFields, variantData);

              existingVariant.description = translatedDescription.description;
            } else {
              existingVariant.description = variant.description;
            }
          }

          // Always update price and discountPrice if provided
          if (variant.price !== undefined) {
            existingVariant.price = variant.price;
          }

          if (variant.unit !== undefined) {
            existingVariant.unit = variant.unit;
          }
          
          if (variant.gstIn !== undefined) {
            existingVariant.gstIn = variant.gstIn;
          }

          if (variant.hsn !== undefined) {
            existingVariant.hsn = variant.hsn;
          }

          if (variant.gstPercentage !== undefined) {
            existingVariant.gstPercentage = variant.gstPercentage;
          }

          if (variant.taxablePrice !== undefined) {
            existingVariant.taxablePrice = variant.taxablePrice;
          }

          if (variant.gstPrice !== undefined) {
            existingVariant.gstPrice = variant.gstPrice;
          }

          if (variant.pickupAddress) {
            existingVariant.pickupAddress = variant.pickupAddress;
          }

          if (variant.shipRocketPickupLocationName) {
            existingVariant.shipRocketPickupLocationName = variant.shipRocketPickupLocationName;
          }

          if (variant.discountPrice !== undefined) {
            existingVariant.discountPrice = variant.discountPrice;
          }

          if (variant.stock !== undefined) {
            existingVariant.stock = variant.stock;
          }

          if (variant.images && variant.images.length > 0) {
            existingVariant.images = processImages(variant.images);
          }

          if (variant.isDefault !== undefined) {
            existingVariant.isDefault = variant.isDefault;
          }

          if (variant.weight !== undefined) {
            existingVariant.weight = variant.weight;
          }

          if (variant.dimensions !== undefined) {
            existingVariant.dimensions = {
              ...existingVariant.dimensions.toObject?.() || existingVariant.dimensions || {},
              ...variant.dimensions
            };
          }

          if (variant.barcode !== undefined) {
            existingVariant.barcode = variant.barcode;
          }

          if (variant.faq !== undefined) {
            existingVariant.faq = variant.faq;
          }

          if (variant.highlight !== undefined) {
            existingVariant.highlight = variant.highlight;
          }

          // Save the updated variant
          await existingVariant.save(sessionOptions(session));
        }
      }

      // Remove variants
      if (removeVariants && removeVariants.length > 0) {
        // Check if we're not removing all variants
        const totalVariants = await ProductVariant.countDocuments({ product: product._id });

        if (totalVariants <= removeVariants.length) {
          throwBadRequestError(messages.CANNOT_REMOVE_ALL_VARIANTS);
        }

        // Check if we're not removing all default variants
        const defaultVariants = await ProductVariant.find({
          product: product._id,
          isDefault: true
        });

        const defaultVariantIds = defaultVariants.map(v => v._id.toString());
        const removingAllDefaults = defaultVariantIds.every(id => removeVariants.includes(id));

        if (removingAllDefaults) {
          throwBadRequestError(messages.CANNOT_REMOVE_ALL_DEFAULT_VARIANTS);
        }

        // Remove the variants
        await ProductVariant.deleteMany({
          _id: { $in: removeVariants },
          product: product._id
        }, sessionOptions(session));
      }
    }

    // Return updated product with variants
    const updatedProduct = await Product.findById(product._id)
      .populate('variants');

    return updatedProduct;
  });
};

/**
 * Delete product
 */
const deleteProduct = async (productId) => {
  // Check if product ID is valid
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  const product = await Product.findById(productId);

  if (!product) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // Instead of deleting, set status to INACTIVE
  product.status = productStatusValue.INACTIVE;
  product.deletedAt = new Date();
  await product.save();

  return product;
};

/**
 * Update product status (approve/reject)
 */
const updateProductStatus = async ({ adminId, productId, status, rejectionReason, variants }) => {
  // Check if product ID is valid
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  // First fetch the basic product
  const productBasic = await Product.findById(productId);

  if (!productBasic || productBasic.deletedAt) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // Now get the full product with populated fields
  const product = await Product.findById(productId)
    .populate({
      path: 'variants',
      match: { isActive: true }
    });

  if (!product) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // Only pending products can be approved or rejected
  if (product.status !== productStatusValue.PENDING) {
    throwBadRequestError(messages.PRODUCT_NOT_PENDING);
  }

  // Update status
  product.status = status;

  // If rejecting, add reason
  if (status === productStatusValue.REJECTED) {
    if (!rejectionReason) {
      throwBadRequestError(messages.REJECTION_REASON_REQUIRED);
    }
    product.rejectionReason = rejectionReason;
  }

  // If approving, add approval info
  if (status === productStatusValue.ACTIVE) {
    product.approvedBy = adminId;
    product.approvedAt = new Date();
    product.rejectionReason = null; // Clear any previous rejection reason

    const productVariants = product.variants;

    const productVariantIds = productVariants.map(variant => variant._id.toString());
    const variantIds = variants.map(variant => variant.id.toString());

    const isAllVariants = variantIds.length && productVariantIds.length && productVariantIds.length === variantIds.length && productVariantIds.every(id => variantIds.includes(id));

    if (!isAllVariants) {
      throwBadRequestError('Price for Each Variant is required');
    }

    const promise = [];

    for (const variant of variants) {
      const existingVariant = productVariants.find(v => v._id.toString() === variant.id);

      if (!existingVariant) {
        throwBadRequestError(`Variant not found with ID: ${variant.id}`);
      }

      if (product.isOneGodProduct) {
        existingVariant.price = variant.price;
        existingVariant.taxablePrice = variant.taxablePrice;
        existingVariant.gstPrice = variant.gstPrice;

        promise.push(existingVariant.save());
      }

    }
  
    if (promise.length) {
      await Promise.all(promise);
    }
  }

  await product.save();

  // Send notification to vendor if product was created by a vendor
  // if (product.createdByModel === 'Vendor') {
  //   try {
  //     const vendor = await Vendor.findById(product.createdBy);

  //     if (vendor) {
  //       if (status === productStatusValue.ACTIVE) {
  //         // Send approval notification
  //         await sendProductApprovalNotification(vendor, product);
  //       } else if (status === productStatusValue.REJECTED) {
  //         // Send rejection notification
  //         await sendProductRejectionNotification(vendor, product, rejectionReason);
  //       }
  //     }
  //   } catch (error) {
  //     console.error('Error sending notification:', error);
  //     // Continue execution even if notification fails
  //   }
  // }

  return product;
};

/**
 * Toggle featured status
 */
const toggleFeaturedStatus = async ({ productId, featured }) => {
  // Check if product ID is valid
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  const product = await Product.findById(productId);

  if (!product) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // Only active products can be featured
  if (product.status !== productStatusValue.ACTIVE) {
    throwBadRequestError(messages.ONLY_ACTIVE_PRODUCTS_CAN_BE_FEATURED);
  }

  // Update featured status
  product.featured = featured;
  await product.save();

  return product;
};

/**
 * Toggle showOnHomepage status
 */
const toggleShowOnHomepageStatus = async ({ productId, showOnHomepage }) => {
  // Check if product ID is valid
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  const product = await Product.findById(productId);

  if (!product || product.deletedAt) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // Only active products can be shown on homepage
  if (product.status !== productStatusValue.ACTIVE) {
    throwBadRequestError('Only active products can be shown on homepage');
  }

  // Update showOnHomepage status
  product.showOnHomepage = showOnHomepage;
  await product.save();

  return product;
};

/**
 * Get products for homepage management
 */
const getHomepageProducts = async () => {
  let homepageProducts = await Product.find({
    showOnHomepage: true,
    status: productStatusValue.ACTIVE
  })
    .populate('category', 'name')
    .populate('subCategory', 'name')
    .sort({ updatedAt: -1 }).collation({ locale: 'en', strength: 1 }).lean();

  let otherProducts = await Product.find({
    showOnHomepage: false,
    status: productStatusValue.ACTIVE
  })
    .populate('category', 'name')
    .populate('subCategory', 'name')
    .populate('temple', 'name')
    .sort({ ['name.en']: 1 }).collation({ locale: 'en', strength: 1 }).lean();

  const language = { code: 'en' };

  homepageProducts = await transformTranslatedFields(homepageProducts, language.code);
  otherProducts = await transformTranslatedFields(otherProducts, language.code);

  return {
    homepageProducts,
    otherProducts
  };
};

/**
 * Get product details for review
 */
const getProductForReview = async (productId) => {
  // Check if product ID is valid
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }
  // First fetch the product to determine the model type
  const productBasic = await Product.findById(productId);

  if (!productBasic || productBasic.deletedAt) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // Now populate with the correct model
  const product = await Product.findById(productId)
    .populate('category', 'name')
    .populate('subCategory', 'name')
    .populate('temple', 'name')
    .populate({
      path: 'createdBy',
      select: 'name email businessName phone address',
      model: productBasic.createdByModel // Use the actual model name from the product
    })
    .populate({
      path: 'variants',
      match: { isActive: true }
    }).lean();

  if (!product) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // Check if product is pending approval
  if (product.status !== productStatusValue.PENDING) {
    throwBadRequestError(messages.PRODUCT_NOT_PENDING);
  }

  const language = { code: 'en' };

  return await transformTranslatedFields(product, language.code);
};

/**
 * Get all stock update requests with pagination and filters
 */
const getAllStockUpdateRequests = async ({ page, limit, status, search, sortBy, sortOrder }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const query = {};

  if (status) {
    query.status = status;
  }

  // Build search query if provided
  if (search) {
    // First, find products that match the search term
    const products = await Product.find({
      $or: [
        { ['name.en']: { $regex: search, $options: 'i' } },
      ]
    }).select('_id');

    const productIds = products.map(product => product._id);

    // Then, find variants that match the search term
    const variants = await ProductVariant.find({
      sku: { $regex: search, $options: 'i' }
    }).select('_id');

    const variantIds = variants.map(variant => variant._id);

    // Add these IDs to the query
    if (productIds.length > 0 || variantIds.length > 0) {
      query.$or = [];

      if (productIds.length > 0) {
        query.$or.push({ product: { $in: productIds } });
      }

      if (variantIds.length > 0) {
        query.$or.push({ variant: { $in: variantIds } });
      }
    }
  }

  // Get stock update requests
  const stockUpdateRequests = await StockUpdateRequest.find(query)
    .populate({
      path: 'product',
    })
    .populate({
      path: 'variant',
    })
    .populate({
      path: 'requestedBy',
      select: 'name email businessName'
    })
    .populate({
      path: 'reviewedBy',
      select: 'name email'
    })
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit).lean();

  const total = await StockUpdateRequest.countDocuments(query);

  const language = { code: 'en' };

  return {
    stockUpdateRequests: await transformTranslatedFields(stockUpdateRequests, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get stock update request by ID
 */
const getStockUpdateRequestById = async (requestId) => {
  // Check if request ID is valid
  if (!mongoose.Types.ObjectId.isValid(requestId)) {
    throwBadRequestError('Invalid stock update request ID');
  }

  // Get stock update request
  const stockUpdateRequest = await StockUpdateRequest.findById(requestId)
    .populate({
      path: 'product',
    })
    .populate({
      path: 'variant',
    })
    .populate({
      path: 'requestedBy',
      select: 'name email businessName'
    })
    .populate({
      path: 'reviewedBy',
      select: 'name email'
    }).lean();

  if (!stockUpdateRequest) {
    throwNotFoundError('Stock update request not found');
  }
  const language = { code: 'en' };

  stockUpdateRequest.product = await transformTranslatedFields(stockUpdateRequest.product, language.code);

  return stockUpdateRequest;
};

/**
 * Update stock update request status (approve/reject)
 */
const updateStockUpdateRequestStatus = async ({ adminId, requestId, status, rejectionReason }) => {
  // Check if request ID is valid
  if (!mongoose.Types.ObjectId.isValid(requestId)) {
    throwBadRequestError('Invalid stock update request ID');
  }

  // Get stock update request
  const stockUpdateRequest = await StockUpdateRequest.findById(requestId);

  if (!stockUpdateRequest) {
    throwNotFoundError('Stock update request not found');
  }

  // Check if request is already processed
  if (stockUpdateRequest.status !== stockUpdateStatusValue.PENDING) {
    throwBadRequestError('Stock update request has already been processed');
  }

  // Use transaction helper
  return await withTransaction(async (session) => {
    // Update request status
    stockUpdateRequest.status = status;
    stockUpdateRequest.reviewedBy = adminId;
    stockUpdateRequest.reviewedAt = new Date();

    if (status === stockUpdateStatusValue.REJECTED) {
      stockUpdateRequest.rejectionReason = rejectionReason;
    }

    await stockUpdateRequest.save(sessionOptions(session));

    // If approved, update product or variant stock
    if (status === stockUpdateStatusValue.APPROVED) {
      if (stockUpdateRequest.variant) {
        // Update variant stock
        await ProductVariant.findByIdAndUpdate(
          stockUpdateRequest.variant,
          {
            $set: {
              stock: stockUpdateRequest.requestedStock,
              stockUpdateStatus: 'NONE'
            }
          },
          sessionOptions(session)
        );
      } else {
        // Update product stock
        await Product.findByIdAndUpdate(
          stockUpdateRequest.product,
          {
            $set: {
              stockUpdateStatus: 'NONE'
            }
          },
          sessionOptions(session)
        );
      }
    } else {
      // If rejected, reset stockUpdateStatus
      if (stockUpdateRequest.variant) {
        await ProductVariant.findByIdAndUpdate(
          stockUpdateRequest.variant,
          { $set: { stockUpdateStatus: 'NONE' } },
          sessionOptions(session)
        );
      } else {
        await Product.findByIdAndUpdate(
          stockUpdateRequest.product,
          { $set: { stockUpdateStatus: 'NONE' } },
          sessionOptions(session)
        );
      }
    }

    // Return updated request
    return stockUpdateRequest;
  });
};

const getNextProductCode = async (reqState, productCode, variantProductCode) => {
  if (productCode) {
    const product = await Product.findOne({
      productCode
    });

    let variantCode = 1;

    if (product) {
      const lastVariant = await ProductVariant.findOne({
        product: product._id
      }).sort({ createdAt: -1 }).lean();

      if (variantProductCode) {
        const variantCodeString = variantProductCode.split('-').pop();

        variantCode = parseInt(variantCodeString) + 1;
      } else if (lastVariant && lastVariant.productCode) {
        const variantCodeString = lastVariant.productCode.split('-').pop();

        variantCode = parseInt(variantCodeString) + 1;
      }
    } else if (variantProductCode) {
      const variantCodeString = variantProductCode.split('-').pop();

      variantCode = parseInt(variantCodeString) + 1;
    }

    const variantCodeString = variantCode.toString().padStart(4, '0');
    const newVariantProductCode = `${productCode}-V-${variantCodeString}`;

    return {
      productCode,
      variant: {
        productCode: newVariantProductCode
      }
    };
  }

  const lastProduct = await Product.findOne({
    'pickupAddress.state': reqState
  }).sort({ createdAt: -1 }).lean();

  // add state logic

  let code = '0001';

  const state = states.find(state => state.name.toLowerCase() === reqState.toLowerCase());
  const stateCode = state ? state.id : 'XX';
    
  if (lastProduct && lastProduct.productCode) {
    code = `${(parseInt(lastProduct.productCode.split('-').pop() || 0) + 1).toString().padStart(4, '0')}`;
  }
  const newProductCode = `P-${stateCode}-${code}`;

  const variantCode = 1;
  const variantCodeString = variantCode.toString().padStart(4, '0');
  const newVariantProductCode = `${newProductCode}-V-${variantCodeString}`;

  return {
    productCode: newProductCode,
    variant: {
      productCode: newVariantProductCode
    }
  };
};

const updateProductStatusActiveInactive = async (productId, status) => {
  // Check if product ID is valid
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  const product = await Product.findById(productId);

  if (!product) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // Update status
  product.status = status;

  await product.save();

  return product;
};

const updateVariantStatusActiveInactive = async (variantId, status) => {
  // Check if variant ID is valid
  if (!mongoose.Types.ObjectId.isValid(variantId)) {
    throwBadRequestError(messages.INVALID_VARIANT_ID);
  }

  const variant = await ProductVariant.findById(variantId).populate('product', 'name');

  if (!variant) {
    throwBadRequestError(messages.VARIANT_NOT_FOUND);
  }

  // Update status
  variant.isActive = status === 'ACTIVE';

  await variant.save();

  variant.name = variant.product.name;

  return variant;
};

module.exports = {
  createProduct,
  getAllProducts,
  getPendingProducts,
  getProductById,
  getProductForReview,
  updateProduct,
  deleteProduct,
  updateProductStatus,
  toggleFeaturedStatus,
  toggleShowOnHomepageStatus,
  getHomepageProducts,
  getAllStockUpdateRequests,
  getStockUpdateRequestById,
  updateStockUpdateRequestStatus,
  getNextProductCode,
  updateProductStatusActiveInactive,
  updateVariantStatusActiveInactive
};
