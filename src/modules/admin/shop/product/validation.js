const joi = require('joi');
const { productStatusValue, stockUpdateStatusValue } = require('../../../../constants/dbEnums');

// const dimensionsSchema = joi.object({
//   length: joi.number().min(0).required(),
//   width: joi.number().min(0).required(),
//   height: joi.number().min(0).required()
// });

const addressSchema = joi.object({
  addressLine1: joi.string().allow('').optional(),
  addressLine2: joi.string().allow('').optional(),
  city: joi.string().allow('').optional(),
  state: joi.string().allow('').optional(),
  postalCode: joi.string().allow('').optional(),
  country: joi.string().default('India'),
  phone: joi.string().allow('').optional()
});

const variantSchema = joi.object({
  attributes: joi.object().pattern(
    joi.string(), // attribute name (e.g., "color", "size")
    joi.string() // attribute value (e.g., "red", "XL")
  ).required(),
  description: joi.string().optional(),
  unit: joi.number().optional(),
  hsn: joi.string().trim().optional(),
  barcode: joi.string()
    .trim()
    .required() 
    .messages({
      'string.base': 'Barcode must be a string',
      'string.empty': 'Barcode cannot be empty',
      'any.required': 'Barcode is required',
    }),
  price: joi.number().min(0).required(),
  gstPercentage: joi.number().min(0).optional(),
  taxablePrice: joi.number().min(0).optional(),
  gstPrice: joi.number().min(0).optional(),
  pickupAddress: addressSchema.optional(),
  shipRocketPickupLocationName: joi.string().trim().optional(),
  gstIn: joi.string().trim().optional(),
  discountPrice: joi.number().min(0).allow(null),
  stock: joi.number().integer().min(0).required(),
  sku: joi.string().required(),
  images: joi.array().items(joi.string()).min(1),
  isDefault: joi.boolean().default(false),
  weight: joi.number().min(0).required(),
  dimensions: joi.object({
    length: joi.number().min(0).required(),
    width: joi.number().min(0).required(),
    height: joi.number().min(0).required(),
  }).required(),
  faq: joi.string().optional().allow(null),
  highlight: joi.string().optional().allow(null)
});

const createProductSchema = joi.object({
  name: joi.string().max(100).required(),
  gstPercentage: joi.number().min(0).optional(),
  pickupAddress: addressSchema.optional(),
  category: joi.string().required(),
  subCategory: joi.string().required(),
  temple: joi.string().allow(null),
  // weight: joi.number().min(0).required(),
  // dimensions: dimensionsSchema.required(),
  featured: joi.boolean(),
  showOnHomepage: joi.boolean().default(false),
  hasVariants: joi.boolean().default(false),
  variantAttributes: joi.array().items(joi.string()).when('hasVariants', {
    is: true,
    then: joi.array().min(1).required(),
    otherwise: joi.array().max(0)
  }),
  variants: joi.array().items(variantSchema).when('hasVariants', {
    is: true,
    then: joi.array().min(1).required(),
    otherwise: joi.array().max(0)
  })
});

const updateVariantSchema = joi.object({
  id: joi.string().required(),
  attributes: joi.object().pattern(
    joi.string(), // attribute name (e.g., "color", "size")
    joi.string() // attribute value (e.g., "red", "XL")
  ),
  description: joi.string().optional(),
  unit: joi.number().optional(),
  hsn: joi.string().trim().optional(),
  price: joi.number().min(0),
  gstPercentage: joi.number().min(0).optional(),
  taxablePrice: joi.number().min(0).optional(),
  gstPrice: joi.number().min(0).optional(),
  pickupAddress: addressSchema.optional(),
  shipRocketPickupLocationName: joi.string().trim().optional(),
  gstIn: joi.string().trim().optional(),
  discountPrice: joi.number().min(0).allow(null),
  stock: joi.number().integer().min(0),
  sku: joi.string(),
  images: joi.array().items(joi.string()).min(1),
  isDefault: joi.boolean(),
  weight: joi.number().min(0).optional(),
  dimensions: joi.object({
    length: joi.number().min(0).optional(),
    width: joi.number().min(0).optional(),
    height: joi.number().min(0).optional(),
  }).optional(),
  faq: joi.string().optional().allow(null),
  highlight: joi.string().optional().allow(null),
  barcode: joi.string().trim().optional() 
});

const updateProductSchema = joi.object({
  name: joi.string().max(100),
  gstPercentage: joi.number().min(0).optional(),
  pickupAddress: addressSchema.optional(),
  category: joi.string(),
  subCategory: joi.string(),
  temple: joi.string().allow(null),
  // weight: joi.number().min(0),
  // dimensions: dimensionsSchema,
  featured: joi.boolean(),
  showOnHomepage: joi.boolean(),
  hasVariants: joi.boolean(),
  variantAttributes: joi.array().items(joi.string()),
  variants: joi.array().items(variantSchema),
  addVariants: joi.array().items(variantSchema),
  updateVariants: joi.array().items(updateVariantSchema),
  removeVariants: joi.array().items(joi.string())
}).min(1);

const updateProductStatusSchema = joi.object({
  status: joi.string().valid(productStatusValue.ACTIVE, productStatusValue.REJECTED).required(),
  variants: joi.array().items(
    joi.object({
      id: joi.string().required(),
      price: joi.number().min(0).required()
        .messages({
          'number.empty': 'Price is required',
          'number.min': 'Price must be at least 0',
          'any.required': 'Price is required when approving a product'
        }),
      taxablePrice: joi.number().min(0).optional(),
      gstPrice: joi.number().min(0).optional(),
    })
  ).when('status', {
    is: productStatusValue.ACTIVE,
    then: joi.array().optional(),
    otherwise: joi.array().max(0)
  }),
  rejectionReason: joi.string().when('status', {
    is: productStatusValue.REJECTED,
    then: joi.string().min(10).max(500).required()
      .messages({
        'string.empty': 'Rejection reason is required',
        'string.min': 'Rejection reason must be at least 10 characters',
        'string.max': 'Rejection reason cannot exceed 500 characters',
        'any.required': 'Rejection reason is required when rejecting a product'
      }),
    otherwise: joi.string().allow(null, '')
  })
});

const stockUpdateRequestStatusSchema = joi.object({
  status: joi.string().valid(stockUpdateStatusValue.APPROVED, stockUpdateStatusValue.REJECTED).required(),
  rejectionReason: joi.string().when('status', {
    is: stockUpdateStatusValue.REJECTED,
    then: joi.string().min(10).max(500).required()
      .messages({
        'string.empty': 'Rejection reason is required',
        'string.min': 'Rejection reason must be at least 10 characters',
        'string.max': 'Rejection reason cannot exceed 500 characters',
        'any.required': 'Rejection reason is required when rejecting a stock update request'
      }),
    otherwise: joi.string().allow(null, '')
  })
});

module.exports = {
  createProductSchema,
  updateProductSchema,
  updateProductStatusSchema,
  stockUpdateRequestStatusSchema
};
