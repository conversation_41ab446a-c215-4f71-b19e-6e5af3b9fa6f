const { apiResponse, errorApiResponse } = require('../../../../config/responseHandler');
const { SUCCESS } = require('../../../../constants/common').commonConstants;
const { messages } = require('../../../../messages');
const settlementService = require('./service');
const { getShipmentsSchema, settleShipmentsSchema, generateInvoiceSchema } = require('./validation');
const { saveAuditLog } = require('../../../../utils/auditLogger');
const { auditLogAction } = require('../../../../constants/dbEnums');

/**
 * Get all shipments for settlement with filters
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getShipmentsForSettlement = async (req, res) => {
  try {
    const { error, value } = getShipmentsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const {
      page = 1,
      limit = 10,
      status,
      vendorId,
      startDate,
      endDate,
      sortBy = 'deliveryDate',
      sortOrder = -1
    } = value;

    const data = await settlementService.getShipmentsForSettlement({
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      vendorId,
      startDate,
      endDate,
      sortBy,
      sortOrder
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};
/**
 * Settle shipments
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const settleShipments = async (req, res) => {
  try {
    const { error, value } = settleShipmentsSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const { shipmentId, notes } = value;
    const adminId = req.user.id;

    const data = await settlementService.settleShipments(shipmentId, adminId, { notes });

    //* Save audit log 
    const detail = `Shipment with id ${shipmentId} settled successfully`;
    const model = 'ProductShipment';

    await saveAuditLog(req, req.user.id, auditLogAction.SHIPMENT_SETTLED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SETTLEMENT_PAID,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Generate commission invoice for a shipment
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const generateInvoice = async (req, res) => {
  try {
    const { error, value } = generateInvoiceSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    const data = await settlementService.generateInvoice(value, adminId);

    //* Save audit log 
    const detail = `Invoice generated successfully for shipment with id ${value.shipmentId}`;
    const model = 'Invoice';

    await saveAuditLog(req, req.user.id, auditLogAction.INVOICE_GENERATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: data.message,
      status: true,
      data: data.data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getShipmentsForSettlement,
  settleShipments,
  generateInvoice,
};
