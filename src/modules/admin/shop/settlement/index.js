const express = require('express');
const router = express.Router();
const settlementController = require('./controller');
const auth = require('../../../../middleware/auth');
const { isSuperAdmin } = require('../../../../middleware/roleCheck');

// All routes require super admin authentication
router.use(auth, isSuperAdmin);

// Settlement routes
router.get('/shipments', settlementController.getShipmentsForSettlement);
router.post('/settle', settlementController.settleShipments);
router.post('/generate-invoice', settlementController.generateInvoice);

module.exports = router;
