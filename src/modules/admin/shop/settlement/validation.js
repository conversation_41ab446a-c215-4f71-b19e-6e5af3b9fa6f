const Joi = require('joi');
const mongoose = require('mongoose');

// Validation schema for getting shipments
const getShipmentsSchema = Joi.object({
  page: Joi.number().integer().min(1),
  limit: Joi.number().integer().min(1).max(100),
  status: Joi.string().valid('PENDING', 'SETTLED'),
  vendorId: Joi.string().custom((value, helpers) => {
    if (!mongoose.Types.ObjectId.isValid(value)) {
      return helpers.error('any.invalid');
    }
    return value;
  }),
  startDate: Joi.date().iso(),
  endDate: Joi.date().iso().min(Joi.ref('startDate')),
  sortBy: Joi.string().valid('createdAt', 'deliveryDate', 'settledAt', 'status'),
  sortOrder: Joi.number().valid(1, -1)
});

// Validation schema for settling shipments
const settleShipmentsSchema = Joi.object({
  shipmentId: Joi.string().custom((value, helpers) => {
    if (!mongoose.Types.ObjectId.isValid(value)) {
      return helpers.error('any.invalid');
    }
    return value;
  }),
  notes: Joi.string().allow('', null)
});

// Validation schema for generating invoice
const generateInvoiceSchema = Joi.object({
  shipmentId: Joi.string().custom((value, helpers) => {
    if (!mongoose.Types.ObjectId.isValid(value)) {
      return helpers.error('any.invalid');
    }
    return value;
  }).required()
});

module.exports = {
  getShipmentsSchema,
  settleShipmentsSchema,
  generateInvoiceSchema,
};
