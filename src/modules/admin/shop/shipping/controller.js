const { errorApiResponse, apiResponse } = require('../../../../config/responseHandler');
const { commonConstants } = require('../../../../constants/common');
const { messages } = require('../../../../messages');
const shippingService = require('./service');
const { createShipRocketOrderSchema } = require('./validation');

const { SUCCESS } = commonConstants;

/**
 * Get courier recommendations for a shipment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getCourierRecommendations = async (req, res) => {
  try {
    const { shipmentId } = req.params;

    const result = await shippingService.getCourierRecommendations(shipmentId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Generate AWB (tracking number) for a shipment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const generateAWB = async (req, res) => {
  try {

    const { shipmentId } = req.params;
    const { courierId } = req.body;

    if (!courierId) {
      return res.status(400).json({
        message: 'Courier ID is required',
        status: false
      });
    }

    const result = await shippingService.generateAWB(shipmentId, courierId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Request pickup for a shipment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const requestPickup = async (req, res) => {
  try {
    const { shipmentId } = req.params;

    const result = await shippingService.requestPickup(shipmentId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Ship an order (complete process: select courier, generate AWB, request pickup)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const shipOrder = async (req, res) => {
  try {
    const { shipmentId } = req.params;
    const { courierId } = req.body;

    if (!courierId) {
      return res.status(400).json({
        message: 'Courier ID is required',
        status: false
      });
    }

    const result = await shippingService.shipOrder(shipmentId, courierId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get all available pickup locations
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getPickupLocations = async (req, res) => {
  try {
    const result = await shippingService.getPickupLocations();

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update pickup location for a shipment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updatePickupLocation = async (req, res) => {
  try {
    const { shipmentId } = req.params;
    const { pickupLocationName } = req.body;

    if (!pickupLocationName) {
      return res.status(400).json({
        message: 'Pickup location name is required',
        status: false
      });
    }

    const result = await shippingService.updatePickupLocation(shipmentId, pickupLocationName);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get all shipments with pagination and filters
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllShipments = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      search,
      startDate,
      endDate,
      sortBy = 'createdAt', 
      sortOrder = -1,
      vendorId
    } = req.query;

    const data = await shippingService.getAllShipments({
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      search,
      startDate,
      endDate,
      sortBy,
      sortOrder,
      vendorId
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Shipments retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get shipment by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getShipmentById = async (req, res) => {
  try {
    const { shipmentId } = req.params;

    const data = await shippingService.getShipmentById(shipmentId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Shipment retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Create ShipRocket order
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createShipRocketOrder = async (req, res) => {
  try {
    const { error } = createShipRocketOrderSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const { shipmentId } = req.params;
    const { pickupLocationName, weight, dimensions } = req.body;

    const data = await shippingService.createShipRocketOrder(
      shipmentId,
      pickupLocationName,
      { weight, dimensions }
    );

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'ShipRocket order created successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Track shipment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const trackShipment = async (req, res) => {
  try {
    const { shipmentId } = req.params;

    const data = await shippingService.trackShipment(shipmentId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Shipment tracking retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get vendor orders with shipments
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getVendorOrders = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      search,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = -1,
      vendorId
    } = req.query;

    // if (!vendorId) {
    //   return res.status(400).json({
    //     message: 'Vendor ID is required',
    //     status: false
    //   });
    // }

    const data = await shippingService.getVendorOrders({
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      search,
      startDate,
      endDate,
      sortBy,
      sortOrder,
      vendorId
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Vendor orders retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getAdminOrders = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, search, sortBy = 'createdAt', sortOrder = -1 } = req.query;

    const data = await shippingService.getAdminOrders({
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      search,
      sortBy,
      sortOrder
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getAllShipments,
  getShipmentById,
  createShipRocketOrder,
  getCourierRecommendations,
  generateAWB,
  requestPickup,
  shipOrder,
  getPickupLocations,
  updatePickupLocation,
  trackShipment,
  getVendorOrders,
  getAdminOrders
};
