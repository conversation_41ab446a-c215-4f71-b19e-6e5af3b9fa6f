const express = require('express');
const router = express.Router();

const shippingController = require('./controller');
const { isAdminOrSuperAdmin } = require('../../../../middleware/roleCheck');
const auth = require('../../../../middleware/auth');

// Apply admin authentication middleware to all routes
router.use(auth, isAdminOrSuperAdmin);

// Shipment listing and details
router.get('/', shippingController.getAllShipments);
router.get('/orders', shippingController.getAdminOrders);
router.get('/vendor-orders', shippingController.getVendorOrders);
router.get('/:shipmentId', shippingController.getShipmentById);

// ShipRocket integration
router.post('/:shipmentId/create-order', shippingController.createShipRocketOrder);
router.get('/:shipmentId/couriers', shippingController.getCourierRecommendations);
router.post('/:shipmentId/awb', shippingController.generateAWB);
router.post('/:shipmentId/pickup', shippingController.requestPickup);
router.post('/:shipmentId/ship', shippingController.shipOrder);
router.get('/:shipmentId/track', shippingController.trackShipment);

// Pickup locations
router.get('/pickup-locations', shippingController.getPickupLocations);
router.post('/:shipmentId/pickup-location', shippingController.updatePickupLocation);

module.exports = router;
