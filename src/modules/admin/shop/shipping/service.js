const axios = require('axios');
const shiprocket = require('../../../../services/shiprocket');
const { logShippingEvent } = require('../../../../utils/logger');
const Order = require('../../../../models/Order');
const ProductShipment = require('../../../../models/ProductShipment');
const Product = require('../../../../models/Product');
const { shipmentStatusValue, orderStatusValue } = require('../../../../constants/dbEnums');
const { withTransaction, sessionOptions } = require('../../../../utils/transactionHelper');
const mongoose = require('mongoose');
const { throwBadRequestError, throwNotFoundError, throwInternalServerError } = require('../../../../errors');
const { messages } = require('../../../../messages');
const { transformTranslatedFields } = require('../../../../utils/localizer');
const moment = require('moment');

/**
 * Get courier recommendations for a shipment
 * @param {String} shipmentId - Shipment ID
 */
const getCourierRecommendations = async (shipmentId) => {
  try {
    // Find shipment
    const shipment = await ProductShipment.findById(shipmentId);

    if (!shipment) {
      throw new Error(`Shipment not found: ${shipmentId}`);
    }

    if (!shipment.shiprocketOrderId) {
      throw new Error(`Shipment does not have ShipRocket order ID: ${shipmentId}`);
    }

    // Get courier recommendations from ShipRocket
    const serviceabilityData = await shiprocket.getCourierRecommendations(shipment.shiprocketOrderId);

    // Format the response for the frontend
    let couriers = [];

    if (serviceabilityData && serviceabilityData.data && serviceabilityData.data.available_courier_companies) {
      couriers = serviceabilityData.data.available_courier_companies.map(courier => ({
        id: courier.courier_company_id,
        name: courier.courier_name,
        rating: courier.rating,
        etd: courier.etd,
        charge: courier.rate,
        cod: courier.cod === 1,
        min_weight: courier.min_weight,
        max_weight: courier.max_weight,
        pickup_performance: courier.pickup_performance,
        delivery_performance: courier.delivery_performance
      }));
    }

    // Log the event
    logShippingEvent('COURIER_RECOMMENDATIONS_FETCHED', {
      shipmentId: shipment._id,
      shiprocketOrderId: shipment.shiprocketOrderId,
      shiprocketShipmentId: shipment.shiprocketShipmentId,
      couriersCount: couriers.length
    });

    return {
      success: true,
      shipmentId: shipment._id,
      couriers,
      rawData: serviceabilityData
    };
  } catch (error) {
    logShippingEvent('GET_COURIER_RECOMMENDATIONS_ERROR', {
      shipmentId,
      error: error.message
    });
    throw error;
  }
};

/**
 * Generate AWB (tracking number) for a shipment
 * @param {String} shipmentId - Shipment ID
 * @param {String} courierId - Courier ID to use for the shipment
 */
const generateAWB = async (shipmentId, courierId) => {
  try {
    // Find shipment
    const shipment = await ProductShipment.findById(shipmentId);

    if (!shipment) {
      throw new Error(`Shipment not found: ${shipmentId}`);
    }

    if (!shipment.shiprocketShipmentId) {
      throw new Error(`Shipment does not have ShipRocket order ID: ${shipmentId}`);
    }

    // Generate AWB from ShipRocket
    const shipRocketResponse = await shiprocket.generateAWB(shipment.shiprocketShipmentId, courierId);

    const awbResponse = shipRocketResponse.response?.data || null;

    if (!awbResponse || !awbResponse.awb_code) {
      throwInternalServerError(`Failed to generate AWB: ${shipRocketResponse}`);
    }

    // Update shipment with tracking details
    await withTransaction(async (session) => {
      // Extract tracking information
      const trackingId = awbResponse.awb_code || awbResponse.awb || '';
      const courierName = awbResponse.courier_name || '';
      const courierId = awbResponse.courier_company_id || awbResponse.courier_id || courierId;

      // Update shipment
      shipment.trackingId = trackingId;
      shipment.courier = {
        courierName,
        courierId
      };

      // Store API response
      shipment.apiResponses = shipment.apiResponses || [];
      shipment.apiResponses.push({
        endpoint: 'generate_awb',
        response: shipRocketResponse,
        timestamp: new Date()
      });

      await shipment.save(sessionOptions(session));

      // Log the event
      logShippingEvent('AWB_GENERATED', {
        shipmentId: shipment._id,
        shiprocketOrderId: shipment.shiprocketOrderId,
        shiprocketShipmentId: shipment.shiprocketShipmentId,
        trackingId,
        courierName,
        courierId
      });
    });

    return {
      success: true,
      shipmentId: shipment._id,
      trackingId: shipment.trackingId,
      courierName: shipment.courier?.courierName,
      courierId: shipment.courier?.courierId,
      awbResponse
    };
  } catch (error) {
    logShippingEvent('GENERATE_AWB_ERROR', {
      shipmentId,
      courierId,
      error: error.message
    });
    throw error;
  }
};

/**
 * Request pickup for a shipment
 * @param {String} shipmentId - Shipment ID
 * @param {String} pickUpDate - Pickup date (optional)
 */
const requestPickup = async (shipmentId, pickUpDate = null) => {
  try {
    // Find shipment
    const shipment = await ProductShipment.findById(shipmentId);

    if (!shipment) {
      throw new Error(`Shipment not found: ${shipmentId}`);
    }

    if (!shipment.shiprocketOrderId) {
      throw new Error(`Shipment does not have ShipRocket order ID: ${shipmentId}`);
    }

    if (!shipment.trackingId) {
      throw new Error(`Shipment does not have tracking ID. Generate AWB first: ${shipmentId}`);
    }

    // Generate pickup request from ShipRocket
    const pickupResponse = await shiprocket.generatePickup(shipment.shiprocketShipmentId, pickUpDate);

    if (!pickupResponse || !pickupResponse.response || !pickupResponse.response.pickup_scheduled_date) {
      throwInternalServerError(`Failed to generate pickup request: ${pickupResponse}`);
    }

    // Update shipment
    await withTransaction(async (session) => {
      // Update shipment status
      shipment.status = shipmentStatusValue.PROCESSING;
      shipment.isPickupRequestCreated = true;
      shipment.pickupScheduledDate = pickupResponse.response.pickup_scheduled_date;

      // Store API response
      shipment.apiResponses = shipment.apiResponses || [];
      shipment.apiResponses.push({
        endpoint: 'generate_pickup',
        response: pickupResponse,
        timestamp: new Date()
      });

      await shipment.save(sessionOptions(session));

      // Update order status if needed
      const order = await Order.findById(shipment.order);

      if (order && order.orderStatus === orderStatusValue.PENDING) {
        order.orderStatus = orderStatusValue.PROCESSING;
        await order.save(sessionOptions(session));

        logShippingEvent('ORDER_STATUS_UPDATED', {
          orderId: order._id,
          orderNumber: order.orderNumber,
          oldStatus: orderStatusValue.PENDING,
          newStatus: orderStatusValue.PROCESSING
        });
      }
    });

    // Log the event
    logShippingEvent('PICKUP_REQUESTED', {
      shipmentId: shipment._id,
      shiprocketOrderId: shipment.shiprocketOrderId,
      trackingId: shipment.trackingId,
      pickupResponse
    });

    return {
      success: true,
      shipmentId: shipment._id,
      status: shipment.status,
      pickupResponse
    };
  } catch (error) {
    logShippingEvent('REQUEST_PICKUP_ERROR', {
      shipmentId,
      error: error.message
    });
    throw error;
  }
};

/**
 * Ship an order (complete process: select courier, generate AWB, request pickup)
 * @param {String} shipmentId - Shipment ID
 * @param {String} courierId - Courier ID to use for the shipment
 */
const shipOrder = async (shipmentId, courierId) => {
  try {
    // Step 1: Generate AWB
    const awbResult = await generateAWB(shipmentId, courierId);

    // Step 2: Request pickup
    const pickupResult = await requestPickup(shipmentId);

    return {
      success: true,
      shipmentId,
      trackingId: awbResult.trackingId,
      courierName: awbResult.courierName,
      status: pickupResult.status
    };
  } catch (error) {
    logShippingEvent('SHIP_ORDER_ERROR', {
      shipmentId,
      courierId,
      error: error.message
    });
    throw error;
  }
};

/**
 * Update pickup location for a shipment
 * @param {String} shipmentId - Shipment ID
 * @param {String} pickupLocationName - Name of the pickup location
 */
const updatePickupLocation = async (shipmentId, pickupLocationName) => {
  try {
    // Find shipment
    const shipment = await ProductShipment.findById(shipmentId);

    if (!shipment) {
      throw new Error(`Shipment not found: ${shipmentId}`);
    }

    if (!shipment.shiprocketOrderId) {
      throw new Error(`Shipment does not have ShipRocket order ID: ${shipmentId}`);
    }

    // Update pickup location in ShipRocket
    const updateResponse = await shiprocket.updatePickupLocation(shipment.shiprocketOrderId, pickupLocationName);

    // Store API response
    await withTransaction(async (session) => {
      shipment.apiResponses = shipment.apiResponses || [];
      shipment.apiResponses.push({
        endpoint: 'update_pickup_location',
        response: updateResponse,
        timestamp: new Date()
      });

      await shipment.save(sessionOptions(session));
    });

    // Log the event
    logShippingEvent('PICKUP_LOCATION_UPDATED', {
      shipmentId: shipment._id,
      shiprocketOrderId: shipment.shiprocketOrderId,
      pickupLocationName
    });

    return {
      success: true,
      shipmentId: shipment._id,
      pickupLocationName,
      updateResponse
    };
  } catch (error) {
    logShippingEvent('UPDATE_PICKUP_LOCATION_ERROR', {
      shipmentId,
      pickupLocationName,
      error: error.message
    });
    throw error;
  }
};

/**
 * Get all available pickup locations
 */
const getPickupLocations = async () => {
  try {
    const token = await shiprocket.authenticate();

    // Get pickup locations from ShipRocket
    const response = await axios.get(
      'https://apiv2.shiprocket.in/v1/external/settings/company/pickup',
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    // Format the response
    let pickupLocations = [];

    if (response.data && response.data.data && response.data.data.recent_addresses) {
      pickupLocations = response.data.data.recent_addresses.map(location => ({
        name: location.pickup_location,
        address: location.address,
        city: location.city,
        state: location.state,
        country: location.country,
        pinCode: location.pin_code,
        phone: location.phone
      }));
    }

    // Log the event
    logShippingEvent('PICKUP_LOCATIONS_FETCHED', {
      count: pickupLocations.length
    });

    return {
      success: true,
      pickupLocations,
      rawData: response.data
    };
  } catch (error) {
    logShippingEvent('GET_PICKUP_LOCATIONS_ERROR', {
      error: error.message
    });
    throw error;
  }
};

/**
 * Get all shipments with pagination and filters
 * @param {Object} options - Query options
 * @returns {Object} Shipments and pagination info
 */
const getAllShipments = async ({ page, limit, status, search, startDate, endDate, sortBy, sortOrder, vendorId }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  // Build query
  const query = {};

  // Add status filter if provided
  if (status) {
    query.status = status;
  }

  // Add vendor filter if provided
  if (vendorId) {
    query.vendor = mongoose.Types.ObjectId(vendorId);
  }

  // Add date range filter if provided
  if (startDate && endDate) {
    query.createdAt = {
      $gte: moment(startDate).format('YYYY-MM-DD') + 'T00:00:00.000Z',
      $lte: moment(endDate).format('YYYY-MM-DD') + 'T23:59:59.999Z'
    };
  } else if (startDate) {
    query.createdAt = { $gte: new Date(startDate) };
  } else if (endDate) {
    query.createdAt = { $lte: new Date(endDate) };
  }

  // Add search filter if provided
  if (search) {
    // First, find orders that match the search term
    const orders = await Order.find({
      $or: [
        { orderNumber: { $regex: search, $options: 'i' } },
        { 'user.name': { $regex: search, $options: 'i' } },
        { 'user.email': { $regex: search, $options: 'i' } },
        { 'user.phoneNumber': { $regex: search, $options: 'i' } }
      ]
    }).select('_id');

    const orderIds = orders.map(order => order._id);

    // Then, find products that match the search term
    const products = await Product.find({
      $or: [
        { 'name.en': { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } }
      ]
    }).select('_id');

    const productIds = products.map(product => product._id);

    // Add these IDs to the query
    if (orderIds.length > 0 || productIds.length > 0) {
      query.$or = [];

      if (orderIds.length > 0) {
        query.$or.push({ order: { $in: orderIds } });
      }

      if (productIds.length > 0) {
        query.$or.push({ 'orderItems.product': { $in: productIds } });
      }
    } else if (search) {
      // If no orders or products match the search term, add a dummy condition to return no results
      query._id = null;
    }
  }

  // Get shipments
  const shipments = await ProductShipment.find(query)
    .populate({
      path: 'order',
      select: 'orderNumber orderStatus paymentStatus totalAmount user createdAt'
    })
    .populate({
      path: 'orderItems.product',
      select: 'name sku images price'
    })
    .populate({
      path: 'orderItems.variant',
      select: 'attributes sku images price'
    })
    .populate({
      path: 'vendor',
      select: 'name email businessName'
    })
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit).lean();

  // Get total count
  const total = await ProductShipment.countDocuments(query);

  const language = { code: 'en' };

  return {
    shipments: await transformTranslatedFields(shipments, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get shipment by ID
 * @param {String} shipmentId - Shipment ID
 * @returns {Object} Shipment details
 */
const getShipmentById = async (shipmentId) => {
  // Check if shipment ID is valid
  if (!mongoose.Types.ObjectId.isValid(shipmentId)) {
    throwBadRequestError('Invalid shipment ID');
  }

  // Get shipment
  const shipment = await ProductShipment.findById(shipmentId)
    .populate('order orderItems.product orderItems.variant')
    .populate({
      path: 'vendor',
      select: 'name email businessName'
    }).lean();

  if (!shipment) {
    throwNotFoundError('Shipment not found');
  }

  const language = { code: 'en' };

  return await transformTranslatedFields(shipment, language.code);
};

/**
 * Create ShipRocket order
 * @param {String} shipmentId - Shipment ID
 * @param {String} pickupLocationName - Name of the pickup location (optional)
 * @param {Object} packageDetails - Weight and dimensions of the package (optional)
 * @returns {Object} Created ShipRocket order
 */
const createShipRocketOrder = async (shipmentId, pickupLocationName, packageDetails = {}) => {
  try {
    // Find shipment
    const shipment = await ProductShipment.findById(shipmentId)
      .populate({
        path: 'orderItems.product',
        select: 'name sku images price weight dimensions'
      })
      .populate({
        path: 'orderItems.variant',
        select: 'attributes sku images price weight dimensions'
      })
      .populate({
        path: 'vendor',
        select: 'name email businessName address shipRocketPickupLocationName'
      });

    if (!shipment) {
      throwNotFoundError(`Shipment not found: ${shipmentId}`);
    }

    // Check if shipment already has a ShipRocket order ID
    if (shipment.shiprocketOrderId) {
      return {
        success: true,
        message: 'ShipRocket order already exists',
        shipmentId: shipment._id,
        shiprocketOrderId: shipment.shiprocketOrderId
      };
    }

    // Get order
    // Get order
    const order = await Order.findById(shipment.order)
      .populate('user')
      .populate({
        path: 'items.product',
        select: 'name images sku'
      })
      .populate({
        path: 'items.variant',
        select: 'attributes images sku'
      });

    if (!order) {
      throwNotFoundError(messages.ORDER_NOT_FOUND);
    }

    // Get user
    const user = order.user;

    if (!user) {
      throwNotFoundError(`User not found for order: ${order._id}`);
    }

    // Get vendor
    // const vendor = shipment.vendor;

    // Add pickup location if needed
    const pickupLocation = pickupLocationName || shipment.shipRocketPickupLocationName;

    // Calculate package weight and dimensions if not provided
    let weight = packageDetails.weight;
    let dimensions = packageDetails.dimensions;

    if (!weight || !dimensions) {
      // Calculate total weight and dimensions from order items
      const { calculatedWeight, calculatedDimensions } = calculatePackageDetails(shipment.orderItems);

      // Use provided values or calculated values
      weight = weight || calculatedWeight;
      dimensions = dimensions || calculatedDimensions;
    }

    // Format order data for ShipRocket
    const orderData = await shiprocket.formatOrderData(order, shipment, user);

    // Set pickup location
    orderData.pickup_location = pickupLocation;

    // Add weight and dimensions to order data
    if (weight) {
      orderData.weight = weight;
    }

    if (dimensions) {
      orderData.length = dimensions.length;
      orderData.breadth = dimensions.width;
      orderData.height = dimensions.height;
    }
    // Create ShipRocket order
    const shiprocketResponse = await shiprocket.createOrder(orderData);

    if (!shiprocketResponse || !shiprocketResponse.order_id) {
      throwInternalServerError(`Failed to create ShipRocket order: ${shiprocketResponse}`);
    }

    // Update shipment with ShipRocket order ID and package details
    await withTransaction(async (session) => {
      shipment.shiprocketOrderId = shiprocketResponse.order_id;
      shipment.shiprocketShipmentId = shiprocketResponse.shipment_id;

      // Store package details
      if (weight || dimensions) {
        shipment.packageDetails = shipment.packageDetails || {};

        if (weight) {
          shipment.packageDetails.weight = weight;
        }

        if (dimensions) {
          shipment.packageDetails.dimensions = dimensions;
        }
      }

      // Store API response
      shipment.apiResponses = shipment.apiResponses || [];
      shipment.apiResponses.push({
        endpoint: 'create_order',
        response: shiprocketResponse,
        timestamp: new Date()
      });

      await shipment.save(sessionOptions(session));
    });

    // Log the event
    logShippingEvent('SHIPROCKET_ORDER_CREATED', {
      shipmentId: shipment._id,
      orderId: order._id,
      orderNumber: order.orderNumber,
      shiprocketOrderId: shiprocketResponse.order_id,
      shiprocketShipmentId: shiprocketResponse.shipment_id,
      weight,
      dimensions
    });

    return {
      success: true,
      shipmentId: shipment._id,
      shiprocketOrderId: shipment.shiprocketOrderId,
      shiprocketShipmentId: shipment.shiprocketShipmentId,
      pickupLocation,
      weight,
      dimensions,
      shiprocketResponse
    };
  } catch (error) {
    logShippingEvent('CREATE_SHIPROCKET_ORDER_ERROR', {
      shipmentId,
      pickupLocationName,
      packageDetails,
      error: error.message
    });
    throw error;
  }
};

/**
 * Calculate package weight and dimensions from order items
 * @param {Array} orderItems - Array of order items
 * @returns {Object} Calculated weight and dimensions
 */
const calculatePackageDetails = (orderItems) => {
  // Default values
  const defaultWeight = 0.5; // 500g
  const defaultDimensions = { length: 10, width: 10, height: 5 }; // 10x10x5 cm

  if (!orderItems || orderItems.length === 0) {
    return { calculatedWeight: defaultWeight, calculatedDimensions: defaultDimensions };
  }

  // Calculate total weight
  let totalWeight = 0;
  let maxLength = 0;
  let maxWidth = 0;
  let totalHeight = 0;

  // Track if we have at least one item with dimensions
  let hasDimensions = false;

  orderItems.forEach(item => {
    const quantity = item.quantity || 1;

    // Add weight
    const itemWeight = item.product?.weight || item.variant?.weight || 0;

    totalWeight += (itemWeight * quantity);

    // Process dimensions if available
    const itemDimensions = item.product?.dimensions || item.variant?.dimensions;

    if (itemDimensions) {
      hasDimensions = true;

      // For simplicity, we're using the largest length and width,
      // and adding up the heights (assuming items can be stacked)
      maxLength = Math.max(maxLength, itemDimensions.length || 0);
      maxWidth = Math.max(maxWidth, itemDimensions.width || 0);
      totalHeight += ((itemDimensions.height || 0) * quantity);
    }
  });

  // Use default weight if calculated weight is too small
  if (totalWeight < 0.1) {
    totalWeight = defaultWeight;
  }

  // Use default dimensions if we don't have any dimensions
  let calculatedDimensions;

  if (hasDimensions && maxLength > 0 && maxWidth > 0 && totalHeight > 0) {
    calculatedDimensions = {
      length: Math.max(maxLength, 1), // Ensure minimum 1 cm
      width: Math.max(maxWidth, 1),
      height: Math.max(totalHeight, 1)
    };
  } else {
    calculatedDimensions = defaultDimensions;
  }

  return { calculatedWeight: totalWeight, calculatedDimensions };
};

/**
 * Track shipment
 * @param {String} shipmentId - Shipment ID
 * @returns {Object} Tracking information
 */
const trackShipment = async (shipmentId) => {
  try {
    // Find shipment
    const shipment = await ProductShipment.findById(shipmentId);

    if (!shipment) {
      throwNotFoundError(`Shipment not found: ${shipmentId}`);
    }

    if (!shipment.shiprocketOrderId) {
      throwBadRequestError(`Shipment does not have ShipRocket order ID: ${shipmentId}`);
    }

    // Track shipment
    const trackingData = await shiprocket.trackShipment(shipment.shiprocketOrderId);

    // Update shipment with tracking information
    await withTransaction(async (session) => {
      // Store API response
      shipment.apiResponses = shipment.apiResponses || [];
      shipment.apiResponses.push({
        endpoint: 'track_shipment',
        response: trackingData,
        timestamp: new Date()
      });

      // Update shipment status if needed
      if (trackingData.tracking_data && trackingData.tracking_data.shipment_track) {
        const shipmentTrack = trackingData.tracking_data.shipment_track;

        // Update status if available
        if (shipmentTrack.current_status) {
          const currentStatus = shipmentTrack.current_status.toLowerCase();

          // Map ShipRocket status to our status
          if (currentStatus.includes('delivered')) {
            shipment.status = shipmentStatusValue.DELIVERED;
          } else if (currentStatus.includes('in transit') || currentStatus.includes('shipped')) {
            shipment.status = shipmentStatusValue.SHIPPED;
          } else if (currentStatus.includes('pickup')) {
            shipment.status = shipmentStatusValue.PROCESSING;
          }
        }
      }

      await shipment.save(sessionOptions(session));

      // Update order status if needed
      if (shipment.status === shipmentStatusValue.DELIVERED) {
        const order = await Order.findById(shipment.order);

        if (order) {
          // Check if all shipments for this order are delivered
          const allShipments = await ProductShipment.find({ order: order._id });
          const allDelivered = allShipments.every(s => s.status === shipmentStatusValue.DELIVERED);

          if (allDelivered) {
            order.orderStatus = orderStatusValue.DELIVERED;
            await order.save(sessionOptions(session));

            logShippingEvent('ORDER_STATUS_UPDATED', {
              orderId: order._id,
              orderNumber: order.orderNumber,
              oldStatus: order.orderStatus,
              newStatus: orderStatusValue.DELIVERED
            });
          }
        }
      }
    });

    return {
      success: true,
      shipmentId: shipment._id,
      status: shipment.status,
      trackingData
    };
  } catch (error) {
    logShippingEvent('TRACK_SHIPMENT_ERROR', {
      shipmentId,
      error: error.message
    });
    throw error;
  }
};

/**
 * Get vendor orders with shipments
 * @param {Object} options - Query options
 * @returns {Object} Orders and pagination info
 */
const getVendorOrders = async ({ page, limit, status, search, startDate, endDate, sortBy, sortOrder, vendorId }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  // Build query
  const query = {};

  if (vendorId) {
    query.vendor = new mongoose.Types.ObjectId(vendorId);
  }

  // Add status filter if provided
  if (status) {
    query.status = status;
  }

  // Add date range filter if provided
  if (startDate && endDate) {
    query.createdAt = {
      $gte: moment(startDate).format('YYYY-MM-DD') + 'T00:00:00.000Z',
      $lte: moment(endDate).format('YYYY-MM-DD') + 'T23:59:59.999Z'
    };
  } else if (startDate) {
    query.createdAt = { $gte: new Date(startDate) };
  } else if (endDate) {
    query.createdAt = { $lte: new Date(endDate) };
  }

  // Add search filter if provided
  if (search) {
    // First, find orders that match the search term
    const orders = await Order.find({
      $or: [
        { orderNumber: { $regex: search, $options: 'i' } },
        { 'user.name': { $regex: search, $options: 'i' } },
        { 'user.email': { $regex: search, $options: 'i' } },
        { 'user.phoneNumber': { $regex: search, $options: 'i' } }
      ]
    }).select('_id');

    const orderIds = orders.map(order => order._id);

    // Then, find products that match the search term
    const products = await Product.find({
      $or: [
        { 'name.en': { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } }
      ]
    }).select('_id');

    const productIds = products.map(product => product._id);

    // Add these IDs to the query
    if (orderIds.length > 0 || productIds.length > 0) {
      query.$or = [];

      if (orderIds.length > 0) {
        query.$or.push({ order: { $in: orderIds } });
      }

      if (productIds.length > 0) {
        query.$or.push({ 'orderItems.product': { $in: productIds } });
      }
    } else if (search) {
      // If no orders or products match the search term, add a dummy condition to return no results
      query._id = null;
    }
  }

  // Get shipments
  const shipments = await ProductShipment.find(query)
    .populate({
      path: 'order',
      select: 'orderNumber orderStatus paymentStatus totalAmount user createdAt'
    })
    .populate({
      path: 'orderItems.product',
      select: 'name sku images price'
    })
    .populate({
      path: 'orderItems.variant',
      select: 'attributes sku images price'
    })
    .populate({
      path: 'vendor',
      select: 'name email businessName address shipRocketPickupLocationName'
    })
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit).lean();

  // Get total count
  const total = await ProductShipment.countDocuments(query);

  const language = { code: 'en' };

  return {
    shipments: await transformTranslatedFields(shipments, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

const getAdminOrders = async ({ page, limit, status, search, sortBy, sortOrder, startDate, endDate }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const language = { code: 'en' };

  // Query for order-level filters
  const query = {
    paymentStatus: 'COMPLETED' // Only show paid orders
  };

  if (status) {
    query.orderStatus = status;
  } else {
    query.orderStatus = { $in: [ orderStatusValue.PROCESSING, orderStatusValue.PARTIALLY_SHIPPED ] };
  }

  if (startDate && endDate) {
    query.createdAt = {
      $gte: new Date(moment(startDate).format('YYYY-MM-DD') + 'T00:00:00.000Z'),
      $lte: new Date(moment(endDate).format('YYYY-MM-DD') + 'T23:59:59.999Z')
    };
  } else if (startDate) {
    query.createdAt = { $gte: new Date(moment(startDate).format('YYYY-MM-DD') + 'T00:00:00.000Z') };
  } else if (endDate) {
    query.createdAt = { $lte: new Date(moment(endDate).format('YYYY-MM-DD') + 'T23:59:59.999Z') };
  }

  // Build the aggregation pipeline
  const pipeline = [
    {
      $lookup: {
        from: 'productshipments',
        localField: '_id',
        foreignField: 'order',
        as: 'shipments'
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'userDetails'
      }
    },
    {
      $addFields: {
        user: { $arrayElemAt: [ '$userDetails', 0 ] },
        shipments: {
          $filter: {
            input: '$shipments',
            as: 'shipment',
            cond: {
              $and: [
                { $eq: [ '$$shipment.isOneGodProduct', true ] }
              ]
            }
          }
        }
      }
    },
    { $unwind: '$shipments' },
    { $match: query },
    {
      $lookup: {
        from: 'products',
        localField: 'shipments.orderItems.product',
        foreignField: '_id',
        as: 'productDetails'
      }
    },
    {
      $lookup: {
        from: 'productvariants',
        localField: 'shipments.orderItems.variant',
        foreignField: '_id',
        as: 'variantDetails'
      }
    }
  ];

  // Search filter
  if (search) {
    pipeline.push({
      $match: {
        $or: [
          { orderNumber: { $regex: search, $options: 'i' } },
          { 'user.email': { $regex: search, $options: 'i' } },
          { [`shipments.orderItems.name.${language.code}`]: { $regex: search, $options: 'i' } },
          {
            $expr: {
              $regexMatch: {
                input: { $concat: [ '$user.firstName.en', ' ', '$user.lastName.en' ] },
                regex: search,
                options: 'i'
              }
            }
          }
        ]
      }
    });
  }

  // Pagination and sorting
  pipeline.push(
    { $sort: sortOptions },
    { $skip: skip },
    { $limit: limit }
  );

  const orders = await Order.aggregate(pipeline).collation({ locale: 'en', strength: 1 });

  // Transform each shipment as a separate record
  let orderDetails = orders.map(order => {
    const shipment = order.shipments;

    shipment.orderItems = shipment.orderItems.map(item => ({
      ...item,
      product: order.productDetails.find(p => p._id.toString() === item.product.toString()),
      variant: order.variantDetails.find(v => v._id.toString() === item.variant.toString())
    }));

    return {
      _id: order._id,
      orderNumber: order.orderNumber,
      orderStatus: order.orderStatus,
      createdAt: order.createdAt,
      user: order.user,
      shipments: shipment,
      subtotal: order.subtotal,
      shippingCost: order.shippingCost,
      total: order.total,
      tax: order.tax,
      discount: order.discount,
      shippingAddress: order.shippingAddress,
      billingAddress: order.billingAddress,
      paymentMethod: order.paymentMethod,
      paymentStatus: order.paymentStatus,
      notes: order.notes,
      trackingNumber: order.trackingNumber,
      shippingProvider: order.shippingProvider,
      estimatedDelivery: order.estimatedDelivery,
      cancelReason: order.cancelReason,
      cancelledAt: order.cancelledAt,
      deliveredAt: order.deliveredAt
    };
  });

  orderDetails = await transformTranslatedFields(orderDetails, language.code);

  // Count pipeline
  const countPipeline = [
    {
      $lookup: {
        from: 'productshipments',
        localField: '_id',
        foreignField: 'order',
        as: 'shipments'
      }
    },
    {
      $addFields: {
        shipments: {
          $filter: {
            input: '$shipments',
            as: 'shipment',
            cond: {
              $and: [
                { $eq: [ '$$shipment.isOneGodProduct', true ] }
              ]
            }
          }
        }
      }
    },
    { $unwind: '$shipments' },
    { $match: query }
  ];

  // Search condition for count
  if (search) {
    countPipeline.push({
      $match: {
        $or: [
          { orderNumber: { $regex: search, $options: 'i' } },
          { 'user.email': { $regex: search, $options: 'i' } },
          { [`shipments.orderItems.name.${language.code}`]: { $regex: search, $options: 'i' } },
          {
            $expr: {
              $regexMatch: {
                input: { $concat: [ '$user.firstName.en', ' ', '$user.lastName.en' ] },
                regex: search,
                options: 'i'
              }
            }
          }
        ]
      }
    });
  }

  countPipeline.push({ $count: 'total' });

  const totalCount = await Order.aggregate(countPipeline).collation({ locale: 'en', strength: 1 });
  const total = totalCount.length > 0 ? totalCount[0].total : 0;

  return {
    orders: orderDetails,
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

module.exports = {
  getCourierRecommendations,
  generateAWB,
  requestPickup,
  shipOrder,
  updatePickupLocation,
  getPickupLocations,
  getAllShipments,
  getShipmentById,
  createShipRocketOrder,
  trackShipment,
  getVendorOrders,
  getAdminOrders
};
