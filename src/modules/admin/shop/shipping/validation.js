const joi = require('joi');

/**
 * Validation schema for creating a ShipRocket order
 */
const createShipRocketOrderSchema = joi.object({
  pickupLocationName: joi.string().allow(null, '')
    .messages({
      'string.base': 'Pickup location name must be a string'
    }),
  weight: joi.number().min(0.001).allow(null)
    .messages({
      'number.base': 'Weight must be a number',
      'number.min': 'Weight must be at least 0.001 kg'
    }),
  dimensions: joi.object({
    length: joi.number().min(0.1).required()
      .messages({
        'number.base': 'Length must be a number',
        'number.min': 'Length must be at least 0.1 cm',
        'any.required': 'Length is required'
      }),
    width: joi.number().min(0.1).required()
      .messages({
        'number.base': 'Width must be a number',
        'number.min': 'Width must be at least 0.1 cm',
        'any.required': 'Width is required'
      }),
    height: joi.number().min(0.1).required()
      .messages({
        'number.base': 'Height must be a number',
        'number.min': 'Height must be at least 0.1 cm',
        'any.required': 'Height is required'
      })
  }).allow(null)
});

/**
 * Validation schema for generating AWB
 */
const generateAWBSchema = joi.object({
  courierId: joi.string().required()
    .messages({
      'string.empty': 'Courier ID is required',
      'any.required': 'Courier ID is required'
    })
});

/**
 * Validation schema for shipping an order
 */
const shipOrderSchema = joi.object({
  courierId: joi.string().required()
    .messages({
      'string.empty': 'Courier ID is required',
      'any.required': 'Courier ID is required'
    })
});

/**
 * Validation schema for updating pickup location
 */
const updatePickupLocationSchema = joi.object({
  pickupLocationName: joi.string().required()
    .messages({
      'string.empty': 'Pickup location name is required',
      'any.required': 'Pickup location name is required'
    })
});

module.exports = {
  createShipRocketOrderSchema,
  generateAWBSchema,
  shipOrderSchema,
  updatePickupLocationSchema
};
