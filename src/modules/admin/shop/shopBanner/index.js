const express = require('express');
const router = express.Router();
const shopBannerController = require('./controller');
const auth = require('../../../../middleware/auth');
const { isSuperAdmin } = require('../../../../middleware/roleCheck');

router.post('/', auth, isSuperAdmin, shopBannerController.createBanner);

router.put('/:id', auth, isSuperAdmin, shopBannerController.updateBanner);

router.get('/', auth, isSuperAdmin, shopBannerController.listBanners);

router.get('/:id', auth, isSuperAdmin, shopBannerController.getBannerById);

router.delete('/:id', auth, isSuperAdmin, shopBannerController.deleteBanner);

router.post('/upload-url', auth, isSuper<PERSON><PERSON><PERSON>, shopBannerController.getUploadUrl);

router.delete('/media/remove', auth, isSuperAdmin, shopBannerController.removeUploadedImage);

module.exports = router;
