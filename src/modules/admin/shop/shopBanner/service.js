const ShopBanner = require('../../../../models/ShopBanner');
const { throwBadRequestError } = require('../../../../errors');
const { messages } = require('../../../../messages');
const { deleteFile } = require('../../../../utils/s3Service');
const { translateDataForStore } = require('../../../../utils/translateInput');
const { transformTranslatedFields } = require('../../../../utils/localizer');

const createBanner = async (bannerData, userId) => {

  const cloudFrontUrl = `${process.env.MEDIA_URL}/${bannerData.url}`;

  bannerData.key = bannerData.url;
  bannerData.url = cloudFrontUrl;

  // Translate fields
  const translatedFields = ['title', 'description'];
  const translatedData = await translateDataForStore(translatedFields, bannerData);

  translatedFields.forEach(field => {
    if (bannerData[field]) {
      delete bannerData[field];
    }
  });

  const banner = await ShopBanner.create({
    ...bannerData,
    ...translatedData,
    createdBy: userId,
    updatedBy: userId
  });

  return banner;
};

const updateBanner = async (bannerId, updateData, userId) => {
  const banner = await ShopBanner.findById(bannerId);
  
  if (!banner) {
    throwBadRequestError(messages.BANNER_NOT_FOUND);
  }

  // Translate fields if provided
  const translatedFields = ['title', 'description'];
  const hasTranslatableFields = translatedFields.some(field => updateData[field]);

  if (hasTranslatableFields) {
    const translatedData = await translateDataForStore(translatedFields, updateData);

    translatedFields.forEach(field => {
      if (updateData[field]) {
        delete updateData[field];
      }
    });

    updateData = {
      ...updateData,
      ...translatedData
    };
  }

  if (updateData?.url) {
    if (banner.key && banner.key !== updateData.url) {
      await deleteFile(banner.key);
    }

    updateData.key = updateData.url;
    const cloudFrontUrl = `${process.env.MEDIA_URL}/${updateData.url}`;

    updateData.url = cloudFrontUrl;
  }

  return await ShopBanner.findByIdAndUpdate(
    bannerId,
    { ...updateData, updatedBy: userId },
    { new: true }
  );
};

const listBanners = async (query) => {
  const { page = 1, limit = 10, status, search } = query;
  
  const filter = {};
  
  if (status) {
    filter.status = status;
  }

  const language = { code: 'en' };
  
  if (search) {
    filter.$or = [
      { [`title.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`description.${language.code}`]: { $regex: search, $options: 'i' } }
    ];
  }

  const banners = await ShopBanner.find(filter)
    .sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 })
    .skip((page - 1) * limit)
    .limit(limit)
    .populate('createdBy', '-password')
    .populate('updatedBy', '-password').lean();

  const total = await ShopBanner.countDocuments(filter);

  return {
    banners: await transformTranslatedFields(banners, language.code),
    pagination: {
      total,
      page: parseInt(page),
      pages: Math.ceil(total / limit)
    }
  };
};

const deleteBanner = async (bannerId) => {
  const banner = await ShopBanner.findByIdAndDelete(bannerId);

  if (!banner) {
    throwBadRequestError(messages.BANNER_NOT_FOUND);
  }

  if (banner.key) {
    await deleteFile(banner.key);
  }

  return banner;
};

const getActiveBanners = async () => {
  const now = new Date();
  
  const shopBanners = await ShopBanner.find({
    isActive: true,
    status: 'ACTIVE',
    startDate: { $lte: now },
    endDate: { $gte: now }
  }).sort({ startDate: 1 }).collation({ locale: 'en', strength: 1 }).lean();

  return await transformTranslatedFields(shopBanners, 'en');
};

const getBannerById = async (id) => {
  const banner = await ShopBanner.findById(id).lean();
  
  if (!banner) {
    throwBadRequestError(messages.BANNER_NOT_FOUND);
  }

  const language = { code: 'en' };

  return await transformTranslatedFields(banner, language.code);
};

module.exports = {
  createBanner,
  updateBanner,
  listBanners,
  deleteBanner,
  getActiveBanners,
  getBannerById
};
