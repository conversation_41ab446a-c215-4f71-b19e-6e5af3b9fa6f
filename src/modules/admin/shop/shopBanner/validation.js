const joi = require('joi');
const mongoose = require('mongoose');
const { extension } = require('../../../../constants/dbEnums');

const createBannerSchema = joi.object({
  title: joi.string().optional().allow('', null),
  url: joi.string().required(),
  description: joi.string().optional().allow('', null),
  isActive: joi.boolean().optional(),
  order: joi.number().optional(),
});

const updateBannerSchema = joi.object({
  title: joi.string().optional().allow('', null),
  url: joi.string().optional(),
  description: joi.string().optional().allow('', null),
  isActive: joi.boolean().optional(),
  order: joi.number().optional(),
}).min(1);

const uploadUrlSchema = joi.object({
  extension: joi.string()
    .valid(extension.FLV, extension.MP4, extension.MOV, extension.HEVC, extension.WEBM, extension.MPEG)
    .required()
    .messages({
      'any.required': 'File extension is required',
      'any.only': 'Invalid file extension. Only FLV, MP4, MOV, HEVC, WEBM and MPEG are allowed'
    })
});

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string().custom(validateObjectId).required().messages({
  'any.invalid': 'Invalid ID format',
  'any.required': 'ID is required'
});

const deleteVideoSchema = joi.object({
  key: joi.string().required()
    .messages({
      'string.empty': 'Key is required',
      'any.required': 'Key is required'
    })
});

module.exports = {
  createBannerSchema,
  updateBannerSchema,
  uploadUrlSchema,
  validateId,
  deleteVideoSchema,
};