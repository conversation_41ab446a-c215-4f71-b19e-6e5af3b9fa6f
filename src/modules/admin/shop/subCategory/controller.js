const { apiResponse, errorApiResponse } = require('../../../../config/responseHandler');
const { commonConstants } = require('../../../../constants/common');
const { messages } = require('../../../../messages');
const { getPresignedUrl } = require('../../../../utils/s3Service');
const subCategoryService = require('./service');
const { createSubCategorySchema, updateSubCategorySchema } = require('./validation');
const { SUCCESS } = commonConstants;
const { saveAuditLog } = require('../../../../utils/auditLogger');
const { auditLogAction } = require('../../../../constants/dbEnums');

/**
 * Create a new subcategory
 */
const createSubCategory = async (req, res) => {
  try {
    const { error } = createSubCategorySchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    
    const data = await subCategoryService.createSubCategory({
      adminId,
      ...req.body
    });

    //* Save audit log 
    const detail = `Subcategory ${data.name.en} created successfully`;
    const model = 'SubCategory';

    await saveAuditLog(req, req.user.id, auditLogAction.SUBCATEGORY_CREATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUBCATEGORY_CREATED || 'Subcategory created successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get all subcategories
 */
const getAllSubCategories = async (req, res) => {
  try {
    const { includeInactive = false } = req.query;
    
    const data = await subCategoryService.getAllSubCategories(includeInactive === 'true');

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get subcategories by category ID
 */
const getSubCategoriesByCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { includeInactive = false } = req.query;
    
    const data = await subCategoryService.getSubCategoriesByCategory(categoryId, includeInactive === 'true');

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get subcategory by ID
 */
const getSubCategoryById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const data = await subCategoryService.getSubCategoryById(id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update subcategory
 */
const updateSubCategory = async (req, res) => {
  try {
    const { error } = updateSubCategorySchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const adminId = req.user.id;
    const { id } = req.params;
    
    const data = await subCategoryService.updateSubCategory({
      adminId,
      subCategoryId: id,
      ...req.body
    });

    //* Save audit log 
    const detail = `Subcategory ${data.name.en} updated successfully`;
    const model = 'SubCategory';

    await saveAuditLog(req, req.user.id, auditLogAction.SUBCATEGORY_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUBCATEGORY_UPDATED || 'Subcategory updated successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete subcategory
 */
const deleteSubCategory = async (req, res) => {
  try {
    const { id } = req.params;
    
    const data = await subCategoryService.deleteSubCategory(id);

    //* Save audit log 
    const detail = `Subcategory ${data.name.en} deleted successfully`;
    const model = 'SubCategory';

    await saveAuditLog(req, req.user.id, auditLogAction.SUBCATEGORY_DELETED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUBCATEGORY_DELETED || 'Subcategory deleted successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get upload URL for subcategory image
 */
const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }

    const { extension } = req.body;
    
    const uploadData = await getPresignedUrl(extension, 'subcategories');

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createSubCategory,
  getAllSubCategories,
  getSubCategoriesByCategory,
  getSubCategoryById,
  updateSubCategory,
  deleteSubCategory,
  getUploadUrl
};
