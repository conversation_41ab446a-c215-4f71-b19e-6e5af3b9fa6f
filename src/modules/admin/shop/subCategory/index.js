const express = require('express');
const router = express.Router();
const subCategoryController = require('./controller');
const auth = require('../../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../../middleware/roleCheck');

// All routes require admin authentication
router.use(auth, isAdminOrSuperAdmin);

router.post('/', subCategoryController.createSubCategory);
router.get('/', subCategoryController.getAllSubCategories);
router.get('/category/:categoryId', subCategoryController.getSubCategoriesByCategory);
router.get('/:id', subCategoryController.getSubCategoryById);
router.put('/:id', subCategoryController.updateSubCategory);
router.delete('/:id', subCategoryController.deleteSubCategory);
router.post('/upload-url', subCategoryController.getUploadUrl);

module.exports = router;
