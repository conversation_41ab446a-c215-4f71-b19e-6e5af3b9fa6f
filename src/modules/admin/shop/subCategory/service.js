const mongoose = require('mongoose');
const SubCategory = require('../../../../models/SubCategory');
const Category = require('../../../../models/Category');
const { throwBadRequestError } = require('../../../../errors');
const { messages } = require('../../../../messages');
const { processImages } = require('../../../../utils/processImage');
const Product = require('../../../../models/Product');
const { translateDataForStore } = require('../../../../utils/translateInput');
const { transformTranslatedFields } = require('../../../../utils/localizer');

/**
 * Create a new subcategory
 */
const createSubCategory = async ({ adminId, name, description, image, category, variantTypes }) => {
  // Check if subcategory name already exists
  const subCategoryExists = await SubCategory.findOne({ name });

  if (subCategoryExists) {
    throwBadRequestError(messages.SUBCATEGORY_ALREADY_EXISTS || 'Subcategory with this name already exists');
  }

  // Check if category exists
  if (!mongoose.Types.ObjectId.isValid(category)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID || 'Invalid category ID');
  }

  const categoryExists = await Category.findById(category);

  if (!categoryExists) {
    throwBadRequestError(messages.CATEGORY_NOT_FOUND || 'Category not found');
  }

  // Process the image if provided
  let processedImage = image;

  if (image) {
    const processedImages = processImages([ image ]);

    processedImage = processedImages[0];
  }

  const translatedData = await translateDataForStore([ 'name', 'description' ], { name, description });

  name = translatedData.name;
  description = translatedData.description;

  // Create subcategory
  const subCategory = new SubCategory({
    name,
    description,
    image: processedImage,
    category,
    variantTypes,
    isActive: true,
    createdBy: adminId
  });

  await subCategory.save();

  return subCategory;
};

/**
 * Get all subcategories
 */
const getAllSubCategories = async (includeInactive, queryParams) => {
  const { search } = queryParams;

  //* Base query
  const baseMatch = includeInactive ? {} : { isActive: true };

  const pipeline = [
    { $match: baseMatch },

    //* Lookup Category
    {
      $lookup: {
        from: 'categories', //* collection name
        localField: 'category',
        foreignField: '_id',
        as: 'category'
      },
    },
    {
      $unwind: {
        path: '$category',
        preserveNullAndEmptyArrays: true
      }
    }
  ];
  
  const language = { code: 'en' };

  //* If search is provided 
  if (search) {
    pipeline.push({
      $match: {
        $or: [
          //* Sub-Category Name and Desc. 
          { 'name.en': { $regex: search, $options: 'i' } },
          { 'name.hi': { $regex: search, $options: 'i' } },
          { 'description.en': { $regex: search, $options: 'i' } },
          { 'description.hi': { $regex: search, $options: 'i' } },
          { 'category.name.en': { $regex: search, $options: 'i' } },
          { 'category.name.hi': { $regex: search, $options: 'i' } },
          { 'category.description.en': { $regex: search, $options: 'i' } },
          { 'category.description.hi': { $regex: search, $options: 'i' } }
        ]
      }
    });
  }

  pipeline.push({
    $project: {
      _id: 1,
      name: 1,
      description: 1,
      image: 1,
      category: {
        _id: '$category._id',
        name: '$category.name' 
      },
      isActive: 1,
      variantTypes: 1,
      createdBy: 1,
      createdAt: 1,
      updatedAt: 1,
      __v: 1
    }
  });

  pipeline.push({ $sort: { createdAt: -1 } });

  const subCategories = await SubCategory.aggregate(pipeline);

  return await transformTranslatedFields(subCategories, language.code);
};

/**
 * Get subcategories by category ID
 */
const getSubCategoriesByCategory = async (categoryId, includeInactive) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID || 'Invalid category ID');
  }

  const query = { category: categoryId };
  
  if (!includeInactive) {
    query.isActive = true;
  }

  const subCategories = await SubCategory.find(query)
    .populate('category', 'name')
    .sort({ name: 1 }).collation({ locale: 'en', strength: 1 }).lean();

  return await transformTranslatedFields(subCategories, 'en');
};

/**
 * Get subcategory by ID
 */
const getSubCategoryById = async (subCategoryId) => {
  // Check if subcategory ID is valid
  if (!mongoose.Types.ObjectId.isValid(subCategoryId)) {
    throwBadRequestError(messages.INVALID_SUBCATEGORY_ID || 'Invalid subcategory ID');
  }

  const subCategory = await SubCategory.findById(subCategoryId).lean()
    .populate('category', 'name');

  if (!subCategory) {
    throwBadRequestError(messages.SUBCATEGORY_NOT_FOUND || 'Subcategory not found');
  }

  return await transformTranslatedFields(subCategory, 'en');

};

/**
 * Update subcategory
 */
const updateSubCategory = async ({ adminId, subCategoryId, name, description, image, category, variantTypes, isActive }) => {
  // Check if subcategory ID is valid
  if (!mongoose.Types.ObjectId.isValid(subCategoryId)) {
    throwBadRequestError(messages.INVALID_SUBCATEGORY_ID || 'Invalid subcategory ID');
  }

  const subCategory = await SubCategory.findById(subCategoryId);

  if (!subCategory) {
    throwBadRequestError(messages.SUBCATEGORY_NOT_FOUND || 'Subcategory not found');
  }

  // Check if name already exists for another subcategory
  if (name && name !== subCategory.name) {
    const nameExists = await SubCategory.findOne({ name, _id: { $ne: subCategoryId } });

    if (nameExists) {
      throwBadRequestError(messages.SUBCATEGORY_NAME_ALREADY_EXISTS || 'Subcategory with this name already exists');
    }
  }

  // Check if category exists if provided
  if (category) {
    if (!mongoose.Types.ObjectId.isValid(category)) {
      throwBadRequestError(messages.INVALID_CATEGORY_ID || 'Invalid category ID');
    }

    const categoryExists = await Category.findById(category);

    if (!categoryExists) {
      throwBadRequestError(messages.CATEGORY_NOT_FOUND || 'Category not found');
    }
  }

  if (name) {
    const translatedData = await translateDataForStore([ 'name' ], { name });

    name = translatedData.name;
  }

  if (description) {
    const translatedData = await translateDataForStore([ 'description' ], { description });

    description = translatedData.description;
  }

  // Update subcategory fields
  if (name) {
    subCategory.name = name;
  }
  if (description !== undefined) {
    subCategory.description = description;
  }
  if (image) {
    const processedImage = processImages([ image ]);

    subCategory.image = processedImage[0];
  }
  if (category) {
    subCategory.category = category;
  }
  if (variantTypes) {
    subCategory.variantTypes = variantTypes;
  }
  if (isActive !== undefined) {
    subCategory.isActive = isActive;
  }

  subCategory.updatedBy = adminId;

  await subCategory.save();

  return subCategory;
};

/**
 * Delete subcategory
 */
const deleteSubCategory = async (subCategoryId) => {
  // Check if subcategory ID is valid
  if (!mongoose.Types.ObjectId.isValid(subCategoryId)) {
    throwBadRequestError(messages.INVALID_SUBCATEGORY_ID || 'Invalid subcategory ID');
  }

  const subCategory = await SubCategory.findById(subCategoryId);

  if (!subCategory) {
    throwBadRequestError(messages.SUBCATEGORY_NOT_FOUND || 'Subcategory not found');
  }

  // Check if subcategory has products
  const products = await Product.countDocuments({ subCategory: subCategoryId });

  if (products > 0) {
    throwBadRequestError(messages.SUBCATEGORY_HAS_PRODUCTS || 'Subcategory has products');
  }

  // Instead of deleting, mark as inactive
  subCategory.isActive = false;
  await subCategory.save();

  return subCategory;
};

module.exports = {
  createSubCategory,
  getAllSubCategories,
  getSubCategoriesByCategory,
  getSubCategoryById,
  updateSubCategory,
  deleteSubCategory
};
