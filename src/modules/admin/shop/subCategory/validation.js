const joi = require('joi');

const createSubCategorySchema = joi.object({
  name: joi.string().max(50).required(),
  description: joi.string().max(200).allow(''),
  image: joi.string().allow(''),
  category: joi.string().required(),
  variantTypes: joi.array().items(joi.string())
});

const updateSubCategorySchema = joi.object({
  name: joi.string().max(50),
  description: joi.string().max(200).allow(''),
  image: joi.string().allow(''),
  category: joi.string(),
  variantTypes: joi.array().items(joi.string()),
  isActive: joi.boolean()
}).min(1);

module.exports = {
  createSubCategorySchema,
  updateSubCategorySchema
};
