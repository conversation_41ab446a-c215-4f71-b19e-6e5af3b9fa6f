const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { SUCCESS } = require('../../../constants/common').commonConstants;
const { messages } = require('../../../messages');
const settlementService = require('./service');
const { getShipmentsSchema, settleBookingsSchema } = require('./validation');

/**
 * Get all shipments for settlement with filters
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getBookingsForSettlement = async (req, res) => {
  try {
    const { error, value } = getShipmentsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const {
      page = 1,
      limit = 10,
      status,
      vendorId,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = -1
    } = value;

    const data = await settlementService.getBookingsForSettlement({
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      vendorId,
      startDate,
      endDate,
      sortBy,
      sortOrder
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};
/**
 * Settle shipments
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const settleBookings = async (req, res) => {
  try {
    const { error, value } = settleBookingsSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const { bookingId, notes } = value;
    const adminId = req.user.id;

    const data = await settlementService.settleBookings(bookingId, adminId, { notes });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SETTLEMENT_PAID,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getBookingsForSettlement,
  settleBookings,
};
