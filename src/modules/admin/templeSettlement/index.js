const express = require('express');
const router = express.Router();
const settlementController = require('./controller');
const auth = require('../../../middleware/auth');
const { isSuperAdmin } = require('../../../middleware/roleCheck');

// All routes require super admin authentication
router.use(auth, isSuperAdmin);

// Settlement routes
router.get('/booking', settlementController.getBookingsForSettlement);
router.post('/settle', settlementController.settleBookings);

module.exports = router;
