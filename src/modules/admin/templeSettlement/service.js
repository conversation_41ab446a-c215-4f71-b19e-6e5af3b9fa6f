const { throwBadRequestError } = require('../../../errors');
const { settlementStatusValue, status: bookingStatusValue } = require('../../../constants/dbEnums');
const mongoose = require('mongoose');
const Booking = require('../../../models/Booking');

/**
 * Get all shipments with filters for settlement
 * @param {Object} params - Query parameters
 * @returns {Object} Shipments with pagination
 */
const getBookingsForSettlement = async ({ page, limit, temple, startDate, endDate, status, sortBy, sortOrder }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  // Build the query
  const query = {
    status: bookingStatusValue.COMPLETED
  };

  // Add filters if provided
  if (status) {
    if (status === 'PENDING') {
      query.$or = [
        { settlementStatus: settlementStatusValue.PENDING },
        { settlementStatus: { $exists: false } }
      ];
    } else {
      query.settlementStatus = status;
    }
  }

  if (temple) {
    query.temple = new mongoose.Types.ObjectId(temple);
  }

  if (startDate && endDate) {
    query.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(new Date(endDate).setUTCHours(23, 59, 59, 999))
    };
  }

  // Get shipments
  const bookings = await Booking.find(query)
    .populate('temple poojaSchedule event darshanSchedule')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit);

  // Get total count
  const total = await Booking.countDocuments(query);

  return {
    bookings,
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Settle shipments for a vendor
 * @param {Array} shipmentIds - Array of shipment IDs to settle
 * @param {String} adminId - Admin ID
 * @param {Object} data - Settlement data
 * @returns {Object} Result of settlement operation
 */
const settleBookings = async (bookingId, adminId, data = {}) => {
  const { notes } = data;

  if (!bookingId) {
    throwBadRequestError('No shipments selected for settlement');
  }

  // Check if shipments exist and are eligible for settlement
  const booking = await Booking.findOne({
    _id: bookingId,
    status: bookingStatusValue.COMPLETED,
    $or: [
      { settlementStatus: settlementStatusValue.PENDING },
      { settlementStatus: { $exists: false } }
    ]
  });

  if (!booking) {
    throwBadRequestError('No eligible shipments found for settlement');
  }

  // Update shipments in a transaction
  const updatedShipments = await Booking.updateOne(
    {
      _id: bookingId
    },
    {
      $set: {
        settlementStatus: settlementStatusValue.SETTLED,
        settledAt: new Date(),
        settlementNotes: notes,
        settledBy: adminId
      }
    }
  );

  return {
    success: true,
    message: 'Shipments settled successfully',
    count: updatedShipments
  };
};

module.exports = {
  getBookingsForSettlement,
  settleBookings,
};
