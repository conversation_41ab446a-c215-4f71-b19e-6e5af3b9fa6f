const { throwBadRequestError } = require('../../../errors');
const { settlementStatusValue, status: bookingStatusValue } = require('../../../constants/dbEnums');
const mongoose = require('mongoose');
const Booking = require('../../../models/Booking');
const { transformTranslatedFields } = require('../../../utils/localizer');
const moment = require('moment');

/**
 * Get all shipments with filters for settlement
 * @param {Object} params - Query parameters
 * @returns {Object} Shipments with pagination
 */
const getBookingsForSettlement = async ({ page, limit, temple, startDate, endDate, status, sortBy, sortOrder, search }) => {
  const skip = (page - 1) * limit;

  const matchStage = {
    status: bookingStatusValue.COMPLETED
  };

  // Add filters if provided
  if (status) {
    if (status === 'PENDING') {
      matchStage.$or = [
        { settlementStatus: settlementStatusValue.PENDING },
        { settlementStatus: { $exists: false } }
      ];
    } else {
      matchStage.settlementStatus = status;
    }
  }

  if (temple) {
    matchStage.temple = new mongoose.Types.ObjectId(temple);
  }

  if (startDate || endDate) {
    matchStage.createdAt = {};
  
    if (startDate) {
      const start = moment.utc(startDate).startOf('day'); // 00:00:00 UTC
  
      matchStage.createdAt.$gte = start.toDate();
    }
  
    if (endDate) {
      const end = moment.utc(endDate).endOf('day'); // 23:59:59.999 UTC
  
      matchStage.createdAt.$lte = end.toDate();
    }
  }

  const pipeline = [
    { $match: matchStage },

    // Lookup temples
    {
      $lookup: {
        from: 'temples',
        localField: 'temple',
        foreignField: '_id',
        as: 'temple'
      }
    },
    { $unwind: { path: '$temple', preserveNullAndEmptyArrays: true } },

    // Lookup pooja schedule
    {
      $lookup: {
        from: 'poojaschedules',
        localField: 'poojaSchedule',
        foreignField: '_id',
        as: 'poojaSchedule'
      }
    },
    { $unwind: { path: '$poojaSchedule', preserveNullAndEmptyArrays: true } },

    // Lookup event
    {
      $lookup: {
        from: 'events',
        localField: 'event',
        foreignField: '_id',
        as: 'event'
      }
    },
    { $unwind: { path: '$event', preserveNullAndEmptyArrays: true } },

    // Lookup darshan schedule
    {
      $lookup: {
        from: 'darshanschedules',
        localField: 'darshanSchedule',
        foreignField: '_id',
        as: 'darshanSchedule'
      }
    },
    { $unwind: { path: '$darshanSchedule', preserveNullAndEmptyArrays: true } },

    // Optional search
    ...(search ? [{
      $match: {
        $or: [
          // { bookingNumber: { $regex: search.toUpperCase(), $options: 'i' } ,
          { 'temple.name.en': { $regex: search, $options: 'i' } },
          { 'temple.description.en': { $regex: search, $options: 'i' } },
          { 'event.name.en': { $regex: search, $options: 'i' } },
          { 'poojaSchedule.name.en': { $regex: search, $options: 'i' } },
          { 'poojaSchedule.description.en': { $regex: search, $options: 'i' } },
          { 'darshanSchedule.name.en': { $regex: search, $options: 'i' } },
          { 'darshanSchedule.description.en': { $regex: search, $options: 'i' } }
        ]
      }
    }] : []),

    // Sort
    {
      $sort: { [sortBy]: parseInt(sortOrder) || -1 }
    },
  ];

  // Clone pipeline and calculate total
  const countPipeline = [ ...pipeline ]; // shallow copy

  countPipeline.push({ $count: 'total' });
 
  pipeline.push({ $skip: skip }, { $limit: parseInt(limit) });

  // Execute
  const [ countResult ] = await Booking.aggregate(countPipeline);
  const total = countResult ? countResult.total : 0;

  const bookings = await Booking.aggregate(pipeline);

  return {
    bookings: await transformTranslatedFields(bookings, 'en'),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Settle shipments for a vendor
 * @param {Array} shipmentIds - Array of shipment IDs to settle
 * @param {String} adminId - Admin ID
 * @param {Object} data - Settlement data
 * @returns {Object} Result of settlement operation
 */
const settleBookings = async (bookingId, adminId, data = {}) => {
  const { notes } = data;

  if (!bookingId) {
    throwBadRequestError('No shipments selected for settlement');
  }

  // Check if shipments exist and are eligible for settlement
  const booking = await Booking.findOne({
    _id: bookingId,
    status: bookingStatusValue.COMPLETED,
    $or: [
      { settlementStatus: settlementStatusValue.PENDING },
      { settlementStatus: { $exists: false } }
    ]
  });

  if (!booking) {
    throwBadRequestError('No eligible shipments found for settlement');
  }

  // Update shipments in a transaction
  const updatedShipments = await Booking.updateOne(
    {
      _id: bookingId
    },
    {
      $set: {
        settlementStatus: settlementStatusValue.SETTLED,
        settledAt: new Date(),
        settlementNotes: notes,
        settledBy: adminId
      }
    }
  );

  return {
    success: true,
    message: 'Shipments settled successfully',
    count: updatedShipments
  };
};

module.exports = {
  getBookingsForSettlement,
  settleBookings,
};
