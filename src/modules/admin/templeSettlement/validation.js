const Joi = require('joi');
const mongoose = require('mongoose');

// Validation schema for getting shipments
const getShipmentsSchema = Joi.object({
  page: Joi.number().integer().min(1),
  limit: Joi.number().integer().min(1).max(100),
  status: Joi.string().valid('PENDING', 'SETTLED'),
  temple: Joi.string().custom((value, helpers) => {
    if (!mongoose.Types.ObjectId.isValid(value)) {
      return helpers.error('any.invalid');
    }
    return value;
  }),
  startDate: Joi.date().iso(),
  endDate: Joi.date().iso().min(Joi.ref('startDate')),
  sortBy: Joi.string().valid('createdAt', 'settledAt', 'status'),
  sortOrder: Joi.number().valid(1, -1)
});

// Validation schema for settling shipments
const settleBookingsSchema = Joi.object({
  bookingId: Joi.string().custom((value, helpers) => {
    if (!mongoose.Types.ObjectId.isValid(value)) {
      return helpers.error('any.invalid');
    }
    return value;
  }),
  notes: Joi.string().allow('', null)
});

module.exports = {
  getShipmentsSchema,
  settleBookingsSchema,
};
