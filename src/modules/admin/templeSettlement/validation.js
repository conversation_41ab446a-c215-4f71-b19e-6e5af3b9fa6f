const Joi = require('joi');
const mongoose = require('mongoose');

// Validation schema for getting shipments
const getShipmentsSchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .optional()
    .messages({
      'number.base': 'Page must be a number',
      'number.min': 'Page must be at least 1',
    }),

  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .optional()
    .messages({
      'number.base': 'Limit must be a number',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit must be at most 100',
    }),

  status: Joi.string()
    .valid('PENDING', 'SETTLED')
    .optional()
    .messages({
      'any.only': 'Status must be either PENDING or SETTLED',
    }),

  temple: Joi.string()
    .custom((value, helpers) => {
      if (!mongoose.Types.ObjectId.isValid(value)) {
        return helpers.error('any.invalid');
      }
      return value;
    }, 'ObjectId validation')
    .optional()
    .messages({
      'any.invalid': 'Temple must be a valid MongoDB ObjectId',
    }),

  startDate: Joi.date()
    .iso()
    .optional()
    .messages({
      'date.format': 'Start Date must be in ISO format',
      'date.base': 'Start Date must be a valid date',
    }),

  endDate: Joi.date()
    .iso()
    .min(Joi.ref('startDate'))
    .optional()
    .messages({
      'date.format': 'End Date must be in ISO format',
      'date.min': 'End Date must be greater than or equal to Start Date',
      'date.base': 'End Date must be a valid date',
    }),

  sortBy: Joi.string()
    .valid('createdAt', 'settledAt', 'status')
    .optional()
    .messages({
      'any.only': 'Sort By must be one of createdAt, settledAt, or status',
    }),

  sortOrder: Joi.number()
    .valid(1, -1)
    .optional()
    .messages({
      'any.only': 'Sort Order must be either 1 (ascending) or -1 (descending)',
    }),

  search: Joi.string()
    .allow('', null)
    .optional()
    .messages({
      'string.base': 'Search must be a string',
    }),
});

// Validation schema for settling shipments
const settleBookingsSchema = Joi.object({
  bookingId: Joi.string().custom((value, helpers) => {
    if (!mongoose.Types.ObjectId.isValid(value)) {
      return helpers.error('any.invalid');
    }
    return value;
  }),
  notes: Joi.string().allow('', null)
});

module.exports = {
  getShipmentsSchema,
  settleBookingsSchema,
};
