const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const userAccountManagementService = require('./service');
const { listUsersSchema, updateUserAccountStatusSchema, validateId } = require('./validation');
const { SUCCESS } = commonConstants;
const { saveAuditLog } = require('../../../utils/auditLogger');
const { auditLogAction } = require('../../../constants/dbEnums');

//* 1. Function to list users
const listUsers = async (req, res) => {
  try {
    const { error } = listUsersSchema.validate(req.query);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const users = await userAccountManagementService.listUsers(req.query);

    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: messages.SUCCESS, 
      status: true, 
      data: users 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 2. Function to update user account status 
const updateUserAccountStatus = async (req, res) => {
  try {
    const { error } = updateUserAccountStatusSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const { error: idError } = validateId.validate(req.params.id);

    if (idError) {
      return res.status(400).json({ 
        status: false,
        message: idError.details[0].message 
      });
    }

    const user = await userAccountManagementService.updateUserAccountStatus(req.params.id, req.body.status);

    //* Save audit log 
    const detail = `User account ${user.email} ${req.body.status === 'ACTIVATE' ? 'activated' : 'deactivated'} successfully`;
    const model = 'User';

    await saveAuditLog(req, req.user.id, auditLogAction.USER_ACCOUNT_UPDATED, detail, model);

    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: messages.SUCCESS, 
      status: true, 
      data: user 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  listUsers,
  updateUserAccountStatus
};
