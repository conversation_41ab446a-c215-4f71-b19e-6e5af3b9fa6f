const express = require('express');
const router = express.Router();
const userAccountManagementController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

router.get('/list', auth, isAdminOrSuperAdmin, userAccountManagementController.listUsers);
router.put('/:id/status', auth, isAdminOrSuperAdmin, userAccountManagementController.updateUserAccountStatus);

module.exports = router;