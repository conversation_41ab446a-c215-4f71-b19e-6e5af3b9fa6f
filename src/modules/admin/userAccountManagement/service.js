const User = require('../../../models/User');
const { throwBadRequestError } = require('../../../errors');
const { checkDeletionEligibility } = require('../../../modules/user/service');
const { sendMail } = require('../../../utils/sendMail');
const { emailSubjects } = require('../../../messages/emailSubjects');
const path = require('path');

//* 1. Function to list users
const listUsers = async (query) => {
  const { page = 1, limit = 10, search, status, sortBy = 'createdAt', sortOrder = -1 } = query;
  const skip = (page - 1) * limit;

  const filter = {};

  const language = { code: 'en' };

  if (search) {
    filter.$or = [
      { [`firstName.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`lastName.${language.code}`]: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } },
      { phoneNumber: { $regex: search, $options: 'i' } }
    ];
  }

  if (status === 'ACTIVE') {
    filter.deletedAt = null;
  } else if (status === 'DEACTIVATED') {
    filter.deletedAt = { $ne: null };
  }

  const sort = {};

  sort[sortBy] = sortOrder;

  let users = await User.find(filter)
    .select('-password')
    .sort(sort).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  users = users.map(user => ({
    ...user,
    status: user.deletedAt === null ? 'ACTIVE' : 'DEACTIVATED'
  }));

  const total = await User.countDocuments(filter);

  return {
    users,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / limit),
      totalUsers: await User.countDocuments()
    }
  };
};

//* 2. Function to update user account status 
const updateUserAccountStatus = async (userId, status) => {
  const user = await User.findById(userId);

  if (!user) {
    throwBadRequestError('User not found');
  }

  if (status === 'ACTIVATE') {
    
    //* Check if user is already active
    if (user.deletedAt === null) {
      throwBadRequestError('User is already active');
    }

    user.deletedAt = null;

  } else if (status === 'DEACTIVATE') {
    
    //* Check if user is already deactivated
    if (user.deletedAt) {
      throwBadRequestError('User is already deactivated');
    }

    //* Check if user has upcoming bookings or active orders
    const hasUpcomingBookingOrOrder = await checkDeletionEligibility(userId);

    if (hasUpcomingBookingOrOrder) {
      throwBadRequestError('Cannot deactivate user with upcoming bookings or active orders');
    }

    user.deletedAt = new Date();
  }

  await user.save();

  const data = {
    name: `${user?.firstName?.en || user?.firstName} ${user?.lastName?.en || user?.lastName}`,
    appStoreLink: process.env.APP_STORE_LINK,
    playStoreLink: process.env.PLAY_STORE_LINK,
    supportEmail: process.env.SUPPORT_CONTACT
  };

  //* Send activation email to user if activated
  if (status === 'ACTIVATE' && user.email) {
    const subject = emailSubjects.USER_ACCOUNT_REACTIVATED;
    const templatePath = path.join(__dirname, '../../../views/userAccountReactivation.html');

    await sendMail(user.email, subject, templatePath, data);
  }

  if (status === 'DEACTIVATE' && user.email) {
    const subject = emailSubjects.USER_ACCOUNT_DEACTIVATED;
    const templatePath = path.join(__dirname, '../../../views/userAccountDeactivation.html');

    await sendMail(user.email, subject, templatePath, data);
  }

  return user;
};

module.exports = {
  listUsers,
  updateUserAccountStatus
};