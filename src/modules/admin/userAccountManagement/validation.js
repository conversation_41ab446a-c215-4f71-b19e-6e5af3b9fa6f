const joi = require('joi');
const mongoose = require('mongoose');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string().custom(validateObjectId).required().messages({
  'any.invalid': 'Invalid user ID format',
  'any.required': 'User ID is required'
});

const listUsersSchema = joi.object({
  page: joi.number().integer().min(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
  limit: joi.number().integer().min(1).max(100)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
  search: joi.string().trim().allow('').optional()
    .messages({
      'string.base': 'Search query must be a string'
    }),
  status: joi.string().valid('ACTIVE', 'DEACTIVATED').allow('').optional()
    .messages({
      'any.only': 'Status must be one of: ACTIVE, DEACTIVATED'
    }),
  sortBy: joi.string().valid('name', 'email', 'phoneNumber', 'createdAt', 'updatedAt')
    .messages({
      'any.only': 'Sort by field must be one of: name, email, phoneNumber, status, createdAt, or updatedAt'
    }),
  sortOrder: joi.number().valid(1, -1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    })
});

const updateUserAccountStatusSchema = joi.object({ status: joi.string()
  .valid('ACTIVATE', 'DEACTIVATE')
  .required()
  .messages({
    'any.required': 'Status is required',
    'any.only': 'Status must be either ACTIVATE or DEACTIVATE'
  })
});

module.exports = {
  validateId,
  listUsersSchema,
  updateUserAccountStatusSchema
};
