const { apiResponse, errorApiResponse } = require('../../config/responseHandler');
const { SUCCESS } = require('../../constants/common').commonConstants;
const appVersionService = require('./service');
const { updateAppVersionSchema } = require('./validation');
const { messages } = require('../../messages');

//* 1. Function to create da<PERSON>han booking
const updateAppVersion = async (req, res) => {
  try {
    const { error } = updateAppVersionSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const result = await appVersionService.updateAppVersion(req.body, req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: req.user?.preferredLanguage === 'Hindi' ? messages.APP_VERSION_UPDATED_HI : messages.APP_VERSION_UPDATED,
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getAppVersion = async (req, res) => {
  try {
    const { platform, type } = req.query;

    if (!platform || !type) {
      return res.status(400).json({
        status: false,
        message: req.user?.preferredLanguage === 'Hindi' ? messages.PLATFORM_AND_TYPE_REQUIRED_HI : messages.PLATFORM_AND_TYPE_REQUIRED
      });
    }

    const appVersions = await appVersionService.getAppVersion(platform, type, req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: appVersions
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const listAppVersions = async (req, res) => {
  try {
    const appVersions = await appVersionService.listAppVersions();

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: req.user?.preferredLanguage === 'Hindi' ? messages.APP_VERSIONS_RETRIEVED_HI : messages.APP_VERSIONS_RETRIEVED,
      status: true,
      data: appVersions
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  updateAppVersion,
  getAppVersion,
  listAppVersions
};
