const express = require('express');
const router = express.Router();
const appVersionController = require('./controller');
const auth = require('../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../middleware/roleCheck');

router.get('/', appVersionController.getAppVersion);
router.post('/update', auth, isAdminOrSuperAdmin, appVersionController.updateAppVersion);
router.get('/list', auth, isAdminOrSuperAdmin, appVersionController.listAppVersions);

module.exports = router;