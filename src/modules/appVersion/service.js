const { throwNotFoundError } = require('../../errors');
const { messages } = require('../../messages');
const AppVersion = require('../../models/AppVersion');

const updateAppVersion = async (body) => {
  const { platform, latestVersion, forceUpdate, message, type } = body;

  const appVersion = await AppVersion.findOne({ platform, type });

  if (appVersion) {
    appVersion.latestVersion = latestVersion;
    appVersion.forceUpdate = forceUpdate;
    appVersion.message = message;
    await appVersion.save();
  } else {
    await AppVersion.create({
      platform,
      latestVersion,
      forceUpdate,
      message,
      type
    });
  }
};

const getAppVersion = async (platform, type, user) => {
  const appVersions = await AppVersion.findOne({ platform, type }).lean();

  if (!appVersions) {
    throwNotFoundError(user?.preferredLanguage === 'Hindi' ? messages.APP_VERSION_NOT_FOUND_HI : messages.APP_VERSION_NOT_FOUND);
  }

  return appVersions;
};

const listAppVersions = async () => {
  const appVersions = await AppVersion.find().lean();

  return appVersions;
};

module.exports = {
  updateAppVersion,
  getAppVersion,
  listAppVersions
};