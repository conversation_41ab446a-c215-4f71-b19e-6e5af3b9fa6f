// create validation to update the app version
const joi = require('joi');
const { appPlatforms, userTypeValue } = require('../../constants/dbEnums');

const updateAppVersionSchema = joi.object({
  platform: joi.string().valid(...Object.values(appPlatforms)).required().messages({
    'string.empty': 'Platform is required',
    'any.required': 'Platform is required',
    'any.only': `Platform must be one of: ${Object.values(appPlatforms).join(', ')}`
  }),
  latestVersion: joi.string().required().messages({
    'string.empty': 'Latest version is required',
    'any.required': 'Latest version is required'
  }),
  forceUpdate: joi.boolean().required().messages({
    'boolean.base': 'Force update must be a boolean'
  }),
  message: joi.string().allow('').optional().messages({
    'string.base': 'Message must be a string'
  }),
  type: joi.string().valid(userTypeValue.USER, userTypeValue.KIOSK).required().messages({
    'string.empty': 'Type is required',
    'any.required': 'Type is required',
    'any.only': `Type must be one of: ${userTypeValue.USER}, ${userTypeValue.KIOSK}`
  })
});

module.exports = {
  updateAppVersionSchema
};