const { apiResponse, errorApiResponse } = require('../../config/responseHandler');
const { SUCCESS } = require('../../constants/common').commonConstants;
const bookingService = require('./service');
const { createBookingSchema, getDarshanSlotAvailabilitySchema, getPoojaSlotAvailabilitySchema, getMyBookingsSchema, validateId, fetchAllBookingsSchema, getTempleBookingsSchema, uploadUrlSchema } = require('./validation');
const { messages } = require('../../messages');
const { getPresignedUrl, deleteFile } = require('../../utils/s3Service');

//* 1. Function to create darshan booking
const createDarshanBooking = async (req, res) => {
  try {
    const { error } = createBookingSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const result = await bookingService.createDarshanBooking(req.body, req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: req.user?.preferredLanguage === 'Hindi' ? messages.DARSHAN_BOOKING_CREATED_HI : messages.DARSHAN_BOOKING_CREATED,
      status: true,
      data: {
        booking: result.booking,
        payment: result.payment
      }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 2. Function to create pooja booking
const createPoojaBooking = async (req, res) => {
  try {
    const { error } = createBookingSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const result = await bookingService.createPoojaBooking(req.body, req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.POOJA_BOOKING_CREATED,
      status: true,
      data: {
        booking: result.booking,
        address: result.address,
        payment: result.payment
      }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 3 Function to create event booking
const createEventBooking = async (req, res) => {
  try {
    const { error } = createBookingSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const result = await bookingService.createEventBooking(req.body, req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: req.user?.preferredLanguage === 'Hindi' ? messages.EVENT_BOOKING_CREATED_HI : messages.EVENT_BOOKING_CREATED,
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 4. Function to get my bookings
const getMyBookings = async (req, res) => {
  try {
    const { error, value } = getMyBookingsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const result = await bookingService.getMyBookings(req.user.id, value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Bookings retrieved successfully',
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 5. Function to get temple bookings
const getTempleBookings = async (req, res) => {
  try {
    const { error, value } = getTempleBookingsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const role = req.user.userType;
    const result = await bookingService.getTempleBookings(req.user.id, value, role);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Bookings retrieved successfully',
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 6. Function to get booking by id
const getBookingById = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const booking = await bookingService.getBookingById(req.params.id, req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Booking retrieved successfully',
      data: booking
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 7. Function to get darshan slot availability
const getDarshanSlotAvailability = async (req, res) => {
  try {
    const { error } = getDarshanSlotAvailabilitySchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await bookingService.getDarshanSlotAvailability(req.body);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Darshan slots availability retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 8. Function to get pooja slot availability
const getPoojaSlotAvailability = async (req, res) => {
  try {
    const { error } = getPoojaSlotAvailabilitySchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await bookingService.getPoojaSlotAvailability(req.body);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Puja slots availability retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 9. Function to fetch all user bookings
const fetchAllBookings = async (req, res) => {
  try {
    const { error, value } = fetchAllBookingsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await bookingService.fetchAllBookings(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Bookings retrieved successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get presigned URL for uploading event poster
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }
    
    const { error } = uploadUrlSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const { extension } = req.body;

    const uploadData = await getPresignedUrl(extension, 'aadhar', req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.UPLOAD_URL_GENERATED,
      status: true,
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete an event poster image
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const deleteImage = async (req, res) => {
  try {
    const { key } = req.params;

    await deleteFile(key);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.EVENT_IMAGE_DELETED,
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createDarshanBooking,
  createPoojaBooking,
  createEventBooking,
  getMyBookings,
  getTempleBookings,
  getBookingById,
  getDarshanSlotAvailability,
  getPoojaSlotAvailability,
  fetchAllBookings,
  getUploadUrl,
  deleteImage
};

