const express = require('express');
const router = express.Router();
const bookingController = require('./controller');
const auth = require('../../middleware/auth');
const { isUser, isAdminOrSuperAdmin } = require('../../middleware/roleCheck');

router.post('/darshan/create', auth, isUser, bookingController.createDarshanBooking);
router.post('/pooja/create', auth, isUser, bookingController.createPoojaBooking);
router.post('/event/create', auth, isUser, bookingController.createEventBooking);
router.get('/all', auth, isUser, bookingController.getMyBookings);
router.get('/temple', auth, bookingController.getTempleBookings);
router.get('/:id', auth, isUser, bookingController.getBookingById);
router.post('/darshan/slots', auth, isUser, bookingController.getDarshanSlotAvailability);
router.post('/pooja/slots', auth, isUser, bookingController.getPoojaSlotAvailability);
router.get('/', auth, isAdminOrSuperAdmin, bookingController.fetchAllBookings);
router.post('/aadhar/upload-url', auth, bookingController.getUploadUrl);
router.delete('/image/:key(*)', auth, bookingController.deleteImage);

module.exports = router;