const joi = require('joi');
const { type, status, bookingType, paymentGateway } = require('../../constants/dbEnums');
const mongoose = require('mongoose');
const { parsePhoneNumberFromString } = require('libphonenumber-js');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string()
  .custom(validateObjectId)
  .required()
  .messages({
    'any.invalid': 'Invalid booking ID format',
    'any.required': 'Booking ID is required'
  });

const createBookingSchema = joi.object({
  discount: joi.string()
    .custom(validateObjectId)
    .optional()
    .allow('')
    .messages({
      'any.invalid': 'Invalid discount ID format'
    }),
  paymentGateway: joi.string()
    .valid(...Object.values(paymentGateway))
    .optional()
    .default(paymentGateway.RAZORPAY)
    .messages({
      'any.only': `Payment gateway must be one of: ${Object.values(paymentGateway).join(', ')}`
    }),
  promotionalKitCount: joi.number()
    .integer()
    .min(1) // Must be greater than 0 if provided
    .optional() // Field is optional
    .messages({
      'number.base': 'Promotional kit count must be a number',
      'number.integer': 'Promotional kit count must be a whole number',
      'number.min': 'Promotional kit count must be greater than 0 if provided'
    }),
  offerings: joi.array()
    .items(
      joi.object({
        offering: joi.string()
          .custom(validateObjectId)
          .required()
          .messages({
            'any.required': 'Offering ID is required',
            'any.invalid': 'Invalid offering ID format'
          }),
        quantity: joi.number()
          .integer()
          .min(1)
          .required()
          .messages({
            'number.base': 'Quantity must be a number',
            'number.integer': 'Quantity must be a whole number',
            'number.min': 'Quantity must be at least 1',
            'any.required': 'Quantity is required'
          })
      })
    )
    .optional()
    .allow(null) // Allow null but not empty array
    .min(1) // If array is provided, it must have at least one item
    .custom((value, helpers) => {
      // Skip validation if null or undefined
      if (value === null || value === undefined) {
        return value;
      }
      
      // If it's an array with items, validate each item
      if (Array.isArray(value) && value.length > 0) {
        // Check for duplicate offerings
        const offeringIds = value.map(item => item.offering?.toString());
        const uniqueIds = new Set(offeringIds);
        
        if (uniqueIds.size !== offeringIds.length) {
          return helpers.error('array.unique', { 
            message: 'Duplicate offerings are not allowed' 
          });
        }
      }
      return value;
    })
    .messages({
      'array.base': 'Offerings must be an array',
      'array.min': 'Offerings array cannot be empty. Either provide at least one offering or set it to null',
      'array.unique': 'Duplicate offerings are not allowed'
    }),
  temple: joi.string()
    .custom(validateObjectId)
    .required()
    .messages({
      'any.required': 'Temple ID is required',
      'any.invalid': 'Invalid temple ID format'
    }),
  type: joi.string()
    .valid(...Object.values(type))
    .required()
    .messages({
      'any.required': 'Type is required',
      'any.only': `Type must be one of: ${Object.values(type).join(', ')}`
    }),
  // Event specific fields - required only when type is EVENT
  event: joi.string()
    .custom(validateObjectId)
    .when('type', {
      is: 'EVENT',
      then: joi.required(),
      otherwise: joi.forbidden()
    })
    .messages({
      'any.required': 'Event ID is required for event bookings',
      'any.invalid': 'Invalid event ID format',
      'any.forbidden': 'Event ID should not be provided for darshan/puja bookings'
    }),
  eventDates: joi.array()
    .items(joi.object({
      date: joi.date()
        .iso()
        .required()
        .messages({
          'date.base': 'Invalid date format',
          'date.format': 'Date must be in YYYY-MM-DD format',
          'any.required': 'Date is required for each event date entry'
        })
    }))
    .when('type', {
      is: 'EVENT',
      then: joi.array().min(1).required(),
      otherwise: joi.forbidden()
    })
    .messages({
      'array.min': 'At least one date must be provided for events',
      'any.required': 'Event dates are required for event bookings',
      'any.forbidden': 'Event dates should not be provided for darshan/puja bookings'
    }),
  darshanSchedule: joi.string()
    .custom(validateObjectId)
    .when('type', {
      is: 'PHYSICAL_DARSHAN',
      then: joi.required(),
      otherwise: joi.forbidden()
    })
    .messages({
      'any.required': 'Darshan schedule ID is required for physical darshan',
      'any.invalid': 'Invalid darshan schedule ID format',
      'any.forbidden': 'Darshan schedule should not be provided for other booking types'
    }),
  poojaSchedule: joi.string()
    .custom(validateObjectId)
    .when('type', {
      is: joi.valid('PHYSICAL_POOJA', 'VIRTUAL_POOJA'),
      then: joi.required(),
      otherwise: joi.forbidden()
    })
    .messages({
      'any.required': 'Puja schedule ID is required for puja bookings',
      'any.invalid': 'Invalid puja schedule ID format',
      'any.forbidden': 'Puja schedule should not be provided for other booking types'
    }),
  date: joi.date()
    .min('now')
    .when('type', {
      is: 'EVENT',
      then: joi.forbidden(),
      otherwise: joi.required()
    })
    .iso()
    .messages({
      'date.base': 'Please provide a valid date',
      'date.format': 'Date must be in YYYY-MM-DD format',
      'date.greater': 'Booking date must be in the future',
      'any.required': 'Booking date is required for darshan/puja bookings',
      'any.forbidden': 'Date should not be provided for event bookings'
    }),
  timeSlot: joi.object({
    startTime: joi.string()
      .pattern(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/)
      .required()
      .messages({
        'string.empty': 'Start time is required',
        'string.pattern.base': 'Invalid start time format. Use HH:MM AM/PM format',
        'any.required': 'Start time is required'
      }),
    endTime: joi.string()
      .pattern(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/)
      .required()
      .messages({
        'string.empty': 'End time is required',
        'string.pattern.base': 'Invalid end time format. Use HH:MM AM/PM format',
        'any.required': 'End time is required'
      })
  })
    .when('type', {
      is: 'EVENT',
      then: joi.forbidden(),
      otherwise: joi.required()
    })
    .messages({
      'object.base': 'Time slot must be an object',
      'any.required': 'Time slot is required'
    })
    .custom((obj, helpers) => {
      // If empty object is passed
      if (Object.keys(obj).length === 0) {
        return helpers.error('object.min', {
          message: 'Time slot must contain startTime and endTime'
        });
      }
      return obj;
    })
    .custom((obj, helpers) => {
      const { startTime, endTime } = obj;

      // Convert time to 24-hour format for comparison
      const convertTo24Hour = (time) => {
        const [ timeStr, period ] = time.split(' ');
        const [ hoursStr, minutes ] = timeStr.split(':');

        let hours = parseInt(hoursStr);

        if (period === 'PM' && hours !== 12) {
          hours += 12;
        }
        if (period === 'AM' && hours === 12) {
          hours = 0;
        }

        return hours * 60 + parseInt(minutes); // Convert to minutes for easier comparison
      };

      const startMinutes = convertTo24Hour(startTime);
      const endMinutes = convertTo24Hour(endTime);

      if (endMinutes <= startMinutes) {
        return helpers.error('timeSlot.endTime', {
          message: 'End time must be greater than start time'
        });
      }

      return obj;
    })
    .messages({
      'timeSlot.endTime': 'End time must be greater than start time'
    }),
  individual: joi.number()
    .integer()
    .min(0)
    .max(8)
    .default(0)
    .messages({
      'number.base': 'Individual count must be a number',
      'number.integer': 'Individual count must be a whole number',
      'number.min': 'Individual count cannot be negative',
      'number.max': 'Individual count cannot exceed 8'
    }),
  couple: joi.number()
    .integer()
    .min(0)
    .max(4)
    .default(0)
    .messages({
      'number.base': 'Couple count must be a number',
      'number.integer': 'Couple count must be a whole number',
      'number.min': 'Couple count cannot be negative',
      'number.max': 'Couple count cannot exceed 4'
    }),
  family: joi.number()
    .integer()
    .min(0)
    .max(2)
    .default(0)
    .messages({
      'number.base': 'Family count must be a number',
      'number.integer': 'Family count must be a whole number',
      'number.min': 'Family count cannot be negative',
      'number.max': 'Family count cannot exceed 2'
    }),
  primaryDevoteeDetails: joi.object({
    fullName: joi.string()
      .required()
      .trim()
      .messages({
        'string.empty': 'Full name is required',
        'any.required': 'Full name is required'
      }),
    fatherOrHusbandName: joi.string()
      .allow('')
      .trim()
      .messages({
        'string.base': 'Father/Husband name must be a string'
      }),
    gender: joi.string()
      .valid('Male', 'Female', 'Other')
      .required()
      .messages({
        'any.only': 'Gender must be Male, Female, or Other',
        'any.required': 'Gender is required'
      }),
    aadharNumber: joi.string().optional(),
    aadharImage: joi.string().optional(),
    gotra: joi.string().trim().optional().allow('', null),
    sankalp: joi.string().trim().optional(),
    countryCode: joi.string()
      .trim()
      .pattern(/^\+\d{1,3}$/)
      .required()
      .messages({
        'string.empty': 'Country code is required',
        'string.pattern.base': 'Country code must start with + followed by 1 to 3 digits',
      }),
    phoneNumber: joi.string()
      // .pattern(/^[6-9]\d{9}$/)
      .required()
      .messages({
        'string.pattern.base': 'Phone number must be a valid 10-digit Indian mobile number',
        'any.required': 'Phone number is required'
      }),
    countryCodeForWhatsapp: joi.string()
      .trim()
      .pattern(/^\+\d{1,3}$/)
      .messages({
        'string.pattern.base': 'Country code must start with + followed by 1 to 3 digits',
      })
      .optional(),
    whatsappNumber: joi.string()
      // .pattern(/^[6-9]\d{9}$/)
      .allow('')
      .messages({
        'string.pattern.base': 'WhatsApp number must be a valid 10-digit Indian mobile number'
      })
  })
    .required()
    .messages({
      'any.required': 'Primary devotee details are required'
    }),
  otherDevotees: joi.array()
    .items(
      joi.object({
        fullName: joi.string()
          .required()
          .messages({
            'string.empty': 'Full name is required',
            'any.required': 'Full name is required'
          }),
        gender: joi.string()
          .valid('Male', 'Female', 'Other')
          .required()
          .messages({
            'any.only': 'Gender must be Male, Female, or Other',
            'any.required': 'Gender is required'
          }),
        aadharNumber: joi.string().optional(),
        aadharImage: joi.string().optional(),
      })
    )
    .messages({
      'array.base': 'Other devotees must be an array'
    }),
  address: joi.string().optional()
}).custom((value, helpers) => {
  const { individual = 0, couple = 0, family = 0 } = value;

  // Check if at least one booking type is selected
  if (individual + couple + family === 0) {
    return helpers.message('At least one booking count (individual, couple, or family) must be greater than 0');
  }

  // Calculate total number of people
  const totalPeople = individual + (couple * 2) + (family * 4);

  // Check if total exceeds maximum limit
  if (totalPeople > 8) {
    return helpers.message('Total number of devotees cannot exceed 8 people');
  }

  // Calculate expected number of other devotees (excluding primary devotee)
  const expectedOtherDevotees = totalPeople - 1; // -1 for primary devotee

  // Check if otherDevotees array is provided when needed
  if (expectedOtherDevotees > 0) {
    if (!value.otherDevotees || !Array.isArray(value.otherDevotees)) {
      return helpers.message('Other devotees details are required');
    }

    if (value.otherDevotees.length !== expectedOtherDevotees) {
      return helpers.message(
        `Expected ${expectedOtherDevotees} other devotees details but got ${
          value.otherDevotees?.length || 0
        }. Please provide details for all devotees.`
      );
    }
  }

  return value;
});

const getDarshanSlotAvailabilitySchema = joi.object({
  temple: joi.string()
    .custom(validateObjectId)
    .required()
    .messages({
      'any.required': 'Temple ID is required',
      'any.invalid': 'Invalid temple ID format'
    }),
  date: joi.date()
    .iso()
    .required()
    .messages({
      'date.base': 'Invalid date format. Use YYYY-MM-DD format',
      'date.format': 'Date must be in YYYY-MM-DD format',
      'any.required': 'Date is required'
    }),
  darshanSchedule: joi.string()
    .custom(validateObjectId)
    .messages({
      'any.required': 'Darshan schedule ID is required for physical darshan',
      'any.invalid': 'Invalid darshan schedule ID format'
    }),
});

const getPoojaSlotAvailabilitySchema = joi.object({
  temple: joi.string()
    .custom(validateObjectId)
    .required()
    .messages({
      'any.required': 'Temple ID is required',
      'any.invalid': 'Invalid temple ID format'
    }),
  date: joi.date()
    .iso()
    .required()
    .messages({
      'date.base': 'Invalid date format. Use YYYY-MM-DD format',
      'date.format': 'Date must be in YYYY-MM-DD format',
      'any.required': 'Date is required'
    }),
  poojaSchedule: joi.string()
    .custom(validateObjectId)
    .messages({
      'any.required': 'Darshan schedule ID is required for physical darshan',
      'any.invalid': 'Invalid darshan schedule ID format'
    }),
});

const getMyBookingsSchema = joi.object({
  page: joi.number()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.min': 'Page number must be greater than or equal to 1'
    }),
  limit: joi.number()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.min': 'Limit must be greater than or equal to 1',
      'number.max': 'Limit cannot exceed 100 records per page'
    }),
  sortBy: joi.string()
    .valid('createdAt', 'date', 'status', 'totalAmount', 'bookingNumber', 'bookingName', 'type', 'pujari')
    .default('createdAt')
    .messages({
      'string.base': 'Sort field must be a string',
      'any.only': 'Sort field must be one of: createdAt, date, status, totalAmount or bookingNumber'
    }),
  sortOrder: joi.number().valid(1, -1).default(-1),
  status: joi.string()
    .valid(...Object.values(status))
    .optional()
    .messages({
      'string.base': 'Status must be a string',
      'any.only': 'Status must be one of: PENDING, COMPLETED, or CANCELLED'
    }),
  startDate: joi.date()
    .iso()
    .messages({
      'date.base': 'Start date must be a valid date',
      'date.format': 'Start date must be in ISO format (YYYY-MM-DD)'
    }),
  endDate: joi.date()
    .iso()
    .when('startDate', {
      is: joi.date().required(),
      then: joi.date().min(joi.ref('startDate'))
    })
    .messages({
      'date.base': 'End date must be a valid date',
      'date.format': 'End date must be in ISO format (YYYY-MM-DD)',
      'date.greater': 'End date must be greater than start date'
    }),
  search: joi.string()
    .trim()
    .allow('')
    .optional()
    .messages({
      'string.base': 'Search must be a string'
    }),
  type: joi.string()
    .valid(...Object.values(type))
    .optional()
    .messages({
      'string.base': 'Type must be a string',
      'any.only': `Type must be one of: ${Object.values(type).join(', ')}`
    }),
  bookingType: joi.string()
    .valid(...Object.values(bookingType))
    .optional()
    .messages({
      'string.base': 'Booking type must be a string',
      'any.only': `Booking type must be one of: ${Object.values(bookingType).join(', ')}`
    })
});

const getTempleBookingsSchema = joi.object({
  page: joi.number()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.min': 'Page number must be greater than or equal to 1'
    }),
  limit: joi.number()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.min': 'Limit must be greater than or equal to 1',
      'number.max': 'Limit cannot exceed 100 records per page'
    }),
  isPujariAssigned: joi.boolean()
    .optional()
    .messages({
      'boolean.base': 'isPujariAssigned must be a boolean'
    }),
  sortBy: joi.string()
    .valid('createdAt', 'date', 'status', 'totalAmount', 'bookingNumber', 'bookingName', 'type', 'pujari')
    .default('createdAt')
    .messages({
      'string.base': 'Sort field must be a string',
      'any.only': 'Sort field must be one of: createdAt, date, status, totalAmount or bookingNumber'
    }),
  sortOrder: joi.number().valid(1, -1).default(-1),
  status: joi.string()
    .valid(...Object.values(status))
    .optional()
    .messages({
      'string.base': 'Status must be a string',
      'any.only': 'Status must be one of: PENDING, COMPLETED, or CANCELLED'
    }),
  date: joi.date()
    .iso()
    .messages({
      'date.base': 'Date must be a valid date',
      'date.format': 'Date must be in ISO format (YYYY-MM-DD)'
    }),
  startDate: joi.date()
    .iso()
    .messages({
      'date.base': 'Start date must be a valid date',
      'date.format': 'Start date must be in ISO format (YYYY-MM-DD)'
    }),
  endDate: joi.date()
    .iso()
    .when('startDate', {
      is: joi.date().required(),
      then: joi.date().min(joi.ref('startDate'))
    })
    .messages({
      'date.base': 'End date must be a valid date',
      'date.format': 'End date must be in ISO format (YYYY-MM-DD)',
      'date.greater': 'End date must be greater than start date'
    }),
  startTime: joi.string()
    .pattern(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/)
    .messages({
      'string.pattern.base': 'Invalid start time format. Use HH:MM AM/PM format (e.g., 9:30 AM)'
    }),
  endTime: joi.string()
    .pattern(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/)
    .messages({
      'string.pattern.base': 'Invalid end time format. Use HH:MM AM/PM format (e.g., 11:30 AM)'
    }),
  search: joi.string()
    .trim()
    .allow('')
    .optional()
    .messages({
      'string.base': 'Search must be a string'
    }),
  type: joi.string()
    .valid(...Object.values(type))
    .optional()
    .messages({
      'string.base': 'Type must be a string',
      'any.only': `Type must be one of: ${Object.values(type).join(', ')}`
    }),
  event: joi.string()
    .custom(validateObjectId)
    .messages({
      'any.invalid': 'Invalid temple ID format'
    }),
  poojaSchedule: joi.string()
    .custom(validateObjectId)
    .messages({
      'any.invalid': 'Invalid temple ID format'
    }),
  darshanSchedule: joi.string()
    .custom(validateObjectId)
    .messages({
      'any.invalid': 'Invalid temple ID format'
    })
});

const fetchAllBookingsSchema = joi.object({
  page: joi.number()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.min': 'Page number must be greater than or equal to 1'
    }),
  limit: joi.number()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.min': 'Limit must be greater than or equal to 1',
      'number.max': 'Limit cannot exceed 100 records per page'
    }),
  sortBy: joi.string()
    .valid('createdAt', 'date', 'status', 'totalAmount', 'bookingNumber', 'bookingName', 'type', 'pujari')
    .default('createdAt')
    .messages({
      'string.base': 'Sort field must be a string',
      'any.only': 'Sort field must be one of: createdAt, date, status, totalAmount or bookingNumber'
    }),
  sortOrder: joi.number().valid(1, -1).default(-1),
  status: joi.string()
    .valid(...Object.values(status))
    .messages({
      'string.base': 'Status must be a string',
      'any.only': `Status must be one of: ${Object.values(status).join(', ')}`
    }),
  startDate: joi.date()
    .iso()
    .messages({
      'date.base': 'Start date must be a valid date',
      'date.format': 'Start date must be in ISO format (YYYY-MM-DD)'
    }),
  endDate: joi.date()
    .iso()
    .when('startDate', {
      is: joi.date().required(),
      then: joi.date().min(joi.ref('startDate'))
    })
    .messages({
      'date.base': 'End date must be a valid date',
      'date.format': 'End date must be in ISO format (YYYY-MM-DD)',
      'date.greater': 'End date must be greater than start date'
    }),
  type: joi.string()
    .valid(...Object.values(type))
    .messages({
      'string.base': 'Type must be a string',
      'any.only': `Type must be one of: ${Object.values(type).join(', ')}`
    }),
  search: joi.string()
    .trim()
    .messages({
      'string.base': 'Search query must be a string'
    }),
  user: joi.string()
    .custom(validateObjectId)
    .messages({
      'any.invalid': 'Invalid user ID format'
    }),
  temple: joi.string()
    .custom(validateObjectId)
    .messages({
      'any.invalid': 'Invalid temple ID format'
    })
});

const uploadUrlSchema = joi.object({
  extension: joi.string()
    .valid('jpeg', 'jpg', 'png')
    .required()
    .messages({
      'any.required': 'File extension is required',
      'any.only': 'Invalid file extension. Only JPEG, JPG and PNG are allowed'
    })
});

const phoneNumberSchema = joi.object({
  countryCode: joi.string()
    .trim()
    .pattern(/^\+\d{1,3}$/)
    .required()
    .messages({
      'string.empty': 'Country code is required',
      'string.pattern.base': 'Country code must start with + followed by 1 to 3 digits',
    }),
  
  phone: joi.string()
    .required()
    .custom((value, helpers) => {
      const { countryCode } = helpers.state.ancestors[0];
  
      const fullNumber = `${countryCode}${value}`;
      const parsed = parsePhoneNumberFromString(fullNumber);
  
      if (!parsed || !parsed.isValid()) {
        return helpers.message('Invalid phone number for the selected country');
      }
  
      return value; // or `parsed.number` if you want to store in E.164 format
    })
    .messages({
      'string.empty': 'Phone number is required',
    }),
});

const whatsappNumberSchema = joi.object({
  page: joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.min': 'Page number must be at least 1',
      'number.integer': 'Page must be an integer'
    }),

  limit: joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100 records per page',
      'number.integer': 'Limit must be an integer'
    }),

  search: joi.string()
    .trim()
    .optional()
    .messages({
      'string.base': 'Search must be a string'
    }),

  countryCode: joi.string()
    .trim()
    .pattern(/^\+\d{1,4}$/)
    .optional()
    .messages({
      'string.pattern.base': 'Country code must start with + followed by 1 to 4 digits',
    })
});

module.exports = {
  createBookingSchema,
  getMyBookingsSchema,
  getTempleBookingsSchema,
  validateId,
  getDarshanSlotAvailabilitySchema,
  getPoojaSlotAvailabilitySchema,
  fetchAllBookingsSchema,
  uploadUrlSchema,
  phoneNumberSchema,
  whatsappNumberSchema
};
