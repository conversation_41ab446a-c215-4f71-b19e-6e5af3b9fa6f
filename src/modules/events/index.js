const express = require('express');
const router = express.Router();
const eventController = require('./controller');
const auth = require('../../middleware/auth');
const { isSuperAdmin } = require('../../middleware/roleCheck');

// Admin routes - require Super Admin access
router.get('/', eventController.listEvents);
router.post('/', auth, isSuperAdmin, eventController.createEvent);
router.get('/homepage', auth, eventController.getHomepageEvents);
router.get('/:id', eventController.getEventById);
router.put('/:id', auth, isSuperAdmin, eventController.updateEvent);
router.delete('/:id', auth, isSuperAdmin, eventController.deleteEvent);
router.post('/upload-url', auth, isSuperAdmin, eventController.getUploadUrl);
router.delete('/image/:key(*)', auth, isSuperAdmin, eventController.deleteImage);

module.exports = router;
