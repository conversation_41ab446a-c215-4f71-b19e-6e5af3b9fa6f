const Event = require('../../models/Event');
const Temple = require('../../models/Temple');
const { throwBadRequestError, throwNotFoundError } = require('../../errors');
const { messages } = require('../../messages');
const { translateDataForStore } = require('../../utils/translateInput');
const { transformTranslatedFields } = require('../../utils/localizer');
const mongoose = require('mongoose');
const { deleteFile } = require('../../utils/s3Service');
const User = require('../../models/User');
const Language = require('../../models/Language');

/**
 * Create a new event
 * @param {Object} eventData - Event data
 * @param {String} userId - ID of the admin creating the event
 * @returns {Promise<Object>} Created event
 */
const createEvent = async (eventData, userId) => {
  // If temple ID is provided, check if it exists
  if (eventData.temple) {
    const temple = await Temple.findById(eventData.temple);

    if (!temple) {
      throwBadRequestError(messages.TEMPLE_NOT_FOUND);
    }
  }

  // Process poster image if it doesn't already have the MEDIA_URL
  if (eventData.posterImage && !eventData.posterImage.startsWith(process.env.MEDIA_URL)) {
    eventData.posterImage = `${process.env.MEDIA_URL}/${eventData.posterImage}`;
  }

  // Validate date-specific pricing for DATE_RANGE events
  if (eventData.dateType === 'DATE_RANGE' && eventData.datePricing && eventData.datePricing.length > 0) {
    // Ensure all dates in datePricing are within the date range
    const { startDate, endDate } = eventData.dateRange;
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    // Set time to beginning of day for accurate comparison
    endDateObj.setHours(23, 59, 59, 999);

    // Check each date in datePricing
    for (const datePricing of eventData.datePricing) {
      const pricingDate = new Date(datePricing.date);

      // pricingDate.setHours(0, 0, 0, 0);

      if (pricingDate < startDateObj || pricingDate > endDateObj) {
        throwBadRequestError(`Date ${pricingDate.toISOString().split('T')[0]} in pricing is outside the event date range`);
      }
    }

    // Check if all dates in the range have pricing
    const dateSet = new Set();

    eventData.datePricing.forEach(dp => {
      const dateStr = new Date(dp.date).toISOString().split('T')[0];

      dateSet.add(dateStr);
    });

    // Generate all dates in the range
    const allDates = [];
    const currentDate = new Date(startDateObj);

    while (currentDate <= endDateObj) {
      allDates.push(currentDate.toISOString().split('T')[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Check if any dates are missing pricing
    const missingDates = allDates.filter(date => !dateSet.has(date));

    if (missingDates.length > 0) {
      throwBadRequestError(`Missing pricing for dates: ${missingDates.join(', ')}`);
    }
  }

  // Translate fields
  const translatedFields = ['name', 'location', 'description', 'guidelines'];
  const translatedData = await translateDataForStore(translatedFields, eventData);

  translatedFields.forEach(field => {
    if (eventData[field]) {
      delete eventData[field];
    }
  });

  eventData = {
    ...eventData,
    ...translatedData
  };

  // Create the event
  const event = new Event({
    ...eventData,
    createdBy: userId
  });

  await event.save();
  return event;
};

/**
 * Get event by ID
 * @param {String} eventId - Event ID
 * @param {Object} user - User object (optional, for language preference)
 * @returns {Promise<Object>} Event
 */
const getEventById = async (eventId, user) => {
  // Use aggregation pipeline for consistent data format with other functions
  const aggregationPipeline = [
    // Match by ID
    {
      $match: {
        _id: new mongoose.Types.ObjectId(eventId)
      }
    },
    // Lookup temple data
    {
      $lookup: {
        from: 'temples',
        localField: 'temple',
        foreignField: '_id',
        as: 'templeData'
      }
    },
    // Unwind the arrays to get objects
    {
      $addFields: {
        temple: { $arrayElemAt: [ '$templeData', 0 ] },
      }
    },
    {
      $project: {
        templeData: 0,
      }
    }
  ];

  // Execute the aggregation pipeline
  const events = await Event.aggregate(aggregationPipeline);

  // Check if event exists
  if (!events || events.length === 0) {
    throwNotFoundError(messages.EVENT_NOT_FOUND);
  }

  const event = events[0];
  let startPricing = null;

  if (event.dateType === 'SPECIFIC_DATE') {
    // For SPECIFIC_DATE events, find the minimum price among individual, couple, and family
    if (event.pricing) {
      const prices = [
        event.pricing.individual,
        event.pricing.couple,
        event.pricing.family
      ].filter(price => price !== undefined && price !== null);

      if (prices.length > 0) {
        startPricing = Math.min(...prices);
      }
    }
  } else if (event.dateType === 'DATE_RANGE') {
    // For DATE_RANGE events, find the minimum price across all dates and categories
    if (event.datePricing && event.datePricing.length > 0) {
      const allPrices = [];

      event.datePricing.forEach(datePricing => {
        if (datePricing.pricing) {
          if (datePricing.pricing.individual) {
            allPrices.push(datePricing.pricing.individual);
          }
          if (datePricing.pricing.couple) {
            allPrices.push(datePricing.pricing.couple);
          }
          if (datePricing.pricing.family) {
            allPrices.push(datePricing.pricing.family);
          }
        }
      });

      if (allPrices.length > 0) {
        startPricing = Math.min(...allPrices);
      }
    }
  }

  // Localize the event if user is provided
    
  const loggedInUser = await User.findById(user.id);
  const language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  const localizedEvent = await transformTranslatedFields(event, language.code);

  return {
    ...localizedEvent,
    startPricing    
  };
};

/**
 * Update an event
 * @param {String} eventId - Event ID
 * @param {Object} updateData - Data to update
 * @param {String} userId - ID of the admin updating the event
 * @returns {Promise<Object>} Updated event
 */
const updateEvent = async (eventId, updateData, userId) => {
  // Check if event exists
  const event = await Event.findById(eventId);

  if (!event) {
    throwNotFoundError(messages.EVENT_NOT_FOUND);
  }

  // If temple ID is provided, check if it exists
  if (updateData.temple) {
    const temple = await Temple.findById(updateData.temple);

    if (!temple) {
      throwBadRequestError(messages.TEMPLE_NOT_FOUND);
    }
  }

  // Process poster image if it's provided and doesn't already have the MEDIA_URL
  if (updateData.posterImage && !updateData.posterImage.startsWith(process.env.MEDIA_URL)) {
    updateData.posterImage = `${process.env.MEDIA_URL}/${updateData.posterImage}`;
  }

  // Validate date-specific pricing for DATE_RANGE events
  if (updateData.dateType === 'DATE_RANGE' && updateData.datePricing && updateData.datePricing.length > 0) {
    // Get the date range (either from update data or existing event)
    const startDate = updateData.dateRange?.startDate || event.dateRange.startDate;
    const endDate = updateData.dateRange?.endDate || event.dateRange.endDate;

    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    // Set time to beginning of day for accurate comparison
    startDateObj.setHours(0, 0, 0, 0);
    endDateObj.setHours(23, 59, 59, 999);

    // Check each date in datePricing
    for (const datePricing of updateData.datePricing) {
      const pricingDate = new Date(datePricing.date);

      pricingDate.setHours(0, 0, 0, 0);

      if (pricingDate < startDateObj || pricingDate > endDateObj) {
        throwBadRequestError(`Date ${pricingDate.toISOString().split('T')[0]} in pricing is outside the event date range`);
      }
    }

    // Check if all dates in the range have pricing
    const dateSet = new Set();

    updateData.datePricing.forEach(dp => {
      const dateStr = new Date(dp.date).toISOString().split('T')[0];

      dateSet.add(dateStr);
    });

    // Generate all dates in the range
    const allDates = [];
    const currentDate = new Date(startDateObj);

    while (currentDate <= endDateObj) {
      allDates.push(currentDate.toISOString().split('T')[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Check if any dates are missing pricing
    const missingDates = allDates.filter(date => !dateSet.has(date));

    if (missingDates.length > 0) {
      throwBadRequestError(`Missing pricing for dates: ${missingDates.join(', ')}`);
    }
  }

  // If changing from DATE_RANGE to SPECIFIC_DATE, remove datePricing
  if (updateData.dateType === 'SPECIFIC_DATE' && event.dateType === 'DATE_RANGE') {
    updateData.datePricing = [];
  }

  // Translate fields if provided
  const translatedFields = ['name', 'location', 'description', 'guidelines'];
  const hasTranslatableFields = translatedFields.some(field => updateData[field]);

  if (hasTranslatableFields) {
    const translatedData = await translateDataForStore(translatedFields, updateData);

    translatedFields.forEach(field => {
      if (updateData[field]) {
        delete updateData[field];
      }
    });

    updateData = {
      ...updateData,
      ...translatedData
    };
  }

  // Update the event
  const updatedEvent = await Event.findByIdAndUpdate(
    eventId,
    {
      ...updateData,
      updatedBy: userId
    },
    { new: true, runValidators: true }
  ).populate('temple', 'name');

  return updatedEvent;
};

/**
 * Delete an event
 * @param {String} eventId - Event ID
 * @returns {Promise<void>}
 */
const deleteEvent = async (eventId) => {
  const event = await Event.findById(eventId);

  if (!event) {
    throwNotFoundError(messages.EVENT_NOT_FOUND);
  }

  if (event.deletedAt || event.status === 'INACTIVE') {
    throwBadRequestError('Event already deleted or inactive');
  }

  // Extract the image key from the poster URL
  if (event.posterImage) {
    try {
      // The posterImage URL format is: MEDIA_URL/key
      // We need to extract the key part
      const mediaUrl = process.env.MEDIA_URL;

      if (event.posterImage.startsWith(mediaUrl)) {
        const key = event.posterImage.substring(mediaUrl.length + 1); // +1 for the '/' character

        // Delete the image file from storage
        await deleteFile(key);
      }
    } catch (error) {
      // Log error but continue with event deletion even if image deletion fails
      // eslint-disable-next-line no-console
      console.error('Error deleting event poster image:', error);
    }
  }

  //* Soft delete the event
  event.deletedAt = new Date();
  event.status = 'INACTIVE';
  await event.save();

  return { message: 'Event deleted successfully' };
};

/**
 * List events with filtering and pagination
 * @param {Object} query - Query parameters
 * @param {Object} user - User object (optional, for language preference)
 * @returns {Promise<Object>} Events and pagination info
 */
const listEvents = async (query, user) => {
  const {
    page = 1,
    limit = 10,
    search,
    temple,
    status,
    showOnHomepage,
    dateFrom,
    dateTo,
    sortBy = 'createdAt',
    sortOrder = -1
  } = query;

  const loggedInUser = await User.findById(user.id);
  const language = await Language.findOne({ name: loggedInUser.preferredLanguage });

  // Use aggregation pipeline for more complex search
  const aggregationPipeline = [];
  const matchStage = {};

  // Search by event name or temple name
  if (search) {
    // First, we need to lookup the temple to search by temple name
    aggregationPipeline.push({
      $lookup: {
        from: 'temples',
        localField: 'temple',
        foreignField: '_id',
        as: 'templeData'
      }
    });

    // Add search condition for event name or temple name using user's language
    matchStage.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`templeData.name.${language.code}`]: { $regex: search, $options: 'i' } }
    ];
  }

  // Filter by specific temple
  if (temple) {
    matchStage.temple = new mongoose.Types.ObjectId(temple);
  }

  // Filter by status
  if (status) {
    matchStage.status = status;
  }

  // Filter by showOnHomepage
  if (showOnHomepage !== undefined) {
    matchStage.showOnHomepage = showOnHomepage;
  }

  // Date filtering
  if (dateFrom || dateTo) {
    const dateFilter = {};

    if (dateFrom) {
      dateFilter.$gte = new Date(dateFrom);
    }

    if (dateTo) {
      dateFilter.$lte = new Date(dateTo);
    }

    // Apply date filter to both specific date and date range
    matchStage.$or = matchStage.$or || [];
    matchStage.$or.push(
      { specificDate: dateFilter },
      { 'dateRange.startDate': dateFilter },
      { 'dateRange.endDate': dateFilter }
    );
  }

  // Add match stage to pipeline if we have any filters
  if (Object.keys(matchStage).length > 0) {
    aggregationPipeline.push({ $match: matchStage });
  }

  // Add lookup stages for related data
  aggregationPipeline.push(
    // Lookup temple data
    {
      $lookup: {
        from: 'temples',
        localField: 'temple',
        foreignField: '_id',
        as: 'templeData'
      }
    },
    // Unwind the arrays to get objects
    {
      $addFields: {
        temple: { $arrayElemAt: [ '$templeData', 0 ] },
      }
    },
    {
      $project: {
        templeData: 0,
      }
    }
  );

  // Add sorting
  let sortField = sortBy;

  if (sortBy === 'date') {
    sortField = 'specificDate';
  }

  aggregationPipeline.push({
    $sort: { [sortField]: parseInt(sortOrder) }
  });

  // Add facet for pagination
  aggregationPipeline.push({
    $facet: {
      metadata: [{ $count: 'total' }],
      events: [
        { $skip: (parseInt(page) - 1) * parseInt(limit) },
        { $limit: parseInt(limit) }
      ]
    }
  });

  // Execute the aggregation pipeline
  const result = await Event.aggregate(aggregationPipeline).collation({ locale: 'en', strength: 1 });

  // Extract data from result
  const events = result[0].events || [];
  const total = result[0].metadata[0]?.total || 0;

  // Calculate minimum price for each event using JavaScript
  const eventsWithMinPrice = events.map(event => {
    let startPricing = null;

    if (event.dateType === 'SPECIFIC_DATE') {
      // For SPECIFIC_DATE events, find the minimum price among individual, couple, and family
      if (event.pricing) {
        const prices = [
          event.pricing.individual,
          event.pricing.couple,
          event.pricing.family
        ].filter(price => price !== undefined && price !== null);

        if (prices.length > 0) {
          startPricing = Math.min(...prices);
        }
      }
    } else if (event.dateType === 'DATE_RANGE') {
      // For DATE_RANGE events, find the minimum price across all dates and categories
      if (event.datePricing && event.datePricing.length > 0) {
        const allPrices = [];

        event.datePricing.forEach(datePricing => {
          if (datePricing.pricing) {
            if (datePricing.pricing.individual) {
              allPrices.push(datePricing.pricing.individual);
            }
            if (datePricing.pricing.couple) {
              allPrices.push(datePricing.pricing.couple);
            }
            if (datePricing.pricing.family) {
              allPrices.push(datePricing.pricing.family);
            }
          }
        });

        if (allPrices.length > 0) {
          startPricing = Math.min(...allPrices);
        }
      }
    }

    return {
      ...event,
      startPricing
    };
  });

  const localizedEvents = await transformTranslatedFields(eventsWithMinPrice, language.code);

  return {
    events: localizedEvents,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

/**
 * Get events for homepage
 * @returns {Promise<Array>} Events to display on homepage
 */
const getHomepageEvents = async () => {
  const dashboardEvents = await Event.find({ showOnHomepage: true })
    .sort({ updatedAt: -1 }).collation({ locale: 'en', strength: 1 });

  const otherEvents = await Event.find({ showOnHomepage: false })
    .sort({ name: 1 }).collation({ locale: 'en', strength: 1 });

  return {
    dashboardEvents,
    otherEvents
  };
};

module.exports = {
  createEvent,
  getEventById,
  updateEvent,
  deleteEvent,
  listEvents,
  getHomepageEvents
};
