const joi = require('joi');
const mongoose = require('mongoose');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string()
  .custom(validateObjectId)
  .required()
  .messages({
    'any.invalid': 'Invalid event ID format',
    'any.required': 'Event ID is required'
  });

const timePattern = /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/;

const createEventSchema = joi.object({
  name: joi.string()
    .min(1)
    .max(255)
    .required()
    .messages({
      'string.empty': 'Event name is required',
      'string.min': 'Event name must have at least 1 character',
      'string.max': 'Event name cannot exceed 255 characters',
      'any.required': 'Event name is required'
    }),
  location: joi.string()
    .min(1)
    .max(500)
    .required()
    .messages({
      'string.empty': 'Location is required',
      'string.min': 'Location must have at least 1 character',
      'string.max': 'Location cannot exceed 500 characters',
      'any.required': 'Location is required'
    }),
  dateType: joi.string()
    .valid('SPECIFIC_DATE', 'DATE_RANGE')
    .required()
    .messages({
      'any.only': 'Date type must be either SPECIFIC_DATE or DATE_RANGE',
      'any.required': 'Date type is required'
    }),
  specificDate: joi.when('dateType', {
    is: 'SPECIFIC_DATE',
    then: joi.date().greater('now').required(),
    otherwise: joi.forbidden()
  }),
  dateRange: joi.when('dateType', {
    is: 'DATE_RANGE',
    then: joi.object({
      startDate: joi.date().greater('now').required(),
      endDate: joi.date().greater(joi.ref('startDate')).required()
    }).required(),
    otherwise: joi.forbidden()
  }),
  startTime: joi.string()
    .pattern(timePattern)
    .required()
    .messages({
      'string.pattern.base': 'Start time must be in HH:MM AM/PM format',
      'any.required': 'Start time is required'
    }),
  endTime: joi.string()
    .pattern(timePattern)
    .required()
    .messages({
      'string.pattern.base': 'End time must be in HH:MM AM/PM format',
      'any.required': 'End time is required'
    }),
  // Default pricing for the entire event (used for SPECIFIC_DATE events)
  pricing: joi.object({
    individual: joi.number()
      .integer()
      .min(1)
      .messages({
        'number.base': 'Individual price must be a number',
        'number.integer': 'Individual price must be an integer',
        'number.min': 'Individual price must be greater than 0'
      }),
    couple: joi.number()
      .integer()
      .min(1)
      .messages({
        'number.base': 'Couple price must be a number',
        'number.integer': 'Couple price must be an integer',
        'number.min': 'Couple price must be greater than 0'
      }),
    family: joi.number()
      .integer()
      .min(1)
      .messages({
        'number.base': 'Family price must be a number',
        'number.integer': 'Family price must be an integer',
        'number.min': 'Family price must be greater than 0'
      })
  }).when('dateType', {
    is: 'SPECIFIC_DATE',
    then: joi.required(),
    otherwise: joi.optional()
  }),

  // Date-specific pricing for DATE_RANGE events
  datePricing: joi.array().items(
    joi.object({
      date: joi.date().required().messages({
        'date.base': 'Date must be a valid date',
        'any.required': 'Date is required for date-specific pricing'
      }),
      pricing: joi.object({
        individual: joi.number()
          .integer()
          .min(1)
          .required()
          .messages({
            'number.base': 'Individual price must be a number',
            'number.integer': 'Individual price must be an integer',
            'number.min': 'Individual price must be greater than 0',
            'any.required': 'Individual price is required'
          }),
        couple: joi.number()
          .integer()
          .min(1)
          .required()
          .messages({
            'number.base': 'Couple price must be a number',
            'number.integer': 'Couple price must be an integer',
            'number.min': 'Couple price must be greater than 0',
            'any.required': 'Couple price is required'
          }),
        family: joi.number()
          .integer()
          .min(1)
          .required()
          .messages({
            'number.base': 'Family price must be a number',
            'number.integer': 'Family price must be an integer',
            'number.min': 'Family price must be greater than 0',
            'any.required': 'Family price is required'
          })
      }).required()
    })
  ).when('dateType', {
    is: 'DATE_RANGE',
    then: joi.required().messages({
      'array.min': 'At least one date-specific pricing entry is required for date range events',
      'any.required': 'Date-specific pricing is required for date range events'
    }),
    otherwise: joi.optional()
  }),
  totalCapacityPerDay: joi.number()
    .integer()
    .min(1)
    .required()
    .messages({
      'number.base': 'Total capacity per day must be a number',
      'number.integer': 'Total capacity per day must be an integer',
      'number.min': 'Total capacity per day must be greater than 0',
      'any.required': 'Total capacity per day is required'
    }),
  description: joi.string()
    .min(1)
    .max(5000)
    .required()
    .messages({
      'string.empty': 'Description is required',
      'string.min': 'Description must have at least 1 character',
      'string.max': 'Description cannot exceed 5000 characters',
      'any.required': 'Description is required'
    }),
  guidelines: joi.string()
    .min(1)
    .max(2000)
    .required()
    .messages({
      'string.empty': 'Guidelines are required',
      'string.min': 'Guidelines must have at least 1 character',
      'string.max': 'Guidelines cannot exceed 2000 characters',
      'any.required': 'Guidelines are required'
    }),
  posterImage: joi.string()
    .required()
    .messages({
      'string.empty': 'Poster image is required',
      'any.required': 'Poster image is required'
    }),
  temple: joi.string()
    .custom(validateObjectId)
    .allow(null)
    .messages({
      'any.invalid': 'Invalid temple ID format'
    }),
  showOnHomepage: joi.boolean()
    .default(false)
});

const updateEventSchema = joi.object({
  name: joi.string()
    .min(1)
    .max(255)
    .messages({
      'string.empty': 'Event name cannot be empty',
      'string.min': 'Event name must have at least 1 character',
      'string.max': 'Event name cannot exceed 255 characters'
    }),
  location: joi.string()
    .min(1)
    .max(500)
    .messages({
      'string.empty': 'Location cannot be empty',
      'string.min': 'Location must have at least 1 character',
      'string.max': 'Location cannot exceed 500 characters'
    }),
  dateType: joi.string()
    .valid('SPECIFIC_DATE', 'DATE_RANGE')
    .messages({
      'any.only': 'Date type must be either SPECIFIC_DATE or DATE_RANGE'
    }),
  specificDate: joi.when('dateType', {
    is: 'SPECIFIC_DATE',
    then: joi.date().greater('now').required(),
    otherwise: joi.forbidden()
  }),
  dateRange: joi.when('dateType', {
    is: 'DATE_RANGE',
    then: joi.object({
      startDate: joi.date().greater('now').required(),
      endDate: joi.date().greater(joi.ref('startDate')).required()
    }).required(),
    otherwise: joi.forbidden()
  }),
  startTime: joi.string()
    .pattern(timePattern)
    .messages({
      'string.pattern.base': 'Start time must be in HH:MM AM/PM format'
    }),
  endTime: joi.string()
    .pattern(timePattern)
    .messages({
      'string.pattern.base': 'End time must be in HH:MM AM/PM format'
    }),
  pricing: joi.object({
    individual: joi.number()
      .integer()
      .min(1)
      .messages({
        'number.base': 'Individual price must be a number',
        'number.integer': 'Individual price must be an integer',
        'number.min': 'Individual price must be greater than 0'
      }),
    couple: joi.number()
      .integer()
      .min(1)
      .messages({
        'number.base': 'Couple price must be a number',
        'number.integer': 'Couple price must be an integer',
        'number.min': 'Couple price must be greater than 0'
      }),
    family: joi.number()
      .integer()
      .min(1)
      .messages({
        'number.base': 'Family price must be a number',
        'number.integer': 'Family price must be an integer',
        'number.min': 'Family price must be greater than 0'
      })
  }),

  // Date-specific pricing for DATE_RANGE events
  datePricing: joi.array().items(
    joi.object({
      date: joi.date().required().messages({
        'date.base': 'Date must be a valid date',
        'any.required': 'Date is required for date-specific pricing'
      }),
      pricing: joi.object({
        individual: joi.number()
          .integer()
          .min(1)
          .required()
          .messages({
            'number.base': 'Individual price must be a number',
            'number.integer': 'Individual price must be an integer',
            'number.min': 'Individual price must be greater than 0',
            'any.required': 'Individual price is required'
          }),
        couple: joi.number()
          .integer()
          .min(1)
          .required()
          .messages({
            'number.base': 'Couple price must be a number',
            'number.integer': 'Couple price must be an integer',
            'number.min': 'Couple price must be greater than 0',
            'any.required': 'Couple price is required'
          }),
        family: joi.number()
          .integer()
          .min(1)
          .required()
          .messages({
            'number.base': 'Family price must be a number',
            'number.integer': 'Family price must be an integer',
            'number.min': 'Family price must be greater than 0',
            'any.required': 'Family price is required'
          })
      }).required()
    })
  ),
  totalCapacityPerDay: joi.number()
    .integer()
    .min(1)
    .messages({
      'number.base': 'Total capacity per day must be a number',
      'number.integer': 'Total capacity per day must be an integer',
      'number.min': 'Total capacity per day must be greater than 0'
    }),
  description: joi.string()
    .min(1)
    .max(5000)
    .messages({
      'string.empty': 'Description cannot be empty',
      'string.min': 'Description must have at least 1 character',
      'string.max': 'Description cannot exceed 5000 characters'
    }),
  guidelines: joi.string()
    .min(1)
    .max(2000)
    .messages({
      'string.empty': 'Guidelines cannot be empty',
      'string.min': 'Guidelines must have at least 1 character',
      'string.max': 'Guidelines cannot exceed 2000 characters'
    }),
  posterImage: joi.string(),
  temple: joi.string()
    .custom(validateObjectId)
    .allow(null)
    .messages({
      'any.invalid': 'Invalid temple ID format'
    }),
  showOnHomepage: joi.boolean(),
  status: joi.string()
    .valid('ACTIVE', 'INACTIVE')
    .messages({
      'any.only': 'Status must be either ACTIVE or INACTIVE'
    })
}).min(1); // Ensure at least one field is being updated

const listEventsSchema = joi.object({
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(10),
  search: joi.string(),
  temple: joi.string().custom(validateObjectId).messages({
    'any.invalid': 'Invalid temple ID format'
  }),
  status: joi.string().valid('ACTIVE', 'INACTIVE'),
  showOnHomepage: joi.boolean(),
  dateFrom: joi.date(),
  dateTo: joi.date().min(joi.ref('dateFrom')).messages({
    'date.min': 'End date must be greater than or equal to start date'
  }),
  sortBy: joi.string().valid('name', 'description', 'createdAt', 'specificDate', 'dateRange.startDate').default('createdAt'),
  sortOrder: joi.number().valid(1, -1).default(-1)
});

const uploadUrlSchema = joi.object({
  extension: joi.string()
    .valid('jpeg', 'jpg', 'png')
    .required()
    .messages({
      'any.required': 'File extension is required',
      'any.only': 'Invalid file extension. Only JPEG, JPG and PNG are allowed'
    })
});

module.exports = {
  createEventSchema,
  updateEventSchema,
  listEventsSchema,
  validateId,
  uploadUrlSchema
};
