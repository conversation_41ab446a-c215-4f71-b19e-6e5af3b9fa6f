const { apiResponse, errorApiResponse } = require('../../config/responseHandler');
const { commonConstants } = require('../../constants/common');
const { messages } = require('../../messages');
const gotraService = require('./service');
const { addGotraSchema, validateId, updateGotraSchema } = require('./validation');
const { SUCCESS } = commonConstants;
const { saveAuditLog } = require('../../utils/auditLogger');
const { auditLogAction } = require('../../constants/dbEnums');

//* 1. Funtion to create gotra 
const addGotra = async (req, res) => {
  try {
    const { error } = addGotraSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }
    
    const data = await gotraService.addGotra(req.body, req.user.id);

    //* Save audit log 
    const detail = `Gotra ${data.gotra.en.toLowerCase()} created successfully`;
    const model = 'Gotra';

    await saveAuditLog(req, req.user.id, auditLogAction.GOTRA_CREATED, detail, model);
    
    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.GOTRA_CREATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 2. Function to list all the gotras
const listGotras = async (req, res) => {
  try {
    const data = await gotraService.listGotras(req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: req.user.preferredLanguage === 'English' ? messages.GOTRA_CREATED : messages.GOTRA_CREATED_HI,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 3. Funtion to update gotra 
const updateGotra = async (req, res) => {
  try {
    const { error: idError } = validateId.validate(req.params.id);
        
    if (idError) {
      return res.status(400).json({
        status: false,
        message: idError.details[0].message
      });
    }

    const { error } = updateGotraSchema.validate(req.body);
        
    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }
        
    const data = await gotraService.updateGotra(req.params.id, req.body, req.user.id);

    //* Save audit log 
    const detail = `Gotra ${data.gotra.en} updated successfully`;
    const model = 'Gotra';

    await saveAuditLog(req, req.user.id, auditLogAction.GOTRA_UPDATED, detail, model);
        
    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.GOTRA_UPDATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  addGotra,
  listGotras,
  updateGotra,
};