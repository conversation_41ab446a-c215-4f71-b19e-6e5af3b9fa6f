const express = require('express');
const router = express.Router();
const gotraController = require('./controller');
const auth = require('../../middleware/auth');
const { isAdminOrSuperAdmin, isUser } = require('../../middleware/roleCheck');

router.post('/add', auth, isAdminOrSuperAdmin, gotraController.addGotra);
router.get('/list', auth, isUser, gotraController.listGotras);
router.put('/:id', auth, isAdminOrSuperAdmin, gotraController.updateGotra);

module.exports = router;