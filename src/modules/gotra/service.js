const Gotra = require('../../models/Gotra');
const User = require('../../models/User');
const Language = require('../../models/Language');
const { throwNotFoundError, throwBadRequestError } = require('../../errors');
const { messages } = require('../../messages');
const { translateDataForStore } = require('../../utils/translateInput');
const { transformTranslatedFields } = require('../../utils/localizer');

//* 1. Function to create gotra 
const addGotra = async (body, adminId) => {
  const { gotra } = body;

  const gotraExist = await Gotra.findOne({ 'gotra.en': gotra });

  if (gotraExist) {
    throwBadRequestError(messages.GOTRA_EXIST);
  }

  const translatedData = await translateDataForStore([ 'gotra' ], body);
  
  const newGotra = await Gotra.create({
    gotra: translatedData.gotra, 
    createdBy: adminId, 
    updatedBy: adminId
  });

  return newGotra;
};

//* 2. Funtion to list all the gotra's 
const listGotras = async (userId) => {
  const gotras = await Gotra.find({ deletedAt: null }).lean();

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  const localizedGotras = await transformTranslatedFields(gotras, language.code);

  return localizedGotras;
};

//* 3. Function to update gotra 
const updateGotra = async (gotraId, updateData, adminId) => {
  const gotra = await Gotra.findById(gotraId);

  if (!gotra) {
    throwNotFoundError(messages.GOTRA_NOT_FOUND);
  }

  if (updateData.gotra && updateData.gotra !== gotra.gotra.en) {
    const nameExists = await Gotra.findOne({ 
      'gotra.en': updateData.gotra,
      _id: { $ne: gotraId }
    });
    
    if (nameExists) {
      throwBadRequestError(messages.GOTRA_ALREADY_EXISTS);
    }
  } 

  if (updateData.gotra) {
    const translatedData = await translateDataForStore([ 'gotra' ], updateData);

    updateData.gotra = translatedData.gotra;
  }

  //* Update gotra 
  const updatedGotra = await Gotra.findByIdAndUpdate(
    gotraId,
    {
      ...updateData,
      updatedBy: adminId
    },
    { new: true }
  );

  return updatedGotra;
};

module.exports = {
  addGotra,
  listGotras,
  updateGotra
};