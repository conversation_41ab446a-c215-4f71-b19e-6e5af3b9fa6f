const joi = require('joi');
const mongoose = require('mongoose');

const addGotraSchema = joi.object({
  gotra: joi.string().trim().required().messages({
    'string.base': 'Gotra must be a string',
    'any.required': 'Gotra is required'
  })
});

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string()
  .custom(validateObjectId)
  .required()
  .messages({
    'any.invalid': 'Invalid gotra ID format',
    'any.required': 'Gotra ID is required'
  });

const updateGotraSchema = joi.object({
  gotra: joi.string().trim().required().messages({
    'string.base': 'Gotra must be a string',
    'any.required': 'Gotra is required'
  })
});

module.exports = {
  addGotraSchema,
  updateGotraSchema,
  validateId
};