const userModule = require('./user');
const adminModule = require('./admin');
const templeModule = require('./temple');
const templeAdminModule = require('./templeAdmin');
const bookingModule = require('./booking');
const paymentModule = require('./payment');
const pujariModule = require('./pujari');
const eventsModule = require('./events');
const kioskModule = require('./kiosk');
const templeCategoryModule = require('./templeCategory');
const vendorModule = require('./vendor');
const shopModule = require('./shop');

module.exports = (app) => {
  app.use('/api/v1/user', userModule);
  app.use('/api/v1/admin', adminModule);
  app.use('/api/v1/temples', templeModule);
  app.use('/api/v1/temple-admin', templeAdminModule);
  app.use('/api/v1/booking', bookingModule);
  app.use('/api/v1/payment', paymentModule);
  app.use('/api/v1/pujaris', pujariModule);
  app.use('/api/v1/events', eventsModule);
  app.use('/api/v1/kiosks', kioskModule);
  app.use('/api/v1/temple-category', templeCategoryModule);
  app.use('/api/v1/vendors', vendorModule);
  app.use('/api/v1/shop', shopModule);
};
