const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const attendanceService = require('./service');
const { checkInSchema, checkOutSchema, getAttendanceSchema, markAbsenceSchema } = require('./validation');
const { SUCCESS } = commonConstants;
const { saveAuditLog } = require('../../../utils/auditLogger');
const { auditLogAction } = require('../../../constants/dbEnums');

/**
 * Mark check-in for a kiosk user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const markCheckIn = async (req, res) => {
  try {
    const { error, value } = checkInSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await attendanceService.markCheckIn(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Check-in marked successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Mark check-out for a kiosk user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const markCheckOut = async (req, res) => {
  try {
    const { error, value } = checkOutSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await attendanceService.markCheckOut(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Check-out marked successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Mark absence for a kiosk user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const markAbsence = async (req, res) => {
  try {
    const { error, value } = markAbsenceSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await attendanceService.markAbsence(value);

    //* Save audit log 
    const detail = `Absence marked for kiosk user ${result.kioskUser} on ${result.date}`;
    const model = 'Attendance';

    await saveAuditLog(req, req.user.id, auditLogAction.ATTENDANCE_MARKED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Absence marked successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get attendance records for a kiosk user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getAttendance = async (req, res) => {
  try {
    const { error, value } = getAttendanceSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await attendanceService.getAttendance(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Attendance records retrieved successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get today's attendance for a kiosk user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getTodayAttendance = async (req, res) => {
  try {
    const kioskUserId = req.params.kioskUserId;

    if (!kioskUserId) {
      return res.status(400).json({
        status: false,
        message: 'Kiosk user ID is required'
      });
    }

    const result = await attendanceService.getTodayAttendance(kioskUserId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Today\'s attendance retrieved successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  markCheckIn,
  markCheckOut,
  markAbsence,
  getAttendance,
  getTodayAttendance
};
