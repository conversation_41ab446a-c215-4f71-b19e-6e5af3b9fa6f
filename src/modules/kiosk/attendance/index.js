const express = require('express');
const router = express.Router();
const attendanceController = require('./controller');
const auth = require('../../../middleware/auth');
const { isKioskUser, isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

// Kiosk user routes
router.post('/check-in', auth, isKioskUser, attendanceController.markCheckIn);
router.post('/check-out', auth, isKioskUser, attendanceController.markCheckOut);
router.get('/today/:kioskUserId', auth, attendanceController.getTodayAttendance);
router.get('/', auth, attendanceController.getAttendance);

// Admin routes
router.post('/absence', auth, isAdminOrSuperAdmin, attendanceController.markAbsence);

module.exports = router;
