const Attendance = require('../../../models/Attendance');
const KioskUser = require('../../../models/KioskUser');
const { throwBadRequestError, throwNotFoundError } = require('../../../errors');
const moment = require('moment');

/**
 * Mark check-in for a kiosk user
 * @param {Object} data - Check-in data
 * @returns {Object} Attendance record
 */
const markCheckIn = async (data) => {
  // Check if kiosk user exists
  const kioskUser = await KioskUser.findById(data.kioskUser);

  if (!kioskUser) {
    throwNotFoundError('Kiosk user not found');
  }

  // Format date to remove time component for uniqueness check
  const dateOnly = moment(data.date).format('YYYY-MM-DD');

  // Check if attendance record exists for this date
  let attendance = await Attendance.findOne({
    kioskUser: data.kioskUser,
    date: dateOnly
  });

  if (attendance) {
    // Check if already checked in
    if (attendance.checkInTime) {
      throwBadRequestError('Already checked in for today');
    }

    // Update existing record
    attendance.checkInTime = data.time;
    attendance.checkInTimezone = data.timezone;

    // Only mark as PRESENT if both check-in and check-out are done
    if (attendance.checkOutTime) {
      attendance.status = 'PRESENT';
    }

    if (data.notes) {
      attendance.notes = data.notes;
    }
  } else {
    // Create new attendance record
    attendance = new Attendance({
      kioskUser: data.kioskUser,
      date: dateOnly,
      checkInTime: data.time,
      checkInTimezone: data.timezone,
      status: 'ABSENT', // Will be updated to PRESENT when check-out is done
      notes: data.notes || ''
    });
  }

  await attendance.save();

  return attendance;
};

/**
 * Mark check-out for a kiosk user
 * @param {Object} data - Check-out data
 * @returns {Object} Attendance record
 */
const markCheckOut = async (data) => {
  // Check if kiosk user exists
  const kioskUser = await KioskUser.findById(data.kioskUser);

  if (!kioskUser) {
    throwNotFoundError('Kiosk user not found');
  }

  // Format date to remove time component for uniqueness check
  const dateOnly = moment(data.date).format('YYYY-MM-DD');

  // Check if attendance record exists for this date
  let attendance = await Attendance.findOne({
    kioskUser: data.kioskUser,
    date: dateOnly
  });

  if (attendance) {
    // Check if already checked out
    if (attendance.checkOutTime) {
      throwBadRequestError('Already checked out for today');
    }

    // Update existing record
    attendance.checkOutTime = data.time;
    attendance.checkOutTimezone = data.timezone;

    // Mark as PRESENT only if both check-in and check-out are done
    if (attendance.checkInTime) {
      attendance.status = 'PRESENT';
    }

    if (data.notes) {
      attendance.notes = data.notes;
    }
  } else {
    // Create new attendance record with only check-out time
    attendance = new Attendance({
      kioskUser: data.kioskUser,
      date: dateOnly,
      checkOutTime: data.time,
      checkOutTimezone: data.timezone,
      status: 'ABSENT', // Will remain ABSENT since there's no check-in
      notes: data.notes || ''
    });
  }

  await attendance.save();

  return attendance;
};

/**
 * Mark absence for a kiosk user
 * @param {Object} data - Absence data
 * @returns {Object} Attendance record
 */
const markAbsence = async (data) => {
  // Check if kiosk user exists
  const kioskUser = await KioskUser.findById(data.kioskUser);

  if (!kioskUser) {
    throwNotFoundError('Kiosk user not found');
  }

  // Format date to remove time component for uniqueness check
  const dateOnly = moment(data.date).format('YYYY-MM-DD');

  // Check if attendance record exists for this date
  let attendance = await Attendance.findOne({
    kioskUser: data.kioskUser,
    date: dateOnly
  });

  if (attendance) {
    // Update existing record
    attendance.status = data.status;
    if (data.notes) {
      attendance.notes = data.notes;
    }
  } else {
    // Create new attendance record
    attendance = new Attendance({
      kioskUser: data.kioskUser,
      date: dateOnly,
      status: data.status,
      notes: data.notes || ''
    });
  }

  await attendance.save();

  return attendance;
};

/**
 * Get attendance records for a kiosk user by date range
 * @param {Object} data - Query data
 * @returns {Object} Attendance records
 */
const getAttendance = async (data) => {
  // Check if kiosk user exists
  const kioskUser = await KioskUser.findById(data.kioskUser);

  if (!kioskUser) {
    throwNotFoundError('Kiosk user not found');
  }

  // Format dates
  const startDate = moment(data.startDate).format('YYYY-MM-DD');
  const endDate = moment(data.endDate).format('YYYY-MM-DD');

  // Validate date range
  if (moment(startDate, 'YYYY-MM-DD').isAfter(moment(endDate, 'YYYY-MM-DD'))) {
    throwBadRequestError('Start date cannot be after end date');
  }

  // Get attendance records
  const attendanceRecords = await Attendance.find({
    kioskUser: data.kioskUser,
    date: {
      $gte: startDate,
      $lte: endDate
    }
  }).sort({ date: 1 }).collation({ locale: 'en', strength: 1 });

  // Process data based on view type
  const result = processDailyView(attendanceRecords, startDate, endDate, kioskUser);

  return {
    kioskUser,
    startDate: startDate,
    endDate: endDate,
    attendance: result,
    attendanceRecords
  };
};

/**
 * Process attendance records for daily view
 * @param {Array} records - Attendance records
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {Object} kioskUser - Kiosk user object with creation date
 * @returns {Array} Processed records
 */
const processDailyView = (records, startDate, endDate, kioskUser) => {
  const result = [];
  const recordMap = {};

  // Get the kiosk user creation date
  const creationDate = moment(kioskUser.createdAt).startOf('day');

  // Create a map of records by date
  records.forEach(record => {
    const dateStr = moment(record.date).format('YYYY-MM-DD');

    recordMap[dateStr] = {
      date: record.date,
      checkInTime: record.checkInTime,
      checkOutTime: record.checkOutTime,
      status: record.status,
      notes: record.notes
    };
  });

  // Fill in all dates in the range
  let currentDate = moment(startDate);
  const lastDate = moment(endDate);

  while (currentDate <= lastDate) {
    const dateStr = currentDate.format('YYYY-MM-DD');

    if (recordMap[dateStr]) {
      result.push(recordMap[dateStr]);
    } else {
      // Check if the current date is before the kiosk user was created
      const status = currentDate.isBefore(creationDate) ? 'N/A' : 'ABSENT';

      result.push({
        date: currentDate.format('YYYY-MM-DD'),
        checkInTime: null,
        checkOutTime: null,
        status: status,
        notes: ''
      });
    }
    currentDate = currentDate.add(1, 'days');
  }

  return result;
};

/**
 * Get today's attendance for a kiosk user
 * @param {string} kioskUserId - Kiosk user ID
 * @returns {Object} Today's attendance
 */
const getTodayAttendance = async (kioskUserId) => {
  // Check if kiosk user exists
  const kioskUser = await KioskUser.findById(kioskUserId).select('-password');

  if (!kioskUser) {
    throwNotFoundError('Kiosk user not found');
  }

  // Get today's date
  const today = moment().format('YYYY-MM-DD');

  // Get attendance record for today
  const attendance = await Attendance.findOne({
    kioskUser: kioskUserId,
    date: today
  });

  if (!attendance) {
    // Check if the kiosk user was created today or earlier
    const creationDate = moment(kioskUser.createdAt).startOf('day');
    const todayDate = moment(today).startOf('day');

    // If the kiosk user was created today or earlier, show ABSENT, otherwise show N/A
    const status = todayDate.isSameOrAfter(creationDate) ? 'ABSENT' : 'N/A';

    return {
      kioskUser,
      date: today,
      checkInTime: null,
      checkOutTime: null,
      status: status,
      notes: ''
    };
  }

  return {
    kioskUser,
    date: attendance.date,
    checkInTime: attendance.checkInTime,
    checkOutTime: attendance.checkOutTime,
    status: attendance.status,
    notes: attendance.notes
  };
};

module.exports = {
  markCheckIn,
  markCheckOut,
  markAbsence,
  getAttendance,
  getTodayAttendance
};
