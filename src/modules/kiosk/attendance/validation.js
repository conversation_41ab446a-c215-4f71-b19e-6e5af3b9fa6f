const joi = require('joi');
const mongoose = require('mongoose');
const moment = require('moment');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

// Custom validation function for date strings in YYYY-MM-DD format
const validateDateString = (value, helpers) => {
  // Check if the format is correct
  if (!moment(value, 'YYYY-MM-DD', true).isValid()) {
    return helpers.error('string.dateFormat');
  }
  return value;
};

// Schema for marking check-in
const checkInSchema = joi.object({
  kioskUser: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Kiosk user ID is required',
      'any.required': 'Kiosk user ID is required'
    }),
  date: joi.string().custom(validateDateString).default(() => moment().format('YYYY-MM-DD'))
    .messages({
      'string.dateFormat': 'Date must be in YYYY-MM-DD format and be a valid date'
    }),
  time: joi.string().pattern(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/)
    .required()
    .messages({
      'string.empty': 'Start time is required',
      'string.pattern.base': 'Invalid start time format. Use HH:MM AM/PM format',
      'any.required': 'Start time is required'
    }),
  timezone: joi.string().required()
    .messages({
      'string.empty': 'Timezone is required',
      'any.required': 'Timezone is required'
    }),
  notes: joi.string().allow('').optional()
});

// Schema for marking check-out
const checkOutSchema = joi.object({
  kioskUser: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Kiosk user ID is required',
      'any.required': 'Kiosk user ID is required'
    }),
  date: joi.string().custom(validateDateString).default(() => moment().format('YYYY-MM-DD'))
    .messages({
      'string.dateFormat': 'Date must be in YYYY-MM-DD format and be a valid date'
    }),
  
  time: joi.string().pattern(/^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/)
    .required()
    .messages({
      'string.empty': 'Start time is required',
      'string.pattern.base': 'Invalid start time format. Use HH:MM AM/PM format',
      'any.required': 'Start time is required'
    }),
  timezone: joi.string().required()
    .messages({
      'string.empty': 'Timezone is required',
      'any.required': 'Timezone is required'
    }),
  notes: joi.string().allow('').optional()
});

// Schema for getting attendance by date range
const getAttendanceSchema = joi.object({
  kioskUser: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Kiosk user ID is required',
      'any.required': 'Kiosk user ID is required'
    }),
  startDate: joi.string().custom(validateDateString).required()
    .messages({
      'string.empty': 'Start date is required',
      'string.dateFormat': 'Start date must be in YYYY-MM-DD format and be a valid date',
      'any.required': 'Start date is required'
    }),
  endDate: joi.string().custom(validateDateString).required()
    .messages({
      'string.empty': 'End date is required',
      'string.dateFormat': 'End date must be in YYYY-MM-DD format and be a valid date',
      'any.required': 'End date is required'
    })
});

// Schema for marking absence
const markAbsenceSchema = joi.object({
  kioskUser: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Kiosk user ID is required',
      'any.required': 'Kiosk user ID is required'
    }),
  date: joi.string().custom(validateDateString).required()
    .messages({
      'string.empty': 'Date is required',
      'string.dateFormat': 'Date must be in YYYY-MM-DD format and be a valid date',
      'any.required': 'Date is required'
    }),
  status: joi.string().valid('ABSENT', 'HALF_DAY').required()
    .messages({
      'string.empty': 'Status is required',
      'any.required': 'Status is required'
    }),
  notes: joi.string().allow('').optional()
});

module.exports = {
  checkInSchema,
  checkOutSchema,
  getAttendanceSchema,
  markAbsenceSchema
};
