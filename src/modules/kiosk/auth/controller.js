const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const authService = require('./service');
const { loginSchema } = require('./validation');
const { SUCCESS } = commonConstants;

/**
 * Login a kiosk user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const login = async (req, res) => {
  try {
    const { error, value } = loginSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await authService.login(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Login successful',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const logout = async (req, res) => {
  try {
    await authService.logout(req.user.id, req.headers.authorization.replace('Bearer ', ''));

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Logged out successfully',
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  login,
  logout
};
