const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const authService = require('./service');
const { loginSchema, sendOtpSchema, verifyOtpSchema } = require('./validation');
const { SUCCESS } = commonConstants;
const { messages } = require('../../../messages');

/**
 * Login a kiosk user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const login = async (req, res) => {
  try {
    const { error, value } = loginSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await authService.login(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Login successful',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* Function to send OTP
const sendOtp = async (req, res) => {
  try {
    const { error } = sendOtpSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await authService.sendOtp(req.body);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: req.body.preferredLanguage === 'Hindi' ? messages.OTP_SENT_HI : messages.OTP_SENT, 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* Function for verify otp
const verifyOtp = async (req, res) => {
  try {
    const { error } = verifyOtpSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await authService.verifyOtp(req.body);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: messages.SUCCESS, 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const logout = async (req, res) => {
  try {
    await authService.logout(req.user.id, req.headers.authorization.replace('Bearer ', ''));

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Logged out successfully',
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  login,
  sendOtp,
  verifyOtp,
  logout
};
