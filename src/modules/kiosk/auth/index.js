const express = require('express');
const router = express.Router();
const authController = require('./controller');
const auth = require('../../../middleware/auth');
const { isKioskUser } = require('../../../middleware/roleCheck');

// Public routes
router.post('/login', authController.login);
router.post('/send-otp', authController.sendOtp);
router.post('/verify-otp', authController.verifyOtp);
router.delete('/logout', auth, isKioskUser, authController.logout);

module.exports = router;
