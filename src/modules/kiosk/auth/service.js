const bcrypt = require('bcryptjs');
const KioskUser = require('../../../models/KioskUser');
const Temple = require('../../../models/Temple');
const Kiosk = require('../../../models/Kiosk');
const Token = require('../../../models/Token');
const { throwBadRequestError, throwNotFoundError } = require('../../../errors');
const { messages } = require('../../../messages');
const { userTypeValue, otpTypeValue, userStatusValue } = require('../../../constants/dbEnums');
const { generateToken } = require('../../../utils/jwt');
const { createToken } = require('../../../database/queries/accessToken.query');
const { createOtp, findOtp, findAndUpdateOtp, deleteOtps, deleteOtp } = require('../../../database/queries/otp.query');
const { sendSMS } = require('../../../utils/sms');
const { transformTranslatedFields } = require('../../../utils/localizer');
const moment = require('moment');

/**
 * Login a kiosk user
 * @param {Object} loginData - Login credentials
 * @returns {Object} User data and token
 */
const login = async (loginData) => {
  // Find user by email
  const user = await KioskUser.findOne({ email: loginData.email.toLowerCase() });
  
  if (!user) {
    throwBadRequestError(messages.INVALID_CREDENTIALS);
  }

  // Check if user is active
  if (user.status !== 'ACTIVE') {
    throwBadRequestError(messages.USER_INACTIVE);
  }

  // Verify password
  const isPasswordValid = await bcrypt.compare(loginData.password, user.password);
  
  if (!isPasswordValid) {
    throwBadRequestError(messages.INVALID_CREDENTIALS);
  }

  // Get kiosk and temple details
  const kiosk = await Kiosk.findById(user.kiosk);

  if (!kiosk) {
    throwNotFoundError('Associated kiosk not found');
  }

  const temple = await Temple.findById(kiosk.temple);

  if (!temple) {
    throwNotFoundError(messages.TEMPLE_NOT_FOUND);
  }

  // Generate token
  const token = await generateToken({ 
    _id: user._id,
    email: user.email,
    name: user.name,
    userType: userTypeValue.KIOSK
  });

  // Save token
  const expireTime = process.env.JWT_EXPIRY;
  const days = expireTime.toLowerCase().replace('d', '');

  await createToken({
    token: token,
    userType: userTypeValue.KIOSK,
    userId: user._id,
    expiresAt: new Date().getTime() + (parseInt(days) * 24 * 60 * 60 * 1000),
  });

  return {
    user: {
      id: user._id,
      name: user.name,
      email: user.email,
      phoneNumber: user.phoneNumber,
      countryCode: user.countryCode,
      passwordChangeRequired: user.passwordChangeRequired,
      kiosk: {
        id: kiosk._id,
        location: kiosk.location,
        temple: {
          id: temple._id,
          name: temple.name.en || temple.name,
          city: temple.city.en || temple.city,
          state: temple.state.en || temple.state
        }
      }
    },
    token
  };
};

/**
 * Function to send OTP to phone number
 * @param {*}
 * @returns empty array on success
 */
const sendOtp = async ({ phoneNumber, countryCode, preferredLanguage }) => {

  //* Check if account exist and it is deactivated 
  const userExists = await KioskUser.findOne({
    phoneNumber,
    countryCode
  });

  //* If account not exist 
  if (!userExists) {
    throwBadRequestError('Account not registered');
  }

  //* If account is deleted 
  if (userExists?.deletedAt) {
    throwBadRequestError('Your account is deactivated. Please contact support.');
  }

  //* If account's status is in-active, blocked or pending 
  if (userExists?.status === userStatusValue.INACTIVE || userExists?.status === userStatusValue.BLOCKED || userExists?.status === userStatusValue.PENDING) {
    throwBadRequestError(`Your account is currently ${userExists?.status.toLowerCase()}. Please contact support for assistance.`);
  }

  //* Remove these 3 lines when not needed
  const completeNumber = `${countryCode}${phoneNumber}`;
  const dummyPhoneNumbers = process.env.DUMMY_PHONE_NUMBERS?.split(',').map(number => number.trim()) || [];
  const isDummy = dummyPhoneNumbers.includes(completeNumber);

  const otpCode = isDummy ? '797979' : generateOTP();

  let recipients = [];
  let template = '';
 
  if (!isDummy && (process.env.SEND_MESSAGE === 'true' || process.env.SEND_MESSAGE === true)) {
    const countryCodeValue = countryCode.replace('+', '') ?? '91';

    recipients = [
      {
        mobiles: `${countryCodeValue}${phoneNumber}`,
        var1: otpCode,
        var2: 5
      }
    ];

    template = preferredLanguage === 'Hindi' ? process.env.HINDI_SMS_TEMPLATE_ID : process.env.ENGLISH_SMS_TEMPLATE_ID;
  }

  await Promise.all([
    !isDummy && (process.env.SEND_MESSAGE === 'true' || process.env.SEND_MESSAGE === true) ? sendSMS(recipients, template) : '',
    deleteOtps({
      phoneNumber,
      countryCode,
      type: otpTypeValue.PHONE_NUMBER,
      userType: userTypeValue.KIOSK,
      deletedAt: null,
      isVerified: false,
    })
  ]);

  await createOtp({
    phoneNumber,
    countryCode,
    type: otpTypeValue.PHONE_NUMBER,
    otp: otpCode,
    userType: userTypeValue.KIOSK,
    expiresAt: moment().add(5, 'minutes')
  });

  return [];
};

const generateOTP = () => Math.floor(100000 + Math.random() * 900000);

/**
 * Function to verify User OTP
 * @param {*} userObj
 * @returns Object containing message and phone number on success
 */
const verifyOtp = async (userObj) => {
  const completeNumber = `${userObj.countryCode}${userObj.phoneNumber}`;
  const dummyPhoneNumbers = process.env.DUMMY_PHONE_NUMBERS?.split(',').map(number => number.trim()) || [];
  const isDummy = dummyPhoneNumbers.includes(completeNumber);

  const otp = await findOtp({
    phoneNumber: userObj.phoneNumber,
    countryCode: userObj.countryCode,
    type: otpTypeValue.PHONE_NUMBER,
    userType: userTypeValue.KIOSK,
    deletedAt: null,
    isVerified: false,
  });

  const userExists = await KioskUser.findOne({
    phoneNumber: userObj.phoneNumber,
    countryCode: userObj.countryCode,
    deletedAt: null,
  });

  if (!otp) {
    throwBadRequestError(messages.INVALID_OTP);
  }

  if (new Date(otp.expiresAt) < new Date()) {
    await deleteOtp({ _id: otp._id });
    throwBadRequestError(messages.OTP_EXPIRED);
  }

  const receivedOtp = String(userObj.otp).trim();
  const storedOtp = String(otp.otp).trim();

  if (isDummy) {
    if (receivedOtp !== '797979') {
      throwBadRequestError(messages.INVALID_OTP);
    }
  } else {
    if (process.env.APP_ENV && process.env.APP_ENV === 'development') {
      if (receivedOtp !== '797979' && receivedOtp !== storedOtp) {
        throwBadRequestError(messages.INVALID_OTP);
      }
    } else {
      if (receivedOtp !== storedOtp) {
        throwBadRequestError(messages.INVALID_OTP);
      }
    }
  }

  await findAndUpdateOtp({ _id: otp._id }, { isVerified: true });

  const response = {
    message: messages.PHONE_NUMBER_VERIFIED,
    phone: userObj.phoneNumber,
    countryCode: userObj.countryCode,
    accountExists: false,
  };

  if (userExists) {
    const token = await generateToken({
      _id: userExists._id,
      email: userExists.email,
      name: userExists.name,
      userType: userTypeValue.KIOSK
    });

    const expireTime = process.env.JWT_EXPIRY;
    const days = expireTime.toLowerCase().replace('d', '');

    await createToken({
      token: token,
      userType: userTypeValue.KIOSK,
      userId: userExists._id,
      fcmToken: userObj?.fcmToken,
      expiresAt: new Date().getTime() + (parseInt(days) * 24 * 60 * 60 * 1000),
    });

    const language = { code: 'en' };

    // if (userExists?.preferredLanguage) {
    //   const userLanguage = await Language.findOne({ name: userExists.preferredLanguage });

    //   if (userLanguage) {
    //     language = userLanguage;
    //   }
    // }

    response.accountExists = await transformTranslatedFields(userExists, language.code);

    // Get kiosk and temple details
    const kiosk = await Kiosk.findById(userExists.kiosk);

    if (!kiosk) {
      throwNotFoundError('Associated kiosk not found');
    }

    const temple = await Temple.findById(kiosk.temple);

    if (!temple) {
      throwNotFoundError(messages.TEMPLE_NOT_FOUND);
    }

    response.token = token;

    return {
      accountExists: true,
      user: {
        id: userExists._id,
        name: userExists.name,
        email: userExists.email,
        phoneNumber: userExists.phoneNumber,
        countryCode: userExists.countryCode,
        passwordChangeRequired: userExists.passwordChangeRequired,
        kiosk: {
          id: kiosk._id,
          location: kiosk.location,
          temple: {
            id: temple._id,
            name: temple.name.en || temple.name,
            city: temple.city.en || temple.city,
            state: temple.state.en || temple.state
          }
        }
      },
      token
    };
  }

  return response;
};

/**
 * Change password for a kiosk user
 * @param {string} userId - User ID
 * @param {Object} passwordData - Password data
 * @returns {Object} Success message
 */
const changePassword = async (userId, passwordData) => {
  // Find user
  const user = await KioskUser.findById(userId);
  
  if (!user) {
    throwNotFoundError('User not found');
  }

  // Verify current password
  const isPasswordValid = await bcrypt.compare(passwordData.currentPassword, user.password);
  
  if (!isPasswordValid) {
    throwBadRequestError('Current password is incorrect');
  }

  // Hash new password
  const hashedPassword = await bcrypt.hash(passwordData.newPassword, 10);

  // Update password
  user.password = hashedPassword;
  user.passwordChangeRequired = false;
  user.updatedBy = userId;
  
  await user.save();

  return { success: true, message: 'Password changed successfully' };
};

/**
 * Logout a kiosk user
 * @param {string} userId - User ID
 * @param {string} token - JWT token
 * @returns {Object} Success message
 */
const logout = async (userId, token) => {
  await Token.deleteOne({ 
    userId,
    token,
    userType: userTypeValue.KIOSK
  });

  return { success: true, message: 'Logged out successfully' };
};

module.exports = {
  login,
  changePassword,
  sendOtp,
  verifyOtp,
  logout
};
