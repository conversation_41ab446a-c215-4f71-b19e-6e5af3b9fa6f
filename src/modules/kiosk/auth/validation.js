const joi = require('joi');
const { parsePhoneNumberFromString } = require('libphonenumber-js');

const loginSchema = joi.object({
  email: joi.string().email().lowercase().required().trim()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
  password: joi.string().required()
    .messages({
      'string.empty': 'Password is required',
      'any.required': 'Password is required'
    })
});

const sendOtpSchema = joi.object({
  countryCode: joi.string()
    .trim()
    .pattern(/^\+\d{1,3}$/)
    .required()
    .messages({
      'string.empty': 'Country code is required',
      'string.pattern.base': 'Country code must start with + followed by 1 to 3 digits',
    }),

  phoneNumber: joi.string()
    .required()
    .custom((value, helpers) => {
      const { countryCode } = helpers.state.ancestors[0];

      const fullNumber = `${countryCode}${value}`;
      const parsed = parsePhoneNumberFromString(fullNumber);

      if (!parsed || !parsed.isValid()) {
        return helpers.message('Invalid phone number for the selected country');
      }

      return value; // Or return `parsed.number` to replace with E.164 format
    })
    .messages({
      'string.empty': 'Phone number is required',
    }),

  preferredLanguage: joi.string()
    .valid('English', 'Hindi', '', null)
    .optional()
    .messages({
      'any.only': 'Preferred language must be either English or Hindi',
    }),
});

const verifyOtpSchema = joi.object({
  countryCode: joi.string()
    .trim()
    .pattern(/^\+\d{1,3}$/)
    .required()
    .messages({
      'string.empty': 'Country code is required',
      'string.pattern.base': 'Country code must start with + followed by 1 to 3 digits',
    }),

  phoneNumber: joi.string()
    .required()
    .custom((value, helpers) => {
      const { countryCode } = helpers.state.ancestors[0];

      const fullNumber = `${countryCode}${value}`;
      const parsed = parsePhoneNumberFromString(fullNumber);

      if (!parsed || !parsed.isValid()) {
        return helpers.message('Invalid phone number for the selected country');
      }

      return value; // or `parsed.number` if you want to store in E.164 format
    })
    .messages({
      'string.empty': 'Phone number is required',
    }),

  otp: joi.string()
    .length(6)
    .pattern(/^[0-9]+$/)
    .required()
    .messages({
      'string.pattern.base': 'OTP must contain only numbers',
      'string.length': 'OTP must be exactly 6 digits',
      'any.required': 'OTP is required',
    }),

  fcmToken: joi.string().optional().allow('', null),
});

module.exports = {
  loginSchema,
  sendOtpSchema,
  verifyOtpSchema
};
