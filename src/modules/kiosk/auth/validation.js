const joi = require('joi');

const loginSchema = joi.object({
  email: joi.string().email().lowercase().required().trim()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
  password: joi.string().required()
    .messages({
      'string.empty': 'Password is required',
      'any.required': 'Password is required'
    })
});

module.exports = {
  loginSchema
};
