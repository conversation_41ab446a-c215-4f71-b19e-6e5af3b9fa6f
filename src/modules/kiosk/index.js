const express = require('express');
const router = express.Router();
const kioskController = require('./controller');
const auth = require('../../middleware/auth');
const { isAdminOrSuperAdmin, isSuperAdmin, isKioskUser } = require('../../middleware/roleCheck');

// Sub-Modules
router.use('/auth', require('./auth'));
router.use('/users', require('./kioskUser'));
router.use('/attendance', require('./attendance'));
router.use('/verification', require('./verification'));
router.use('/schedule', require('./schedule'));
router.use('/inventory', require('./inventory'));
router.use('/pooja-recording', require('./poojaRecording'));

// Super Admin routes
router.post('/', auth, isSuperAdmin, kioskController.createKiosk);
router.get('/', auth, isAdminOrSuperAdmin, kioskController.listKiosks);
router.get('/:id', auth, kioskController.getKioskById);
router.put('/:id', auth, isSuperAdmin, kioskController.updateKiosk);
router.delete('/:id', auth, isSuperAdmin, kioskController.deleteKiosk);

// Kiosk Virtual Pooja Video routes
router.post('/upload-url',auth ,isKioskUser, kioskController.getUploadUrl);
router.post('/delete-pooja-video', auth, isKioskUser, kioskController.deletePoojaVideo);

router.patch('/update-inventory', auth, isKioskUser, kioskController.updateKioskInventory);
router.get('/below-threshold/inventory', auth, isSuperAdmin, kioskController.getKioskWithBelowThresholdInventory);

module.exports = router;
