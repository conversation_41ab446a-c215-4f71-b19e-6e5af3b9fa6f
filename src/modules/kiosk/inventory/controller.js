const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { SUCCESS } = require('../../../constants/common').commonConstants;
const { sendInventorySchema, receiveInventorySchema, listInventoriesSchema, validateId } = require('./validation');
const inventoryService = require('./service');
const { messages } = require('../../../messages');
const { saveAuditLog } = require('../../../utils/auditLogger');
const { auditLogAction } = require('../../../constants/dbEnums');

//* 1. Function to send inventory
const sendInventory = async (req, res) => {
  try {
    const { error } = sendInventorySchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const inventory = await inventoryService.sendInventory(req.body, req.user.id);

    //* Save audit log 
    const detail = 'Inventory sent successfully';
    const model = 'Inventory';

    await saveAuditLog(req, req.user.id, auditLogAction.INVENTORY_SENT, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.INVENTORY_SENT,
      data: inventory,
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 2. Function to receive inventory
const receiveInventory = async (req, res) => {
  try {
    const { error } = receiveInventorySchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const inventory = await inventoryService.receiveInventory(req.body, req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.INVENTORY_RECEIVED,
      data: inventory,
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 3. Function to list inventories
const listInventories = async (req, res) => {
  try {
    const { error } = listInventoriesSchema.validate(req.query);    
    
    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const inventories = await inventoryService.listInventories(req.query);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.INVENTORIES_RETRIEVED,
      data: inventories,
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 4. Function to get inventory by id
const getInventoryById = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);
    
    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const inventory = await inventoryService.getInventoryById(req.params.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.INVENTORY_RETRIEVED,
      data: inventory,
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  sendInventory,
  receiveInventory,
  listInventories,
  getInventoryById
};
