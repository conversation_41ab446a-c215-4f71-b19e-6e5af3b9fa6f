const express = require('express');
const router = express.Router();
const inventoryController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin, isKioskUser } = require('../../../middleware/roleCheck');

router.post('/send', auth, isAdminOrSuperAdmin, inventoryController.sendInventory);
router.post('/receive', auth, isKioskUser, inventoryController.receiveInventory);
router.get('/active/:kioskId', auth, isKioskUser, inventoryController.checkPendingInventory);
router.get('/', auth, inventoryController.listInventories);
router.get('/:id', auth, inventoryController.getInventoryById);

module.exports = router;