const Inventory = require('../../../models/Inventory');
const Temple = require('../../../models/Temple');
const Kiosk = require('../../../models/Kiosk');
const KioskUser = require('../../../models/KioskUser');
const { throwBadRequestError, throwNotFoundError } = require('../../../errors');
const { transformTranslatedFields } = require('../../../utils/localizer');

//* 1. Function to send inventory
const sendInventory = async (body, adminId) => {
  // Verify temple exists
  const temple = await Temple.findById(body.temple);

  if (!temple) {
    throwNotFoundError('Temple not found');
  }

  // Verify kiosk exists and is active
  const kiosk = await Kiosk.findById(body.kiosk);

  if (!kiosk) {
    throwNotFoundError('Kiosk not found');
  }

  if (kiosk.status !== 'ACTIVE') {
    throwBadRequestError('Kiosk is not active');
  }

  // Verify kiosk belongs to the temple
  if (kiosk.temple.toString() !== temple.id) {
    throwBadRequestError('Kiosk does not belong to the specified temple');
  }

  // Create new inventory record
  const inventory = await Inventory.create({
    temple: temple,
    kiosk: kiosk,
    admin: adminId,
    sentQuantity: body.quantity,
    sentAt: new Date()
  });

  return inventory;
};

//* 2. Function to receive inventory
const receiveInventory = async (body, kioskUserId) => {
  // Find the inventory record 
  const inventory = await Inventory.findById(body.inventory);

  if (!inventory) {
    throwNotFoundError('Inventory not found');
  }

  // Verify inventory status is SENT
  if (inventory.receivedQuantity > 0) {
    throwBadRequestError('Inventory has already been received');
  }

  // Get kiosk user details
  const kioskUser = await KioskUser.findById(kioskUserId);

  if (!kioskUser) {
    throwNotFoundError('Kiosk user not found');
  }

  // Verify kiosk user belongs to the kiosk
  if (kioskUser.kiosk.toString() !== inventory.kiosk.toString()) {
    throwBadRequestError('You are not authorized to receive inventory for this kiosk');
  }

  // Verify received quantity is not more than sent quantity
  if (body.quantity > inventory.sentQuantity) {
    throwBadRequestError(`Received quantity must be less than or equal to ${inventory.sentQuantity}`);
  }

  // Update inventory record
  inventory.receivedQuantity = body.quantity;
  inventory.receivedBy = kioskUserId;
  inventory.receivedAt = new Date();
  
  await inventory.save();

  // Update kiosk's remaining inventory count
  await Kiosk.findByIdAndUpdate(
    inventory.kiosk,
    { $inc: { inventoryRemaining: body.quantity } }
  );

  return inventory;
};

//* 3. Function to list inventories
const listInventories = async (queryParams) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, temple, kiosk, status, startDate, endDate } = queryParams;

  // Build filter object
  const filter = {};

  if (temple) {
    filter.temple = temple;
  }

  if (kiosk) {
    filter.kiosk = kiosk;
  }

  if (status) {
    filter.status = status;
  }

  // Add date range filter if provided
  if (startDate || endDate) {
    filter.createdAt = {};
    if (startDate) {
      filter.createdAt.$gte = new Date(startDate);
    }
    if (endDate) {
      filter.createdAt.$lte = new Date(endDate);
    }
  }

  // Create sort object
  const sort = {
    [sortBy]: parseInt(sortOrder)
  };

  // Get total count
  const total = await Inventory.countDocuments(filter);

  // Get paginated and sorted data
  let inventories = await Inventory.find(filter)
    .sort(sort).collation({ locale: 'en', strength: 1 })
    .skip((parseInt(page) - 1) * parseInt(limit))
    .limit(parseInt(limit))
    .populate({ path: 'temple' })
    .populate({ path: 'kiosk' }).lean();

  inventories = await transformTranslatedFields(inventories, 'en');

  return {
    inventories,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

//* 4. Function to get inventory by id
const getInventoryById = async (inventoryId) => {
  let inventory = await Inventory.findById(inventoryId)
    .populate({ path: 'temple' })
    .populate({ path: 'kiosk' }).lean();

  if (!inventory) {
    throwNotFoundError('Inventory not found');
  }

  inventory = await transformTranslatedFields(inventory, 'en');

  return inventory;
};

module.exports = {
  sendInventory,
  receiveInventory,
  listInventories,
  getInventoryById
};
