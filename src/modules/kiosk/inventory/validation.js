const joi = require('joi');
const mongoose = require('mongoose');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string()
  .custom(validateObjectId)
  .required()
  .messages({
    'any.invalid': 'Invalid inventory ID format',
    'any.required': 'Inventory ID is required'
  });

const sendInventorySchema = joi.object({
  temple: joi.string()
    .custom(validateObjectId)
    .required()
    .messages({
      'any.invalid': 'Invalid temple ID format',
      'any.required': 'Temple ID is required'
    }),
  kiosk: joi.string()
    .custom(validateObjectId)
    .required()
    .messages({
      'any.invalid': 'Invalid kiosk ID format',
      'any.required': 'Kiosk ID is required'
    }),
  quantity: joi.number()
    .integer()
    .min(1)
    .required()
    .messages({
      'number.base': 'Quantity must be a number',
      'number.integer': 'Quantity must be an integer',
      'number.min': 'Quantity must be at least 1',
      'any.required': 'Quantity is required'
    })
});

const receiveInventorySchema = joi.object({
  inventory: joi.string()
    .custom(validateObjectId)
    .required()
    .messages({
      'any.invalid': 'Invalid inventory ID format',
      'any.required': 'Inventory ID is required'
    }),
  quantity: joi.number()
    .integer()
    .min(1)
    .required()
    .messages({
      'number.base': 'Quantity must be a number',
      'number.integer': 'Quantity must be an integer',
      'number.min': 'Quantity must be at least 1',
      'any.required': 'Quantity is required'
    })
});

const listInventoriesSchema = joi.object({
  page: joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page number must be greater than or equal to 1'
    }),
  limit: joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be greater than or equal to 1',
      'number.max': 'Limit cannot exceed 100 records per page'
    }),
  sortBy: joi.string()
    .valid('createdAt', 'sentAt', 'receivedAt', 'sentQuantity', 'receivedQuantity')
    .default('createdAt')
    .messages({
      'string.base': 'Sort field must be a string',
      'any.only': 'Sort field must be one of: createdAt, sentAt, receivedAt, sentQuantity, or receivedQuantity'
    }),
  sortOrder: joi.number()
    .valid(1, -1)
    .default(-1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    }),
  temple: joi.string()
    .custom(validateObjectId)
    .messages({
      'string.base': 'Temple ID must be a string',
      'any.invalid': 'Invalid temple ID format'
    }),
  kiosk: joi.string()
    .custom(validateObjectId)
    .messages({
      'string.base': 'Kiosk ID must be a string',
      'any.invalid': 'Invalid kiosk ID format'
    }),
  startDate: joi.date()
    .iso()
    .allow(null)
    .messages({
      'date.base': 'Start date must be a valid date',
      'date.format': 'Start date must be in ISO format (YYYY-MM-DD)'
    }),
  endDate: joi.date()
    .iso()
    .allow(null)
    .when('startDate', {
      is: joi.date().required(),
      then: joi.date().min(joi.ref('startDate')),
      otherwise: joi.optional()
    })
    .messages({
      'date.base': 'End date must be a valid date',
      'date.format': 'End date must be in ISO format (YYYY-MM-DD)',
      'date.min': 'End date must be greater than or equal to start date',
      'any.required': 'End date is required when start date is provided'
    })
});

module.exports = {
  validateId,
  sendInventorySchema,
  receiveInventorySchema,
  listInventoriesSchema
};

