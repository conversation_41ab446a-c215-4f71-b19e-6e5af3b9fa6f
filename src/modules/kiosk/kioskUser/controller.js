const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const kioskUserService = require('./service');
const { createKioskUserSchema, updateKioskUserSchema, validateId, listKioskUsersSchema } = require('./validation');
const { SUCCESS } = commonConstants;
const { saveAuditLog } = require('../../../utils/auditLogger');
const { auditLogAction } = require('../../../constants/dbEnums');

/**
 * Create a new kiosk user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const createKioskUser = async (req, res) => {
  try {
    const { error, value } = createKioskUserSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await kioskUserService.createKioskUser(value, req.user);

    //* Save audit log 
    const detail = `Kiosk user ${result.name} created successfully`;
    const model = 'KioskUser';

    await saveAuditLog(req, req.user.id, auditLogAction.KIOSK_USER_CREATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Kiosk user created successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get a kiosk user by ID
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getKioskUserById = async (req, res) => {
  try {
    const { error } = validateId.validate({ id: req.params.id });

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await kioskUserService.getKioskUserById(req.params.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Kiosk user retrieved successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update a kiosk user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const updateKioskUser = async (req, res) => {
  try {
    const idValidation = validateId.validate({ id: req.params.id });

    if (idValidation.error) {
      return res.status(400).json({
        status: false,
        message: idValidation.error.details[0].message
      });
    }

    const { error, value } = updateKioskUserSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await kioskUserService.updateKioskUser(req.params.id, value, req.user);

    //* Save audit log 
    const detail = `Kiosk user ${result.name} updated successfully`;
    const model = 'KioskUser';

    await saveAuditLog(req, req.user.id, auditLogAction.KIOSK_USER_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Kiosk user updated successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete a kiosk user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const deleteKioskUser = async (req, res) => {
  try {
    const { error } = validateId.validate({ id: req.params.id });

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await kioskUserService.deleteKioskUser(req.params.id);

    //* Save audit log 
    const detail = `Kiosk user ${result.name} deleted successfully`;
    const model = 'KioskUser';

    await saveAuditLog(req, req.user.id, auditLogAction.KIOSK_USER_DELETED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Kiosk user deleted successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * List kiosk users with pagination and filtering
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const listKioskUsers = async (req, res) => {
  try {
    const { error, value } = listKioskUsersSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await kioskUserService.listKioskUsers(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Kiosk users retrieved successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createKioskUser,
  getKioskUserById,
  updateKioskUser,
  deleteKioskUser,
  listKioskUsers
};
