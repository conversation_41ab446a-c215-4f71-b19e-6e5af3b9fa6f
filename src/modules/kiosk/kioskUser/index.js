const express = require('express');
const router = express.Router();
const kioskUserController = require('./controller');
const auth = require('../../../middleware/auth');
const { isAdminOrSuperAdmin, isSuperAdmin } = require('../../../middleware/roleCheck');

// Super Admin routes
router.post('/', auth, isSuperAdmin, kioskUserController.createKioskUser);
router.get('/', auth, isAdminOrSuperAdmin, kioskUserController.listKioskUsers);
router.get('/:id', auth, kioskUserController.getKioskUserById);
router.put('/:id', auth, isSuperAdmin, kioskUserController.updateKioskUser);
router.delete('/:id', auth, isSuperAdmin, kioskUserController.deleteKioskUser);

module.exports = router;
