const joi = require('joi');
const mongoose = require('mongoose');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

// Schema for creating a new kiosk user
const createKioskUserSchema = joi.object({
  name: joi.string().required().trim()
    .messages({
      'string.empty': 'Name is required',
      'any.required': 'Name is required'
    }),
  email: joi.string().email().lowercase().required().trim()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
  phoneNumber: joi.string().pattern(/^\+[1-9]\d{1,14}$/),
  countryCode: joi.string().pattern(/^\+\d{1,3}$/).default('+91').trim()
    .messages({
      'string.pattern.base': 'Please provide a valid country code (e.g., +91)'
    }),
  kiosk: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Kiosk ID is required',
      'any.required': 'Kiosk ID is required'
    }),
  status: joi.string().valid('ACTIVE', 'INACTIVE').default('ACTIVE')
});

// Schema for updating a kiosk user
const updateKioskUserSchema = joi.object({
  name: joi.string().trim()
    .messages({
      'string.empty': 'Name is required'
    }),
  email: joi.string().email().lowercase().trim()
    .messages({
      'string.email': 'Please provide a valid email address'
    }),
  phoneNumber: joi.string().pattern(/^\+[1-9]\d{1,14}$/),
  countryCode: joi.string().pattern(/^\+\d{1,3}$/).trim()
    .messages({
      'string.pattern.base': 'Please provide a valid country code (e.g., +91)'
    }),
  kiosk: joi.string().custom(validateObjectId)
    .messages({
      'string.empty': 'Kiosk ID is required'
    }),
  status: joi.string().valid('ACTIVE', 'INACTIVE'),
  resetPassword: joi.boolean().default(false)
}).min(1);

// Schema for validating ID parameter
const validateId = joi.object({
  id: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'ID is required',
      'any.required': 'ID is required'
    })
});

// Schema for listing kiosk users with pagination
const listKioskUsersSchema = joi.object({
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(10),
  kiosk: joi.string().custom(validateObjectId).allow(''),
  temple: joi.string().custom(validateObjectId).allow(''),
  status: joi.string().valid('ACTIVE', 'INACTIVE').allow(''),
  search: joi.string().allow('')
});

module.exports = {
  createKioskUserSchema,
  updateKioskUserSchema,
  validateId,
  listKioskUsersSchema
};
