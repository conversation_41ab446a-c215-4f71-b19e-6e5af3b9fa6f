const joi = require('joi');
const mongoose = require('mongoose');
const { parsePhoneNumberFromString } = require('libphonenumber-js');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

// Schema for creating a new kiosk user
const createKioskUserSchema = joi.object({
  name: joi.string().required().trim()
    .messages({
      'string.empty': 'Name is required',
      'any.required': 'Name is required'
    }),
  email: joi.string().email().lowercase().required().trim()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
  countryCode: joi.string()
    .trim()
    .pattern(/^\+\d{1,3}$/)
    .required()
    .messages({
      'string.empty': 'Country code is required',
      'string.pattern.base': 'Country code must start with + followed by 1 to 3 digits',
    }),
  phoneNumber: joi.string()
    .required()
    .custom((value, helpers) => {
      const { countryCode } = helpers.state.ancestors[0];
  
      const fullNumber = `${countryCode}${value}`;
      const parsed = parsePhoneNumberFromString(fullNumber);
  
      if (!parsed || !parsed.isValid()) {
        return helpers.message('Invalid phone number for the selected country');
      }
  
      return value; // Or return `parsed.number` to replace with E.164 format
    })
    .messages({
      'string.empty': 'Phone number is required',
    }),
  kiosk: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Kiosk ID is required',
      'any.required': 'Kiosk ID is required'
    }),
  status: joi.string().valid('ACTIVE', 'INACTIVE').default('ACTIVE')
});

// Schema for updating a kiosk user
const updateKioskUserSchema = joi.object({
  name: joi.string().trim()
    .messages({
      'string.empty': 'Name is required'
    }),
  email: joi.string().email().lowercase().trim()
    .messages({
      'string.email': 'Please provide a valid email address'
    }),
  countryCode: joi.string()
    .trim()
    .pattern(/^\+\d{1,3}$/)
    .messages({
      'string.pattern.base': 'Country code must start with + followed by 1 to 3 digits',
      'string.empty': 'Country code is required',
    }),
  phoneNumber: joi.string()
    .custom((value, helpers) => {
      const { countryCode } = helpers.state.ancestors[0];

      if (!countryCode) {
        return helpers.message('Country code is required when phone number is provided');
      }

      const fullNumber = `${countryCode}${value}`;
      const parsed = parsePhoneNumberFromString(fullNumber);

      if (!parsed || !parsed.isValid()) {
        return helpers.message('Invalid phone number for the selected country');
      }

      return value;
    })
    .messages({
      'string.empty': 'Phone number is required',
    }),
  kiosk: joi.string().custom(validateObjectId)
    .messages({
      'string.empty': 'Kiosk ID is required'
    }),
  status: joi.string().valid('ACTIVE', 'INACTIVE'),
  resetPassword: joi.boolean().default(false)
}).with('phoneNumber', 'countryCode').with('countryCode', 'phoneNumber').min(1);

// Schema for validating ID parameter
const validateId = joi.object({
  id: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'ID is required',
      'any.required': 'ID is required'
    })
});

// Schema for listing kiosk users with pagination
const listKioskUsersSchema = joi.object({
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(10),
  kiosk: joi.string().custom(validateObjectId).allow(''),
  temple: joi.string().custom(validateObjectId).allow(''),
  status: joi.string().valid('ACTIVE', 'INACTIVE').allow(''),
  search: joi.string().allow('')
});

module.exports = {
  createKioskUserSchema,
  updateKioskUserSchema,
  validateId,
  listKioskUsersSchema
};
