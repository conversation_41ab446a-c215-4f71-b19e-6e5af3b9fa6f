const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const poojaRecordingService = require('./service');
const { 
  createRecordingSchema, 
  updateRecordingUrlSchema,
  updateRecordingStatusSchema, 
  getByIdSchema
} = require('./validation');
const { saveAuditLog } = require('../../../utils/auditLogger');
const { auditLogAction } = require('../../../constants/dbEnums');

const createRecording = async (req, res) => {
  try {
    const { error } = createRecordingSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const recording = await poojaRecordingService.createRecording(
      req.body,
      req.user.id,
    );

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data: recording
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateRecordingUrl = async (req, res) => {
  try {
    const { error } = updateRecordingUrlSchema.validate({ ...req.params ,...req.body });

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const recording = await poojaRecordingService.updateRecordingUrl(
      req.params,
      req.user,
      req.body
    );

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data: recording
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const deleteRecording = async (req, res) => {
  try {
    const { error } = getByIdSchema.validate(req.params);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    await poojaRecordingService.deleteRecording(req.params.id, req.user.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateRecordingStatus = async (req, res) => {
  try {
    const { error } = updateRecordingStatusSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const recording = await poojaRecordingService.updateRecordingStatus(
      req.params.id,
      req.body
    );

    //* Save audit log 
    const detail = `Recording ${recording._id || recording.id} status updated to ${recording.status}`;
    const model = 'PoojaRecording';

    await saveAuditLog(req, req.user.id, auditLogAction.RECORDING_STATUS_UPDATED, detail, model);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data: recording
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getRecordingById = async (req, res) => {
  try {

    const { error } = getByIdSchema.validate(req.params);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const recording = await poojaRecordingService.getRecordingById(req.params.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data: recording
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const listRecordings = async (req, res) => {
  try {
    const recordings = await poojaRecordingService.listRecordings(req.query);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data: recordings
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createRecording,
  updateRecordingUrl,
  deleteRecording,
  updateRecordingStatus,
  getRecordingById,
  listRecordings
};