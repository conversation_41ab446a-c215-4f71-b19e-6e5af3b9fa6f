const express = require('express');
const router = express.Router();
const poojaRecordingController = require('./controller');
const auth = require('../../../middleware/auth');
const { isKioskUser, isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

// Kiosk user routes
router.post('/', auth, isKioskUser, poojaRecordingController.createRecording);
router.patch('/:id', auth, isKioskUser, poojaRecordingController.updateRecordingUrl);
router.delete('/:id', auth, isKioskUser, poojaRecordingController.deleteRecording);

// Admin/SuperAdmin routes
router.patch('/:id/status', auth, isAdminOrSuperAdmin, poojaRecordingController.updateRecordingStatus);

// Common routes
router.get('/:id', auth, poojaRecordingController.getRecordingById);

module.exports = router;