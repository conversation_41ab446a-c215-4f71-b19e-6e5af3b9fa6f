const PoojaRecording = require('../../../models/PoojaRecording');
const Booking = require('../../../models/Booking');
const { throwBadRequestError, throwNotFoundError } = require('../../../errors');
const { messages } = require('../../../messages');
const KioskUser = require('../../../models/KioskUser');
const { deleteFile } = require('../../../utils/s3Service');
const mongoose = require('mongoose');
const moment = require('moment');
const { transformTranslatedFields } = require('../../../utils/localizer');
const Notification = require('../../../models/Notification');
const Admin = require('../../../models/Admin');
const { userTypeValue, notificationType } = require('../../../constants/dbEnums');

const createRecording = async (data, kioskUserId) => {
  const booking = await Booking.findById(data.booking);

  if (!booking) {
    throwBadRequestError(messages.BOOKING_NOT_FOUND);
  }

  const kioskUser = await KioskUser.findById(kioskUserId).populate('kiosk');

  if (kioskUser && kioskUser?.kiosk?.temple?.toString() !== booking?.temple?.toString()) {
    throwBadRequestError(messages.BOOKING_NOT_FOUND);
  }

  const existingRecording = await PoojaRecording.findOne({ booking: data.booking, status: { $ne: 'REJECTED' } });

  if (existingRecording) {
    throwBadRequestError(messages.ALREADY_EXISTS);
  }

  data.recordingKey = data.recordingUrl;
  data.recordingUrl = process.env.MEDIA_URL + '/' + data.recordingUrl;

  const recording = await PoojaRecording.create({
    ...data,
    kioskUser: kioskUserId,
    kiosk: kioskUser.kiosk,
    temple: kioskUser.kiosk.temple
  });

  //* Send notification to admin
  const admin = await Admin.findOne({ userType: userTypeValue.SUPER_ADMIN }).select('name userType');

  await Notification.create({
    userId: admin._id,
    title: 'Puja Recording Created',
    body: 'A new puja recording has been created. Please review and approve.',
    type: userTypeValue.SUPER_ADMIN,
    redirectPath: '/admin/video-approvals',
    metaData: {
      recordingId: recording._id
    },
    notificationType: notificationType.PUJA_RECORDING_APPROVAL
  });

  return recording;
};

const updateRecordingUrl = async (params, user, body) => {
  const recording = await PoojaRecording.findOne({ 
    _id: params.id,
    kioskUser: user.id
  });

  if (!recording) {
    throwBadRequestError(messages.RECORDING_NOT_FOUND);
  }

  await deleteFile(recording.recordingKey);

  return await PoojaRecording.findByIdAndUpdate(
    params.id,
    { 
      recordingKey: body.recordingUrl,
      recordingUrl: process.env.MEDIA_URL + '/' + body.recordingUrl,
      status: 'PENDING'
    },
    { new: true }
  );
};

const deleteRecording = async (id, kioskUserId) => {
  const recording = await PoojaRecording.findOne({ 
    _id: id,
    kioskUser: kioskUserId
  });

  if (!recording) {
    throwBadRequestError(messages.RECORDING_NOT_FOUND);
  }
  
  await recording.deleteOne();
  
  // delete from s3
  await deleteFile(recording.recordingKey);
};

const updateRecordingStatus = async (id, statusData) => {
  const recording = await PoojaRecording.findById(id);

  if (!recording) {
    throwBadRequestError(messages.RECORDING_NOT_FOUND);
  }

  recording.status = statusData.status;
  recording.rejectionReason = statusData.rejectionReason;

  if (statusData?.updatedUrl) {
    recording.updatedKey = statusData.updatedUrl;
    recording.updatedUrl = process.env.MEDIA_URL + '/' + statusData.updatedUrl;
  }

  if (statusData.updatedUrl === null) {
    recording.updatedKey = null;
    recording.updatedUrl = null;
  }

  recording.reviewedAt = new Date();
  await recording.save();

  return recording.populate([
    { path: 'booking', select: 'bookingNumber' },
    { path: 'kioskUser', select: 'name' }
  ]);
};

const getRecordingById = async (id) => {
  const recording = await PoojaRecording.findById(id).populate([
    { path: 'booking' },
    { path: 'kioskUser', select: '-password' }
  ]).lean();

  if (!recording) {
    throwNotFoundError(messages.RECORDING_NOT_FOUND);
  }

  const language = { code: 'en' };

  recording.booking = await transformTranslatedFields(recording.booking, language.code);

  return recording;
};

const listRecordings = async (query) => {
  const {
    page = 1,
    limit = 10,
    status,
    search,
    temple,
    kiosk,
    startDate,
    endDate,
    specificDate,
    booking,
  } = query;

  const matchStage = {};

  if (status) { // 
    matchStage.status = status;
  }

  if (temple) { // 
    matchStage['temple._id'] = new mongoose.Types.ObjectId(temple);
  }

  if (kiosk) { // 
    matchStage['kiosk._id'] = new mongoose.Types.ObjectId(kiosk);
  }

  if (booking) { // 
    matchStage['booking._id'] = new mongoose.Types.ObjectId(booking);
  }

  if (startDate) { // 
    matchStage['booking.poojaSchedule.dateRange.startDate'] = { $gte: moment(startDate).format('YYYY-MM-DD') + 'T00:00:00.000Z' };
  }

  if (endDate) { // 
    const end = new Date(endDate);

    matchStage['booking.poojaSchedule.dateRange.endDate'] = {
      $lt: moment(end).format('YYYY-MM-DD') + 'T23:59:59.999Z'
    };
  }

  if (specificDate) { // 
    matchStage['booking.poojaSchedule.specificDate'] = {
      $gte: moment(specificDate).format('YYYY-MM-DD') + 'T00:00:00.000Z',
      $lt: moment(specificDate).format('YYYY-MM-DD') + 'T23:59:59.999Z'
    };
  }

  const pipeline = [
    {
      $lookup: {
        from: 'bookings',
        localField: 'booking',
        foreignField: '_id',
        as: 'booking'
      }
    },
    { $unwind: { path: '$booking', preserveNullAndEmptyArrays: true } },
  
    {
      $lookup: {
        from: 'poojaschedules',
        localField: 'booking.poojaSchedule',
        foreignField: '_id',
        as: 'booking.poojaSchedule'
      }
    },
    { $unwind: { path: '$booking.poojaSchedule', preserveNullAndEmptyArrays: true } },

    {
      $lookup: {
        from: 'temples',
        localField: 'temple',
        foreignField: '_id',
        as: 'temple'
      }
    },
    { $unwind: { path: '$temple', preserveNullAndEmptyArrays: true } },

    {
      $lookup: {
        from: 'kiosks',
        localField: 'kiosk',
        foreignField: '_id',
        as: 'kiosk'
      }
    },
    { $unwind: { path: '$kiosk', preserveNullAndEmptyArrays: true } },

    {
      $lookup: {
        from: 'kioskusers',
        localField: 'kioskUser',
        foreignField: '_id',
        as: 'kioskUser'
      }
    },
    { $unwind: { path: '$kioskUser', preserveNullAndEmptyArrays: true } },

    { $match: matchStage }
  ];

  const language = { code: 'en' };

  if (search) {
    pipeline.push({
      $match: {
        $or: [
          { 'booking.bookingNumber': { $regex: search, $options: 'i' } },
          { [`booking.primaryDevoteeDetails.fullName.${language.code}`]: { $regex: search, $options: 'i' } },
          { [`temple.name.${language.code}`]: { $regex: search, $options: 'i' } },
          { 'kiosk.name': { $regex: search, $options: 'i' } },
          { 'kioskUser.name': { $regex: search, $options: 'i' } }
        ]
      }
    });
  }

  const skip = (parseInt(page) - 1) * parseInt(limit);
  const sortStage = { createdAt: 1 };

  pipeline.push({
    $facet: {
      metadata: [{ $count: 'total' }],
      data: [
        { $sort: sortStage },
        { $skip: skip },
        { $limit: parseInt(limit) },
        {
          $project: {
            'kioskUser.password': 0
          }
        }
      ]
    }
  });

  const result = await PoojaRecording.aggregate(pipeline).collation({ locale: 'en', strength: 1 });
  const recordings = result[0].data;
  const total = result[0].metadata[0]?.total || 0;

  return {
    recordings: await transformTranslatedFields(recordings, language.code),
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

module.exports = {
  createRecording,
  updateRecordingUrl,
  updateRecordingStatus,
  getRecordingById,
  deleteRecording,
  listRecordings
};
