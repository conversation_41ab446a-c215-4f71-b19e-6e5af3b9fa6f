const joi = require('joi');
const mongoose = require('mongoose');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const createRecordingSchema = joi.object({
  booking: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Booking ID is required',
      'any.required': 'Booking ID is required'
    }),
  recordingUrl: joi.string().trim().required()
    .messages({
      'string.empty': 'Recording URL is required',
      'any.required': 'Recording URL is required'
    })
});

const updateRecordingUrlSchema = joi.object({
  id: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Recording ID is required',
      'any.required': 'Recording ID is required'
    }),
  recordingUrl: joi.string().trim().required()
    .messages({
      'string.empty': 'Recording URL is required',
      'any.required': 'Recording URL is required'
    })
});

const updateRecordingStatusSchema = joi.object({
  status: joi.string().valid('APPROVED', 'REJECTED', 'PENDING').required()
    .messages({
      'string.empty': 'Status is required',
      'any.required': 'Status is required',
      'any.only': 'Status must be either APPROVED, REJECTED, or PENDING'
    }),
  rejectionReason: joi.string().optional().allow(null , ''),
  updatedUrl: joi.string().trim().optional().allow(null , '')
});

const getByIdSchema = joi.object({
  id: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Recording ID is required',
      'any.required': 'Recording ID is required'
    })
});

module.exports = {
  createRecordingSchema,
  updateRecordingUrlSchema,
  updateRecordingStatusSchema,
  getByIdSchema,
};