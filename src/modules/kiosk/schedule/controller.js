const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const scheduleService = require('./service');
const { getTimeSlotsSchema } = require('./validation');
const { SUCCESS } = commonConstants;

/**
 * Get all time slots for a specific date
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getTimeSlots = async (req, res) => {
  try {
    const { error, value } = getTimeSlotsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await scheduleService.getTimeSlots(value, req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Time slots retrieved successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getTimeSlots
};
