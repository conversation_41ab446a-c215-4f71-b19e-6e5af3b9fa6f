const DarshanSchedule = require('../../../models/DarshanSchedule');
const PoojaSchedule = require('../../../models/PoojaSchedule');
const Event = require('../../../models/Event');
const KioskUser = require('../../../models/KioskUser');
const { throwBadRequestError, throwNotFoundError } = require('../../../errors');

/**
 * Get all time slots for a specific date
 * @param {Object} data - Request data containing date
 * @param {Object} user - Kiosk user information
 * @returns {Object} Time slots for darshan, pooja, and events
 */
const getTimeSlots = async (data, user) => {
  const { date, isEvent } = data;
  
  // Convert date string to Date object
  const requestedDate = new Date(date);

  // requestedDate.setUTCHours(0, 0, 0, 0);
  
  // Get end of day for date range queries
  const endOfDay = new Date(requestedDate);

  endOfDay.setUTCHours(23, 59, 59, 999);
  
  // Get kiosk user details to find associated temple
  const kioskUser = await KioskUser.findById(user.id).populate('kiosk');
  
  if (!kioskUser || !kioskUser.kiosk) {
    throwNotFoundError('Kiosk user or kiosk not found');
  }
  
  const templeId = kioskUser.kiosk.temple;
  
  if (!templeId) {
    throwBadRequestError('Kiosk is not associated with any temple');
  }

  if (isEvent) {
    const events = await Event.find({
      temple: templeId,
      status: 'ACTIVE',
      $or: [
        // For SPECIFIC_DATE events
        {
          dateType: 'SPECIFIC_DATE',
          specificDate: {
            $gte: requestedDate,
            $lte: endOfDay
          }
        },
        // For DATE_RANGE events
        {
          dateType: 'DATE_RANGE',
          'dateRange.startDate': { $lte: endOfDay },
          'dateRange.endDate': { $gte: requestedDate }
        },
      ]
    }).select('name startTime endTime');

    const formattedEvents = events.map(event => ({
      _id: event._id,
      name: event.name,
      type: 'EVENT',
      timeSlots: [{
        startTime: event.startTime,
        endTime: event.endTime
      }]
    }));

    const timeSlots = formattedEvents
      .flatMap(schedule => schedule.timeSlots.map(slot => ({
        startTime: slot.startTime,
        endTime: slot.endTime
      })));

    const uniqueTimeSlots = timeSlots.filter((slot, index, self) =>
      index === self.findIndex(s =>
        s.startTime === slot.startTime && s.endTime === slot.endTime
      )
    );
    
    return {
      date: date,
      uniqueTimeSlots: uniqueTimeSlots
    };
  }
  
  // Find darshan schedules for the requested date
  const darshanSchedules = await DarshanSchedule.find({
    temple: templeId,
    status: 'ACTIVE',
    $or: [
      // For SPECIFIC_DATE schedules
      {
        dateType: 'SPECIFIC_DATE',
        specificDate: {
          $gte: requestedDate,
          $lte: endOfDay
        }
      },
      // For DATE_RANGE schedules
      {
        dateType: 'DATE_RANGE',
        'dateRange.startDate': { $lte: endOfDay },
        'dateRange.endDate': { $gte: requestedDate }
      }
    ]
  }).select('name timeSlots');
  
  // Find pooja schedules for the requested date
  const poojaSchedules = await PoojaSchedule.find({
    temple: templeId,
    status: 'ACTIVE',
    $or: [
      // For SPECIFIC_DATE schedules
      {
        dateType: 'SPECIFIC_DATE',
        specificDate: {
          $gte: requestedDate,
          $lte: endOfDay
        }
      },
      // For DATE_RANGE schedules
      {
        dateType: 'DATE_RANGE',
        'dateRange.startDate': { $lte: endOfDay },
        'dateRange.endDate': { $gte: requestedDate }
      }
    ]
  }).select('name type timeSlots');
  
  // Format the response
  const formattedDarshanSchedules = darshanSchedules.map(schedule => ({
    _id: schedule._id,
    name: schedule.name,
    type: 'PHYSICAL_DARSHAN',
    timeSlots: schedule.timeSlots.map(slot => ({
      startTime: slot.startTime,
      endTime: slot.endTime
    }))
  }));
  
  const formattedPoojaSchedules = poojaSchedules.map(schedule => ({
    _id: schedule._id,
    name: schedule.name,
    type: schedule.type === 'PHYSICAL' ? 'PHYSICAL_POOJA' : 'VIRTUAL_POOJA',
    timeSlots: schedule.timeSlots.map(slot => ({
      startTime: slot.startTime,
      endTime: slot.endTime
    }))
  }));

  const schedules = [ ...formattedDarshanSchedules, ...formattedPoojaSchedules ];

  const timeSlots = schedules
    .flatMap(schedule => schedule.timeSlots.map(slot => ({
      startTime: slot.startTime,
      endTime: slot.endTime
    })));

  const uniqueTimeSlots = timeSlots.filter((slot, index, self) =>
    index === self.findIndex(s =>
      s.startTime === slot.startTime && s.endTime === slot.endTime
    )
  );
  
  return {
    date: date,
    timeSlots: uniqueTimeSlots
  };
};

module.exports = {
  getTimeSlots
};
