const joi = require('joi');

/**
 * Validation schema for getting time slots
 */
const getTimeSlotsSchema = joi.object({
  date: joi.date()
    .iso()
    .required()
    .messages({
      'date.base': 'Date must be a valid date',
      'date.format': 'Date must be in ISO format (YYYY-MM-DD)',
      'any.required': 'Date is required'
    }),
  isEvent: joi.boolean().optional()
});

module.exports = {
  getTimeSlotsSchema
};
