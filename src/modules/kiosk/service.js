const Kiosk = require('../../models/Kiosk');
const KioskUser = require('../../models/KioskUser');
const Notification = require('../../models/Notification');
const Temple = require('../../models/Temple');
const { throwNotFoundError, throwBadRequestError } = require('../../errors');
const { messages } = require('../../messages');
const Booking = require('../../models/Booking');
const { transformTranslatedFields } = require('../../utils/localizer');
const { notificationType } = require('../../constants/dbEnums');
const Admin = require('../../models/Admin');

/**
 * Create a new kiosk
 * @param {Object} kioskData - Kiosk data
 * @param {Object} adminUser - Admin user creating the kiosk
 * @returns {Object} Created kiosk
 */
const createKiosk = async (kioskData, adminUser) => {
  // Check if temple exists
  const temple = await Temple.findById(kioskData.temple);

  if (!temple) {
    throwNotFoundError(messages.TEMPLE_NOT_FOUND);
  }

  const existingKiosk = await Kiosk.findOne({ temple: kioskData.temple, deletedAt: null });

  if (existingKiosk) {
    throwBadRequestError(messages.KIOSK_ALREADY_EXISTS);
  }

  // Create the kiosk
  const kiosk = new Kiosk({
    name: kioskData.name,
    temple: kioskData.temple,
    location: kioskData.location,
    description: kioskData.description || '',
    status: kioskData.status || 'ACTIVE',
    createdBy: adminUser.id,
    updatedBy: adminUser.id
  });

  await kiosk.save();

  // Return the created kiosk with temple details
  const createdKiosk = await Kiosk.findById(kiosk._id)
    .populate('temple');

  return createdKiosk;
};

/**
 * Get a kiosk by ID with its associated users
 * @param {string} id - Kiosk ID
 * @returns {Object} Kiosk with users
 */
const getKioskById = async (id) => {
  let kiosk = await Kiosk.findById(id)
    .populate('temple').lean();

  kiosk = await transformTranslatedFields(kiosk, 'en');

  if (!kiosk) {
    throwNotFoundError('Kiosk not found');
  }

  // Get associated kiosk users
  const kioskUsers = await KioskUser.find({ kiosk: id, deletedAt: null })
    .select('-password')
    .sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 }).lean();

  const language = { code: 'en' };

  return {
    ...kiosk,
    kioskUsers: await transformTranslatedFields(kioskUsers, language.code)
  };
};

/**
 * Update a kiosk
 * @param {string} id - Kiosk ID
 * @param {Object} updateData - Updated kiosk data
 * @param {Object} adminUser - Admin user updating the kiosk
 * @returns {Object} Updated kiosk
 */
const updateKiosk = async (id, updateData, adminUser) => {
  // Check if kiosk exists
  const kiosk = await Kiosk.findById(id);

  if (!kiosk) {
    throwNotFoundError('Kiosk not found');
  }

  // Check if temple exists if temple is being updated
  if (updateData.temple) {
    const temple = await Temple.findById(updateData.temple);

    if (!temple) {
      throwNotFoundError(messages.TEMPLE_NOT_FOUND);
    }
  }

  // Update kiosk fields
  if (updateData.temple) {
    kiosk.temple = updateData.temple;
  }
  if (updateData.location) {
    kiosk.location = updateData.location;
  }
  if (updateData.description !== undefined) {
    kiosk.description = updateData.description;
  }
  if (updateData.status) {
    kiosk.status = updateData.status;
  }

  kiosk.updatedBy = adminUser.id;
  await kiosk.save();

  // Return the updated kiosk with temple details
  const updatedKiosk = await Kiosk.findById(id)
    .populate('temple');

  return updatedKiosk;
};

/**
 * Delete a kiosk and its associated users
 * @param {string} id - Kiosk ID
 * @returns {Object} Deletion result
 */
const deleteKiosk = async (id) => {
  const kiosk = await Kiosk.findById(id);

  if (!kiosk) {
    throwNotFoundError('Kiosk not found');
  }

  if (kiosk.deletedAt || kiosk.status === 'INACTIVE') {
    throwBadRequestError('Kiosk already deleted or inactive');
  }

  // Soft delete associated kiosk users
  await KioskUser.updateMany(
    { kiosk: id },
    { deletedAt: new Date(), status: 'INACTIVE' }
  );

  // Soft delete the kiosk
  kiosk.deletedAt = new Date();
  kiosk.status = 'INACTIVE';
  await kiosk.save();

  return kiosk;
};

/**
 * List kiosks with pagination and filtering
 * @param {Object} filters - Filter criteria
 * @returns {Object} Paginated list of kiosks
 */
const listKiosks = async (filters) => {
  const { page = 1, limit = 10, temple, status } = filters;
  const skip = (page - 1) * limit;

  // Build query
  const query = {
    deletedAt: null
  };

  if (temple) {
    query.temple = temple;
  }
  if (status) {
    query.status = status;
  }

  // Get total count
  const total = await Kiosk.countDocuments(query);

  // Get kiosks
  const kiosks = await Kiosk.find(query)
    .populate('temple')
    .sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit)).lean();

  // Get kiosk users count for each kiosk
  const kiosksWithUserCount = await Promise.all(kiosks.map(async (kiosk) => {
    const userCount = await KioskUser.countDocuments({ kiosk: kiosk._id });

    return {
      ...kiosk,
      userCount
    };
  }));

  const language = { code: 'en' };

  return {
    kiosks: await transformTranslatedFields(kiosksWithUserCount, language.code),
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / limit)
    }
  };
};

const updateInventory = async (body, kioskUser) => {

  const booking = await Booking.findById(body.bookingId);

  if (!booking) {
    throwBadRequestError(messages.BOOKING_NOT_FOUND);
  }
  
  const kiosk = await Kiosk.findById(kioskUser.kiosk).populate('temple');

  const kitCount = parseInt(booking?.promotionalKitCount || 0);
  const inventory = parseInt(kiosk?.inventoryRemaining || 0);

  if (!inventory || !kitCount || (inventory < kitCount)) {
    throwBadRequestError(messages.INSUFFICIENT_STOCK);
  }

  const deductedInventory = parseInt(kiosk.inventoryRemaining - kitCount);

  let updatedKioskInventory;

  //* Trigger notification if it drops below threshold and not already triggered
  if (deductedInventory < 250 && !kiosk.isNotificationTriggered) {
    const admin = await Admin.findById(kiosk.createdBy);

    await Notification.create({
      userId: kiosk.createdBy || admin._id,
      title: 'Low Stock Alert',
      body: `Inventory low at ${kiosk.temple.name.en || kiosk.temple.name}. Immediate stock refill recommended`,
      type: admin.userType || 'SUPER_ADMIN',
      redirectPath: '/kiosks/inventory',
      metaData: {
        kioskName: kiosk.name
      },
      notificationType: notificationType.LOW_STOCK_ALERT
    });

    updatedKioskInventory = await Kiosk.findByIdAndUpdate(kioskUser.kiosk, { inventoryRemaining: deductedInventory, isNotificationTriggered: true });
  } else if (deductedInventory >= 250 && kiosk.isNotificationTriggered) {
    updatedKioskInventory = await Kiosk.findByIdAndUpdate(kioskUser.kiosk, { inventoryRemaining: deductedInventory, isNotificationTriggered: false });
  } else {
    updatedKioskInventory = await Kiosk.findByIdAndUpdate(kioskUser.kiosk, { inventoryRemaining: deductedInventory });
  }

  return updatedKioskInventory.inventoryRemaining;
};

const getKioskWithBelowThresholdInventory = async () => {
  const kiosks = await Kiosk.find({ inventoryRemaining: { $lt: 250 }, deletedAt: null })
    .populate('temple').lean();

  const language = { code: 'en' };

  return await transformTranslatedFields(kiosks, language.code);

};

module.exports = {
  createKiosk,
  getKioskById,
  updateKiosk,
  deleteKiosk,
  updateInventory,
  listKiosks,
  getKioskWithBelowThresholdInventory
};
