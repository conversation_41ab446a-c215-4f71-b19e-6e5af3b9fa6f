const joi = require('joi');
const mongoose = require('mongoose');
const { extension } = require('../../constants/dbEnums');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

// Schema for creating a new kiosk
const createKioskSchema = joi.object({
  name: joi.string().trim(),
  temple: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Temple ID is required',
      'any.required': 'Temple ID is required'
    }),
  location: joi.string().required().trim()
    .messages({
      'string.empty': 'Kiosk location is required',
      'any.required': 'Kiosk location is required'
    }),
  description: joi.string().allow('').trim(),
  status: joi.string().valid('ACTIVE', 'INACTIVE').default('ACTIVE')
});

// Schema for updating an existing kiosk
const updateKioskSchema = joi.object({
  name: joi.string().trim(),
  temple: joi.string().custom(validateObjectId)
    .messages({
      'string.empty': 'Temple ID is required'
    }),
  location: joi.string().trim()
    .messages({
      'string.empty': 'Kiosk location is required'
    }),
  description: joi.string().allow('').trim(),
  status: joi.string().valid('ACTIVE', 'INACTIVE')
}).min(1);

// Schema for validating ID parameter
const validateId = joi.object({
  id: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'ID is required',
      'any.required': 'ID is required'
    })
});

// Schema for listing kiosks with pagination
const listKiosksSchema = joi.object({
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(10),
  temple: joi.string().custom(validateObjectId).allow(''),
  status: joi.string().valid('ACTIVE', 'INACTIVE').allow('')
});

const uploadUrlSchema = joi.object({
  extension: joi.string()
    .valid(extension.FLV, extension.MP4, extension.MOV, extension.HEVC, extension.WEBM, extension.MPEG)
    .required()
    .messages({
      'any.required': 'File extension is required',
      'any.only': 'Invalid file extension. Only FLV, MP4, MOV, HEVC, WEBM and MPEG are allowed'
    })
});

const deletePoojaVideoSchema = joi.object({
  key: joi.string().required()
    .messages({
      'string.empty': 'Key is required',
      'any.required': 'Key is required'
    })
});

const updateKioskInventorySchema = joi.object({
  bookingId: joi.string()
    .custom(validateObjectId)
    .required()
    .messages({
      'string.base': 'Booking ID must be a string',
      'any.invalid': 'Invalid booking ID format',
      'any.required': 'Booking ID is required'
    })
});

module.exports = {
  createKioskSchema,
  updateKioskSchema,
  validateId,
  uploadUrlSchema,
  deletePoojaVideoSchema,
  updateKioskInventorySchema,
  listKiosksSchema
};
