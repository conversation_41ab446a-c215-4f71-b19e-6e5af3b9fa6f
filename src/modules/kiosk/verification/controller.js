const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const verificationService = require('./service');
const { verifyDevoteeSchema, checkVerificationStatusSchema, getBookingVerificationSchema, uploadAadharImageSchema, saveAadharImageSchema } = require('./validation');
const { SUCCESS } = commonConstants;

/**
 * Verify a devotee using QR code information
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const verifyDevotee = async (req, res) => {
  try {
    const { error, value } = verifyDevoteeSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await verificationService.verifyDevotee(value, req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: result.message,
      status: true,
      data: result.data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Check verification status of a devotee
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const checkVerificationStatus = async (req, res) => {
  try {
    const { error, value } = checkVerificationStatusSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await verificationService.checkVerificationStatus(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Verification status retrieved successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get verification details for all devotees in a booking
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getBookingVerificationDetails = async (req, res) => {
  try {
    const bookingId = req.params.bookingId;
    const date = req.query.date;

    const { error, value } = getBookingVerificationSchema.validate({ bookingId, date });

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await verificationService.getBookingVerificationDetails(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Booking verification details retrieved successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const uploadAadharImage = async (req, res) => {
  try {
    const bookingId = req.params.bookingId;
    const devoteeId = req.params.devoteeId;

    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }

    const { error } = uploadAadharImageSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const { extension } = req.body;

    const result = await verificationService.uploadAadharImage(bookingId, devoteeId, extension);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Aadhar image uploaded successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const saveAadharImage = async (req, res) => {
  try {
    const bookingId = req.params.bookingId;
    const devoteeId = req.params.devoteeId;

    const { error } = saveAadharImageSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const result = await verificationService.saveAadharImage(bookingId, devoteeId, req.body);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Aadhar image saved successfully',
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  verifyDevotee,
  checkVerificationStatus,
  getBookingVerificationDetails,
  uploadAadharImage,
  saveAadharImage
};
