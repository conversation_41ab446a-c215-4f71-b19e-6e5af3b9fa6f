const express = require('express');
const router = express.Router();
const verificationController = require('./controller');
const auth = require('../../../middleware/auth');
const { isKioskUser } = require('../../../middleware/roleCheck');

// Kiosk user routes
router.post('/aadhar/upload-url/:bookingId/:devoteeId', auth, isKioskUser, verificationController.uploadAadharImage);
router.post('/aadhar/save-url/:bookingId/:devoteeId', auth, isKioskUser, verificationController.saveAadharImage);
router.post('/verify-devotee', auth, isKioskUser, verificationController.verifyDevotee);
router.get('/check-status', auth, isKioskUser, verificationController.checkVerificationStatus);
router.get('/booking-details/:bookingId', auth, isKioskUser, verificationController.getBookingVerificationDetails);
router.patch('/booking/:bookingId/complete', auth, isKiosk<PERSON>ser, verificationController.markAsComplete);

module.exports = router;
