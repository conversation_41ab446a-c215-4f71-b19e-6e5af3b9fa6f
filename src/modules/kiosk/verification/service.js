const moment = require('moment');
const Booking = require('../../../models/Booking');
const KioskUser = require('../../../models/KioskUser');
const { throwBadRequestError, throwNotFoundError } = require('../../../errors');
const { type, status } = require('../../../constants/dbEnums');
const { messages } = require('../../../messages');
const PoojaRecording = require('../../../models/PoojaRecording');
const { transformTranslatedFields } = require('../../../utils/localizer');
const { getPresignedUrl } = require('../../../utils/s3Service');
const Language = require('../../../models/Language');

/**
 * Verify a devotee using QR code information
 * @param {Object} data - Verification data
 * @param {Object} user - Kiosk user performing the verification
 * @returns {Object} Verification result
 */
const verifyDevotee = async (data, user) => {
  // Check if booking exists
  const booking = await Booking.findById(data.bookingId).populate('temple poojaSchedule event darshanSchedule');

  if (!booking || booking.status !== status.COMPLETED) {
    throwNotFoundError('Booking not found');
  }

  // Check if the booking is for today's date
  const today = moment().startOf('day');

  if (booking.type === type.EVENT) {
    // For events, check if the date parameter matches one of the event dates and is today
    if (!data.date) {
      throwBadRequestError('Date is required for event verification');
    }

    const formattedDate = moment(data.date).format('YYYY-MM-DD');
    const todayFormatted = today.format('YYYY-MM-DD');

    // Check if the provided date is today
    if (formattedDate !== todayFormatted) {
      throwBadRequestError('Verification is only allowed for today\'s date');
    }

    // Check if the date exists in event dates
    const eventDate = booking.eventDates.find(d =>
      moment(d.date).format('YYYY-MM-DD') === formattedDate
    );

    if (!eventDate) {
      throwBadRequestError(messages.INVALID_EVENT_DATE);
    }
  } else {
    // For darshan and pooja, check if booking date is today
    const bookingDate = moment(booking.date).startOf('day');

    if (!bookingDate.isSame(today)) {
      throwBadRequestError('Verification is only allowed for today\'s bookings');
    }
  }

  // Get kiosk user details
  const kioskUser = await KioskUser.findById(user.id);

  if (!kioskUser) {
    throwNotFoundError('Kiosk user not found');
  }

  // Determine if this is a primary devotee or other devotee
  let isPrimaryDevotee = false;
  let devotee = null;
  let devoteeIndex = -1;

  if (booking.primaryDevoteeDetails._id.toString() === data.devoteeId) {
    isPrimaryDevotee = true;
    devotee = booking.primaryDevoteeDetails;
  } else {
    // Find in other devotees
    devoteeIndex = booking.otherDevotees.findIndex(d => d._id.toString() === data.devoteeId);

    if (devoteeIndex === -1) {
      throwNotFoundError(messages.DEVOTEE_NOT_FOUND);
    }

    devotee = booking.otherDevotees[devoteeIndex];
  }

  // Handle verification based on booking type
  if (booking.type === type.EVENT) {
    // We already validated the date is today and exists in event dates
    const formattedDate = moment(data.date).format('YYYY-MM-DD');

    // Check if already verified for this date
    const dateKey = formattedDate;
    const verificationMap = devotee.verification || new Map();

    if (verificationMap.get(dateKey) && verificationMap.get(dateKey).verified) {
      // Throw error for already verified devotee
      throwBadRequestError(messages.DEVOTEE_ALREADY_VERIFIED_DATE);
    }

    // Mark as verified for this date
    const verificationInfo = {
      verified: true,
      verifiedAt: new Date(),
      verifiedBy: kioskUser._id,
      kioskId: kioskUser.kiosk
    };

    // Update the booking
    if (isPrimaryDevotee) {
      // For primary devotee
      await Booking.updateOne(
        { _id: booking._id },
        { $set: { [`primaryDevoteeDetails.verification.${dateKey}`]: verificationInfo } }
      );
    } else {
      // For other devotees
      await Booking.updateOne(
        { _id: booking._id },
        { $set: { [`otherDevotees.${devoteeIndex}.verification.${dateKey}`]: verificationInfo } }
      );
    }

    // Get the updated booking
    let updatedBooking = await Booking.findById(booking._id).populate('temple poojaSchedule event darshanSchedule').lean();
    
    let language = { code: 'en' };

    if (user && user?.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: user.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }

    updatedBooking = await transformTranslatedFields(updatedBooking, language.code);

    // Get the kiosk user details
    const verifiedByUser = await KioskUser.findById(kioskUser._id)
      .select('name email phoneNumber')
      .populate('kiosk');

    // Prepare verified by details
    const verifiedByDetails = {
      _id: verifiedByUser._id,
      name: verifiedByUser.name,
      email: verifiedByUser.email,
      phoneNumber: verifiedByUser.phoneNumber,
      kiosk: verifiedByUser.kiosk
    };

    return {
      message: messages.DEVOTEE_VERIFIED_SUCCESS_DATE,
      data: {
        booking: updatedBooking,
        devotee: isPrimaryDevotee ?
          updatedBooking.primaryDevoteeDetails :
          updatedBooking.otherDevotees[devoteeIndex],
        verificationInfo,
        verifiedBy: verifiedByDetails
      }
    };
  } else {
    // For darshan and pooja (single date)
    // Use booking date as the key
    const dateKey = moment(booking.date).format('YYYY-MM-DD');
    const verificationMap = devotee.verification || new Map();

    if (verificationMap.get(dateKey) && verificationMap.get(dateKey).verified) {
      // Throw error for already verified devotee
      throwBadRequestError(messages.DEVOTEE_ALREADY_VERIFIED);
    }

    // Mark as verified
    const verificationInfo = {
      verified: true,
      verifiedAt: new Date(),
      verifiedBy: kioskUser._id,
      kioskId: kioskUser.kiosk
    };

    // Update the booking
    if (isPrimaryDevotee) {
      // For primary devotee
      await Booking.updateOne(
        { _id: booking._id },
        { $set: { [`primaryDevoteeDetails.verification.${dateKey}`]: verificationInfo } }
      );
    } else {
      // For other devotees
      await Booking.updateOne(
        { _id: booking._id },
        { $set: { [`otherDevotees.${devoteeIndex}.verification.${dateKey}`]: verificationInfo } }
      );
    }

    // Get the updated booking
    let updatedBooking = await Booking.findById(booking._id).populate('temple poojaSchedule event darshanSchedule').lean();

    let language = { code: 'en' };

    if (user && user?.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: user.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }

    updatedBooking = await transformTranslatedFields(updatedBooking, language.code);

    // Get the kiosk user details
    const verifiedByUser = await KioskUser.findById(kioskUser._id)
      .select('name email phoneNumber')
      .populate('kiosk');

    // Prepare verified by details
    const verifiedByDetails = {
      _id: verifiedByUser._id,
      name: verifiedByUser.name,
      email: verifiedByUser.email,
      phoneNumber: verifiedByUser.phoneNumber,
      kiosk: verifiedByUser.kiosk
    };

    return {
      message: messages.DEVOTEE_VERIFIED_SUCCESS,
      data: {
        booking: updatedBooking,
        devotee: isPrimaryDevotee ?
          updatedBooking.primaryDevoteeDetails :
          updatedBooking.otherDevotees[devoteeIndex],
        verificationInfo,
        verifiedBy: verifiedByDetails
      }
    };
  }
};

/**
 * Check verification status of a devotee
 * @param {Object} data - Query data
 * @returns {Object} Verification status
 */
const checkVerificationStatus = async (data) => {
  // Check if booking exists
  const booking = await Booking.findById(data.bookingId);

  if (!booking) {
    throwNotFoundError('Booking not found');
  }

  // Determine if this is a primary devotee or other devotee
  let isPrimaryDevotee = false;
  let devotee = null;

  if (booking.primaryDevoteeDetails._id.toString() === data.devoteeId) {
    isPrimaryDevotee = true;
    devotee = booking.primaryDevoteeDetails;
  } else {
    // Find in other devotees
    const devoteeIndex = booking.otherDevotees.findIndex(d => d._id.toString() === data.devoteeId);

    if (devoteeIndex === -1) {
      throwNotFoundError(messages.DEVOTEE_NOT_FOUND);
    }

    devotee = booking.otherDevotees[devoteeIndex];
  }

  // Handle verification check based on booking type
  if (booking.type === type.EVENT) {
    // For events, we need a date parameter
    if (!data.date) {
      throwBadRequestError('Date is required for event verification check');
    }

    // Check if the date is valid for this event
    const formattedDate = moment(data.date).format('YYYY-MM-DD');
    const eventDate = booking.eventDates.find(d =>
      moment(d.date).format('YYYY-MM-DD') === formattedDate
    );

    if (!eventDate) {
      throwBadRequestError(messages.INVALID_EVENT_DATE);
    }

    // Check verification status for this date
    const dateKey = formattedDate;
    const verificationMap = devotee.verification || new Map();
    const verificationInfo = verificationMap.get(dateKey);

    // Get the kiosk user who verified if applicable
    let verifiedByDetails = null;

    if (verificationInfo && verificationInfo.verified && verificationInfo.verifiedBy) {
      const verifiedByUser = await KioskUser.findById(verificationInfo.verifiedBy)
        .select('name email phoneNumber')
        .populate('kiosk');

      if (verifiedByUser) {
        verifiedByDetails = {
          _id: verifiedByUser._id,
          name: verifiedByUser.name,
          email: verifiedByUser.email,
          phoneNumber: verifiedByUser.phoneNumber,
          kiosk: verifiedByUser.kiosk
        };
      }
    }

    return {
      booking: {
        _id: booking._id,
        bookingNumber: booking.bookingNumber,
        type: booking.type,
        date: booking.date,
        eventDates: booking.eventDates,
        status: booking.status
      },
      devotee: {
        _id: devotee._id,
        fullName: devotee.fullName,
        isPrimaryDevotee
      },
      date: formattedDate,
      isVerified: verificationInfo ? verificationInfo.verified : false,
      verificationInfo: verificationInfo || null,
      verifiedBy: verifiedByDetails
    };
  } else {
    // For darshan and pooja (single date)
    const dateKey = moment(booking.date).format('YYYY-MM-DD');
    const verificationMap = devotee.verification || new Map();
    const verificationInfo = verificationMap.get(dateKey);

    // Get the kiosk user who verified if applicable
    let verifiedByDetails = null;

    if (verificationInfo && verificationInfo.verified && verificationInfo.verifiedBy) {
      const verifiedByUser = await KioskUser.findById(verificationInfo.verifiedBy)
        .select('name email phoneNumber')
        .populate('kiosk');

      if (verifiedByUser) {
        verifiedByDetails = {
          _id: verifiedByUser._id,
          name: verifiedByUser.name,
          email: verifiedByUser.email,
          phoneNumber: verifiedByUser.phoneNumber,
          kiosk: verifiedByUser.kiosk
        };
      }
    }

    return {
      booking: {
        _id: booking._id,
        bookingNumber: booking.bookingNumber,
        type: booking.type,
        date: booking.date,
        status: booking.status
      },
      devotee: {
        _id: devotee._id,
        fullName: devotee.fullName,
        isPrimaryDevotee
      },
      date: dateKey,
      isVerified: verificationInfo ? verificationInfo.verified : false,
      verificationInfo: verificationInfo || null,
      verifiedBy: verifiedByDetails
    };
  }
};

/**
 * Get verification details for all devotees in a booking
 * @param {Object} data - Query data containing bookingId and optional date
 * @returns {Object} Verification details for all devotees
 */
const getBookingVerificationDetails = async (data) => {
  // Check if booking exists
  const booking = await Booking.findById(data.bookingId)
    .populate('temple darshanSchedule poojaSchedule event').lean();

  if (!booking) {
    throwNotFoundError('Booking not found');
  }

  const poojaRecording = await PoojaRecording.find({ booking: data.bookingId }).populate('kioskUser').lean();

  // Prepare response data
  const responseData = {
    booking,
    poojaRecording: poojaRecording || null,
    devotees: [],
    selectedDate: null
  };

  // Determine which date to use
  let dateToUse;

  if (booking.type === type.EVENT) {
    if (data.date) {
      // Use provided date if valid
      const formattedDate = moment(data.date).format('YYYY-MM-DD');
      const eventDate = booking.eventDates.find(d =>
        moment(d.date).format('YYYY-MM-DD') === formattedDate
      );

      if (!eventDate) {
        throwBadRequestError(messages.INVALID_EVENT_DATE);
      }

      dateToUse = formattedDate;
    } else {
      // Use first date by default for events
      if (booking.eventDates && booking.eventDates.length > 0) {
        dateToUse = moment(booking.eventDates[0].date).format('YYYY-MM-DD');
      } else {
        throwBadRequestError(messages.NO_EVENT_DATES);
      }
    }
    responseData.selectedDate = dateToUse;
  } else {
    // For darshan and pooja, use the booking date
    dateToUse = moment(booking.date).format('YYYY-MM-DD');
    responseData.selectedDate = dateToUse;
  }

  // Add primary devotee details
  const primaryDevotee = booking.primaryDevoteeDetails;
  const primaryVerificationMap = primaryDevotee.verification;
  const primaryVerificationInfo = primaryVerificationMap[dateToUse];

  // Get verification details if verified
  let primaryVerifiedByDetails = null;

  if (primaryVerificationInfo && primaryVerificationInfo.verified && primaryVerificationInfo.verifiedBy) {
    const verifiedByUser = await KioskUser.findById(primaryVerificationInfo.verifiedBy)
      .select('name email phoneNumber')
      .populate('kiosk');

    if (verifiedByUser) {
      primaryVerifiedByDetails = {
        _id: verifiedByUser._id,
        name: verifiedByUser.name,
        email: verifiedByUser.email,
        phoneNumber: verifiedByUser.phoneNumber,
        kiosk: verifiedByUser.kiosk
      };
    }
  }

  responseData.devotees.push({
    _id: primaryDevotee._id,
    fullName: primaryDevotee.fullName,
    gender: primaryDevotee.gender,
    aadharNumber: primaryDevotee.aadharNumber,
    aadharImage: primaryDevotee.aadharImage,
    phoneNumber: primaryDevotee.phoneNumber,
    isPrimaryDevotee: true,
    isVerified: primaryVerificationInfo ? primaryVerificationInfo.verified : false,
    date: dateToUse,
    verificationInfo: primaryVerificationInfo || null,
    verifiedBy: primaryVerifiedByDetails
  });

  // Add other devotees details
  if (booking.otherDevotees && booking.otherDevotees.length > 0) {
    // Process each devotee sequentially to handle async operations
    for (const devotee of booking.otherDevotees) {
      const verificationMap = devotee.verification;
      const verificationInfo = verificationMap[dateToUse];

      // Get verification details if verified
      let verifiedByDetails = null;

      if (verificationInfo && verificationInfo.verified && verificationInfo.verifiedBy) {
        const verifiedByUser = await KioskUser.findById(verificationInfo.verifiedBy)
          .select('name email phoneNumber')
          .populate('kiosk');

        if (verifiedByUser) {
          verifiedByDetails = {
            _id: verifiedByUser._id,
            name: verifiedByUser.name,
            email: verifiedByUser.email,
            phoneNumber: verifiedByUser.phoneNumber,
            kiosk: verifiedByUser.kiosk
          };
        }
      }

      responseData.devotees.push({
        _id: devotee._id,
        fullName: devotee.fullName,
        gender: devotee.gender,
        aadharNumber: devotee.aadharNumber,
        aadharImage: devotee.aadharImage,
        isPrimaryDevotee: false,
        isVerified: verificationInfo ? verificationInfo.verified : false,
        date: dateToUse,
        verificationInfo: verificationInfo || null,
        verifiedBy: verifiedByDetails
      });
    }
  }

  const language = { code: 'en' };

  return await transformTranslatedFields(responseData, language.code);
};

const uploadAadharImage = async (bookingId, devoteeId, extension) => {
  const booking = await Booking.findById(bookingId);

  if (!booking) {
    throwNotFoundError('Booking not found');
  }

  const devotee = booking.primaryDevoteeDetails._id.toString() === devoteeId ? booking.primaryDevoteeDetails : booking.otherDevotees.find(d => d._id.toString() === devoteeId);

  if (!devotee) {
    throwNotFoundError('Devotee not found');
  }

  const uploadData = await getPresignedUrl(extension, 'aadhar', devoteeId);

  return uploadData;
};

const saveAadharImage = async (bookingId, devoteeId, body) => {
  const booking = await Booking.findById(bookingId);

  if (!booking) {
    throwNotFoundError('Booking not found');
  }

  let primaryDevotee = null, otherDevotee = null;

  if (booking.primaryDevoteeDetails._id.toString() === devoteeId) {
    primaryDevotee = booking.primaryDevoteeDetails;
  } else {
    otherDevotee = booking.otherDevotees.find(d => d._id.toString() === devoteeId);
  }

  if (primaryDevotee) {
    await Booking.updateOne(
      { _id: bookingId },
      { $set: { 'primaryDevoteeDetails.aadharImage': process.env.MEDIA_URL + '/' + body.key } }
    );
  } else if (otherDevotee) {
    await Booking.updateOne(
      { _id: bookingId, 'otherDevotees._id': otherDevotee._id },
      { $set: { 'otherDevotees.$.aadharImage': process.env.MEDIA_URL + '/' + body.key } }
    );
  } else {
    throwNotFoundError('Devotee not found');
  }

  if (primaryDevotee) {
    return {
      primaryDevotee,
      aadharImage: process.env.MEDIA_URL + '/' + body.key
    };
  } else {
    return {
      otherDevotee,
      aadharImage: process.env.MEDIA_URL + '/' + body.key
    };
  }
};

module.exports = {
  verifyDevotee,
  checkVerificationStatus,
  getBookingVerificationDetails,
  uploadAadharImage,
  saveAadharImage
};
