const { apiResponse, errorApiResponse } = require('../../config/responseHandler');
const { commonConstants } = require('../../constants/common');
const musicService = require('./service');
const { uploadUrlSchema, saveMusicSchema, listSongsSchema, validateId } = require('./validation');
const { SUCCESS } = commonConstants;
const { saveAuditLog } = require('../../utils/auditLogger');
const { auditLogAction } = require('../../constants/dbEnums');
const { getPresignedUrl } = require('../../utils/s3Service');

//* 1. Function to get upload URL 
const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }

    const { error } = uploadUrlSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const { extension } = req.body;

    const uploadData = await getPresignedUrl(extension, 'music');

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Upload URL generated successfully',
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 2. Function to add song 
const addSong = async (req, res) => {
  try {
    const { error, value } = saveMusicSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const song = await musicService.addSong(value, req.user.id);
    
    //* Save audit log 
    const detail = `Song ${song.title.en} added successfully`;
    const model = 'Music';
    
    await saveAuditLog(req, req.user.id, auditLogAction.MUSIC_ADDED, detail, model);
    
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Song added successfully',
      status: true,
      data: song
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 3. Function to list all the songs 
const listAllSongs = async (req, res) => {
  try {
    const { error, value } = listSongsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const data = await musicService.listAllSongs(value, req.user.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Songs retrieved successfully',
      status: true, 
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 4. Function to delete the song 
const deleteSong = async (req, res) => {
  try {
    const { error, value } = validateId.validate(req.params.id);

    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const data = await musicService.deleteSong(value);

    //* Save audit log 
    const detail = `${data.title.en} deleted successfully`;
    const model = 'Music';
    
    await saveAuditLog(req, req.user.id, auditLogAction.MUSIC_DELETED, detail, model);

    return apiResponse({
      res, 
      code: commonConstants.SUCCESS.CODE,
      message: 'Song deleted successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getUploadUrl,
  addSong,
  listAllSongs,
  deleteSong
};