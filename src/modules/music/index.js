const express = require('express');
const router = express.Router();
const musicController = require('./controller');
const auth = require('../../middleware/auth');
const { isAdminOrSuperAdmin, isUser } = require('../../middleware/roleCheck');

router.post('/upload-url', auth, isAdminOrSuperAdmin, musicController.getUploadUrl);
router.post('/add', auth, isAdminOrSuperAdmin, musicController.addSong);
router.get('/list', auth, isUser, musicController.listAllSongs);
router.delete('/delete/:id', auth, isAdminOrSuperAdmin, musicController.deleteSong);

module.exports = router;