const { apiResponse, errorApiResponse } = require('../../config/responseHandler');
const { validateWebhookSignature } = require('razorpay/dist/utils/razorpay-utils');
const { throwBadRequestError } = require('../../errors');
const { SUCCESS } = require('../../constants/common').commonConstants;
const { messages } = require('../../messages');
const paymentService = require('./service');

const handleICICIWebhook = async (req, res) => {
  try {

    await paymentService.handleICICIWebhook(req.body);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const handleWebhook = async (req, res) => {
  try {
    const signature = req.headers['x-razorpay-signature'];
    const webhookBody = req.body;

    if (!signature) {
      throwBadRequestError('Webhook signature is missing');
    }
    
    if (!webhookBody || !webhookBody.event) {
      throwBadRequestError('Invalid webhook payload');
    }

    const bodyStr = JSON.stringify(webhookBody);

    try {
      const isSignatureValid = validateWebhookSignature(bodyStr, signature, process.env.RAZORPAY_WEBHOOK_SECRET);

      if (!isSignatureValid) {
        throwBadRequestError('Invalid webhook signature');
      }
    } catch (error) {
      throwBadRequestError('Webhook signature validation failed');
    }

    const data = await paymentService.handlePaymentWebhook(webhookBody);

    return apiResponse({
      res, 
      code: SUCCESS.CODE,
      message: messages.SUCCESS, 
      status: true, 
      data
    });

  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  handleICICIWebhook,
  handleWebhook
};