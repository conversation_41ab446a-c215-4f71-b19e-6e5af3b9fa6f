const razorpay = require('../../config/razorpay');
const PaymentBooking = require('../../models/PaymentBooking');
const TransactionBooking = require('../../models/TransactionBooking');
const Booking = require('../../models/Booking');
const { paymentStatus, transactionStatus, transactionType, type, userTypeValue, notificationType } = require('../../constants/dbEnums');
const { throwBadRequestError } = require('../../errors');
const Pujari = require('../../models/Pujari');
const { sendFirebasePushMulticast } = require('../../utils/firebase_cm');
const Token = require('../../models/Token');
const Notification = require('../../models/Notification');
const { sendMail } = require('../../utils/sendMail');
const path = require('path');
const UserDiscount = require('../../models/UserDiscount');
const Discount = require('../../models/Discount');
const { generateBookingConfirmationPDF } = require('../../utils/pdfGenerator');
const QRCode = require('qrcode');
const { generateTicketDetails } = require('../../utils/pdfGenerator');
const fs = require('fs');
const util = require('util');
const { translateDataForStore } = require('../../utils/translateInput');
const readFile = util.promisify(fs.readFile);
const { assignmentMode } = require('../../constants/dbEnums');
const Temple = require('../../models/Temple');
const Language = require('../../models/Language');
const DarshanSchedule = require('../../models/DarshanSchedule');
const PoojaSchedule = require('../../models/PoojaSchedule'); 
const Address = require('../../models/Address');
const Admin = require('../../models/Admin');

//* 1. Function to create razorpay order
const createRazorpayOrder = async ({ amount, currency = 'INR', receipt, userId, type, bookingId }) => {

  //* 1. Create Razorpay order
  const razorpayOrder = await razorpay.orders.create({
    amount: amount * 100, // Convert to paise
    currency,
    receipt
  });

  //* 2. Create payment entry in our database with PENDING status
  const paymentBooking = await PaymentBooking.create({
    userId,
    razorpayOrderId: razorpayOrder.id,
    amount,
    currency,
    status: paymentStatus.PENDING,
    booking: bookingId,
    type
  });

  //* 3. Create transaction entry with PENDING status
  const transactionBooking = await TransactionBooking.create({
    userId,
    paymentId: paymentBooking._id,
    booking: bookingId,
    type,
    amount,
    status: transactionStatus.PENDING,
    description: `Payment initiated for ${type} booking`,
    transactionType: transactionType.PAYMENT,
    currency,
    metadata: {
      razorpayOrderId: razorpayOrder.id
    }
  });

  return {
    paymentBooking,
    razorpayOrder,
    transactionBooking
  };
};

//* 2. Function to handle payment webhook
const handlePaymentWebhook = async (webhookBody) => {
  const { event, payload } = webhookBody;
  const { order, payment } = payload;

  if (!order || !payment) {
    return {};
  }

  const paymentBooking = await PaymentBooking.findOne({
    razorpayOrderId: order.entity.id
  });

  if (!paymentBooking) {
    return {
      message: 'Payment booking not found'
    };
  }

  const orderEntity = order?.entity;
  const receipt = orderEntity?.receipt || '';
  const isBookingOrder = receipt.startsWith('DARSHAN') || receipt.startsWith('PUJA') || receipt.startsWith('EVENT');

  if (!isBookingOrder) {
    throw new Error('Invalid webhook event');
  }

  switch (event) {
    case 'order.paid': {
      const [ , , updatedBooking ] = await Promise.all([
        PaymentBooking.findByIdAndUpdate(
          paymentBooking._id,
          {
            status: paymentStatus.CAPTURED,
            razorpayPaymentId: payment.entity.id,
            paymentMethod: payment.entity.method?.toUpperCase(),
            upiVpa: payment?.entity?.vpa || '',
            bankName: payment?.entity?.bank || '',
            cardId: payment?.entity?.card_id || '',
            paymentResponse: payment.entity,
            razorpaySignature: payment.entity.signature,
            metadata: {
              ...payment.entity,
              razorpayOrderId: order.entity.id
            }
          }
        ),
        TransactionBooking.findOneAndUpdate(
          { paymentId: paymentBooking._id },
          {
            status: transactionStatus.SUCCESS,
            transactionType: transactionType.PAYMENT,
            metadata: {
              ...payment.entity,
              razorpayOrderId: order.entity.id
            }
          }
        ),
        Booking.findByIdAndUpdate(
          paymentBooking.booking,
          { status: 'COMPLETED' }
        ).populate([
          { path: 'temple' },
          { path: 'user' },
          { path: 'darshanSchedule' },
          { path: 'poojaSchedule' },
          { path: 'event' },
        ])
      ]);

      // Only assign pujari for DARSHAN and POOJA bookings
      if (receipt.startsWith('DARSHAN') || receipt.startsWith('PUJA')) {
        const temple = await Temple.findById(updatedBooking.temple);

        if (temple.pujariBookingAssignmentMode === assignmentMode.AUTOMATIC || !temple.pujariBookingAssignmentMode) {
          await assignPujariToBooking(updatedBooking._id);
        }

        //* Save notification for super admin if manual assignment mode is enabled
        if (temple.pujariBookingAssignmentMode && temple.pujariBookingAssignmentMode === assignmentMode.MANUAL) {
          const schedule = updatedBooking.type === type.PHYSICAL_DARSHAN ? updatedBooking.darshanSchedule : updatedBooking.poojaSchedule;
          const admin = await Admin.findById(schedule.createdBy);
      
          await Notification.create({
            userId: admin._id,
            title: `${updatedBooking.type === type.PHYSICAL_DARSHAN ? 'Darshan' : 'Puja'} booking requires pujari assignment`,
            body: `${updatedBooking.type === type.PHYSICAL_DARSHAN ? 'Darshan' : 'Puja'} booked for ${updatedBooking.temple.name.en || updatedBooking.temple.name} have no pujari assigned`,
            type: admin.userType || userTypeValue.SUPER_ADMIN,
            booking: updatedBooking._id,
            redirectPath: '/temple-admin/assign-pujari',
            metaData: {
              templeName: updatedBooking.temple.name.en || updatedBooking.temple.name,
            },
            notificationType: updatedBooking.type === type.PHYSICAL_DARSHAN ? notificationType.UNASSIGNED_DARSHAN : notificationType.UNASSIGNED_PUJA
          });
        }
      }

      //* Now, update Discount and UserDiscount collection
      if (updatedBooking.discount) {
        await Promise.all([
          Discount.findByIdAndUpdate(
            updatedBooking.discount,
            { $inc: { totalUsageCount: 1 } }
          ),
          UserDiscount.create({
            user: updatedBooking.user,
            discount: updatedBooking.discount,
            booking: updatedBooking._id
          })
        ]);
      }

      //* Add whatsapp number to user model 
      if (updatedBooking.primaryDevoteeDetails && updatedBooking.primaryDevoteeDetails.countryCodeForWhatsapp && updatedBooking.primaryDevoteeDetails.whatsappNumber) {
        await addWhatsAppNumberToUser({
          user: updatedBooking.user,
          countryCode: updatedBooking.primaryDevoteeDetails.countryCodeForWhatsapp,
          phoneNumber: updatedBooking.primaryDevoteeDetails.whatsappNumber,
          booking: updatedBooking._id
        });
      }

      if (updatedBooking) {
        const tokens = await Token.find({ 
          userId: updatedBooking.user._id,
          fcmToken: { $ne: null, $exists: true }
        }).select('fcmToken');
        
        const fcmTokens = [ ...new Set(tokens.map(t => t.fcmToken)) ];

        let bookingType = receipt.startsWith('DARSHAN') ? 'Darshan' : receipt.startsWith('PUJA') ? 'Puja' : 'Event';
        const templeName = updatedBooking.temple?.name?.en || updatedBooking.temple?.name || '';
        let bookingDate;

        if (bookingType === 'Event') {
          // instead of takin 0, join all dates with comma
          bookingDate = updatedBooking.eventDates.map(date => date.date.toLocaleDateString()).join(', ');
        } else {
          bookingDate = updatedBooking.date.toLocaleDateString();
        }
        const bookingTime = updatedBooking.timeSlot?.startTime || '';
        const bookingId = updatedBooking.bookingNumber || '';

        if (updatedBooking.type === type.VIRTUAL_POOJA) {
          bookingType = 'Virtual Puja';
        } else if (updatedBooking.type === type.PHYSICAL_POOJA) {
          bookingType = 'Physical Puja';
        }

        let message = `Your booking for ${bookingType} at ${templeName} on ${bookingDate} at ${bookingTime} is confirmed. Booking ID: ${bookingId}. Please check your email for details.`;

        let title = `${bookingType} Booking Confirmed`;
        const notificationPromises = [];

        const data = {
          title, message
        };

        // Translate the title and body
        const translatedFields = [ 'title', 'message' ];
        const translatedData = await translateDataForStore(translatedFields, data);
      
        title = translatedData.title;
        message = translatedData.message;

        notificationPromises.push(Notification.create({
          userId: updatedBooking.user,
          title,
          body: message,
          booking: updatedBooking._id,
          type: 'USER'
        }));

        // Set title as per user's preferred language to send localized message and title 

        let pushTitle = title?.en;
        let pushMessage = message?.en;

        if (updatedBooking.user && updatedBooking.user?.preferredLanguage) {
          const userLanguage = await Language.findOne({ name: updatedBooking.user.preferredLanguage });

          if (userLanguage) {
            pushTitle = title[userLanguage?.code || 'en'];
            pushMessage = message[userLanguage?.code || 'en'];
          }
        }

        if (fcmTokens.length > 0) {
          notificationPromises.push(sendFirebasePushMulticast(pushMessage, pushTitle, fcmTokens, '', { bookingId: updatedBooking._id.toString(), type: bookingType }));
        }

        await Promise.all(notificationPromises);

        //* Send booking confirmation email with PDF link
        sendBookingConfirmationEmail(updatedBooking);
      }

      return {
        message: 'Payment successful',
        orderId: order.entity.id,
        paymentId: payment.entity.id,
        bookingId: updatedBooking._id,
        bookingType: paymentBooking.type
      };
    }

    case 'payment.failed': {

      await Promise.all([
        PaymentBooking.findByIdAndUpdate(
          paymentBooking._id,
          {
            status: paymentStatus.FAILED,
            razorpayPaymentId: payment.entity.id,
            paymentMethod: payment.entity.method?.toUpperCase(),
            paymentResponse: payment.entity,
            razorpaySignature: payment.entity.signature,
            failureReason: payment.entity.error_description || payment.entity.error_reason
          }
        ),
        TransactionBooking.findOneAndUpdate(
          { paymentId: paymentBooking._id },
          {
            status: transactionStatus.FAILED,
            transactionType: transactionType.PAYMENT,
            metadata: {
              ...payment.entity,
              razorpayOrderId: order.entity.id,
              failureReason: payment.entity.error_description || payment.entity.error_reason
            }
          }
        ),
        Booking.findByIdAndUpdate(
          paymentBooking.booking,
          { status: 'PENDING' }
        )
      ]);

      return {
        message: 'Payment failed',
        orderId: order.entity.id,
        paymentId: payment.entity.id,
        bookingId: paymentBooking.booking,
        bookingType: paymentBooking.type,
        reason: payment.entity.error_description || payment.entity.error_reason
      };
    }

    default:
      return {
        message: `Webhook event ${event} ignored`
      };
  }
};

//* Funtion to add whatsapp number to user 
const addWhatsAppNumberToUser = async ({ user, countryCode, phoneNumber, booking }) => {
  const alreadyExists = user.whatsappNumbers.some((num) => num.countryCode === countryCode && num.phoneNumber === phoneNumber);

  if (!alreadyExists) {
    user.whatsappNumbers.push({
      countryCode,
      phoneNumber,
      booking
    });
    await user.save();
  }
};

/**
 * Assign a pujari to a booking using round-robin assignment
 * @param {string} bookingId - The ID of the booking to assign a pujari to
 * @returns {Promise<Object>} The updated booking with assigned pujari
 */
const assignPujariToBooking = async (bookingId) => {
  // Get the booking details
  const booking = await Booking.findById(bookingId);

  if (!booking) {
    throwBadRequestError('Booking not found');
  }

  // Skip if booking already has an assigned pujari
  if (booking.assignedPujari) {
    return booking;
  }

  // Get all active and approved pujaris for this temple
  const pujaris = await Pujari.find({
    temple: booking.temple,
    status: 'ACTIVE',
    approvalStatus: 'APPROVED'
  }).sort({ templeSequenceNumber: 1 });

  if (!pujaris || pujaris.length === 0) {
    // No pujaris available for this temple
    return booking;
  }

  // Find the last booking of the same type, schedule, date and time slot that has an assigned pujari
  const lastBooking = await Booking.findOne({
    temple: booking.temple,
    date: booking.date,
    assignedPujari: { $ne: null },
    _id: { $ne: booking._id } // Exclude the current booking
  }).sort({ createdAt: -1 }).populate('assignedPujari');

  let nextPujariIndex = 0;

  if (lastBooking && lastBooking.assignedPujari) {
    // Find the index of the last assigned pujari in the pujaris array
    const lastPujariIndex = pujaris.findIndex(p =>
      p._id.toString() === lastBooking.assignedPujari._id.toString());

    if (lastPujariIndex !== -1) {
      // Get the next pujari in the sequence (round-robin)
      nextPujariIndex = (lastPujariIndex + 1) % pujaris.length;
    }
  }

  // Assign the next pujari
  const assignedPujari = pujaris[nextPujariIndex];

  // Update the booking with the assigned pujari
  const updatedBooking = await Booking.findByIdAndUpdate(
    bookingId,
    { assignedPujari: assignedPujari._id },
    { new: true }
  ).populate('assignedPujari');

  return updatedBooking;
};

const sendBookingConfirmationEmail = async (updatedBooking) => {
  const email = updatedBooking.user.email;
  const templatePath = path.join(__dirname, '../../views/bookingConfirmation.html');
  let bookingType = '';
  const bookingDates = [], individualPrice = [], couplePrice = [], familyPrice = [];

  let bookingEntity, serviceName, serviceCode, promotionalKit, shippingCharges;

  const address = await Address.findOne({ userId: updatedBooking.user, isDefault: true });

  if (updatedBooking.darshanSchedule) {
    bookingEntity = await DarshanSchedule.findById(updatedBooking.darshanSchedule);
    serviceName = 'Physical Darshan';
    serviceCode = bookingEntity.serviceCode;
    promotionalKit = bookingEntity.promotionalKit;
  } else if (updatedBooking.poojaSchedule) {
    bookingEntity = await PoojaSchedule.findById(updatedBooking.poojaSchedule);
    serviceName = updatedBooking.type === 'PHYSICAL_POOJA' ? 'Physical Puja' : 'Virtual Puja';
    serviceCode = bookingEntity.serviceCode;
    promotionalKit = updatedBooking.type === 'PHYSICAL_POOJA' ? bookingEntity.promotionalKit : null;
    shippingCharges = updatedBooking.type === 'VIRTUAL_POOJA' ? bookingEntity.shipping : null;
  } else if (updatedBooking.event) {
    bookingEntity = updatedBooking.event;
    serviceName = 'Event';
    serviceCode = bookingEntity.serviceCode;
    promotionalKit = null;
  }

  if (updatedBooking.type === 'PHYSICAL_POOJA' || updatedBooking.type === 'VIRTUAL_POOJA') {
    bookingType = updatedBooking.type === 'PHYSICAL_POOJA' ? 'Physical Puja' : 'Virtual Puja'; 
    bookingDates.push(updatedBooking.date.toDateString());
    individualPrice.push(
      {
        date: updatedBooking.date,
        price: bookingEntity.pricing.individualTaxablePrice,
        quantity: updatedBooking.individual,
        subtotal: updatedBooking.poojaSchedule.pricing.individual * updatedBooking.individual,
        taxableValue: bookingEntity.pricing.individualTaxablePrice * updatedBooking.individual,
        gstPercentage: bookingEntity.pricing.gstPercentage,
        gstCharges: bookingEntity.pricing.individualGstPrice * updatedBooking.individual,
        cgstPercentage: bookingEntity.pricing.gstPercentage / 2,
        cgstCharges: (bookingEntity.pricing.individualGstPrice / 2) * updatedBooking.individual,
        sgstPercentage: bookingEntity.pricing.gstPercentage / 2,
        sgstCharges: (bookingEntity.pricing.individualGstPrice / 2) * updatedBooking.individual,
        totalAmount: bookingEntity.pricing.individual * updatedBooking.individual
      }
    );
    couplePrice.push(
      {
        date: updatedBooking.date,
        price: bookingEntity.pricing.coupleTaxablePrice,
        quantity: updatedBooking.couple * 2,
        subtotal: updatedBooking.poojaSchedule.pricing.couple * updatedBooking.couple,
        taxableValue: bookingEntity.pricing.coupleTaxablePrice * updatedBooking.couple,
        gstPercentage: bookingEntity.pricing.gstPercentage,
        gstCharges: bookingEntity.pricing.coupleGstPrice * updatedBooking.couple,
        cgstPercentage: bookingEntity.pricing.gstPercentage / 2,
        cgstCharges: (bookingEntity.pricing.coupleGstPrice / 2) * updatedBooking.couple,
        sgstPercentage: bookingEntity.pricing.gstPercentage / 2,
        sgstCharges: (bookingEntity.pricing.coupleGstPrice / 2) * updatedBooking.couple,
        totalAmount: bookingEntity.pricing.couple * updatedBooking.couple
      }
    );
    familyPrice.push(
      {
        date: updatedBooking.date,
        price: bookingEntity.pricing.familyTaxablePrice,
        quantity: updatedBooking.family * 4,
        subtotal: updatedBooking.poojaSchedule.pricing.family * updatedBooking.family,
        taxableValue: bookingEntity.pricing.familyTaxablePrice * updatedBooking.family,
        gstPercentage: bookingEntity.pricing.gstPercentage,
        gstCharges: bookingEntity.pricing.familyGstPrice * updatedBooking.family,
        cgstPercentage: bookingEntity.pricing.gstPercentage / 2,
        cgstCharges: (bookingEntity.pricing.familyGstPrice / 2) * updatedBooking.family,
        sgstPercentage: bookingEntity.pricing.gstPercentage / 2,
        sgstCharges: (bookingEntity.pricing.familyGstPrice / 2) * updatedBooking.family,
        totalAmount: bookingEntity.pricing.family * updatedBooking.family
      }
    );
  } else if (updatedBooking.type === 'PHYSICAL_DARSHAN') {
    bookingType = 'Darshan';
    bookingDates.push(updatedBooking.date.toDateString());
    individualPrice.push(
      {
        date: updatedBooking.date,
        price: bookingEntity.pricing.individualTaxablePrice,
        quantity: updatedBooking.individual,
        subtotal: updatedBooking.darshanSchedule.pricing.individual * updatedBooking.individual,
        taxableValue: bookingEntity.pricing.individualTaxablePrice * updatedBooking.individual,
        gstPercentage: bookingEntity.pricing.gstPercentage,
        gstCharges: bookingEntity.pricing.individualGstPrice * updatedBooking.individual,
        cgstPercentage: bookingEntity.pricing.gstPercentage / 2,
        cgstCharges: (bookingEntity.pricing.individualGstPrice / 2) * updatedBooking.individual,
        sgstPercentage: bookingEntity.pricing.gstPercentage / 2,
        sgstCharges: (bookingEntity.pricing.individualGstPrice / 2) * updatedBooking.individual,
        totalAmount: bookingEntity.pricing.individual * updatedBooking.individual
      }
    );
    couplePrice.push(
      {
        date: updatedBooking.date,
        price: bookingEntity.pricing.coupleTaxablePrice,
        quantity: updatedBooking.couple * 2,
        subtotal: updatedBooking.darshanSchedule.pricing.couple * updatedBooking.couple,
        taxableValue: bookingEntity.pricing.coupleTaxablePrice * updatedBooking.couple,
        gstPercentage: bookingEntity.pricing.gstPercentage,
        gstCharges: bookingEntity.pricing.coupleGstPrice * updatedBooking.couple,
        cgstPercentage: bookingEntity.pricing.gstPercentage / 2,
        cgstCharges: (bookingEntity.pricing.coupleGstPrice / 2) * updatedBooking.couple,
        sgstPercentage: bookingEntity.pricing.gstPercentage / 2,
        sgstCharges: (bookingEntity.pricing.coupleGstPrice / 2) * updatedBooking.couple,
        totalAmount: bookingEntity.pricing.couple * updatedBooking.couple
      }
    );
    familyPrice.push(
      {
        date: updatedBooking.date,
        price: bookingEntity.pricing.familyTaxablePrice,
        quantity: updatedBooking.family * 4,
        subtotal: updatedBooking.darshanSchedule.pricing.family * updatedBooking.family,
        taxableValue: bookingEntity.pricing.familyTaxablePrice * updatedBooking.family,
        gstPercentage: bookingEntity.pricing.gstPercentage,
        gstCharges: bookingEntity.pricing.familyGstPrice * updatedBooking.family,
        cgstPercentage: bookingEntity.pricing.gstPercentage / 2,
        cgstCharges: (bookingEntity.pricing.familyGstPrice / 2) * updatedBooking.family,
        sgstPercentage: bookingEntity.pricing.gstPercentage / 2,
        sgstCharges: (bookingEntity.pricing.familyGstPrice / 2) * updatedBooking.family,
        totalAmount: bookingEntity.pricing.family * updatedBooking.family
      }
    );
  } else if (updatedBooking.type === 'EVENT') {
    bookingType = 'Event';
    updatedBooking.eventDates.forEach(dateObj => {
      bookingDates.push(dateObj.date.toDateString());
      individualPrice.push({
        date: dateObj.date,
        quantity: dateObj.pricing.individual.quantity,
      });
      couplePrice.push({
        date: dateObj.date,
        quantity: dateObj.pricing.couple.quantity * 2
      });
      familyPrice.push({
        date: dateObj.date,
        quantity: dateObj.pricing.family.quantity * 4
      });
    });
    updatedBooking.eventDateType === 'DATE_RANGE' ? bookingEntity.datePricing.forEach(datePrice => {
      const dateKey = datePrice.date.toDateString();
    
      // Individual
      const indiv = individualPrice.find(o => o.date.toDateString() === dateKey);

      if (indiv) {
        indiv.price = datePrice.pricing.individual;
        indiv.subtotal = indiv.price * indiv.quantity;
        indiv.taxableValue = datePrice.pricing.individualTaxablePrice * indiv.quantity;
        indiv.gstPercentage = datePrice.pricing.gstPercentage;
        indiv.gstCharges = datePrice.pricing.individualGstPrice * indiv.quantity;
        indiv.cgstPercentage = datePrice.pricing.gstPercentage / 2;
        indiv.cgstCharges = (datePrice.pricing.individualGstPrice / 2) * indiv.quantity;
        indiv.sgstPercentage = datePrice.pricing.gstPercentage / 2;
        indiv.sgstCharges = (datePrice.pricing.individualGstPrice / 2) * indiv.quantity;
        indiv.totalAmount = datePrice.pricing.individual * indiv.quantity;
      }
    
      // Couple
      const couple = couplePrice.find(o => o.date.toDateString() === dateKey);

      if (couple) {
        couple.price = datePrice.pricing.couple;
        couple.subtotal = couple.price * (couple.quantity / 2); // divide by 2 because qty was doubled
        couple.taxableValue = datePrice.pricing.coupleTaxablePrice * (couple.quantity / 2);
        couple.gstPercentage = datePrice.pricing.gstPercentage;
        couple.gstCharges = datePrice.pricing.coupleGstPrice * (couple.quantity / 2);
        couple.cgstPercentage = datePrice.pricing.gstPercentage / 2;
        couple.cgstCharges = (datePrice.pricing.coupleGstPrice / 2) * (couple.quantity / 2);
        couple.sgstPercentage = datePrice.pricing.gstPercentage / 2;
        couple.sgstCharges = (datePrice.pricing.coupleGstPrice / 2) * (couple.quantity / 2);
        couple.totalAmount = datePrice.pricing.couple * (couple.quantity / 2);
      }
    
      // Family
      const family = familyPrice.find(o => o.date.toDateString() === dateKey);

      if (family) {
        family.price = datePrice.pricing.family;
        family.subtotal = family.price * (family.quantity / 4); // divide by 4 because qty was x4
        family.taxableValue = datePrice.pricing.familyTaxablePrice * (family.quantity / 4);
        family.gstPercentage = datePrice.pricing.gstPercentage;
        family.gstCharges = datePrice.pricing.familyGstPrice * (family.quantity / 4);
        family.cgstPercentage = datePrice.pricing.gstPercentage / 2;
        family.cgstCharges = (datePrice.pricing.familyGstPrice / 2) * (family.quantity / 4);
        family.sgstPercentage = datePrice.pricing.gstPercentage / 2;
        family.sgstCharges = (datePrice.pricing.familyGstPrice / 2) * (family.quantity / 4);
        family.totalAmount = datePrice.pricing.family * (family.quantity / 4);
      }
    }) : (
      individualPrice.forEach(indiv => {
        indiv.price = bookingEntity.pricing.individual;
        indiv.subtotal = indiv.price * indiv.quantity;
        indiv.taxableValue = bookingEntity.pricing.individualTaxablePrice * indiv.quantity;
        indiv.gstPercentage = bookingEntity.pricing.gstPercentage;
        indiv.gstCharges = bookingEntity.pricing.individualGstPrice * indiv.quantity;
        indiv.cgstPercentage = bookingEntity.pricing.gstPercentage / 2;
        indiv.cgstCharges = (bookingEntity.pricing.individualGstPrice / 2) * indiv.quantity;
        indiv.sgstPercentage = bookingEntity.pricing.gstPercentage / 2;
        indiv.sgstCharges = (bookingEntity.pricing.individualGstPrice / 2) * indiv.quantity;
        indiv.totalAmount = bookingEntity.pricing.individual * indiv.quantity;
      }),
      couplePrice.forEach(couple => {
        couple.price = bookingEntity.pricing.couple;
        couple.subtotal = couple.price * (couple.quantity / 2); // divide by 2 because qty was doubled
        couple.taxableValue = bookingEntity.pricing.coupleTaxablePrice * (couple.quantity / 2);
        couple.gstPercentage = bookingEntity.pricing.gstPercentage;
        couple.gstCharges = bookingEntity.pricing.coupleGstPrice * (couple.quantity / 2);
        couple.cgstPercentage = bookingEntity.pricing.gstPercentage / 2;
        couple.cgstCharges = (bookingEntity.pricing.coupleGstPrice / 2) * (couple.quantity / 2);
        couple.sgstPercentage = bookingEntity.pricing.gstPercentage / 2;
        couple.sgstCharges = (bookingEntity.pricing.coupleGstPrice / 2) * (couple.quantity / 2);
        couple.totalAmount = bookingEntity.pricing.couple * (couple.quantity / 2);
      }),
      familyPrice.forEach(family => {
        family.price = bookingEntity.pricing.family;
        family.subtotal = family.price * (family.quantity / 4); // divide by 4 because qty was x4
        family.taxableValue = bookingEntity.pricing.familyTaxablePrice * (family.quantity / 4);
        family.gstPercentage = bookingEntity.pricing.gstPercentage;
        family.gstCharges = bookingEntity.pricing.familyGstPrice * (family.quantity / 4);
        family.cgstPercentage = bookingEntity.pricing.gstPercentage / 2;
        family.cgstCharges = (bookingEntity.pricing.familyGstPrice / 2) * (family.quantity / 4);
        family.sgstPercentage = bookingEntity.pricing.gstPercentage / 2;
        family.sgstCharges = (bookingEntity.pricing.familyGstPrice / 2) * (family.quantity / 4);
        family.totalAmount = bookingEntity.pricing.family * (family.quantity / 4);
      })
    );
  }

  const subject = `Your OneGod ${bookingType} Booking is Confirmed – ${updatedBooking.temple.name.en || updatedBooking.temple.name}`;
  
  //* Generate booking confirmation PDF
  const pdfKey = await generateBookingConfirmationPDF(updatedBooking, bookingType, bookingDates, individualPrice, couplePrice, familyPrice, serviceName, serviceCode, promotionalKit, address, shippingCharges);

  // Ticket PDF with booking details : 
  const attendeeDetails = await Promise.all([
    {
      name: updatedBooking.primaryDevoteeDetails?.fullName?.en || '',
      gender: updatedBooking.primaryDevoteeDetails.gender || '',
      qrCodeUrl: await QRCode.toDataURL(`${updatedBooking.primaryDevoteeDetails._id}|${updatedBooking._id}`)
    },
    ...updatedBooking.otherDevotees.map(async (devotee) => ({
      name: devotee?.fullName?.en,
      gender: devotee?.gender,
      qrCodeUrl: await QRCode.toDataURL(`${devotee._id}|${updatedBooking._id}`),
    }))
  ]);

  // Converting Logo file to Base64 for rendering: 
  const logoPath = path.join(__dirname, '../../../public/images/logo.png');
  const logoBuffer = await readFile(logoPath);
  const logoBase64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;

  let entityName = '';

  if (updatedBooking.darshanSchedule) {
    entityName = updatedBooking.darshanSchedule.name.en || updatedBooking.darshanSchedule.name;
  } else if (updatedBooking.poojaSchedule) {
    entityName = updatedBooking.poojaSchedule.name.en || updatedBooking.poojaSchedule.name;
  } else if (updatedBooking.event) {
    entityName = updatedBooking.event.name.en || updatedBooking.event.name;
  }

  const ticketDetails = {
    entityType: bookingType,
    bookingNumber: updatedBooking?.bookingNumber || 'QWERTY12345',
    entityImage: updatedBooking.temple?.images?.[0] || updatedBooking.event?.posterImage || '',
    entityName: entityName,
    bookingDate: bookingDates[0],
    startTime: updatedBooking.timeSlot?.startTime || '',
    endTime: updatedBooking.timeSlot?.endTime || '',
    templeName: updatedBooking.temple?.name?.en || updatedBooking.temple?.name,
    bookingAmount: updatedBooking?.totalAmount || 0,
    attendees: attendeeDetails,
    guidelines: updatedBooking.temple?.guidelines.en || updatedBooking.temple?.guidelines || '',
    supportContact: process.env.SUPPORT_CONTACT || '<EMAIL>',
    contactDetails: process.env.CONTACT_DETAILS || '080-122-76632',
    logo: logoBase64
  }; 

  const ticketKey = await generateTicketDetails(ticketDetails);
  const ticketUrl = process.env.MEDIA_URL + '/' + ticketKey;

  const data = { 
    bookingType,
    templeName: updatedBooking.temple.name.en || updatedBooking.temple.name,
    devoteeName: updatedBooking.primaryDevoteeDetails.fullName.en,
    bookingDates: bookingDates.join(', '),
    timeSlot: `${updatedBooking.timeSlot.startTime} - ${updatedBooking.timeSlot.endTime}`,
    numberOfPeople: 1 + updatedBooking.otherDevotees.length,
    bookingId: updatedBooking.bookingNumber,
    supportContact: process.env.SUPPORT_CONTACT,
    contactDetails: process.env.CONTACT_DETAILS,
    pdfUrl: process.env.MEDIA_URL + '/' + pdfKey,
    ticketUrl
  };

  await Promise.all([
    Booking.findByIdAndUpdate(
      updatedBooking._id,
      { bookingInvoice: process.env.MEDIA_URL + '/' + pdfKey, ticketPdfUrl: ticketUrl }
    ),
    sendMail(email, subject, templatePath, data)
  ]); 
};

module.exports = {
  createRazorpayOrder,
  handlePaymentWebhook,
  assignPujariToBooking,
  sendBookingConfirmationEmail,
};
