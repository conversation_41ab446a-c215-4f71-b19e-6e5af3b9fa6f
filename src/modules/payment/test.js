const crypto = require('crypto');

function generateMessageString (payload) {
  const sortedKeys = Object.keys(payload).sort();
  const concatenatedValues = sortedKeys
    .map(key => payload[key])
    .filter(value => value !== undefined && value !== null)
    .join('');
    
  return concatenatedValues;
}

const hmacDigest = (msg) => {
  // Create HMAC using SHA-256 algorithm
  const hmac = crypto.createHmac('sha256', "abc");
    
  // Update the HMAC with the message and compute the digest
  hmac.update(msg);

  return hmac.digest('hex');
};

function isWebhookHashValid(webhookPayload) {
  // Extract the secureHash from the webhook payload
  const receivedHash = webhookPayload.secureHash;

  // Remove secureHash from payload before generating our own hash
  const payloadWithoutHash = webhookPayload;
  
  delete payloadWithoutHash.secureHash;

  // Generate message string and calculate HMAC hash
  const messageString = generateMessageString(payloadWithoutHash);
  const expectedHash = hmacDigest(messageString);

  console.log('receivedHash : ', receivedHash);
  console.log('expectedHash: ', expectedHash);
  // Compare receivedHash with expectedHash
  return expectedHash === receivedHash;
}

console.log(isWebhookHashValid({
    "responseCode": "R1000",
    "merchantId": "T_03342",
    "aggregatorID": null,
    "merchantTxnNo": "7253901846726",
    "redirectURI": "https://qa.phicommerce.com/pg/api/v2/authRedirect",
    "showOTPCapturePage": "N",
    "generateOTPURI": null,
    "verifyOTPURI": null,
    "authorizeURI": null,
    "tranCtx": "R2ca2b2da-d62c-4951-b7ea-4a91467afff6",
    "secureHash": "f24e17e410bb153ceffe2c91951289f95b4ab777c92b2e243fbb7e0ffbda1a7e"
}));