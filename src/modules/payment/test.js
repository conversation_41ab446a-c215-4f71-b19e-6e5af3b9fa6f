const crypto = require('crypto');
const moment = require('moment');
const hmacDigest = (msg, keyString) => {
  // Create HMAC using SHA-256 algorithm
  const hmac = crypto.createHmac('sha256', keyString);
    
  // Update the HMAC with the message and compute the digest
  hmac.update(msg);

  // Return the hexadecimal digest
  return hmac.digest('hex');
};

const digest = hmacDigest('Test1Test2300.00356vaibhav.walunj1@phicommerce.com917498791441T_0334272539018467260https://58e9bd8b9504.ngrok-free.app/api/v1/payment/icici/webhookSALE202507241526389', 'abc');

console.log('digest : ', digest);

// const now = moment();
//   const msFirstDigit = String(now.millisecond()).padStart(3, '0').charAt(0);

//   const txnDate = now.format('YYYYMMDDHHmmss') + msFirstDigit;

//   console.log(txnDate);
