const crypto = require('crypto');

const hmacDigest = (msg, keyString) => {
  // Create HMAC using SHA-256 algorithm
  const hmac = crypto.createHmac('sha256', keyString);
    
  // Update the HMAC with the message and compute the digest
  hmac.update(msg);

  // Return the hexadecimal digest
  return hmac.digest('hex');
};

const digest = hmacDigest('300.00356vaibhav.walunj1@phicommerce.com917498791441T_0334227112024222330https://qa.phicommerce.com/pg/api/merchant197613d5ade172104bfd65e72bc07c80a14c65e64ec7335209768d83371243b4SALE202412051620545', 'amountcurrencyCodecustomerEmailIDcustomerMobileNomerchantIdmerchantTxnNopayTypereturnURLsecureHashtransactionTypetxnDate');

console.log('digest : ', digest);

