const { apiResponse, errorApiResponse } = require('../../config/responseHandler');
const { commonConstants } = require('../../constants/common');
const pujariService = require('./service');
const {
  createPujariSchema,
  uploadPujarisSchema,
  listPujarisSchema,
  templeAdminCreatePujariSchema,
  approvePujariSchema,
  editPujariSchema,
  validateId
} = require('./validation');
const { SUCCESS } = commonConstants;
const { saveAuditLog } = require('../../utils/auditLogger');
const { auditLogAction } = require('../../constants/dbEnums');

/**
 * Create a new pujari
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const createPujari = async (req, res) => {
  try {
    const { value, error } = createPujariSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const pujari = await pujariService.createPujari(value);

    //* Save audit log 
    const detail = `Pujari ${pujari.name.en}created successfully`;
    const model = 'Pujari';

    await saveAuditLog(req, req.user.id, auditLogAction.PUJARI_CREATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Pujari created successfully',
      status: true,
      data: pujari
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get pujari by ID
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getPujariById = async (req, res) => {
  try {
    const pujari = await pujariService.getPujariById(req.params.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Pujari retrieved successfully',
      status: true,
      data: pujari
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * List pujaris with optional temple filter
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const listPujaris = async (req, res) => {
  try {
    // Validate query parameters
    const { error, value } = listPujarisSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const { pujaris, pagination } = await pujariService.listPujaris(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Pujaris retrieved successfully',
      status: true,
      data: { pujaris, pagination }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * List pujaris with temple
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const listPujarisByTemple = async (req, res) => {
  try {
    // Validate query parameters
    const { error, value } = listPujarisSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const { templeId } = req.params;

    const { pujaris, pagination } = await pujariService.listPujarisByTemple(value, templeId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Pujaris retrieved successfully',
      status: true,
      data: { pujaris, pagination }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete pujari
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const deletePujari = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await pujariService.deletePujari(req.params.id);

    //* Save audit log 
    const detail = `Pujari ${data.name.en} deleted successfully`;
    const model = 'Pujari';

    await saveAuditLog(req, req.user.id, auditLogAction.PUJARI_DELETED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Pujari deleted successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Upload pujaris from XLSX file
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const uploadPujaris = async (req, res) => {
  try {
    // Validate temple ID
    const { error } = uploadPujarisSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    // Check if file is provided
    if (!req.file) {
      return res.status(400).json({
        status: false,
        message: 'No file uploaded'
      });
    }

    // Process file
    const result = await pujariService.processPujariUpload(req.file.path, req.body.temple);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Pujaris uploaded successfully',
      status: true,
      data: result
    });
  } catch (error) {
    // If there's an error, make sure to clean up the file
    if (req.file && req.file.path) {
      require('fs').unlinkSync(req.file.path);
    }
    return errorApiResponse(res, error);
  }
};

/**
 * Delete all pujaris for a specific temple
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const deleteAllPujarisForTemple = async (req, res) => {
  try {
    const { templeId } = req.params;

    if (!templeId || !/^[0-9a-fA-F]{24}$/.test(templeId)) {
      return res.status(400).json({
        status: false,
        message: 'Invalid temple ID format'
      });
    }

    const result = await pujariService.deleteAllPujarisForTemple(templeId);

    //* Save audit log 
    const detail = `All pujaris for temple ${result.temple} deleted successfully`;
    const model = 'Pujari';

    await saveAuditLog(req, req.user.id, auditLogAction.PUJARI_DELETED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: `Successfully deleted ${result.deletedCount} pujaris from temple ${result.temple}`,
      status: true,
      data: result
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Create a new pujari submission by temple admin
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const createPujariSubmission = async (req, res) => {
  try {
    const { error } = templeAdminCreatePujariSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const pujari = await pujariService.createPujariSubmission(req.body, req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Pujari submitted for approval successfully',
      status: true,
      data: pujari
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * List pujaris for a temple admin
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const listPujarisForTempleAdmin = async (req, res) => {
  try {
    const { error, value } = listPujarisSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const { pujaris, pagination } = await pujariService.listPujarisForTempleAdmin(value, req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Pujaris retrieved successfully',
      status: true,
      data: { pujaris, pagination }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Approve or reject a pujari submission
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const approvePujariSubmission = async (req, res) => {
  try {
    const { error } = approvePujariSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const pujari = await pujariService.approvePujariSubmission(req.params.id, req.body, req.user.id);

    //* Save audit log 
    const detail = `Pujari ${pujari.name.en}${req.body.approvalStatus === 'APPROVED' ? 'approved' : 'rejected'} successfully`;
    const model = 'Pujari';

    await saveAuditLog(req, req.user.id, auditLogAction.PUJARI_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: `Pujari ${req.body.approvalStatus === 'APPROVED' ? 'approved' : 'rejected'} successfully`,
      status: true,
      data: pujari
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Edit a pujari
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const editPujari = async (req, res) => {
  try {
    const { value, error } = editPujariSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const pujari = await pujariService.editPujari(req.params.id, value);

    //* Save audit log 
    const detail = `Pujari ${pujari.name.en}updated successfully`;
    const model = 'Pujari';

    await saveAuditLog(req, req.user.id, auditLogAction.PUJARI_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Pujari updated successfully',
      status: true,
      data: pujari
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createPujari,
  getPujariById,
  listPujaris,
  listPujarisByTemple,
  deletePujari,
  uploadPujaris,
  deleteAllPujarisForTemple,
  createPujariSubmission,
  listPujarisForTempleAdmin,
  approvePujariSubmission,
  editPujari
};
