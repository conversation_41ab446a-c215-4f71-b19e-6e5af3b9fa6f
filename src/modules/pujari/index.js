const express = require('express');
const router = express.Router();
const pujariController = require('./controller');
const auth = require('../../middleware/auth');
const { isAdminOrSuperAdmin, isTempleAdmin } = require('../../middleware/roleCheck');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../../../uploads');

if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);

    cb(null, 'pujari-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req, file, cb) => {
  // Accept only xlsx files
  if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
    cb(null, true);
  } else {
    cb(new Error('Only XLSX files are allowed'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB limit
});

// Admin and Super Admin routes
router.get('/', auth, isAdminOrSuperAdmin, pujariController.listPujaris);
router.post('/', auth, isAdminOrSuperAdmin, pujariController.createPujari);
router.put('/:id', auth, isAdminOrSuperAdmin, pujariController.editPujari);
router.delete('/:id', auth, isAdminOrSuperAdmin, pujariController.deletePujari);
router.delete('/temple/:templeId', auth, isAdminOrSuperAdmin, pujariController.deleteAllPujarisForTemple);
router.post('/approve/:id', auth, isAdminOrSuperAdmin, pujariController.approvePujariSubmission);

// Temple Admin routes
router.get('/temple-admin/list', auth, isTempleAdmin, pujariController.listPujarisForTempleAdmin);
router.post('/temple-admin/submit', auth, isTempleAdmin, pujariController.createPujariSubmission);
router.post('/upload', auth, isTempleAdmin, upload.single('file'), pujariController.uploadPujaris);

// Common routes
router.get('/:id', auth, pujariController.getPujariById);
router.get('/temple/:templeId', auth, pujariController.listPujarisByTemple);

module.exports = router;
