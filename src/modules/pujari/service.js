const Pujari = require('../../models/Pujari');
const Temple = require('../../models/Temple');
const TempleAdmin = require('../../models/TempleAdmin');
const { throwBadRequestError } = require('../../errors');
const XLSX = require('xlsx');
const fs = require('fs');
const Booking = require('../../models/Booking');
const { type, status } = require('../../constants/dbEnums');
const { translateDataForStore } = require('../../utils/translateInput');
const { transformTranslatedFields } = require('../../utils/localizer');

/**
 * Create a new pujari
 * @param {Object} pujariData - Pujari data
 * @returns {Promise<Object>} Created pujari
 */
const createPujari = async (pujariData) => {
  // Check if temple exists
  const temple = await Temple.findById(pujariData.temple);

  if (!temple) {
    throwBadRequestError('Temple not found');
  }

  // Build $or conditions to check existing pujari
  const orConditions = [
    {
      phoneNumber: pujariData.phoneNumber,
      approvalStatus: { $ne: 'REJECTED' }
    }
  ];

  if (pujariData.email) {
    orConditions.push({ email: pujariData.email });
  }

  const existingPujari = await Pujari.findOne({ $or: orConditions });

  if (existingPujari) {
    if (existingPujari.phoneNumber === pujariData.phoneNumber) {
      throwBadRequestError('Pujari with this phone number already exists');
    }

    if (pujariData.email && existingPujari.email === pujariData.email) {
      throwBadRequestError('Pujari with this email already exists');
    }
  }

  // Create pujari
  pujariData.approvalStatus = 'APPROVED';
  
  const highestSequence = await Pujari.findOne(
    { temple: pujariData.temple, approvalStatus: 'APPROVED' },
    { templeSequenceNumber: 1 }
  ).sort({ templeSequenceNumber: -1 }).collation({ locale: 'en', strength: 1 });

  const translatedData = await translateDataForStore([ 'name' ], pujariData);

  pujariData.name = translatedData.name;

  // Assign the next sequence number
  pujariData.templeSequenceNumber = highestSequence ? ((highestSequence.templeSequenceNumber || 0) + 1) : 1;
  const pujari = await Pujari.create(pujariData);

  return pujari;
};

/**
 * Get pujari by ID
 * @param {string} id - Pujari ID
 * @returns {Promise<Object>} Pujari
 */
const getPujariById = async (id) => {
  const pujari = await Pujari.findById(id).populate('temple').lean();

  if (!pujari) {
    throwBadRequestError('Pujari not found');
  }

  const language = { code: 'en' };

  return await transformTranslatedFields(pujari, language.code);
};

/**
 * List pujaris with optional temple filter
 * @param {Object} queryParams - Query parameters
 * @returns {Promise<Object>} Pujaris and pagination
 */
const listPujarisByTemple = async (queryParams, templeId) => {
  const { page = 1, limit = 10, status, search, approvalStatus, sortBy = 'createdAt', sortOrder = -1 } = queryParams;

  // Build filter
  const filter = {
    temple: templeId
  };

  if (approvalStatus) {
    filter.approvalStatus = approvalStatus;
  }

  if (status) {
    filter.status = status;
  }

  const language = { code: 'en' };

  // Add search functionality
  if (search) {
    filter.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } },
      { phoneNumber: { $regex: search, $options: 'i' } }
    ];
  }

  // If no search term, use the regular find method
  const total = await Pujari.countDocuments(filter);

  // Get paginated data
  // Create sort object based on sortBy and sortOrder
  const sortObject = {};

  sortObject[sortBy] = parseInt(sortOrder);

  const pujaris = await Pujari.find(filter)
    .populate('temple')
    .skip((parseInt(page) - 1) * parseInt(limit))
    .limit(parseInt(limit))
    .sort(sortObject).collation({ locale: 'en', strength: 1 }).lean();

  const pujarisWithLocalizedFields = await transformTranslatedFields(pujaris, language.code);

  return {
    pujaris: pujarisWithLocalizedFields,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

/**
 * List pujaris with optional temple filter
 * @param {Object} queryParams - Query parameters
 * @returns {Promise<Object>} Pujaris and pagination
 */
const listPujaris = async (queryParams) => {
  const { page = 1, limit = 10, temple, status, search, approvalStatus, sortBy, sortOrder } = queryParams;

  // Build filter
  const filter = {
    deletedAt: null
  };

  if (temple) {
    filter.temple = temple;
  }

  if (approvalStatus) {
    filter.approvalStatus = approvalStatus;
  }

  if (status) {
    filter.status = status;
  }

  const language = { code: 'en' };

  // Add search functionality
  if (search) {
    // We need to use aggregation to search across related collections
    const aggregatePipeline = [];

    // First, lookup temple details
    aggregatePipeline.push({
      $lookup: {
        from: 'temples',
        localField: 'temple',
        foreignField: '_id',
        as: 'temple'
      }
    });

    // Unwind the temple details
    aggregatePipeline.push({
      $unwind: {
        path: '$temple',
        preserveNullAndEmptyArrays: true
      }
    });

    // Add search filter
    aggregatePipeline.push({
      $match: {
        $or: [
          { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { [`temple.name.${language.code}`]: { $regex: search, $options: 'i' } }
        ]
      }
    });

    // Add other filters
    if (Object.keys(filter).length > 0) {
      aggregatePipeline.push({ $match: filter });
    }

    // Count total results
    const countPipeline = [ ...aggregatePipeline ];

    countPipeline.push({ $count: 'total' });
    const countResult = await Pujari.aggregate(countPipeline).collation({ locale: 'en', strength: 1 });

    const total = countResult.length > 0 ? countResult[0].total : 0;

    // Add pagination
    aggregatePipeline.push({ $skip: (parseInt(page) - 1) * parseInt(limit) });
    aggregatePipeline.push({ $limit: parseInt(limit) });

    // Create sort object based on sortBy and sortOrder
    const sortObject = {};

    if (sortBy === 'name') {
      sortObject[`name.${language.code}`] = parseInt(sortOrder);
    } else {
      sortObject[sortBy] = parseInt(sortOrder);
    }
    aggregatePipeline.push({ $sort: sortObject });

    // Execute the aggregation
    const pujaris = await Pujari.aggregate(aggregatePipeline).collation({ locale: 'en', strength: 1 });

    return {
      pujaris: await transformTranslatedFields(pujaris, language.code),
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    };
  }

  // If no search term, use the regular find method
  const total = await Pujari.countDocuments(filter);

  // Get paginated data
  // Create sort object based on sortBy and sortOrder
  const sortObject = {};

  if (sortBy === 'name') {
    sortObject[`name.${language.code}`] = parseInt(sortOrder);
  } else {
    sortObject[sortBy] = parseInt(sortOrder);
  }

  const pujaris = await Pujari.find(filter)
    .populate('temple')
    .skip((parseInt(page) - 1) * parseInt(limit))
    .limit(parseInt(limit))
    .sort(sortObject).collation({ locale: 'en', strength: 1 }).lean();

  const pujarisWithLocalizedFields = await transformTranslatedFields(pujaris, language.code);

  return {
    pujaris: pujarisWithLocalizedFields,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

//* Function to check if there are active bookings for a pujari
const hasUpcomingBookingForPujari = async (pujariId) => {
  const now = new Date();

  now.setUTCHours(0, 0, 0, 0);

  const bookings = await Booking.find({
    assignedPujari: pujariId,
    status: status.COMPLETED,
    $or: [
      {
        type: { $in: [ type.PHYSICAL_DARSHAN, type.PHYSICAL_POOJA, type.VIRTUAL_POOJA ] },
        date: { $gte: now }
      }
    ]
  });

  return bookings.some((booking) => {
    const bookingDate = new Date(booking.date);

    // Future date bookings
    if (bookingDate.getTime() > now.getTime()) {
      return true;
    }

    // Today's bookings – check if end time is still ahead
    const [ time, period ] = booking.timeSlot.endTime.split(' ');
    const [ hours, minutes ] = time.split(':');
    let bookingHours = parseInt(hours);

    // Convert to 24-hour format
    if (period === 'PM' && bookingHours !== 12) {
      bookingHours += 12;
    }
    if (period === 'AM' && bookingHours === 12) {
      bookingHours = 0;
    }

    const bookingEndTime = new Date(now);

    bookingEndTime.setHours(bookingHours, parseInt(minutes), 0, 0);

    const currentTime = new Date();

    return bookingEndTime > currentTime;
  });
};

/**
 * Delete pujari
 * @param {string} id - Pujari ID
 * @returns {Promise<void>}
 */
const deletePujari = async (id) => {
  //* Check if pujari exists 
  const pujari = await Pujari.findById(id);

  if (!pujari) {
    throwBadRequestError('Pujari not found');
  }
  
  if (pujari.deletedAt || pujari.status === 'INACTIVE') {
    throwBadRequestError('Pujari already deleted or inactive');
  }

  const upcomingBooking = await hasUpcomingBookingForPujari(id);

  if (upcomingBooking) {
    throwBadRequestError('Cannot delete pujari. They have upcoming booking.');
  }

  //* Mark assignedPujari value to null, for all bookings assigned to this pujari
  await Booking.updateMany(
    { assignedPujari: id },
    { assignedPujari: null }
  );

  //* Soft delete pujari 
  pujari.deletedAt = new Date();
  pujari.status = 'INACTIVE';
  await pujari.save();

  return pujari;
};

/**
 * Process and upload pujaris from XLSX file
 * @param {string} filePath - Path to the uploaded XLSX file
 * @param {string} templeId - Temple ID
 * @returns {Promise<Object>} Upload results
 */
const processPujariUpload = async (filePath, templeId) => {
  try {
    // Check if temple exists
    const temple = await Temple.findById(templeId);

    if (!temple) {
      throwBadRequestError('Temple not found');
    }

    // We'll allow adding more pujaris even if some already exist for this temple
    // Just log the count for information
    const existingPujarisCount = await Pujari.countDocuments({ temple: templeId });

    if (existingPujarisCount > 0) {
      throwBadRequestError('Temple already has pujaris. You are not allowed to add more.');
    }

    // Read the file
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    if (!data || data.length === 0) {
      throwBadRequestError('No data found in the uploaded file');
    }

    // Validate and prepare data
    const pujariData = [];
    const errors = [];

    // Process each row
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      const rowNumber = i + 2; // +2 because of 0-indexing and header row

      // Check required fields
      if (!row.name) {
        errors.push(`Row ${rowNumber}: Missing required field 'name'`);
        continue;
      }

      if (!row.phoneNumber) {
        errors.push(`Row ${rowNumber}: Missing required field 'phoneNumber'`);
        continue;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

      if (row.email && !emailRegex.test(row.email)) {
        errors.push(`Row ${rowNumber}: Invalid email format - ${row.email}`);
        continue;
      }

      // Validate phone number (10 digits)
      const phoneRegex = /^[0-9]{10}$/;

      if (!phoneRegex.test(row.phoneNumber)) {
        errors.push(`Row ${rowNumber}: Invalid phone number format - ${row.phoneNumber}`);
        continue;
      }

      // Check for duplicates in the file
      const email = row.email ? row.email.toLowerCase() : '';

      // Create pujari object with required fields
      const pujariObj = {
        name: row.name,
        phoneNumber: row.phoneNumber,
        temple: templeId,
        status: 'ACTIVE'
      };

      // Add email only if it's provided
      if (email) {
        pujariObj.email = email;
      }

      // Add to pujari data array
      pujariData.push(pujariObj);
    }

    // Insert valid pujaris
    let insertedCount = 0;

    if (pujariData.length > 0) {
      const result = await Pujari.insertMany(pujariData, { ordered: false }).catch(err => {
        // Handle duplicate key errors
        if (err.writeErrors) {
          err.writeErrors.forEach(writeError => {
            if (writeError.err.code === 11000) {
              const keyValue = writeError.err.keyValue;

              if (keyValue.email) {
                errors.push(`Duplicate email in database: ${keyValue.email}`);
              } else if (keyValue.phoneNumber) {
                errors.push(`Duplicate phone number in database: ${keyValue.phoneNumber}`);
              }
            }
          });
        }
        return { insertedCount: err.insertedDocs ? err.insertedDocs.length : 0 };
      });

      insertedCount = result.length || 0;
    }

    return {
      success: errors.length === 0,
      totalRows: data.length,
      insertedCount,
      errors,
      temple: temple.name
    };
  } finally {
    // Clean up: Delete the temporary file
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (err) {
      console.log('Error deleting temporary file:', err);
    }
  }
};

/**
 * Delete all pujaris for a specific temple
 * @param {string} templeId - Temple ID
 * @returns {Promise<Object>} Result with count of deleted pujaris
 */
const deleteAllPujarisForTemple = async (templeId) => {
  // Check if temple exists
  const temple = await Temple.findById(templeId);

  if (!temple) {
    throwBadRequestError('Temple not found');
  }

  // Soft delete all pujaris for this temple
  const result = await Pujari.updateMany(
    { temple: templeId },
    { deletedAt: new Date(), status: 'INACTIVE' },
    { new: true }
  );

  return {
    deletedCount: result.modifiedCount,
    temple: temple.name
  };
};

/**
 * Create a new pujari submission by temple admin
 * @param {Object} pujariData - Pujari data
 * @param {string} templeAdminId - Temple admin ID
 * @returns {Promise<Object>} Created pujari
 */
const createPujariSubmission = async (pujariData, templeAdminId) => {
  // Get temple admin details to get the temple
  const templeAdmin = await TempleAdmin.findById(templeAdminId);

  if (!templeAdmin) {
    throwBadRequestError('Temple admin not found');
  }

  // Check if temple exists
  const temple = await Temple.findById(templeAdmin.temple);

  if (!temple) {
    throwBadRequestError('Temple not found');
  }

  // Check if pujari with same WhatsApp number already exists for this temple
  const existingPujari = await Pujari.findOne({
    temple: templeAdmin.temple,
    phoneNumber: pujariData.phoneNumber
  });

  if (existingPujari) {
    throwBadRequestError('Pujari with this WhatsApp number already exists for this temple');
  }

  const translatedData = await translateDataForStore([ 'name' ], pujariData);

  pujariData.name = translatedData.name;

  // Create pujari with pending approval status
  const pujari = await Pujari.create({
    name: pujariData.name,
    phoneNumber: pujariData.phoneNumber,
    temple: templeAdmin.temple,
    status: 'ACTIVE',
    approvalStatus: 'PENDING_APPROVAL'
  });

  return pujari;
};

/**
 * List pujaris for a temple admin
 * @param {Object} queryParams - Query parameters
 * @param {string} templeAdminId - Temple admin ID
 * @returns {Promise<Object>} Pujaris and pagination
 */
const listPujarisForTempleAdmin = async (queryParams, templeAdminId) => {
  const { page = 1, limit = 10, approvalStatus, search, status, sortBy, sortOrder } = queryParams;

  // Get temple admin details to get the temple
  const templeAdmin = await TempleAdmin.findById(templeAdminId);

  if (!templeAdmin) {
    throwBadRequestError('Temple admin not found');
  }

  // Build filter
  const filter = {
    temple: templeAdmin.temple,
    deletedAt: null
  };

  if (approvalStatus) {
    filter.approvalStatus = approvalStatus;
  }

  if (status) {
    filter.status = status;
  }

  const language = { code: 'en' };

  // Add search functionality
  if (search) {
    filter.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
      { phoneNumber: { $regex: search, $options: 'i' } }
    ];
  }

  // Get total count
  const total = await Pujari.countDocuments(filter);

  // Get paginated data
  let sortConfig;

  if (sortBy && sortOrder) {
    // If sortBy and sortOrder are provided, use them
    sortConfig = {};
    sortConfig[sortBy] = parseInt(sortOrder);
  } else {
    // Otherwise use the default sorting
    sortConfig = {
      approvalStatus: 1, // APPROVED first, then PENDING_APPROVAL, then REJECTED
      templeSequenceNumber: 1, // Sort by sequence number for approved pujaris
      createdAt: -1 // Then by creation date (newest first)
    };
  }

  const pujaris = await Pujari.find(filter)
    .skip((parseInt(page) - 1) * parseInt(limit))
    .limit(parseInt(limit))
    .sort(sortConfig).collation({ locale: 'en', strength: 1 }).lean();

  const pujarisWithLocalizedFields = await transformTranslatedFields(pujaris, language.code);

  return {
    pujaris: pujarisWithLocalizedFields,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

/**
 * Approve or reject a pujari submission
 * @param {string} pujariId - Pujari ID
 * @param {Object} approvalData - Approval data
 * @param {string} adminId - Admin ID
 * @returns {Promise<Object>} Updated pujari
 */
const approvePujariSubmission = async (pujariId, approvalData, adminId) => {
  // Check if pujari exists
  const pujari = await Pujari.findById(pujariId);

  if (!pujari) {
    throwBadRequestError('Pujari not found');
  }

  // Check if pujari is already approved or rejected
  if (pujari.approvalStatus !== 'PENDING_APPROVAL') {
    throwBadRequestError(`Pujari is already ${pujari.approvalStatus.toLowerCase()}`);
  }

  // Update pujari with approval status
  const updateData = {
    approvalStatus: approvalData.approvalStatus,
    approvedBy: adminId,
    approvedAt: new Date()
  };

  // Add rejection reason if rejected
  if (approvalData.approvalStatus === 'REJECTED') {
    updateData.rejectionReason = approvalData.rejectionReason;
  }

  // If approved, assign a sequence number
  if (approvalData.approvalStatus === 'APPROVED') {
    // Get the highest sequence number for this temple
    const highestSequence = await Pujari.findOne(
      { temple: pujari.temple, approvalStatus: 'APPROVED' },
      { templeSequenceNumber: 1 }
    ).sort({ templeSequenceNumber: -1 }).collation({ locale: 'en', strength: 1 });

    // Assign the next sequence number
    updateData.templeSequenceNumber = highestSequence ? ((highestSequence.templeSequenceNumber || 0) + 1) : 1;
  }

  // Update the pujari
  const updatedPujari = await Pujari.findByIdAndUpdate(
    pujariId,
    updateData,
    { new: true }
  ).populate('temple approvedBy');

  return updatedPujari;
};

/**
 * Edit a pujari
 * @param {string} id - Pujari ID
 * @param {Object} pujariData - Updated pujari data
 * @returns {Promise<Object>} Updated pujari
 */
const editPujari = async (id, pujariData) => {
  // Check if pujari exists
  const pujari = await Pujari.findById(id);

  if (!pujari) {
    throwBadRequestError('Pujari not found');
  }

  const upcomingBooking = await hasUpcomingBookingForPujari(id);

  if (upcomingBooking) {
    throwBadRequestError('Cannot edit pujari. They have upcoming booking.');
  }

  // Check if another pujari with the same phone number exists (excluding this one)
  const existingPujariWithPhone = await Pujari.findOne({
    _id: { $ne: id },
    phoneNumber: pujariData.phoneNumber
  });

  if (existingPujariWithPhone) {
    throwBadRequestError('Another pujari with this phone number already exists');
  }

  // Check if another pujari with the same email exists (excluding this one)
  if (pujariData.email) {
    const existingPujariWithEmail = await Pujari.findOne({
      _id: { $ne: id },
      email: pujariData.email
    });

    if (existingPujariWithEmail) {
      throwBadRequestError('Another pujari with this email already exists');
    }
  }

  const translatedData = await translateDataForStore([ 'name' ], pujariData);

  pujariData.name = translatedData.name;

  // Update pujari
  const updatedPujari = await Pujari.findByIdAndUpdate(
    id,
    {
      name: pujariData.name,
      phoneNumber: pujariData.phoneNumber,
      email: pujariData.email || null
    },
    { new: true }
  ).populate('temple');

  return updatedPujari;
};

module.exports = {
  createPujari,
  getPujariById,
  listPujaris,
  listPujarisByTemple,
  deletePujari,
  processPujariUpload,
  deleteAllPujarisForTemple,
  createPujariSubmission,
  listPujarisForTempleAdmin,
  approvePujariSubmission,
  editPujari
};
