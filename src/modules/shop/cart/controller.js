const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const cartService = require('./service');
const { addToCartSchema, updateCartItemSchema } = require('./validation');
const { SUCCESS } = commonConstants;

/**
 * Get user's cart
 */
const getCart = async (req, res) => {
  try {
    const userId = req.user.id;

    const data = await cartService.getCart(userId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Add item to cart
 */
const addToCart = async (req, res) => {
  try {
    const { error } = addToCartSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const userId = req.user.id;
    const { productId, variantId, quantity } = req.body;

    const data = await cartService.addToCart({
      userId,
      productId,
      variantId,
      quantity,
      user: req.user
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: req.user?.preferredLanguage === 'Hindi' ? messages.ITEM_ADDED_TO_CART_HI : messages.ITEM_ADDED_TO_CART,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update cart item quantity
 */
const updateCartItem = async (req, res) => {
  try {
    const { error } = updateCartItemSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const userId = req.user.id;
    const { productId } = req.params;
    const { quantity, variantId } = req.body;

    const data = await cartService.updateCartItem({
      userId,
      productId,
      variantId,
      quantity,
      user: req.user
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: req.user?.preferredLanguage === 'Hindi' ? messages.CART_UPDATED_HI : messages.CART_UPDATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Remove item from cart
 */
const removeFromCart = async (req, res) => {
  try {
    const userId = req.user.id;
    const { productId } = req.params;
    const { variantId } = req.query;

    const data = await cartService.removeFromCart({
      userId,
      productId,
      variantId
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.ITEM_REMOVED_FROM_CART,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Clear cart
 */
const clearCart = async (req, res) => {
  try {
    const userId = req.user.id;

    const data = await cartService.clearCart(userId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.CART_CLEARED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart
};
