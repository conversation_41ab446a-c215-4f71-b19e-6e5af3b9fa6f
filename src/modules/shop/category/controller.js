const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const categoryService = require('./service');
const { SUCCESS } = commonConstants;

/**
 * Get all categories
 */
const getAllCategories = async (req, res) => {
  try {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, search } = req.query;
    const data = await categoryService.getAllCategories({
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      search,
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get category by ID
 */
const getCategoryById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const data = await categoryService.getCategoryById(id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getAllCategories,
  getCategoryById
};
