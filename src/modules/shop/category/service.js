const Category = require('../../../models/Category');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const mongoose = require('mongoose');

/**
 * Get all active categories
 */
const getAllCategories = async ({ page, limit, sortBy, sortOrder, search }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const matchStage = { isActive: true };
    
  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
    ];
  }
  const categories = await Category.find(matchStage)
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit);

  return categories;
};

/**
 * Get category by ID
 */
const getCategoryById = async (categoryId) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID);
  }

  const category = await Category.findOne({
    _id: categoryId,
    isActive: true
  });

  if (!category) {
    throwBadRequestError(messages.CATEGORY_NOT_FOUND);
  }

  return category;
};

module.exports = {
  getAllCategories,
  getCategoryById
};
