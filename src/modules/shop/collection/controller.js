const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const collectionService = require('./service');
const { SUCCESS } = commonConstants;

/**
 * Get all collections
 */
const getAllCollections = async (req, res) => {
  try {
    const data = await collectionService.getAllCollections(req.query);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get collections by category ID
 */
const getCollectionsByCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;
    
    const data = await collectionService.getCollectionsByCategory(categoryId, req.query);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get collection by ID
 */
const getCollectionById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const data = await collectionService.getCollectionById(id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getAllCollections,
  getCollectionsByCategory,
  getCollectionById
};
