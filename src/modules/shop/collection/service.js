const Collection = require('../../../models/Collection');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const mongoose = require('mongoose');

/**
 * Get all active collections
 */
const getAllCollections = async (query) => {
  const { page, limit, sortBy, sortOrder, search } = query;

  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const matchStage = {
    isActive: true
  };

  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
    ];
  }
  const collections = await Collection.find(matchStage)
    .populate('category')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit);

  return collections;
};

/**
 * Get collections by category
 */
const getCollectionsByCategory = async (categoryId, query) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID || 'Invalid category ID');
  }

  const { page, limit, sortBy, sortOrder, search } = query;

  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const matchStage = {
    category: categoryId,
    isActive: true
  };

  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
    ];
  }

  const collections = await Collection.find(matchStage)
    .populate('category')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit);

  return collections;
};

/**
 * Get collection by ID
 */
const getCollectionById = async (collectionId) => {
  // Check if collection ID is valid
  if (!mongoose.Types.ObjectId.isValid(collectionId)) {
    throwBadRequestError(messages.INVALID_COLLECTION_ID || 'Invalid collection ID');
  }

  const collection = await Collection.findOne({
    _id: collectionId,
    isActive: true
  })
    .populate('category products subCategories');

  if (!collection) {
    throwBadRequestError(messages.COLLECTION_NOT_FOUND || 'Collection not found');
  }

  return collection;
};

module.exports = {
  getAllCollections,
  getCollectionsByCategory,
  getCollectionById
};
