const Collection = require('../../../models/Collection');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const mongoose = require('mongoose');
const { transformTranslatedFields } = require('../../../utils/localizer');
const User = require('../../../models/User');
const Language = require('../../../models/Language');

/**
 * Get all active collections
 */
const getAllCollections = async (query, user) => {
  const { page, limit, sortBy, sortOrder, search } = query;
  let language = { code: 'en' };

  if (user) {
    const loggedInUser = await User.findById(user.id);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const matchStage = {
    isActive: true
  };

  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { [`name.${language.code || 'en'}`]: { $regex: search, $options: 'i' } },
      { [`description.${language.code || 'en'}`]: { $regex: search, $options: 'i' } },
    ];
  }
  const collections = await Collection.find(matchStage)
    .populate('category')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  return await transformTranslatedFields(collections, language.code);
};

/**
 * Get collections by category
 */
const getCollectionsByCategory = async (categoryId, query, user) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID || 'Invalid category ID');
  }

  const { page, limit, sortBy, sortOrder, search } = query;

  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  let language = { code: 'en' };

  if (user) {
    const loggedInUser = await User.findById(user.id);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  const matchStage = {
    category: categoryId,
    isActive: true
  };

  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { [`name.${language.code || 'en'}`]: { $regex: search, $options: 'i' } },
      { [`description.${language.code || 'en'}`]: { $regex: search, $options: 'i' } },
    ];
  }

  const collections = await Collection.find(matchStage)
    .populate('category')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  return await transformTranslatedFields(collections, language.code);
};

/**
 * Get collection by ID
 */
const getCollectionById = async (collectionId, user = null) => {
  // Check if collection ID is valid
  if (!mongoose.Types.ObjectId.isValid(collectionId)) {
    throwBadRequestError(messages.INVALID_COLLECTION_ID || 'Invalid collection ID');
  }

  let language = { code: 'en' };

  if (user) {
    const loggedInUser = await User.findById(user.id);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  const collection = await Collection.findOne({
    _id: collectionId,
    isActive: true
  })
    .populate('category products subCategories');

  if (!collection) {
    throwBadRequestError(messages.COLLECTION_NOT_FOUND || 'Collection not found');
  }

  return await transformTranslatedFields(collection, language.code);
};

module.exports = {
  getAllCollections,
  getCollectionsByCategory,
  getCollectionById
};
