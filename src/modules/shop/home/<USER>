const Category = require('../../../models/Category');
const ShopBanner = require('../../../models/ShopBanner');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const mongoose = require('mongoose');
const Collection = require('../../../models/Collection');
const SubCategory = require('../../../models/SubCategory');

const getAllHomeDetails = async () => {
  const [ categories, shopBanners ] = await Promise.all([
    Category.find({ isActive: true }).sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 }),
    ShopBanner.find({ isActive: true }).sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 })
  ]);

  return {
    categories,
    shopBanners
  };
};

const getCollectionsAndSubCategory = async (categoryId) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID);
  }

  const [ collections, subCategories ] = await Promise.all([
    Collection.find({
      category: categoryId,
      isActive: true
    }).sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 }).lean(),
    SubCategory.find({
      category: categoryId,
      isActive: true
    }).sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 }).lean()
  ]);

  collections.map(collection => {
    collection.type = 'COLLECTION';
  });

  subCategories.map(subCategory => {
    subCategory.type = 'SUB_CATEGORY';
  });

  const records = [ ...collections, ...subCategories ].sort((a, b) => a.createdAt - b.createdAt);

  return records;
};

module.exports = {
  getAllHomeDetails,
  getCollectionsAndSubCategory
};
