const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const { authenticate } = require('../../../utils/authenticate');
const categoryService = require('./service');
const { SUCCESS } = commonConstants;

const getAllHomeDetails = async (req, res) => {
  try {
    const user = await authenticate(req, res);
    const data = await categoryService.getAllHomeDetails(user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getCollectionsAndSubCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;
    const user = await authenticate(req, res);
    
    const data = await categoryService.getCollectionsAndSubCategory(categoryId, user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getAllHomeDetails,
  getCollectionsAndSubCategory
};
