const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const categoryService = require('./service');
const { SUCCESS } = commonConstants;

const getAllHomeDetails = async (req, res) => {
  try {
    const data = await categoryService.getAllHomeDetails();

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getCollectionsAndSubCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;
    
    const data = await categoryService.getCollectionsAndSubCategory(categoryId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getAllHomeDetails,
  getCollectionsAndSubCategory
};
