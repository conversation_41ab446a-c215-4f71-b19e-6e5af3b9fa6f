const Category = require('../../../models/Category');
const ShopBanner = require('../../../models/ShopBanner');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const mongoose = require('mongoose');
const Collection = require('../../../models/Collection');
const SubCategory = require('../../../models/SubCategory');
const { transformTranslatedFields } = require('../../../utils/localizer');
const User = require('../../../models/User');
const Language = require('../../../models/Language');

const getAllHomeDetails = async (user) => {
  const [ categories, shopBanners ] = await Promise.all([
    Category.find({ isActive: true }).sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 }).lean(),
    ShopBanner.find({ isActive: true }).sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 }).lean()
  ]);

  if (user) {
    let language = { code: 'en' }; // Default to English

    if (user.id) {
      const loggedInUser = await User.findById(user.id);

      if (loggedInUser && loggedInUser.preferredLanguage) {
        const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

        if (userLanguage) {
          language = userLanguage;
        }
      }
    }

    const localizedCategories = await transformTranslatedFields(categories, language.code);
    const localizedShopBanners = await transformTranslatedFields(shopBanners, language.code);

    return {
      categories: localizedCategories,
      shopBanners: localizedShopBanners
    };
  }

  const localizedCategoriesEn = await transformTranslatedFields(categories, 'en');
  const localizedShopBannersEn = await transformTranslatedFields(shopBanners, 'en');

  return {
    categories: localizedCategoriesEn,
    shopBanners: localizedShopBannersEn
  };
};

const getCollectionsAndSubCategory = async (categoryId, user) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID);
  }

  const [ collections, subCategories ] = await Promise.all([
    Collection.find({
      category: categoryId,
      isActive: true
    }).sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 }).lean(),
    SubCategory.find({
      category: categoryId,
      isActive: true
    }).sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 }).lean()
  ]);

  collections.map(collection => {
    collection.type = 'COLLECTION';
  });

  subCategories.map(subCategory => {
    subCategory.type = 'SUB_CATEGORY';
  });

  const records = [ ...collections, ...subCategories ].sort((a, b) => a.createdAt - b.createdAt);

  if (user) {
    let language = { code: 'en' }; // Default to English

    if (user.id) {
      const loggedInUser = await User.findById(user.id);

      if (loggedInUser && loggedInUser.preferredLanguage) {
        const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

        if (userLanguage) {
          language = userLanguage;
        }
      }
    }

    return await transformTranslatedFields(records, language.code);
  }

  return await transformTranslatedFields(records, 'en');
};

module.exports = {
  getAllHomeDetails,
  getCollectionsAndSubCategory
};
