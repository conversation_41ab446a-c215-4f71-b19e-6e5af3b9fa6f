const express = require('express');
const router = express.Router();

// Import sub-modules
const productRoutes = require('./product');
const categoryRoutes = require('./category');
const subCategoryRoutes = require('./subCategory');
const collectionRoutes = require('./collection');
const cartRoutes = require('./cart');
const orderRoutes = require('./order');
const paymentRoutes = require('./payment');
const shippingRoutes = require('./shipping');
const homeRoutes = require('./home');

// Register sub-modules
router.use('/products', productRoutes);
router.use('/categories', categoryRoutes);
router.use('/subcategories', subCategoryRoutes);
router.use('/collections', collectionRoutes);
router.use('/cart', cartRoutes);
router.use('/orders', orderRoutes);
router.use('/payments', paymentRoutes);
router.use('/shipping', shippingRoutes);
router.use('/home', homeRoutes);

module.exports = router;
