const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const orderService = require('./service');
const { createOrderSchema, cancelOrderSchema, generateInvoiceSchema } = require('./validation');
const { SUCCESS } = commonConstants;

/**
 * Create a new order
 */
const createOrder = async (req, res) => {
  try {
    const { error } = createOrderSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const userId = req.user.id;

    const data = await orderService.createOrder({
      userId,
      ...req.body
    });

    // Customize message if discount was applied
    let message = messages.ORDER_CREATED;

    if (data.order.discountAmount > 0) {
      message = `${messages.ORDER_CREATED} A discount of ₹${data.order.discountAmount} was applied.`;
    }

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: message,
      status: true,
      data: {
        order: data.order,
        payment: data.payment,
        razorpay: data.razorpay // Include Razorpay details for frontend
      }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get user's orders
 */
const getUserOrders = async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10, status, search } = req.query;

    const data = await orderService.getUserOrders({
      userId,
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      search
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get order by ID
 */
const getOrderById = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    const data = await orderService.getOrderById({
      userId,
      orderId: id
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Cancel order
 */
const cancelOrder = async (req, res) => {
  try {
    const { error } = cancelOrderSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const userId = req.user.id;
    const { id } = req.params;
    const { reason } = req.body;

    const data = await orderService.cancelOrder({
      userId,
      orderId: id,
      reason
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.ORDER_CANCELLED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Generate invoice for an order
 */
const generateOrderInvoice = async (req, res) => {
  try {
    const { error } = generateInvoiceSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const userId = req.user.id;
    const { orderId } = req.body;

    const data = await orderService.generateInvoiceForOrder(orderId, userId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Invoice generated successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createOrder,
  getUserOrders,
  getOrderById,
  cancelOrder,
  generateOrderInvoice
};
