const express = require('express');
const router = express.Router();
const orderController = require('./controller');
const auth = require('../../../middleware/auth');

// All order routes require authentication
router.use(auth);

router.post('/', orderController.createOrder);
router.get('/', orderController.getUserOrders);
router.get('/:id', orderController.getOrderById);
router.post('/:id/cancel', orderController.cancelOrder);
router.post('/invoice', orderController.generateOrderInvoice);

router.post('/shipping-charges', orderController.getShippingCharges);

module.exports = router;
