/* eslint-disable no-console */
/* eslint-disable max-lines-per-function */
const Order = require('../../../models/Order');
const Cart = require('../../../models/Cart');
const Product = require('../../../models/Product');
const ProductVariant = require('../../../models/ProductVariant');
const Payment = require('../../../models/Payment');
const Transaction = require('../../../models/Transaction');
const ProductShipment = require('../../../models/ProductShipment');
const User = require('../../../models/User');
const Discount = require('../../../models/Discount');
const { throwBadRequestError, throwNotFoundError, throwInternalServerError } = require('../../../errors');
const { messages } = require('../../../messages');
const { orderStatusValue, paymentStatusValue, productStatusValue, transactionTypeValue, transactionStatusValue, shipmentStatusValue, applicabilityType } = require('../../../constants/dbEnums');
const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');
const Razorpay = require('razorpay');
const { withTransaction, sessionOptions } = require('../../../utils/transactionHelper');
const { applyDiscount } = require('../../user/discount/service');
const { generateOrderInvoice } = require('../../../utils/pdfGenerator');
const { translateDataForStore } = require('../../../utils/translateInput');
const { transformTranslatedFields } = require('../../../utils/localizer');
const Language = require('../../../models/Language');

// We no longer need default admin ID as we're using product vendor information

/**
 * Helper function to convert Map to Object
 */
const convertToObject = (mapOrObj) => {
  if (!mapOrObj) {
    return {};
  }

  // If it's already an object, return it
  if (!(mapOrObj instanceof Map)) {
    return mapOrObj;
  }

  // Convert Map to Object
  const obj = {};

  mapOrObj.forEach((value, key) => {
    obj[key] = value;
  });
  return obj;
};

/**
 * Create a new order
 * @param {Object} params - Order parameters
 * @param {string} params.userId - User ID
 * @param {Object} params.shippingAddress - Shipping address
 * @param {Object} [params.billingAddress] - Billing address (optional)
 * @param {string} [params.notes] - Order notes (optional)
 * @param {Object} [params.buyNowItem] - Buy now item (optional)
 * @param {string} params.buyNowItem.productId - Product ID
 * @param {number} params.buyNowItem.quantity - Quantity
 * @param {string} [params.buyNowItem.variantId] - Variant ID (optional)
 * @param {string} [params.discountCode] - Discount code (optional)
 */
const createOrder = async ({ userId, shippingAddress, billingAddress, notes, buyNowItem, discount }) => {
  // Determine if this is a buy now order or a cart order
  let validItems = [];
  let cart = null;
  let isBuyNow = false;

  if (buyNowItem) {
    isBuyNow = true;
    // Buy now order - fetch product and variant if needed
    const product = await Product.findOne({
      _id: buyNowItem.productId,
      status: productStatusValue.ACTIVE
    });

    if (!product) {
      throwBadRequestError(messages.PRODUCT_NOT_FOUND);
    }

    let variant = null;

    if (buyNowItem.variantId) {
      variant = await ProductVariant.findOne({
        _id: buyNowItem.variantId,
        product: buyNowItem.productId
      });

      if (!variant) {
        throwBadRequestError(messages.VARIANT_NOT_FOUND);
      }
    }

    // Get vendor information
    const vendorId = product.createdBy || '67fdf9a02853feb8d13d99b6'; // Default to admin if not set
    const vendorModel = product.createdByModel || 'Admin'; // Default to Admin if not set

    // Create a valid item similar to cart item structure
    validItems = [{
      product,
      variant,
      quantity: buyNowItem.quantity,
      variantAttributes: variant ? variant.attributes : null,
      vendor: vendorId,
      vendorModel: vendorModel
    }];
  } else {
    // Cart order - get user's cart
    cart = await Cart.findOne({ user: userId })
      .populate({
        path: 'items.product',
        select: 'name price taxablePrice gstPercentage gstPrice vendorPrice vendorTaxablePrice vendorGstPrice discountPrice images stock status hasVariants createdBy createdByModel pickupAddress shipRocketPickupLocationName commissionPrice commissionType isCommissionBasedProduct isOneGodProduct',
        match: { status: productStatusValue.ACTIVE }
      })
      .populate({
        path: 'items.variant',
        select: 'price taxablePrice gstPercentage gstPrice vendorPrice vendorTaxablePrice vendorGstPrice discountPrice stock images attributes pickupAddress shipRocketPickupLocationName commissionPrice commissionType',
        match: { isActive: true }
      });

    if (!cart || cart.items.length === 0) {
      throwBadRequestError(messages.CART_EMPTY);
    }

    // Filter out any items where the product is null (inactive or deleted)
    validItems = cart.items.filter(item => item.product);

    if (validItems.length === 0) {
      throwBadRequestError(messages.CART_EMPTY);
    }

    // Add vendor information to each cart item
    for (const item of validItems) {
      // Skip items without product
      if (!item.product) {
        continue;
      }

      // Get vendor information directly from the product
      const vendorId = item.product.createdBy;
      const vendorModel = item.product.createdByModel;

      // Add vendor information directly to the item
      item.vendor = vendorId;
      item.vendorModel = vendorModel;
    }

    // Filter out any items with null products
    validItems = validItems.filter(item => item.product);
  }

  // Get user for email
  const user = await User.findById(userId);

  if (!user) {
    throwBadRequestError(messages.USER_NOT_FOUND);
  }

  if (user.isGuestUser) {
    throwBadRequestError('Please complete your profile to place an order');
  }

  let language = 'en';
  const userLanguage = await Language.findOne({ name: user.preferredLanguage });

  if (userLanguage && userLanguage.code) {
    language = userLanguage.code;
  }

  // Use transaction helper
  return await withTransaction(async (session) => {

    // Check stock availability and prepare order items
    const orderItems = [];
    let subtotal = 0;
    let subTotalTaxablePrice = 0;
    let subTotalGstPrice = 0;
    const stockUpdates = [];
    const variantStockUpdates = [];

    // Group items by vendor for shipment creation
    const vendorItems = {};

    for (const item of validItems) {
      const product = item.product;
      const variant = item.variant;

      // Make sure product exists before accessing its properties
      if (!product) {
        throwBadRequestError('Product not found in cart item');
        continue; // Skip this item
      }

      // Check if we're dealing with a variant or a regular product
      if (product.hasVariants && variant) {
        // Check if variant is in stock
        if (variant.stock < item.quantity) {
          throwBadRequestError(`${product.name[language]} (${Object.values(item.variantAttributes ? convertToObject(item.variantAttributes) : {}).join(', ')}) ${messages.INSUFFICIENT_STOCK}`);
        }

        // Calculate item price from variant
        const price = variant.discountPrice || variant.price;
        const gstPercentage = variant.gstPercentage || 0;
        const taxablePrice = variant.taxablePrice || 0;
        const gstPrice = variant.gstPrice || 0;

        const itemSubtotal = price * item.quantity;
        const itemTaxablePrice = taxablePrice * item.quantity;
        const itemGstPrice = gstPrice * item.quantity;

        const vendorPrice = variant.vendorPrice || 0;
        const vendorTaxablePrice = variant.vendorTaxablePrice || 0;
        const vendorGstPrice = variant.vendorGstPrice || 0;

        const vendorItemSubtotal = vendorPrice * item.quantity;
        const vendorItemTaxablePrice = vendorTaxablePrice * item.quantity;
        const vendorItemGstPrice = vendorGstPrice * item.quantity;

        const commissionPrice = variant.commissionPrice || 0;
        const commissionType = variant.commissionType;

        subtotal += itemSubtotal;
        subTotalTaxablePrice += itemTaxablePrice;
        subTotalGstPrice += itemGstPrice;

        // Convert variant attributes from Map to Object for storage
        const variantAttributesObj = {};

        // Process variant attributes
        if (variant.attributes) {
          // Check if attributes is a Map or an Object
          if (variant.attributes instanceof Map) {
            variant.attributes.forEach((value, key) => {
              variantAttributesObj[key] = value;
            });
          } else if (typeof variant.attributes === 'object') {
            // If it's a regular object, copy the properties
            Object.keys(variant.attributes).forEach(key => {
              variantAttributesObj[key] = variant.attributes[key];
            });
          }
        }

        // Get vendor information directly from the product
        const vendorId = product.createdBy;
        const vendorModel = product.createdByModel;

        // Add to order items with variant info
        const orderItem = {
          product: product._id,
          variant: variant._id,
          name: product.name,
          price: price,
          gstPercentage: gstPercentage,
          taxablePrice: taxablePrice,
          gstPrice: gstPrice,
          vendorPrice: vendorPrice,
          vendorTaxablePrice: vendorTaxablePrice,
          vendorGstPrice: vendorGstPrice,
          quantity: item.quantity,
          subtotal: itemSubtotal,
          subTotalTaxablePrice: itemTaxablePrice,
          subTotalGstPrice: itemGstPrice,
          vendorSubtotal: vendorItemSubtotal,
          vendorSubTotalTaxablePrice: vendorItemTaxablePrice,
          vendorSubTotalGstPrice: vendorItemGstPrice,
          commissionPrice: commissionPrice,
          commissionType: commissionType,
          variantAttributes: item.variantAttributes ? convertToObject(item.variantAttributes) : variantAttributesObj,
          vendor: vendorId,
          vendorModel: vendorModel,
          pickupAddress: variant.pickupAddress,
          shipRocketPickupLocationName: variant.shipRocketPickupLocationName,
          isCommissionBasedProduct: product.isCommissionBasedProduct,
          isOneGodProduct: product.isOneGodProduct
        };

        orderItems.push(orderItem);

        // Group by vendor for shipments
        const vendorKey = `${vendorModel}-${vendorId}-${variant.shipRocketPickupLocationName}`;

        if (!vendorItems[vendorKey]) {
          vendorItems[vendorKey] = [];
        }
        vendorItems[vendorKey].push({
          orderItem: orderItem,
          product: product,
          variant: variant,
          quantity: item.quantity
        });

        // Prepare variant stock update
        variantStockUpdates.push({
          updateOne: {
            filter: { _id: variant._id },
            update: { $inc: { stock: -item.quantity } }
          }
        });
      } else {
        // Regular product without variants
        // Check if product is in stock
        if (product.stock < item.quantity) {
          throwBadRequestError(`${product.name.en}${messages.INSUFFICIENT_STOCK}`);
        }

        // Calculate item price
        const price = product.discountPrice || product.price;
        const gstPercentage = product.gstPercentage || 0;
        const taxablePrice = product.taxablePrice || 0;
        const gstPrice = product.gstPrice || 0;

        const itemSubtotal = price * item.quantity;
        const itemTaxablePrice = taxablePrice * item.quantity;
        const itemGstPrice = gstPrice * item.quantity;

        const vendorPrice = product.vendorPrice || 0;
        const vendorTaxablePrice = product.vendorTaxablePrice || 0;
        const vendorGstPrice = product.vendorGstPrice || 0;

        const vendorItemSubtotal = vendorPrice * item.quantity;
        const vendorItemTaxablePrice = vendorTaxablePrice * item.quantity;
        const vendorItemGstPrice = vendorGstPrice * item.quantity;

        const commissionPrice = product.commissionPrice || 0;
        const commissionType = product.commissionType;

        subtotal += itemSubtotal;
        subTotalTaxablePrice += itemTaxablePrice;
        subTotalGstPrice += itemGstPrice;

        // Get vendor information directly from the product
        const vendorId = product.createdBy;
        const vendorModel = product.createdByModel;

        // Add to order items
        const orderItem = {
          product: product._id,
          name: product.name,
          price: price,
          gstPercentage: gstPercentage,
          taxablePrice: taxablePrice,
          gstPrice: gstPrice,
          vendorPrice: vendorPrice,
          vendorTaxablePrice: vendorTaxablePrice,
          vendorGstPrice: vendorGstPrice,
          quantity: item.quantity,
          subtotal: itemSubtotal,
          subTotalTaxablePrice: itemTaxablePrice,
          subTotalGstPrice: itemGstPrice,
          vendorSubtotal: vendorItemSubtotal,
          vendorSubTotalTaxablePrice: vendorItemTaxablePrice,
          vendorSubTotalGstPrice: vendorItemGstPrice,
          commissionPrice: commissionPrice,
          commissionType: commissionType,
          vendor: vendorId,
          vendorModel: vendorModel,
          pickupAddress: product.pickupAddress,
          shipRocketPickupLocationName: product.shipRocketPickupLocationName,
          isCommissionBasedProduct: product.isCommissionBasedProduct,
          isOneGodProduct: product.isOneGodProduct
        };

        orderItems.push(orderItem);

        // Group by vendor for shipments
        const vendorKey = `${vendorModel}-${vendorId}-${variant.shipRocketPickupLocationName}`;

        if (!vendorItems[vendorKey]) {
          vendorItems[vendorKey] = [];
        }
        vendorItems[vendorKey].push({
          orderItem: orderItem,
          product: product,
          variant: null,
          quantity: item.quantity
        });

        // Prepare stock update
        stockUpdates.push({
          updateOne: {
            filter: { _id: product._id },
            update: { $inc: { stock: -item.quantity } }
          }
        });
      }
    }

    // Calculate tax and shipping cost (simplified for now)
    // const taxRate = 0.18; // 18% GST
    // const tax = Math.round(subtotal * taxRate * 100) / 100;
    const shippingCost = subtotal > 1000 ? 0 : 100; // Free shipping for orders over ₹1000

    // Original total without discount
    const originalTotal = subtotal + shippingCost;

    // Initialize discount variables
    let discountAmount = 0;
    let finalTotal = originalTotal;

    // Apply discount if provided
    if (discount) {
      try {
        // Find the discount by code
        const discountData = await Discount.findById(discount);

        if (!discountData || discountData.discountStatus !== 'ACTIVE') {
          throwBadRequestError('Invalid discount code');
        }

        // Check if discount is applicable for products (not temple specific)
        if (discountData.applicabilityType === applicabilityType.SPECIFIC_TEMPLES) {
          throwBadRequestError('This discount code is not applicable for product orders');
        }

        const productIds = orderItems.map(item => item.product.toString());

        // Apply discount using the existing applyDiscount function
        const discountResult = await applyDiscount(userId, {
          discountCode: discountData.code,
          totalAmount: originalTotal,
          products: discountData.applicabilityType === applicabilityType.SPECIFIC_PRODUCTS ?
            productIds : null
        });

        if (discountResult && discountResult.isValid) {
          discountAmount = discountResult.discountAmount;
          finalTotal = originalTotal - discountAmount;
        }
      } catch (error) {
        throwBadRequestError(error.message);
      }
    }

    // Generate order number
    const orderNumber = `ORD-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    // Create Razorpay order first

    let razorpayOrder;
    
    try {
      const razorpay = new Razorpay({
        key_id: process.env.RAZORPAY_KEY_ID,
        key_secret: process.env.RAZORPAY_KEY_SECRET
      });

      razorpayOrder = await razorpay.orders.create({
        amount: parseInt(finalTotal.toFixed(2) * 100), // Razorpay expects amount in smallest currency unit (paise)
        currency: 'INR',
        receipt: orderNumber,
        notes: {
          userId: userId.toString()
        }
      });
    } catch (error) {
      console.log('Razorpay order creation error:', error);
      throwInternalServerError(`Error From razorpay order creation : ${JSON.stringify(error)}`);
    }

    console.log('Razorpay Order created:', razorpayOrder);

    // Check if we have order items
    if (orderItems.length === 0) {
      throwBadRequestError('No valid items in cart');
    }

    // Translate Notes
    if (notes) {
      const translatedNotes = await translateDataForStore([ 'notes' ], { notes });

      notes = translatedNotes.notes;
    }

    // Create order
    const order = new Order({
      orderNumber,
      user: userId,
      items: orderItems,
      subtotal,
      subTotalTaxablePrice,
      subTotalGstPrice,
      shippingCost,
      discountAmount: discountAmount, // Set the discount amount
      total: finalTotal, // Set the final total after discount
      originalTotal, // Store the original total for vendor calculations
      discount: discount, // Store the discount code if applied
      shippingAddress,
      billingAddress: billingAddress || shippingAddress, // Use shipping address if billing not provided
      paymentMethod: 'RAZORPAY', // Only Razorpay is supported
      orderStatus: orderStatusValue.PENDING,
      paymentStatus: paymentStatusValue.PENDING,
      notes,
      isBuyNow
    });

    // Save the order to get IDs for the order items
    await order.save(sessionOptions(session));

    // Create a mapping from our temporary orderItem objects to the saved order items with IDs
    const orderItemMap = new Map();

    orderItems.forEach((tempItem, index) => {
      orderItemMap.set(tempItem, order.items[index]);
    });

    // Create payment record with Razorpay order ID
    const payment = new Payment({
      order: order._id,
      user: userId,
      amount: finalTotal,
      paymentMethod: 'RAZORPAY',
      status: paymentStatusValue.PENDING,
      razorpayOrderId: razorpayOrder.id
    });

    await payment.save(sessionOptions(session));

    // Create transaction record
    const transaction = new Transaction({
      transactionId: `TXN-${uuidv4()}`,
      order: order._id,
      payment: payment._id,
      user: userId,
      amount: finalTotal,
      type: transactionTypeValue.PAYMENT,
      status: transactionStatusValue.PENDING
    });

    await transaction.save(sessionOptions(session));

    // Instead of immediately updating stock, we'll create a "hold" on the stock
    // The actual stock reduction will happen when payment is confirmed
    // We'll store the stock updates in the order object for later use
    order.stockUpdates = stockUpdates.length > 0 ? stockUpdates : [];
    order.variantStockUpdates = variantStockUpdates.length > 0 ? variantStockUpdates : [];
    order.stockReduced = false; // Flag to track if stock has been reduced

    await order.save(sessionOptions(session));

    // Create shipments for each vendor
    const shipments = [];

    // If no vendor items, create a default shipment with admin as vendor
    if (Object.keys(vendorItems).length === 0) {
      // Creating default shipment with admin as vendor

      // If we have no vendor items, we'll need to get the vendor from the first order item
      const firstOrderItem = orderItems[0];
      const vendorId = firstOrderItem.vendor;
      const vendorModel = firstOrderItem.vendorModel;
      const shipRocketPickupLocationName = firstOrderItem.shipRocketPickupLocationName;

      // First save the order items to get their IDs
      await order.save(sessionOptions(session));

      // Create a default shipment with all order items
      const defaultShipment = new ProductShipment({
        order: order._id,
        orderItems: order.items.map(item => ({
          orderItem: item._id, // Use the saved order item ID
          product: item.product, // Product ID reference
          variant: item.variant || null, // Variant ID reference if it exists
          quantity: item.quantity,
          price: item.price,
          gstPercentage: item.gstPercentage,
          taxablePrice: item.taxablePrice,
          gstPrice: item.gstPrice,
          vendorPrice: item.vendorPrice,
          vendorTaxablePrice: item.vendorTaxablePrice,
          vendorGstPrice: item.vendorGstPrice,
          subtotal: item.subtotal,
          subTotalTaxablePrice: item.subTotalTaxablePrice,
          subTotalGstPrice: item.subTotalGstPrice,
          vendorSubtotal: item.vendorSubtotal,
          vendorSubTotalTaxablePrice: item.vendorSubTotalTaxablePrice,
          vendorSubTotalGstPrice: item.vendorSubTotalGstPrice,
          commissionPrice: item.commissionPrice,
          commissionType: item.commissionType,
          name: item.name,
          variantAttributes: item.variantAttributes || {},
          pickupAddress: item.variant ? item.variant.pickupAddress : item.product.pickupAddress,
          shipRocketPickupLocationName: item.variant ? item.variant.shipRocketPickupLocationName : item.product.shipRocketPickupLocationName,
          isCommissionBasedProduct: item.isCommissionBasedProduct,
          isOneGodProduct: item.isOneGodProduct
        })),
        vendor: vendorId,
        vendorModel: vendorModel,
        shipRocketPickupLocationName: shipRocketPickupLocationName,
        status: shipmentStatusValue.PENDING,
        deliveryAddress: shippingAddress,
        isOneGodProduct: order.items[0].isOneGodProduct,
        isCommissionBasedProduct: order.items[0].isCommissionBasedProduct
      });

      await defaultShipment.save(sessionOptions(session));
      shipments.push(defaultShipment);
    } else {
      // Create shipments for each vendor
      for (const vendorKey in vendorItems) {
        const [ vendorModel, vendorId, shipRocketPickupLocationName ] = vendorKey.split('-');

        let isOneGodProduct = false;
        let isCommissionBasedProduct = false;

        // Create shipment record
        const shipment = new ProductShipment({
          order: order._id,
          orderItems: vendorItems[vendorKey].map(item => {
            // Get the saved order item with ID from our mapping
            const savedOrderItem = orderItemMap.get(item.orderItem);

            // If we can't find the saved order item, use a fallback
            if (!savedOrderItem) {
              // Find the order item in the order items array
              const orderItem = order.items.find(oi =>
                oi.product.toString() === item.product._id.toString() &&
                (item.variant ? oi.variant?.toString() === item.variant._id.toString() : !oi.variant)
              );

              if (!orderItem) {
                // If we can't find the order item, use a fallback with minimal information
                isOneGodProduct = item.product.isOneGodProduct;
                isCommissionBasedProduct = item.product.isCommissionBasedProduct;
                return {
                  orderItem: item.orderItem._id || order.items[0]._id, // Fallback to first order item if needed
                  product: item.product._id,
                  variant: item.variant ? item.variant._id : null,
                  quantity: item.quantity,
                  price: item.product.discountPrice || item.product.price,
                  gstPercentage: item.product.gstPercentage || 0,
                  taxablePrice: item.product.taxablePrice,
                  gstPrice: item.product.gstPrice,
                  vendorPrice: item.product.vendorPrice || 0,
                  vendorTaxablePrice: item.product.vendorTaxablePrice || 0,
                  vendorGstPrice: item.product.vendorGstPrice || 0,
                  subtotal: (item.product.discountPrice || item.product.price) * item.quantity,
                  subTotalTaxablePrice: (item.product.taxablePrice) * item.quantity,
                  subTotalGstPrice: (item.product.gstPrice) * item.quantity,
                  vendorSubtotal: (item.product.vendorPrice || 0) * item.quantity,
                  vendorSubTotalTaxablePrice: (item.product.vendorTaxablePrice || 0) * item.quantity,
                  vendorSubTotalGstPrice: (item.product.vendorGstPrice || 0) * item.quantity,
                  commissionPrice: item.product.commissionPrice || 0,
                  commissionType: item.product.commissionType,
                  name: item.product.name,
                  variantAttributes: item.variantAttributes || {},
                  pickupAddress: item.variant ? item.variant.pickupAddress : item.product.pickupAddress,
                  shipRocketPickupLocationName: item.variant ? item.variant.shipRocketPickupLocationName : item.product.shipRocketPickupLocationName,
                  isCommissionBasedProduct: item.product.isCommissionBasedProduct,
                  isOneGodProduct: item.product.isOneGodProduct
                };
              }

              // Use the found order item

              isOneGodProduct = orderItem.isOneGodProduct;
              isCommissionBasedProduct = orderItem.isCommissionBasedProduct;

              return {
                orderItem: orderItem._id,
                product: item.product._id,
                variant: item.variant ? item.variant._id : null,
                quantity: item.quantity,
                price: orderItem.price,
                gstPercentage: orderItem.gstPercentage,
                taxablePrice: orderItem.taxablePrice,
                gstPrice: orderItem.gstPrice,
                vendorPrice: orderItem.vendorPrice,
                vendorTaxablePrice: orderItem.vendorTaxablePrice,
                vendorGstPrice: orderItem.vendorGstPrice,
                subtotal: orderItem.subtotal,
                subTotalTaxablePrice: orderItem.subTotalTaxablePrice,
                subTotalGstPrice: orderItem.subTotalGstPrice,
                vendorSubtotal: orderItem.vendorSubtotal,
                vendorSubTotalTaxablePrice: orderItem.vendorSubTotalTaxablePrice,
                vendorSubTotalGstPrice: orderItem.vendorSubTotalGstPrice,
                commissionPrice: orderItem.commissionPrice,
                commissionType: orderItem.commissionType,
                name: orderItem.name,
                variantAttributes: orderItem.variantAttributes || {},
                pickupAddress: item.variant ? item.variant.pickupAddress : item.product.pickupAddress,
                shipRocketPickupLocationName: item.variant ? item.variant.shipRocketPickupLocationName : item.product.shipRocketPickupLocationName,
                isCommissionBasedProduct: orderItem.isCommissionBasedProduct,
                isOneGodProduct: orderItem.isOneGodProduct
              };
            }

            // Use the saved order item
            isOneGodProduct = savedOrderItem.isOneGodProduct;
            isCommissionBasedProduct = savedOrderItem.isCommissionBasedProduct;

            return {
              orderItem: savedOrderItem._id, // Use the saved order item ID
              product: item.product._id,
              variant: item.variant ? item.variant._id : null,
              quantity: item.quantity,
              price: savedOrderItem.price,
              gstPercentage: savedOrderItem.gstPercentage,
              taxablePrice: savedOrderItem.taxablePrice,
              gstPrice: savedOrderItem.gstPrice,
              vendorPrice: savedOrderItem.vendorPrice,
              vendorTaxablePrice: savedOrderItem.vendorTaxablePrice,
              vendorGstPrice: savedOrderItem.vendorGstPrice,
              subtotal: savedOrderItem.subtotal,
              subTotalTaxablePrice: savedOrderItem.subTotalTaxablePrice,
              subTotalGstPrice: savedOrderItem.subTotalGstPrice,
              vendorSubtotal: savedOrderItem.vendorSubtotal,
              vendorSubTotalTaxablePrice: savedOrderItem.vendorSubTotalTaxablePrice,
              vendorSubTotalGstPrice: savedOrderItem.vendorSubTotalGstPrice,
              commissionPrice: savedOrderItem.commissionPrice,
              commissionType: savedOrderItem.commissionType,
              name: savedOrderItem.name,
              variantAttributes: savedOrderItem.variantAttributes || {},
              pickupAddress: item.variant ? item.variant.pickupAddress : item.product.pickupAddress,
              shipRocketPickupLocationName: item.variant ? item.variant.shipRocketPickupLocationName : item.product.shipRocketPickupLocationName,
              isCommissionBasedProduct: savedOrderItem.isCommissionBasedProduct,
              isOneGodProduct: savedOrderItem.isOneGodProduct
            };
          }),
          vendor: vendorId,
          vendorModel: vendorModel,
          shipRocketPickupLocationName: shipRocketPickupLocationName,
          status: shipmentStatusValue.PENDING,
          deliveryAddress: shippingAddress,
          isOneGodProduct: isOneGodProduct,
          isCommissionBasedProduct: isCommissionBasedProduct
        });

        await shipment.save(sessionOptions(session));
        shipments.push(shipment);
      }
    }

    // Add shipments to order
    order.shipments = shipments.map(s => s._id);
    await order.save(sessionOptions(session));

    // Return order with payment info and Razorpay details
    return {
      order,
      payment,
      razorpay: {
        orderId: razorpayOrder.id,
        amount: finalTotal * 100, // Amount in smallest currency unit (paise)
        currency: 'INR',
        prefill: {
          name: order.shippingAddress.name,
          contact: order.shippingAddress.phone,
          email: user.email
        }
      }
    };
  });
};

/**
 * Get user's orders
 */
const getUserOrders = async ({ userId, page, limit, status, search }) => {
  const skip = (page - 1) * limit;

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  const query = { user: userId, orderStatus: { $ne: orderStatusValue.PENDING } };

  if (status) {
    query.orderStatus = status;
  }

  if (search) {
    query.$or = [
      { orderNumber: { $regex: search, $options: 'i' } },
      { 'shippingAddress.name': { $regex: search, $options: 'i' } },
      { 'shippingAddress.phone': { $regex: search, $options: 'i' } },
      { [`items.name.${language.code}`]: { $regex: search, $options: 'i' } }
    ];
  }

  const orders = await Order.find(query)
    .populate('items.product items.variant shipments')
    .sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  const total = await Order.countDocuments(query);

  // Localize orders
  const localizedOrders = await transformTranslatedFields(orders, language.code);

  return {
    orders: localizedOrders,
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get order by ID
 */
const getOrderById = async ({ userId, orderId }) => {
  // Check if order ID is valid
  if (!mongoose.Types.ObjectId.isValid(orderId)) {
    throwBadRequestError(messages.INVALID_ORDER_ID);
  }

  const order = await Order.findOne({
    _id: orderId,
    user: userId
  }).populate('items.product items.variant shipments').lean();

  if (!order) {
    throwBadRequestError(messages.ORDER_NOT_FOUND);
  }

  // Get payment info
  const payment = await Payment.findOne({ order: orderId });

  // Get shipping info if available
  // In a real implementation, we would fetch shipping details here

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  const localizedOrder = await transformTranslatedFields(order, language.code);

  return {
    order: localizedOrder,
    payment
  };
};

/**
 * Cancel order
 */
const cancelOrder = async ({ userId, orderId, reason }) => {
  // Check if order ID is valid
  if (!mongoose.Types.ObjectId.isValid(orderId)) {
    throwBadRequestError(messages.INVALID_ORDER_ID);
  }

  const order = await Order.findOne({
    _id: orderId,
    user: userId
  });

  if (!order) {
    throwBadRequestError(messages.ORDER_NOT_FOUND);
  }

  // Check if order can be cancelled
  if (order.orderStatus === orderStatusValue.CANCELLED) {
    throwBadRequestError(messages.ORDER_ALREADY_CANCELLED);
  }

  if (order.orderStatus !== orderStatusValue.PENDING && order.orderStatus !== orderStatusValue.PROCESSING) {
    throwBadRequestError(messages.ORDER_CANNOT_BE_CANCELLED);
  }

  // Use transaction helper
  return await withTransaction(async (session) => {

    const translatedReason = await translateDataForStore([ 'reason' ], { reason });

    reason = translatedReason.reason;

    // Update order status
    order.orderStatus = orderStatusValue.CANCELLED;
    order.cancelReason = reason;
    order.cancelledAt = new Date();
    await order.save(sessionOptions(session));

    // Update payment status if needed
    if (order.paymentStatus === paymentStatusValue.COMPLETED) {
      // In a real implementation, we would initiate a refund here
      const payment = await Payment.findOne({ order: orderId });

      if (payment) {
        payment.status = paymentStatusValue.REFUNDED;
        payment.refundAmount = payment.amount;
        payment.refundReason = 'Order cancelled';
        payment.refundedAt = new Date();
        await payment.save(sessionOptions(session));
      }
    } else {
      // If payment was pending, mark it as failed
      const payment = await Payment.findOne({ order: orderId });

      if (payment) {
        payment.status = paymentStatusValue.FAILED;
        payment.failureReason = 'Order cancelled';
        await payment.save(sessionOptions(session));
      }
    }

    // Restore product stock
    const stockUpdates = [];
    const variantStockUpdates = [];

    for (const item of order.items) {
      if (item.variant) {
        // Restore variant stock
        variantStockUpdates.push({
          updateOne: {
            filter: { _id: item.variant },
            update: { $inc: { stock: item.quantity } }
          }
        });
      } else {
        // Restore product stock
        stockUpdates.push({
          updateOne: {
            filter: { _id: item.product },
            update: { $inc: { stock: item.quantity } }
          }
        });
      }
    }

    if (stockUpdates.length > 0) {
      await Product.bulkWrite(stockUpdates, sessionOptions(session));
    }

    if (variantStockUpdates.length > 0) {
      await ProductVariant.bulkWrite(variantStockUpdates, sessionOptions(session));
    }

    return { success: true };
  });
};

/**
 * Generate an invoice PDF for an order
 * @param {String} orderId - Order ID
 * @param {String} userId - User ID (for authorization)
 * @returns {Promise<Object>} Object with invoice URL
 */
const generateInvoiceForOrder = async (orderId, userId) => {
  // Check if order ID is valid
  if (!mongoose.Types.ObjectId.isValid(orderId)) {
    throwBadRequestError(messages.INVALID_ORDER_ID);
  }

  // Find the order and verify it belongs to the user
  const order = await Order.findOne({
    _id: orderId,
    user: userId,
    status: { $ne: orderStatusValue.PENDING }
  }).populate('items.product items.variant');

  if (!order) {
    throwNotFoundError(messages.ORDER_NOT_FOUND);
  }

  if (order.userInvoice) {
    return {
      invoiceUrl: order.userInvoice
    };
  }

  // Get user details
  const user = await User.findById(userId);

  if (!user) {
    throwNotFoundError('User not found');
  }

  // Get payment details
  const payment = await Payment.findOne({ order: orderId });

  if (!payment) {
    throwNotFoundError(messages.PAYMENT_RECORD_NOT_FOUND);
  }

  // Format order items for the invoice
  const items = order.items.map(item => {
    return {
      name: item.name.en,
      quantity: item.quantity,
      taxablePrice: item.taxablePrice,
      subtotal: item.subTotalTaxablePrice + (parseFloat(item.gstPrice) * item.quantity),
      igstPercentage: parseFloat(item.gstPercentage),
      igstAmount: parseFloat(item.gstPrice) * item.quantity,
      cgstPercentage: parseFloat(item.gstPercentage) / 2,
      cgstAmount: (parseFloat(item.gstPrice) / 2) * item.quantity, 
      sgstPercentage: parseFloat(item.gstPercentage) / 2,
      sgstAmount: (parseFloat(item.gstPrice) / 2) * item.quantity,
      variantAttributes: item.variantAttributes
    };
  });

  // Prepare data for the invoice
  const invoiceData = {
    orderNumber: order.orderNumber,
    orderDate: order.createdAt,
    customerName: order.shippingAddress.name,
    billingAddress: order.billingAddress,
    shippingAddress: order.shippingAddress,
    items,
    subTotal: order.subTotalTaxablePrice,
    tax: order.tax,
    shippingCost: order.shippingCost,
    discountAmount: order.discountAmount,
    total: order.total,
    igstTotal: parseFloat(order.subTotalGstPrice),
    cgstTotal: parseFloat(order.subTotalGstPrice) / 2,
    sgstTotal: parseFloat(order.subTotalGstPrice) / 2,
    paymentMethod: order.paymentMethod,
    paymentStatus: order.paymentStatus
  };

  // Generate the invoice PDF
  const invoiceKey = await generateOrderInvoice(invoiceData);

  // Update the order with the invoice key
  order.userInvoice = process.env.MEDIA_URL + '/' + invoiceKey;
  await order.save();

  // Return the S3 URL for the invoice
  return {
    invoiceUrl: process.env.MEDIA_URL + '/' + invoiceKey
  };
};

module.exports = {
  createOrder,
  getUserOrders,
  getOrderById,
  cancelOrder,
  generateInvoiceForOrder
};
