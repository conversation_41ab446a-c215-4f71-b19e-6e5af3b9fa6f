const joi = require('joi');
const { paymentGateway } = require('../../../constants/dbEnums');

const addressSchema = joi.object({
  name: joi.string().required(),
  addressLine1: joi.string().required(),
  addressLine2: joi.string().allow(''),
  city: joi.string().required(),
  state: joi.string().required(),
  postalCode: joi.string().required(),
  country: joi.string().default('India'),
  phone: joi.string().required()
});

const buyNowItemSchema = joi.object({
  productId: joi.string().required(),
  quantity: joi.number().integer().min(1).required(),
  variantId: joi.string().allow(null, '')
});

const createOrderSchema = joi.object({
  shippingAddress: addressSchema.required(),
  billingAddress: addressSchema,
  notes: joi.string().allow(''),
  buyNowItem: buyNowItemSchema,
  discount: joi.string().allow(null, ''),
  countryCode: joi.string().optional(),
  paymentGateway: joi.string()
    .valid(...Object.values(paymentGateway))
    .optional()
    .default(paymentGateway.RAZORPAY)
    .messages({
      'any.only': `Payment gateway must be one of: ${Object.values(paymentGateway).join(', ')}`
    }),
});

const cancelOrderSchema = joi.object({
  reason: joi.string().required()
});

const generateInvoiceSchema = joi.object({
  orderId: joi.string().required()
});

module.exports = {
  createOrderSchema,
  cancelOrderSchema,
  generateInvoiceSchema
};
