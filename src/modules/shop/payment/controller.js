const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const paymentService = require('./service');
const { verifyPaymentSchema } = require('./validation');
const { SUCCESS } = commonConstants;

/**
 * Verify Razorpay payment
 */
const verifyRazorpayPayment = async (req, res) => {
  try {
    const { error } = verifyPaymentSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const userId = req.user.id;
    const { orderId, paymentId, signature } = req.body;

    const data = await paymentService.verifyRazorpayPayment({
      userId,
      orderId,
      paymentId,
      signature
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.PAYMENT_SUCCESSFUL,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Handle Razorpay webhook
 */
const razorpayWebhook = async (req, res) => {
  try {
    const signature = req.headers['x-razorpay-signature'];
    const data = await paymentService.handleRazorpayWebhook(req.body, signature);

    // Determine the appropriate message based on the response
    let responseMessage = messages.WEBHOOK_PROCESSED;

    if (data.alreadyProcessed) {
      responseMessage = 'Webhook acknowledged (already processed)';
    }

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: responseMessage,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  verifyRazorpayPayment,
  razorpayWebhook
};
