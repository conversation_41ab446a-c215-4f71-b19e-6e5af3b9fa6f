const express = require('express');
const router = express.Router();
const paymentController = require('./controller');
const auth = require('../../../middleware/auth');

// Webhook route - no authentication required
router.post('/razorpay/webhook', paymentController.razorpayWebhook);

// All other payment routes require authentication
router.use(auth);

// Only verification is needed since order creation includes Razorpay order
router.post('/razorpay/verify', paymentController.verifyRazorpayPayment);

module.exports = router;
