const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const { authenticate } = require('../../../utils/authenticate');
const productService = require('./service');
const { searchProductSchema, productReviewSchema } = require('./validation');
const { SUCCESS } = commonConstants;

/**
 * Get all products with pagination
 */
const getAllProducts = async (req, res) => {
  try {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, search } = req.query;
    // Get user ID if authenticated
    const user = await authenticate(req, res);

    const data = await productService.getAllProducts({
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      search,
      userId: user ? user.id : null,
      user
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get featured products
 */
const getFeaturedProducts = async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    // Get user ID if authenticated
    const user = await authenticate(req, res);

    const data = await productService.getFeaturedProducts(parseInt(limit), user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get products by category
 */
const getProductsByCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, search } = req.query;

    const { id: userId } = await authenticate(req, res);

    const data = await productService.getProductsByCategory({
      categoryId,
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      search,
      userId
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get products by subcategory
 */
const getProductsBySubCategory = async (req, res) => {
  try {
    const { subCategoryId } = req.params;
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, search } = req.query;

    const { id: userId } = await authenticate(req, res);

    const data = await productService.getProductsBySubCategory({
      subCategoryId,
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      search,
      userId
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get products by temple
 */
const getProductsByTemple = async (req, res) => {
  try {
    const { templeId } = req.params;
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1 } = req.query;
    // Get user ID if authenticated
    const { id: userId } = await authenticate(req, res);

    const data = await productService.getProductsByTemple({
      templeId,
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      userId
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get products by collection
 */
const getProductsByCollection = async (req, res) => {
  try {
    const { collectionId } = req.params;
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, search } = req.query;
    // Get user ID if authenticated
    const { id: userId } = await authenticate(req, res);

    const data = await productService.getProductsByCollection({
      collectionId,
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      search,
      userId
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Search products
 */
const searchProducts = async (req, res) => {
  try {
    const { error } = searchProductSchema.validate(req.query);
    const { id: userId } = await authenticate(req, res);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const { query, page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1 } = req.query;

    const data = await productService.searchProducts({
      query,
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      userId
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get product by ID
 */
const getProductById = async (req, res) => {
  try {
    const { id } = req.params;
    const { includeVariants = true } = req.query;
    // Get user ID if authenticated
    const { id: userId } = await authenticate(req, res);

    const data = await productService.getProductById(
      id,
      includeVariants === 'false' ? false : true,
      userId
    );

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Add product review
 */
const addProductReview = async (req, res) => {
  try {
    const { error } = productReviewSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const { id } = req.params;
    const userId = req.user.id;

    const data = await productService.addProductReview({
      productId: id,
      userId,
      ...req.body
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.REVIEW_ADDED_SUCCESSFULLY,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getAllProducts,
  getFeaturedProducts,
  getProductsByCategory,
  getProductsBySubCategory,
  getProductsByCollection,
  getProductsByTemple,
  searchProducts,
  getProductById,
  addProductReview
};
