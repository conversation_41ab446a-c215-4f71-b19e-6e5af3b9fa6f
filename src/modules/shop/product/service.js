const Product = require('../../../models/Product');
const FavoriteProduct = require('../../../models/FavoriteProduct');
const Collection = require('../../../models/Collection');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const { productStatusValue } = require('../../../constants/dbEnums');
const mongoose = require('mongoose');
const { transformTranslatedFields } = require('../../../utils/localizer');
const User = require('../../../models/User');
const Language = require('../../../models/Language');
const ProductVariant = require('../../../models/ProductVariant');

/**
 * Get all active products with pagination
 * @param {Object} options - Query options
 * @param {number} options.page - Page number
 * @param {number} options.limit - Items per page
 * @param {string} options.sort - Sort field
 * @param {string} options.order - Sort order (asc/desc)
 * @param {string} [options.userId] - Optional user ID to check favorites
 */
const getAllProducts = async ({ page, limit, sortBy, sortOrder, search, userId, user }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  let language = { code: 'en' };

  if (user && user.id) {
    const loggedInUser = await User.findById(user.id);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  let products;
  const matchStage = { status: productStatusValue.ACTIVE };

  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } }
    ];
  }

  if (userId) {
    // If userId is provided, include favorite status
    // Convert userId to ObjectId only if it's a valid ObjectId string
    const userObjectId = mongoose.Types.ObjectId.isValid(userId) ? new mongoose.Types.ObjectId(userId) : null;

    products = await Product.aggregate([
      { $match: matchStage },
      {
        $lookup: {
          from: 'favoriteproducts',
          let: { productId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [ '$product', '$$productId' ] },
                    { $eq: [ '$user', userObjectId ] }
                  ]
                }
              }
            }
          ],
          as: 'favorites'
        }
      },
      {
        $addFields: {
          isFavorite: { $gt: [{ $size: '$favorites' }, 0 ] }
        }
      },
      { $project: { favorites: 0 } },
      { $sort: sortOptions },
      { $skip: skip },
      { $limit: limit }
    ]);

    // Populate references manually since we're using aggregate
    await Product.populate(products, [
      { path: 'category', select: 'name', options: { lean: true } },
      { path: 'subCategory', select: 'name', options: { lean: true } },
      { path: 'temple', select: 'name', options: { lean: true } },
      { path: 'variants', options: { lean: true } }
    ]);
  } else {
    // If no userId, use regular find query
    products = await Product.find(matchStage)
      .populate('category', 'name')
      .populate('subCategory', 'name')
      .populate('temple', 'name')
      .populate('variants')
      .sort(sortOptions).collation({ locale: 'en', strength: 1 })
      .skip(skip)
      .limit(limit)
      .lean();
  }

  const total = await Product.countDocuments(matchStage);

  return {
    products: await transformTranslatedFields(products, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

const getAllVariants = async ({ page, limit, sortBy, sortOrder, search, userId, user }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  let language = { code: 'en' };

  if (user && user.id) {
    language = await Language.findOne({ name: user.preferredLanguage });
  }

  const userObjectId = userId && mongoose.Types.ObjectId.isValid(userId)
    ? new mongoose.Types.ObjectId(userId)
    : null;

  const matchStage = { isActive: true };

  // Build $match conditions for search after product is joined
  const searchConditions = search ? [
    { [`product.name.${language.code}`]: { $regex: search, $options: 'i' } }
  ] : [];

  const pipeline = [
    { $match: matchStage },

    // Join the referenced product
    {
      $lookup: {
        from: 'products',
        localField: 'product',
        foreignField: '_id',
        as: 'product'
      }
    },
    { $unwind: '$product' },

    // Match product.status here (now available after unwind)
    {
      $match: {
        'product.status': productStatusValue.ACTIVE,
        ...(search ? { $or: searchConditions } : {})
      }
    },

    // Join category
    {
      $lookup: {
        from: 'categories',
        localField: 'product.category',
        foreignField: '_id',
        as: 'product.category'
      }
    },
    { $unwind: { path: '$product.category', preserveNullAndEmptyArrays: true } },

    // Join subCategory
    {
      $lookup: {
        from: 'subcategories',
        localField: 'product.subCategory',
        foreignField: '_id',
        as: 'product.subCategory'
      }
    },
    { $unwind: { path: '$product.subCategory', preserveNullAndEmptyArrays: true } },

    // Join temple
    {
      $lookup: {
        from: 'temples',
        localField: 'product.temple',
        foreignField: '_id',
        as: 'product.temple'
      }
    },
    { $unwind: { path: '$product.temple', preserveNullAndEmptyArrays: true } },

    // Optional: Join favorites
    ...(userObjectId ? [
      {
        $lookup: {
          from: 'favoriteproducts',
          let: { variantId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [ '$variant', '$$variantId' ] },
                    { $eq: [ '$user', userObjectId ] }
                  ]
                }
              }
            }
          ],
          as: 'favorites'
        }
      },
      {
        $addFields: {
          isFavorite: { $gt: [{ $size: '$favorites' }, 0 ] }
        }
      },
      { $project: { favorites: 0 } }
    ] : []),

    { $sort: sortOptions },
    { $skip: skip },
    { $limit: limit }
  ];

  const variants = await ProductVariant.aggregate(pipeline);

  // Count total separately using a similar pipeline but with $count
  const countPipeline = [ ...pipeline ];
  
  countPipeline.splice(countPipeline.findIndex(stage => '$sort' in stage), countPipeline.length);
  countPipeline.push({ $count: 'total' });

  const countResult = await ProductVariant.aggregate(countPipeline);
  const total = countResult[0]?.total || 0;

  return {
    variants: await transformTranslatedFields(variants, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get featured products
 * @param {number} limit - Number of products to return
 * @param {string} [userId] - Optional user ID to check favorites
 */
const getFeaturedProducts = async (limit, userId) => {
  let products;

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  if (userId) {
    // If userId is provided, include favorite status
    // Convert userId to ObjectId only if it's a valid ObjectId string
    const userObjectId = mongoose.Types.ObjectId.isValid(userId) ? new mongoose.Types.ObjectId(userId) : null;

    products = await Product.aggregate([
      {
        $match: {
          status: productStatusValue.ACTIVE,
          featured: true
        }
      },
      {
        $lookup: {
          from: 'favoriteproducts',
          let: { productId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [ '$product', '$$productId' ] },
                    { $eq: [ '$user', userObjectId ] }
                  ]
                }
              }
            }
          ],
          as: 'favorites'
        }
      },
      {
        $addFields: {
          isFavorite: { $gt: [{ $size: '$favorites' }, 0 ] }
        }
      },
      { $project: { favorites: 0 } },
      { $sort: { createdAt: -1 } },
      { $limit: limit }
    ]);

    // Populate references manually since we're using aggregate
    await Product.populate(products, [
      { path: 'category', select: 'name', options: { lean: true } },
      { path: 'subCategory', select: 'name', options: { lean: true } },
      { path: 'temple', select: 'name', options: { lean: true } },
      { path: 'variants', options: { lean: true } }
    ]);
  } else {
    // If no userId, use regular find query
    products = await Product.find({
      status: productStatusValue.ACTIVE,
      featured: true
    })
      .populate('category', 'name')
      .populate('subCategory', 'name')
      .populate('temple', 'name')
      .populate('variants')
      .sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 })
      .limit(limit)
      .lean();
  }

  return await transformTranslatedFields(products, language.code);
};

/**
 * Get products by category
 */
const getProductsByCategory = async ({ categoryId, page, limit, sortBy, sortOrder, search, userId }) => {

  const skip = (page - 1) * limit;
  const sortOptions = {};

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  // Check if category exists
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID);
  }

  const matchStage = {
    category: new mongoose.Types.ObjectId(categoryId),
    status: productStatusValue.ACTIVE
  };

  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
    ];
  }

  let products;

  if (userId) {
    // If userId is provided, include favorite status
    // Convert userId to ObjectId only if it's a valid ObjectId string
    const userObjectId = mongoose.Types.ObjectId.isValid(userId) ? new mongoose.Types.ObjectId(userId) : null;

    products = await Product.aggregate([
      { $match: matchStage },
      {
        $lookup: {
          from: 'favoriteproducts',
          let: { productId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [ '$product', '$$productId' ] },
                    { $eq: [ '$user', userObjectId ] }
                  ]
                }
              }
            }
          ],
          as: 'favorites'
        }
      },
      {
        $addFields: {
          isFavorite: { $gt: [{ $size: '$favorites' }, 0 ] }
        }
      },
      { $project: { favorites: 0 } },
      { $sort: sortOptions },
      { $skip: skip },
      { $limit: limit }
    ]);

    // Populate references manually since we're using aggregate
    await Product.populate(products, [
      { path: 'category', select: 'name', options: { lean: true } },
      { path: 'subCategory', select: 'name', options: { lean: true } },
      { path: 'temple', select: 'name', options: { lean: true } },
      { path: 'variants', options: { lean: true } }
    ]);
  } else {
    // If no userId, use regular find query
    products = await Product.find(matchStage)
      .populate('category', 'name')
      .populate('subCategory', 'name')
      .populate('temple', 'name')
      .populate('variants')
      .sort(sortOptions).collation({ locale: 'en', strength: 1 })
      .skip(skip)
      .limit(limit)
      .lean();
  }

  products = await transformTranslatedFields(products, language.code);

  const total = await Product.countDocuments(matchStage);

  return {
    products,
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get products by subcategory
 */
const getProductsBySubCategory = async ({ subCategoryId, page, limit, sortBy, sortOrder, search, userId }) => {

  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  // Check if category exists
  if (!mongoose.Types.ObjectId.isValid(subCategoryId)) {
    throwBadRequestError(messages.INVALID_SUBCATEGORY_ID || 'Invalid subcategory ID');
  }

  const matchStage = {
    subCategory: new mongoose.Types.ObjectId(subCategoryId),
    status: productStatusValue.ACTIVE
  };

  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
    ];
  }

  let products;

  if (userId) {
    // If userId is provided, include favorite status
    // Convert userId to ObjectId only if it's a valid ObjectId string
    const userObjectId = mongoose.Types.ObjectId.isValid(userId) ? new mongoose.Types.ObjectId(userId) : null;

    products = await Product.aggregate([
      { $match: matchStage },
      {
        $lookup: {
          from: 'favoriteproducts',
          let: { productId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [ '$product', '$$productId' ] },
                    { $eq: [ '$user', userObjectId ] }
                  ]
                }
              }
            }
          ],
          as: 'favorites'
        }
      },
      {
        $addFields: {
          isFavorite: { $gt: [{ $size: '$favorites' }, 0 ] }
        }
      },
      { $project: { favorites: 0 } },
      { $sort: sortOptions },
      { $skip: skip },
      { $limit: limit }
    ]);

    // Populate references manually since we're using aggregate
    await Product.populate(products, [
      { path: 'category', select: 'name', options: { lean: true } },
      { path: 'subCategory', select: 'name', options: { lean: true } },
      { path: 'temple', select: 'name', options: { lean: true } },
      { path: 'variants' }
    ]);
  } else {
    // If no userId, use regular find query
    products = await Product.find(matchStage)
      .populate('category', 'name')
      .populate('subCategory', 'name')
      .populate('temple', 'name')
      .populate('variants')
      .sort(sortOptions).collation({ locale: 'en', strength: 1 })
      .skip(skip)
      .limit(limit)
      .lean();
  }

  const total = await Product.countDocuments(matchStage);

  const localizedProducts = await transformTranslatedFields(products, language.code);

  return {
    products: localizedProducts,
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

const getVariantsBySubCategory = async ({ subCategoryId, page, limit, sortBy, sortOrder, search, userId, user }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  let language = { code: 'en' };

  if (user && user.id) {
    language = await Language.findOne({ name: user.preferredLanguage });
  }

  // Check if category exists

  if (!mongoose.Types.ObjectId.isValid(subCategoryId)) {
    throwBadRequestError(messages.INVALID_SUBCATEGORY_ID || 'Invalid subcategory ID');
  }

  const userObjectId = userId && mongoose.Types.ObjectId.isValid(userId)
    ? new mongoose.Types.ObjectId(userId)
    : null;

  const matchStage = { isActive: true };

  // Build $match conditions for search after product is joined
  const searchConditions = search ? [
    { [`product.name.${language.code}`]: { $regex: search, $options: 'i' } }
  ] : [];

  const pipeline = [
    { $match: matchStage },

    // Join the referenced product
    {
      $lookup: {
        from: 'products',
        localField: 'product',
        foreignField: '_id',
        as: 'product'
      }
    },
    { $unwind: '$product' },

    // Match product.status here (now available after unwind)
    {
      $match: {
        'product.status': productStatusValue.ACTIVE,
        'product.subCategory': new mongoose.Types.ObjectId(subCategoryId),
        ...(search ? { $or: searchConditions } : {})
      }
    },

    // Join category
    {
      $lookup: {
        from: 'categories',
        localField: 'product.category',
        foreignField: '_id',
        as: 'product.category'
      }
    },
    { $unwind: { path: '$product.category', preserveNullAndEmptyArrays: true } },

    // Join subCategory
    {
      $lookup: {
        from: 'subcategories',
        localField: 'product.subCategory',
        foreignField: '_id',
        as: 'product.subCategory'
      }
    },
    { $unwind: { path: '$product.subCategory', preserveNullAndEmptyArrays: true } },

    // Join temple
    {
      $lookup: {
        from: 'temples',
        localField: 'product.temple',
        foreignField: '_id',
        as: 'product.temple'
      }
    },
    { $unwind: { path: '$product.temple', preserveNullAndEmptyArrays: true } },

    // Optional: Join favorites
    ...(userObjectId ? [
      {
        $lookup: {
          from: 'favoriteproducts',
          let: { variantId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [ '$variant', '$$variantId' ] },
                    { $eq: [ '$user', userObjectId ] }
                  ]
                }
              }
            }
          ],
          as: 'favorites'
        }
      },
      {
        $addFields: {
          isFavorite: { $gt: [{ $size: '$favorites' }, 0 ] }
        }
      },
      { $project: { favorites: 0 } }
    ] : []),

    { $sort: sortOptions },
    { $skip: skip },
    { $limit: limit }
  ];

  const variants = await ProductVariant.aggregate(pipeline);

  // Count total separately using a similar pipeline but with $count
  const countPipeline = [ ...pipeline ];
  
  countPipeline.splice(countPipeline.findIndex(stage => '$sort' in stage), countPipeline.length);
  countPipeline.push({ $count: 'total' });

  const countResult = await ProductVariant.aggregate(countPipeline);
  const total = countResult[0]?.total || 0;

  return {
    variants: await transformTranslatedFields(variants, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get products by temple
 */
const getProductsByTemple = async ({ templeId, page, limit, sortBy, sortOrder, search, userId }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  // Check if temple exists
  if (!mongoose.Types.ObjectId.isValid(templeId)) {
    throwBadRequestError(messages.INVALID_TEMPLE_ID);
  }
  const matchStage = {
    temple: new mongoose.Types.ObjectId(templeId),
    status: productStatusValue.ACTIVE
  };

  if (search) {
    matchStage.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
    ];
  }

  const products = await Product.find(matchStage)
    .populate('category', 'name')
    .populate('subCategory', 'name')
    .populate('temple', 'name')
    .populate('variants')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  const total = await Product.countDocuments({
    temple: templeId,
    status: productStatusValue.ACTIVE
  });

  return {
    products: await transformTranslatedFields(products, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get products by collection
 */
const getProductsByCollection = async ({ collectionId, page, limit, sortBy, sortOrder, search, userId }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  // Check if collection exists
  if (!mongoose.Types.ObjectId.isValid(collectionId)) {
    throwBadRequestError(messages.INVALID_COLLECTION_ID || 'Invalid collection ID');
  }

  const collection = await Collection.findOne({
    _id: new mongoose.Types.ObjectId(collectionId),
    isActive: true
  });

  if (!collection) {
    throwBadRequestError(messages.COLLECTION_NOT_FOUND || 'Collection not found');
  }

  const matchStage = {
    _id: { $in: collection.products },
    status: productStatusValue.ACTIVE
  };

  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
    ];
  }

  let products;

  if (userId) {
    // If userId is provided, include favorite status
    // Convert userId to ObjectId only if it's a valid ObjectId string
    const userObjectId = mongoose.Types.ObjectId.isValid(userId) ? new mongoose.Types.ObjectId(userId) : null;

    products = await Product.aggregate([
      { $match: matchStage },
      {
        $lookup: {
          from: 'favoriteproducts',
          let: { productId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [ '$product', '$$productId' ] },
                    { $eq: [ '$user', userObjectId ] }
                  ]
                }
              }
            }
          ],
          as: 'favorites'
        }
      },
      {
        $addFields: {
          isFavorite: { $gt: [{ $size: '$favorites' }, 0 ] }
        }
      },
      { $project: { favorites: 0 } },
      { $sort: sortOptions },
      { $skip: skip },
      { $limit: limit }
    ]);

    // Populate references manually since we're using aggregate
    await Product.populate(products, [
      { path: 'category', select: 'name', options: { lean: true } },
      { path: 'subCategory', select: 'name', options: { lean: true } },
      { path: 'temple', select: 'name', options: { lean: true } },
      { path: 'variants', options: { lean: true } }
    ]);
  } else {
    // If no userId, use regular find query
    products = await Product.find(matchStage)
      .populate('category', 'name')
      .populate('subCategory', 'name')
      .populate('temple', 'name')
      .populate('variants')
      .sort(sortOptions).collation({ locale: 'en', strength: 1 })
      .skip(skip)
      .limit(limit)
      .lean();
  }

  const total = await Product.countDocuments(matchStage);

  return {
    products: await transformTranslatedFields(products, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Search products
 */
const searchProducts = async ({ query, page, limit, sortBy, sortOrder, userId }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  const searchQuery = {
    // $text: { $search: query },
    [`name.${language.code}`]: { $regex: query, $options: 'i' },
    status: productStatusValue.ACTIVE
  };

  const products = await Product.find(searchQuery)
    .populate('category', 'name')
    .populate('subCategory', 'name')
    .populate('temple', 'name')
    .populate('variants')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  const total = await Product.countDocuments(searchQuery);

  return {
    products: await transformTranslatedFields(products, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get product by ID
 * @param {string} productId - Product ID
 * @param {boolean} [includeVariants=true] - Whether to include variants
 * @param {string} [userId] - Optional user ID to check favorites
 */
const getProductById = async (productId, includeVariants = true, userId = null) => {
  // Check if product exists
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  let languageCode = 'en';

  if (userId) {
    const loggedInUser = await User.findById(userId);
    
    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        languageCode = userLanguage.code;
      }
    }
  }

  // Create a query to find the product
  const query = Product.findOne({
    _id: productId,
    status: productStatusValue.ACTIVE
  })
    .populate('category', 'name')
    .populate('subCategory', 'name')
    .populate('temple', 'name').lean();

  // If includeVariants is true, populate the variants
  if (includeVariants) {
    query.populate({
      path: 'variants',
      // No need for match condition as all variants are related to the product
      select: '-createdAt -updatedAt -__v' // Exclude unnecessary fields
    });
  }

  const product = await query;

  if (!product) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // If userId is provided, check if product is in user's favorites
  if (userId) {
    const isFavorite = await FavoriteProduct.findOne({
      user: userId,
      product: productId,
      variant: null
    });

    product.isFavorite = !!isFavorite;

    // Check favorite status for each variant if variants are included
    if (includeVariants && product.variants && product.variants.length > 0) {
      const variantIds = product.variants.map(variant => variant._id);
      const favoriteVariants = await FavoriteProduct.find({
        user: userId,
        product: productId,
        variant: { $in: variantIds }
      });

      // Create a map of variant IDs to favorite status
      const favoriteVariantMap = {};

      favoriteVariants.forEach(fav => {
        favoriteVariantMap[fav.variant.toString()] = true;
      });

      // Add isFavorite flag to each variant
      product.variants.forEach(variant => {
        variant.isFavorite = !!favoriteVariantMap[variant._id.toString()];
      });
    }
  }

  // Convert the product to lean js object
  // const leanProduct = product.toObject({ getters: true });

  return await transformTranslatedFields(product, languageCode);
};

const getVariantById = async (variantId, user) => {
  // Check if product exists
  if (!mongoose.Types.ObjectId.isValid(variantId)) {
    throwBadRequestError(messages.INVALID_VARIANT_ID);
  }

  let languageCode = 'en';

  if (user && user.preferredLanguage) {
    const userLanguage = await Language.findOne({ name: user.preferredLanguage });

    if (userLanguage) {
      languageCode = userLanguage.code;
    }
  }

  // Create a query to find the product
  const variant = await ProductVariant.findOne({
    _id: variantId,
    isActive: true
  })
    .populate({
      path: 'product',
      populate: [
        { path: 'category' },
        { path: 'subCategory' },
        { path: 'temple' },
      ],
    })
    .lean();

  if (!variant) {
    throwBadRequestError(messages.VARIANT_NOT_FOUND);
  }

  // If userId is provided, check if product is in user's favorites
  if (user?._id) {
    const isFavorite = await FavoriteProduct.findOne({
      user: user._id,
      variant: variantId
    });

    variant.isFavorite = !!isFavorite;
  }

  // Convert the product to lean js object
  // const leanProduct = product.toObject({ getters: true });

  return await transformTranslatedFields(variant, languageCode);
};

/**
 * Add product review
 */
const addProductReview = async ({ productId }) => {
  // Check if product exists
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  const product = await Product.findOne({
    _id: productId,
    status: productStatusValue.ACTIVE
  });

  if (!product) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // For now, we'll just return success
  // In a real implementation, we would add the review to a reviews collection
  return { success: true };
};

module.exports = {
  getAllProducts,
  getAllVariants,
  getFeaturedProducts,
  getProductsByCategory,
  getProductsBySubCategory,
  getVariantsBySubCategory,
  getProductsByCollection,
  getProductsByTemple,
  searchProducts,
  getProductById,
  getVariantById,
  addProductReview
};
