const joi = require('joi');

const searchProductSchema = joi.object({
  query: joi.string().required(),
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(10),
  sortBy: joi.string().valid('createdAt', 'name').default('createdAt'),
  sortOrder: joi.number().valid(1, -1).default(-1)
});

const productReviewSchema = joi.object({
  rating: joi.number().integer().min(1).max(5).required(),
  comment: joi.string().max(500)
});

module.exports = {
  searchProductSchema,
  productReviewSchema
};
