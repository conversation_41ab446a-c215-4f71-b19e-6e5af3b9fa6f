const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const shippingService = require('./service');
const { logShippingEvent } = require('../../../utils/logger');
const { SUCCESS } = commonConstants;

/**
 * Handle ShipRocket webhook
 */
const shiprocketWebhook = async (req, res) => {
  try {
    // Log the webhook data
    logShippingEvent('WEBHOOK_RECEIVED', {
      data: req.body
    });

    // Validate webhook data
    if (!req.body || !req.body.current_status || !req.body.order_id) {
      return apiResponse({
        res,
        code: 400,
        message: 'Invalid webhook data',
        status: false,
        data: null
      });
    }

    if (req.headers['x-api-key'] !== process.env.SHIPROCKET_API_KEY) {
      return apiResponse({
        res,
        code: 400,
        message: 'Invalid webhook signature',
        status: false,
        data: null
      });
    }

    // Process the webhook
    const data = await shippingService.updateShipmentStatus(req.body);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.WEBHOOK_PROCESSED,
      status: true,
      data
    });
  } catch (error) {
    // Log error but return success to ShipRocket
    logShippingEvent('WEBHOOK_ERROR', {
      error: error.message,
      data: req.body
    });

    // Always return 200 OK to ShipRocket to prevent retries
    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Webhook received (error logged)',
      status: true,
      data: { error: error.message }
    });
  }
};

/**
 * Track shipment
 */
const trackShipment = async (req, res) => {
  try {
    const { shipmentId } = req.params;

    const data = await shippingService.trackShipment(shipmentId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get tracking history for a shipment
 */
const getTrackingHistory = async (req, res) => {
  try {
    const { shipmentId } = req.params;
    const userId = req.user.id;

    // Get shipment and verify it belongs to the user
    const shipment = await shippingService.getShipmentWithHistory(shipmentId, userId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: shipment
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Cancel shipment
 */
const cancelShipment = async (req, res) => {
  try {
    const { shipmentId } = req.params;

    const data = await shippingService.cancelShipment(shipmentId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SHIPMENT_CANCELLED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  shiprocketWebhook,
  trackShipment,
  getTrackingHistory,
  cancelShipment
};
