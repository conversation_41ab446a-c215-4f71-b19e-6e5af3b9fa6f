const express = require('express');
const router = express.Router();
const shippingController = require('./controller');
const auth = require('../../../middleware/auth');

router.post('/webhook', shippingController.shiprocketWebhook);

// All other shipping routes require authentication
router.use(auth);

router.get('/track/:shipmentId', shippingController.trackShipment);
router.get('/history/:shipmentId', shippingController.getTrackingHistory);
router.post('/cancel/:shipmentId', shippingController.cancelShipment);

module.exports = router;
