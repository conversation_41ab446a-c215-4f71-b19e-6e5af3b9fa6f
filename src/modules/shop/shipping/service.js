const shiprocket = require('../../../services/shiprocket');
const { logShippingEvent } = require('../../../utils/logger');
const Order = require('../../../models/Order');
const User = require('../../../models/User');
const Admin = require('../../../models/Admin');
const Vendor = require('../../../models/Vendor');
const ProductShipment = require('../../../models/ProductShipment');
const { shipmentStatusValue, orderStatusValue } = require('../../../constants/dbEnums');
const { withTransaction, sessionOptions } = require('../../../utils/transactionHelper');
const { transformTranslatedFields } = require('../../../utils/localizer');

/**
 * Create ShipRocket orders for all shipments in an order
 * @param {String} orderId - Order ID
 */
const createShiprocketOrders = async (orderId) => {
  try {
    // Find order and populate necessary fields
    const order = await Order.findById(orderId)
      .populate('items.product items.variant shipments user');

    if (!order) {
      throw new Error(`Order not found: ${orderId}`);
    }

    // Get user details
    const user = await User.findById(order.user);

    if (!user) {
      throw new Error(`User not found for order: ${orderId}`);
    }

    // Find all shipments for this order
    const shipments = await ProductShipment.find({ order: orderId })
      .populate('orderItems.product orderItems.variant');

    if (!shipments || shipments.length === 0) {
      throw new Error(`No shipments found for order: ${orderId}`);
    }

    // Process each shipment
    const results = [];

    for (const shipment of shipments) {
      // Skip shipments that already have ShipRocket IDs
      if (shipment.shiprocketOrderId) {
        results.push({
          shipmentId: shipment._id,
          status: 'SKIPPED',
          message: 'Shipment already has ShipRocket order ID'
        });
        continue;
      }

      // Get vendor information based on vendor model and ID
      let vendor = null;

      if (shipment.vendorModel === 'Admin') {
        vendor = await Admin.findById(shipment.vendor);
      } else if (shipment.vendorModel === 'Vendor') {
        vendor = await Vendor.findById(shipment.vendor);
      }

      if (!vendor) {
        logShippingEvent('VENDOR_NOT_FOUND', {
          shipmentId: shipment._id,
          vendorId: shipment.vendor,
          vendorModel: shipment.vendorModel
        });
      }

      // Format order data for ShipRocket
      const shiprocketOrderData = await shiprocket.formatOrderData(order, shipment, user, vendor);

      // Create ShipRocket order
      const shiprocketResponse = await shiprocket.createOrder(shiprocketOrderData);

      // Update shipment with ShipRocket details
      await withTransaction(async (session) => {
        shipment.shiprocketOrderId = shiprocketResponse.order_id.toString();
        shipment.shiprocketShipmentId = shiprocketResponse.shipment_id.toString();
        shipment.status = shipmentStatusValue.PROCESSING;

        // Store API response
        shipment.apiResponses = shipment.apiResponses || [];
        shipment.apiResponses.push({
          endpoint: 'create_order',
          response: shiprocketResponse,
          timestamp: new Date()
        });

        await shipment.save(sessionOptions(session));

        // Update order status to PROCESSING if it's still PENDING
        const order = await Order.findById(shipment.order);

        if (order && order.orderStatus === orderStatusValue.PENDING) {
          order.orderStatus = orderStatusValue.PROCESSING;
          await order.save(sessionOptions(session));

          logShippingEvent('ORDER_STATUS_UPDATED', {
            orderId: order._id,
            orderNumber: order.orderNumber,
            oldStatus: orderStatusValue.PENDING,
            newStatus: orderStatusValue.PROCESSING
          });
        }
      });

      results.push({
        shipmentId: shipment._id,
        status: 'SUCCESS',
        shiprocketOrderId: shiprocketResponse.order_id,
        shiprocketShipmentId: shiprocketResponse.shipment_id
      });

      logShippingEvent('SHIPROCKET_ORDER_CREATED_FOR_SHIPMENT', {
        orderId: order._id,
        orderNumber: order.orderNumber,
        shipmentId: shipment._id,
        shiprocketOrderId: shiprocketResponse.order_id,
        shiprocketShipmentId: shiprocketResponse.shipment_id
      });
    }

    return {
      orderId: order._id,
      orderNumber: order.orderNumber,
      results
    };
  } catch (error) {
    logShippingEvent('CREATE_SHIPROCKET_ORDERS_ERROR', {
      orderId,
      error: error.message
    });
    throw error;
  }
};

/**
 * Update shipment status based on ShipRocket webhook data
 * @param {Object} webhookData - ShipRocket webhook data
 */
const updateShipmentStatus = async (webhookData) => {
  try {
    // Extract data from webhook
    const {
      order_id: shiprocketOrderId,
      current_status,
      courier_name,
      tracking_number,
      awb, // Extract AWB number
      etd,
      scans
    } = webhookData;

    // Use AWB if tracking_number is not provided
    // We'll use this later in the function

    // Find shipment by ShipRocket order ID
    const shipment = await ProductShipment.findOne({ shiprocketOrderId });

    if (!shipment) {
      throw new Error(`Shipment not found for ShipRocket order ID: ${shiprocketOrderId}`);
    }

    // Map ShipRocket status to our status
    let newStatus;

    switch (current_status.toLowerCase()) {
      case 'pickup scheduled':
      case 'pickup generated':
      case 'awb generated':
      case 'pickup queued':
      case 'manifest generated':
        newStatus = shipmentStatusValue.PROCESSING;
        break;
      case 'shipped':
      case 'in transit':
      case 'out for delivery':
        newStatus = shipmentStatusValue.SHIPPED;
        break;
      case 'delivered':
        newStatus = shipmentStatusValue.DELIVERED;
        break;
      case 'cancelled':
      case 'rto initiated':
      case 'rto delivered':
        newStatus = shipmentStatusValue.CANCELLED;
        break;
      default:
        newStatus = shipment.status; // Keep current status if unknown
    }

    // Update shipment
    await withTransaction(async (session) => {
      // Update shipment details
      shipment.status = newStatus;

      // Check if AWB (tracking number) is being assigned for the first time
      const trackingNumber = tracking_number || awb;
      // const awbAssigned = trackingNumber && !shipment.trackingId;

      shipment.trackingId = trackingNumber || shipment.trackingId;
      shipment.courier = {
        courierName: courier_name || shipment.courier?.courierName,
        courierId: shipment.courier?.courierId
      };

      // If AWB is assigned for the first time, generate pickup request
      // if (awbAssigned) {
      //   try {
      //     logShippingEvent('AWB_ASSIGNED_GENERATING_PICKUP', {
      //       shipmentId: shipment._id,
      //       shiprocketOrderId,
      //       trackingNumber
      //     });

      //     // Generate pickup request
      //     const pickupResponse = await shiprocket.generatePickup(shiprocketOrderId);

      //     // Store pickup response
      //     shipment.apiResponses.push({
      //       endpoint: 'generate_pickup',
      //       response: pickupResponse,
      //       timestamp: new Date()
      //     });

      //     logShippingEvent('PICKUP_GENERATED_AFTER_AWB', {
      //       shipmentId: shipment._id,
      //       shiprocketOrderId,
      //       trackingNumber,
      //       pickupResponse
      //     });
      //   } catch (pickupError) {
      //     // Log error but continue with the rest of the updates
      //     logShippingEvent('PICKUP_GENERATION_ERROR_AFTER_AWB', {
      //       shipmentId: shipment._id,
      //       shiprocketOrderId,
      //       trackingNumber,
      //       error: pickupError.message
      //     });
      //   }
      // }

      if (etd) {
        shipment.estimatedDeliveryDate = new Date(etd);
      }

      // Process scans array to create detailed tracking history
      if (scans && scans.length > 0) {
        // Store the original scans for reference
        shipment.scans = scans;

        // Initialize tracking history if it doesn't exist
        if (!shipment.trackingHistory) {
          shipment.trackingHistory = [];
        }

        // Clear existing tracking history to avoid duplicates
        // Only do this if we're getting a full scan history
        if (scans.length > 1) {
          shipment.trackingHistory = [];
        }

        // Process each scan and add to tracking history
        for (const scan of scans) {
          const historyEntry = {
            status: scan['sr-status-label'] || current_status,
            location: scan.location || 'Unknown',
            timestamp: new Date(scan.date),
            description: scan.activity || scan.status
          };

          // Check if this entry already exists to avoid duplicates
          const exists = shipment.trackingHistory.some(entry =>
            entry.timestamp.getTime() === historyEntry.timestamp.getTime() &&
            entry.status === historyEntry.status &&
            entry.description === historyEntry.description
          );

          if (!exists) {
            shipment.trackingHistory.push(historyEntry);
          }
        }

        // Sort tracking history by timestamp (newest first)
        shipment.trackingHistory.sort((a, b) => b.timestamp - a.timestamp);
      } else {
        // If no scans array, create a single history entry from the webhook data
        const location = webhookData.location || webhookData.facility_location || 'Unknown';
        const description = webhookData.activity || webhookData.description || current_status;

        // Create new tracking history entry
        const historyEntry = {
          status: current_status,
          location: location,
          timestamp: new Date(webhookData.current_timestamp || new Date()),
          description: description
        };

        // Initialize tracking history if it doesn't exist
        if (!shipment.trackingHistory) {
          shipment.trackingHistory = [];
        }

        // Check if this entry already exists to avoid duplicates
        const exists = shipment.trackingHistory.some(entry =>
          entry.status === historyEntry.status &&
          entry.description === historyEntry.description
        );

        if (!exists) {
          // Add new entry to tracking history
          shipment.trackingHistory.push(historyEntry);

          // Sort tracking history by timestamp (newest first)
          shipment.trackingHistory.sort((a, b) => b.timestamp - a.timestamp);
        }
      }

      // Store webhook data
      shipment.apiResponses = shipment.apiResponses || [];
      shipment.apiResponses.push({
        endpoint: 'webhook',
        response: webhookData,
        timestamp: new Date()
      });

      await shipment.save(sessionOptions(session));

      // Update order item shipment status
      const order = await Order.findById(shipment.order);

      if (order) {
        // Get all order item IDs in this shipment
        const shipmentOrderItemIds = shipment.orderItems.map(item =>
          item.orderItem.toString()
        );

        // Update status for each order item in this shipment
        for (const item of order.items) {
          if (shipmentOrderItemIds.includes(item._id.toString())) {
            item.shipmentStatus = newStatus;
          }
        }

        // Update the overall order status based on item statuses
        if (newStatus === shipmentStatusValue.DELIVERED) {
          // Check if all items in the order are delivered
          const allItemsDelivered = order.items.every(item =>
            item.shipmentStatus === shipmentStatusValue.DELIVERED
          );

          if (allItemsDelivered) {
            // Update order status to DELIVERED only if all items are delivered
            order.orderStatus = orderStatusValue.DELIVERED;
            order.deliveredAt = new Date();

            logShippingEvent('ORDER_FULLY_DELIVERED', {
              orderId: order._id,
              orderNumber: order.orderNumber
            });
          }
        } else if (newStatus === shipmentStatusValue.SHIPPED) {
          // Check if all items are shipped or delivered
          const allItemsShippedOrDelivered = order.items.every(item =>
            item.shipmentStatus === shipmentStatusValue.SHIPPED ||
            item.shipmentStatus === shipmentStatusValue.DELIVERED
          );

          // Check if any items are shipped (but not all delivered)
          const anyItemsShipped = order.items.some(item =>
            item.shipmentStatus === shipmentStatusValue.SHIPPED
          );

          if (allItemsShippedOrDelivered) {
            // All items are either shipped or delivered
            if (order.orderStatus !== orderStatusValue.SHIPPED &&
                order.orderStatus !== orderStatusValue.DELIVERED) {
              order.orderStatus = orderStatusValue.SHIPPED;

              logShippingEvent('ORDER_FULLY_SHIPPED', {
                orderId: order._id,
                orderNumber: order.orderNumber
              });
            }
          } else if (anyItemsShipped) {
            // Some items are shipped, but not all
            if (order.orderStatus !== orderStatusValue.PARTIALLY_SHIPPED &&
                order.orderStatus !== orderStatusValue.SHIPPED &&
                order.orderStatus !== orderStatusValue.DELIVERED) {
              order.orderStatus = orderStatusValue.PARTIALLY_SHIPPED;

              logShippingEvent('ORDER_PARTIALLY_SHIPPED', {
                orderId: order._id,
                orderNumber: order.orderNumber
              });
            }
          }
        } else if (newStatus === shipmentStatusValue.CANCELLED) {
          // Check if all items in the order are cancelled
          const allItemsCancelled = order.items.every(item =>
            item.shipmentStatus === shipmentStatusValue.CANCELLED
          );

          if (allItemsCancelled) {
            // Update order status to CANCELLED only if all items are cancelled
            order.orderStatus = orderStatusValue.CANCELLED;
            order.cancelledAt = new Date();

            logShippingEvent('ORDER_FULLY_CANCELLED', {
              orderId: order._id,
              orderNumber: order.orderNumber
            });
          }
        }

        await order.save(sessionOptions(session));
      }
    });

    // Get the tracking number from the shipment (it's already been updated)
    logShippingEvent('SHIPMENT_STATUS_UPDATED', {
      shipmentId: shipment._id,
      orderId: shipment.order,
      shiprocketOrderId,
      oldStatus: shipment.status,
      newStatus,
      trackingNumber: shipment.trackingId,
      courierName: courier_name
    });

    return {
      success: true,
      shipmentId: shipment._id,
      orderId: shipment.order,
      status: newStatus
    };
  } catch (error) {
    logShippingEvent('UPDATE_SHIPMENT_STATUS_ERROR', {
      webhookData,
      error: error.message
    });
    throw error;
  }
};

/**
 * Track shipment
 * @param {String} shipmentId - Shipment ID
 */
const trackShipment = async (shipmentId) => {
  try {
    // Find shipment
    const shipment = await ProductShipment.findById(shipmentId);

    if (!shipment) {
      throw new Error(`Shipment not found: ${shipmentId}`);
    }

    if (!shipment.shiprocketOrderId) {
      throw new Error(`Shipment does not have ShipRocket order ID: ${shipmentId}`);
    }

    // Track shipment
    const trackingData = await shiprocket.trackShipment(shipment.shiprocketOrderId);

    // Update shipment with tracking information
    await withTransaction(async (session) => {
      // Update tracking URL if available
      if (trackingData.tracking_data && trackingData.tracking_data.track_url) {
        shipment.trackingUrl = trackingData.tracking_data.track_url;
      }

      // Update estimated delivery date if available
      if (trackingData.tracking_data && trackingData.tracking_data.shipment_track && trackingData.tracking_data.shipment_track.etd) {
        shipment.estimatedDeliveryDate = new Date(trackingData.tracking_data.shipment_track.etd);
      }

      // Update courier information if available
      if (trackingData.tracking_data && trackingData.tracking_data.shipment_track) {
        const track = trackingData.tracking_data.shipment_track;

        if (track.courier_name) {
          shipment.courier = {
            courierName: track.courier_name,
            courierId: track.courier_id || shipment.courier?.courierId
          };
        }
      }

      // Update tracking history
      if (trackingData.trackingHistory && trackingData.trackingHistory.length > 0) {
        // Clear existing tracking history to avoid duplicates
        shipment.trackingHistory = [];

        // Add new tracking history
        shipment.trackingHistory = trackingData.trackingHistory;
      }

      // Store API response
      shipment.apiResponses = shipment.apiResponses || [];
      shipment.apiResponses.push({
        endpoint: 'track_shipment',
        response: trackingData,
        timestamp: new Date()
      });

      await shipment.save(sessionOptions(session));
    });

    return {
      ...trackingData,
      shipmentId: shipment._id,
      orderId: shipment.order,
      trackingUrl: shipment.trackingUrl,
      trackingHistory: shipment.trackingHistory
    };
  } catch (error) {
    logShippingEvent('TRACK_SHIPMENT_ERROR', {
      shipmentId,
      error: error.message
    });
    throw error;
  }
};

/**
 * Cancel shipment
 * @param {String} shipmentId - Shipment ID
 */
const cancelShipment = async (shipmentId) => {
  try {
    // Find shipment
    const shipment = await ProductShipment.findById(shipmentId);

    if (!shipment) {
      throw new Error(`Shipment not found: ${shipmentId}`);
    }

    if (!shipment.shiprocketOrderId) {
      throw new Error(`Shipment does not have ShipRocket order ID: ${shipmentId}`);
    }

    // Cancel ShipRocket order
    const cancelResponse = await shiprocket.cancelOrder(shipment.shiprocketOrderId);

    // Update shipment status
    await withTransaction(async (session) => {
      shipment.status = shipmentStatusValue.CANCELLED;

      // Store API response
      shipment.apiResponses = shipment.apiResponses || [];
      shipment.apiResponses.push({
        endpoint: 'cancel_order',
        response: cancelResponse,
        timestamp: new Date()
      });

      await shipment.save(sessionOptions(session));

      // Update order item shipment status
      const order = await Order.findById(shipment.order);

      if (order) {
        // Get all order item IDs in this shipment
        const shipmentOrderItemIds = shipment.orderItems.map(item =>
          item.orderItem.toString()
        );

        // Update status for each order item in this shipment
        for (const item of order.items) {
          if (shipmentOrderItemIds.includes(item._id.toString())) {
            item.shipmentStatus = shipmentStatusValue.CANCELLED;
          }
        }

        // Check if all items are cancelled to update the overall order status
        const allItemsCancelled = order.items.every(item =>
          item.shipmentStatus === shipmentStatusValue.CANCELLED
        );

        if (allItemsCancelled) {
          // Update order status to CANCELLED only if all items are cancelled
          order.orderStatus = orderStatusValue.CANCELLED;
          order.cancelledAt = new Date();

          logShippingEvent('ORDER_FULLY_CANCELLED', {
            orderId: order._id,
            orderNumber: order.orderNumber
          });
        }

        await order.save(sessionOptions(session));
      }
    });

    logShippingEvent('SHIPMENT_CANCELLED', {
      shipmentId: shipment._id,
      orderId: shipment.order,
      shiprocketOrderId: shipment.shiprocketOrderId
    });

    return {
      success: true,
      shipmentId: shipment._id,
      orderId: shipment.order
    };
  } catch (error) {
    logShippingEvent('CANCEL_SHIPMENT_ERROR', {
      shipmentId,
      error: error.message
    });
    throw error;
  }
};

/**
 * Get shipment with tracking history
 * @param {String} shipmentId - Shipment ID
 * @param {String} userId - User ID
 */
const getShipmentWithHistory = async (shipmentId, userId) => {
  try {
    // Find shipment
    const shipment = await ProductShipment.findById(shipmentId)
      .populate('orderItems.product orderItems.variant').lean();

    if (!shipment) {
      throw new Error(`Shipment not found: ${shipmentId}`);
    }

    // Find order to verify it belongs to the user
    const order = await Order.findOne({
      _id: shipment.order,
      user: userId
    });

    if (!order) {
      throw new Error('You do not have permission to view this shipment');
    }

    // If tracking history is empty or old, refresh it
    const needsRefresh = !shipment.trackingHistory ||
                        shipment.trackingHistory.length === 0 ||
                        (shipment.updatedAt &&
                         new Date() - shipment.updatedAt > 3600000); // 1 hour

    if (needsRefresh && shipment.shiprocketOrderId) {
      try {
        // Refresh tracking data
        await trackShipment(shipmentId);

        // Get updated shipment
        const refreshedShipment = await ProductShipment.findById(shipmentId)
          .populate('orderItems.product orderItems.variant');

        return refreshedShipment;
      } catch (error) {
        // If refresh fails, return the original shipment
        logShippingEvent('REFRESH_TRACKING_ERROR', {
          shipmentId,
          error: error.message
        });
      }
    }

    return await transformTranslatedFields(shipment, 'en');
  } catch (error) {
    logShippingEvent('GET_SHIPMENT_HISTORY_ERROR', {
      shipmentId,
      userId,
      error: error.message
    });
    throw error;
  }
};

module.exports = {
  createShiprocketOrders,
  updateShipmentStatus,
  trackShipment,
  cancelShipment,
  getShipmentWithHistory,
};