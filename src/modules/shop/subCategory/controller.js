const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const subCategoryService = require('./service');
const { SUCCESS } = commonConstants;

/**
 * Get all subcategories
 */
const getAllSubCategories = async (req, res) => {
  try {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, search } = req.query;
    const data = await subCategoryService.getAllSubCategories({
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder,
      search,
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get subcategories by category ID
 */
const getSubCategoriesByCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;
    
    const data = await subCategoryService.getSubCategoriesByCategory(categoryId, req.query);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get subcategory by ID
 */
const getSubCategoryById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const data = await subCategoryService.getSubCategoryById(id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getAllSubCategories,
  getSubCategoriesByCategory,
  getSubCategoryById
};
