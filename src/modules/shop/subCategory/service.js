const SubCategory = require('../../../models/SubCategory');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const mongoose = require('mongoose');
const { transformTranslatedFields } = require('../../../utils/localizer');
const User = require('../../../models/User');
const Language = require('../../../models/Language');

/**
 * Get all active subcategories
 */
const getAllSubCategories = async ({ page, limit, sortBy, sortOrder, search, user }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  let language = { code: 'en' };

  if (user) {
    const loggedInUser = await User.findById(user.id);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const matchStage = { isActive: true };

  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`description.${language.code}`]: { $regex: search, $options: 'i' } },
    ];
  }
  const subCategories = await SubCategory.find(matchStage)
    .populate('category')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  return await transformTranslatedFields(subCategories, language.code);
};

/**
 * Get subcategories by category ID
 */
const getSubCategoriesByCategory = async (categoryId, query, user) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID || 'Invalid category ID');
  }

  const { page, limit, sortBy, sortOrder, search } = query;

  let language = { code: 'en' }; // default to English

  if (user) {
    const loggedInUser = await User.findById(user.id);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const matchStage = {
    category: categoryId,
    isActive: true
  };

  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`description.${language.code}`]: { $regex: search, $options: 'i' } },
    ];
  }

  const subCategories = await SubCategory.find(matchStage)
    .populate('category')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  return await transformTranslatedFields(subCategories, language.code);
};

/**
 * Get subcategory by ID
 */
const getSubCategoryById = async (subCategoryId, user) => {
  // Check if subcategory ID is valid
  if (!mongoose.Types.ObjectId.isValid(subCategoryId)) {
    throwBadRequestError(messages.INVALID_SUBCATEGORY_ID || 'Invalid subcategory ID');
  }

  let language = { code: 'en' };

  if (user) {
    const loggedInUser = await User.findById(user.id);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  const subCategory = await SubCategory.findOne({
    _id: subCategoryId,
    isActive: true
  }).populate('category', 'name');

  if (!subCategory) {
    throwBadRequestError(messages.SUBCATEGORY_NOT_FOUND || 'Subcategory not found');
  }

  return await transformTranslatedFields(subCategory, language.code);
};

module.exports = {
  getAllSubCategories,
  getSubCategoriesByCategory,
  getSubCategoryById
};
