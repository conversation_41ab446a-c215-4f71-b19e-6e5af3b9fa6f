const SubCategory = require('../../../models/SubCategory');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const mongoose = require('mongoose');

/**
 * Get all active subcategories
 */
const getAllSubCategories = async ({ page, limit, sortBy, sortOrder, search }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const matchStage = { isActive: true };
    
  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
    ];
  }
  const subCategories = await SubCategory.find(matchStage)
    .populate('category')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit);

  return subCategories;
};

/**
 * Get subcategories by category ID
 */
const getSubCategoriesByCategory = async (categoryId, query) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID || 'Invalid category ID');
  }

  const { page, limit, sortBy, sortOrder, search } = query;

  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const matchStage = {
    category: categoryId,
    isActive: true
  };
    
  // Add search filter if provided
  if (search) {
    matchStage.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
    ];
  }

  const subCategories = await SubCategory.find(matchStage)
    .populate('category')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit);

  return subCategories;
};

/**
 * Get subcategory by ID
 */
const getSubCategoryById = async (subCategoryId) => {
  // Check if subcategory ID is valid
  if (!mongoose.Types.ObjectId.isValid(subCategoryId)) {
    throwBadRequestError(messages.INVALID_SUBCATEGORY_ID || 'Invalid subcategory ID');
  }

  const subCategory = await SubCategory.findOne({
    _id: subCategoryId,
    isActive: true
  }).populate('category', 'name');

  if (!subCategory) {
    throwBadRequestError(messages.SUBCATEGORY_NOT_FOUND || 'Subcategory not found');
  }

  return subCategory;
};

module.exports = {
  getAllSubCategories,
  getSubCategoriesByCategory,
  getSubCategoryById
};
