const { apiResponse, errorApiResponse } = require('../../config/responseHandler');
const { commonConstants } = require('../../constants/common');
const { messages } = require('../../messages');
const templeService = require('./service');
const {
  createTempleSchema,
  updateTempleSchema,
  updateDashboardTemplesSchema,
  uploadUrlSchema,
  validateId,
  checkAvailabilitySchema,
  checkServiceSchema,
  getTemplesServiceCodeSchema
} = require('./validation');
const { SUCCESS } = commonConstants;
const { getPresignedUrl, deleteFile } = require('../../utils/s3Service');
const { saveAuditLog } = require('../../utils/auditLogger');
const { auditLogAction } = require('../../constants/dbEnums');
const { authenticate } = require('../../utils/authenticate');

const createTemple = async (req, res) => {
  try {
    const { value, error } = createTempleSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await templeService.createTemple(value);

    //* Save audit log 
    const detail = `Temple ${data.name.en} created successfully`;
    const model = 'Temple';

    await saveAuditLog(req, req.user.id, auditLogAction.TEMPLE_CREATED, detail, model);

    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateTemple = async (req, res) => {
  try {
    const { value, error } = updateTempleSchema.validate(req.body, { abortEarly: false });

    if (error) {
      return res.status(400).json({
        success: false,
        errors: error.details.map(detail => detail.message)
      });
    }

    const data = await templeService.updateTemple(req.params.id, value);

    //* Save audit log 
    const detail = `Temple ${data.name.en} updated successfully`;
    const model = 'Temple';

    await saveAuditLog(req, req.user.id, auditLogAction.TEMPLE_UPDATED, detail, model);

    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const deleteTemple = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const data = await templeService.deleteTemple(req.params.id);

    //* Save audit log 
    const detail = `Temple ${data.name.en} deleted successfully`;
    const model = 'Temple';

    await saveAuditLog(req, req.user.id, auditLogAction.TEMPLE_DELETED, detail, model);

    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: messages.TEMPLE_DELETED, 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getAllTemples = async (req, res) => {
  try {
    const data = await templeService.getAllTemples(req.query);

    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getTempleById = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    // Get user ID if authenticated
    const user = await authenticate(req);

    const data = await templeService.getTempleById(req.params.id, user?.id);

    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getDashboardTemples = async (req, res) => {
  try {
    const data = await templeService.getDashboardTemples(req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateDashboardTemples = async (req, res) => {
  try {
    const { error } = updateDashboardTemplesSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }

    const data = await templeService.updateDashboardTemples(req.body.templeIds);

    //* Save audit log 
    const detail = 'Dashboard temples updated successfully';
    const model = 'Temple';

    await saveAuditLog(req, req.user.id, auditLogAction.TEMPLE_UPDATED, detail, model);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Dashboard temples updated successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }
    
    const { error } = uploadUrlSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const { extension } = req.body;

    const uploadData = await getPresignedUrl(extension, 'temples');

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Upload URL generated successfully',
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const deleteImage = async (req, res) => {
  try {
    const { key } = req.params;

    await deleteFile(key);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: 'Image deleted successfully'
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getTemplesDarshanSchedules = async (req, res) => {
  try {
    // Get user ID if authenticated
    const user = await authenticate(req);

    const data = await templeService.getTemplesDarshanSchedules(req.params.id, req.query, user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Temple darshan schedules retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getTemplesPoojaSchedules = async (req, res) => {
  try {
    // Get user ID if authenticated
    const user = await authenticate(req);

    const data = await templeService.getTemplesPoojaSchedules(req.params.id, req.query, user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Temple puja schedules retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Check if there are darshan schedules, pooja schedules, or events available
 * from or after a specific date and time for a given temple
 */
const checkAvailability = async (req, res) => {
  try {
    const { value, error } = checkAvailabilitySchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const { templeId, date, time } = value;

    const data = await templeService.checkAvailability(templeId, date, time);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Availability checked successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Check if there are darshan schedules, pooja schedules, or events available
 * from or after a specific date and time for a given temple
 */
const getTemplesByService = async (req, res) => {
  try {
    const { value, error } = checkServiceSchema.validate({ ...req.body, ...req.query });

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const { date, time, page, limit, sortBy, sortOrder, search } = value;
    const type = req.params.type;

    // Get user ID if authenticated
    const user = await authenticate(req);

    const data = await templeService.getTemplesByService(user, type, date, time, { page, limit, sortBy, sortOrder, search });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Success',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getTemplesServiceCode = async (req, res) => {
  try {
    const { value, error } = getTemplesServiceCodeSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const { temple, type } = value;

    const data = await templeService.getTemplesServiceCode(temple, type);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Availability checked successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createTemple,
  updateTemple,
  deleteTemple,
  getAllTemples,
  getTempleById,
  getDashboardTemples,
  updateDashboardTemples,
  getUploadUrl,
  deleteImage,
  getTemplesDarshanSchedules,
  getTemplesPoojaSchedules,
  checkAvailability,
  getTemplesByService,
  getTemplesServiceCode
};
