const express = require('express');
const router = express.Router();
const templeController = require('./controller');
const auth = require('../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../middleware/roleCheck');

router.post('/', auth, isAdminOrSuperAdmin, templeController.createTemple);
router.delete('/:id', auth, isAdminOrSuperAdmin, templeController.deleteTemple);
router.get('/', auth, templeController.getAllTemples);
router.get('/dashboard-temples', auth, isAdminOrSuperAdmin, templeController.getDashboardTemples);
router.get('/:id', templeController.getTempleById);
router.get('/:id/darshan-schedules', templeController.getTemplesDarshanSchedules);
router.get('/:id/pooja-schedules', templeController.getTemplesPoojaSchedules);
router.put('/dashboard-temples', auth, isAdminOrSuperAdmin, templeController.updateDashboardTemples);
router.put('/:id', auth, isAdminOrSuperAdmin, templeController.updateTemple);
router.post('/upload-url', auth, isAdminOrSuperAdmin, templeController.getUploadUrl);
router.delete('/image/:key(*)', auth, isAdminOrSuperAdmin, templeController.deleteImage);
router.post('/check-availability', templeController.checkAvailability);
router.post('/service-code', auth, templeController.getTemplesServiceCode);
router.post('/service/:type', templeController.getTemplesByService);

module.exports = router;
