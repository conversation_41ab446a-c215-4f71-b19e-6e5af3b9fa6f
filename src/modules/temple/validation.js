const joi = require('joi');
const mongoose = require('mongoose');
const { assignmentMode } = require('../../constants/dbEnums');

const timePattern = /^([01]?[0-9]|2[0-3]):[0-5][0-9] (AM|PM)$/;

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string()
  .custom(validateObjectId)
  .required()
  .messages({
    'any.invalid': 'Invalid temple ID format',
    'any.required': 'Temple ID is required'
  });

// Define the payout component validation schema
const payoutComponentSchema = joi.object({
  name: joi.string().required().messages({
    'any.required': 'Payout component name is required'
  }),
  amount: joi.number().min(0).required().messages({
    'number.base': 'Amount must be a number',
    'number.min': 'Amount cannot be negative',
    'any.required': 'Amount is required'
  })
});

const createTempleSchema = joi.object({
  name: joi.string()
    .max(255)
    .required()
    .messages({
      'string.max': 'Temple Name cannot exceed 255 characters',
      'any.required': 'Temple Name is required'
    }),
  promotionalKitCost: joi.number()
    .min(1)
    .allow('')
    .allow(null)
    .optional()
    .messages({
      'number.base': 'Promotional kit cost must be a number',
      'number.min': 'Promotional kit cost must be greater than 0 if provided'
    }),
  pujariBookingAssignmentMode: joi.string()
    .valid(...Object.values(assignmentMode))
    .optional()
    .default(assignmentMode.AUTOMATIC)
    .messages({
      'any.only': `Pujari booking assignment mode must be one of: ${Object.values(assignmentMode).join(', ')}`
    }),
  category: joi.string()
    .custom(validateObjectId)
    .optional()
    .messages({
      'any.invalid': 'Invalid category ID format',
      'any.required': 'Category ID is required'
    }),
  tagLine: joi.string()
    .required()
    .messages({
      'any.required': 'Tag Line is required'
    }),
  description: joi.string()
    .min(100)
    .required()
    .messages({
      'string.min': 'General Description must be at least 100 characters long',
      'any.required': 'General Description is required'
    }),
  history: joi.string()
    .min(100)
    .required()
    .messages({
      'string.min': 'History must be at least 100 characters long',
      'any.required': 'History is required'
    }),
  significanceAndArchitecture: joi.string()
    .min(100)
    .required()
    .messages({
      'string.min': 'Significance and Architecture must be at least 100 characters long',
      'any.required': 'Significance and Architecture is required'
    }),
  state: joi.string().required(),
  city: joi.string().required(),
  locationUrl: joi.string()
    .pattern(/^https:\/\/www\.google\.com\/maps/)
    .allow('')
    .messages({
      'string.pattern.base': 'Please enter a valid Google Maps URL'
    }),
  deity: joi.string()
    .max(100)
    .required()
    .messages({
      'string.max': 'Deity cannot exceed 100 characters',
      'any.required': 'Deity is required'
    }),
  guidelines: joi.string().allow(''),
  tags: joi.array().items(joi.string()),
  showOnHomepage: joi.boolean().default(false),
  services: joi.array().items(
    joi.string().valid('PHYSICAL_POOJA', 'PHYSICAL_DARSHAN', 'VIRTUAL_POOJA')
  ),
  templeAdmin: joi.object({
    firstName: joi.string().max(50).required(),
    lastName: joi.string().max(50).required(),
    email: joi.string().email().required(),
    phoneNumber: joi.string().pattern(/^\+[1-9]\d{1,14}$/),
    whatsappEnabled: joi.boolean().default(false)
  }),
  images: joi.array().items(
    joi.string()
  ).max(10),
  video: joi.string().allow(null).optional(),
  operatingHours: joi.object({
    openTime: joi.string()
      .pattern(timePattern)
      .optional()
      .messages({
        'string.pattern.base': 'Invalid time format. Use HH:MM AM/PM format'
      }),
    closeTime: joi.string()
      .pattern(timePattern)
      .optional()
      .messages({
        'string.pattern.base': 'Invalid time format. Use HH:MM AM/PM format'
      })
  }).optional(),
  aartiTimings: joi.string().optional(),
  configurableField: joi.object({
    name: joi.string().optional().allow(''),
    description: joi.string().optional().allow('')
  }).optional(),
  payoutConfiguration: joi.array().items(payoutComponentSchema).optional()
});

const updateTempleSchema = joi.object({
  name: joi.string()
    .max(255)
    .messages({
      'string.max': 'Temple Name cannot exceed 255 characters'
    }),
  promotionalKitCost: joi.number()
    .min(1)
    .optional()
    .allow('')
    .allow(null)
    .messages({
      'number.base': 'Promotional kit cost must be a number',
      'number.min': 'Promotional kit cost must be greater than 0 if provided'
    }),
  pujariBookingAssignmentMode: joi.string()
    .valid(...Object.values(assignmentMode))
    .optional()
    .messages({
      'any.only': `Pujari booking assignment mode must be one of: ${Object.values(assignmentMode).join(', ')}`
    }),
  category: joi.string().optional()
    .custom(validateObjectId)
    .messages({
      'any.invalid': 'Invalid category ID format'
    }),
  tagLine: joi.string()
    .messages({
      'any.required': 'Tag Line is required'
    }),
  description: joi.string()
    .min(100)
    .messages({
      'string.min': 'General Description must be at least 100 characters long'
    }),
  history: joi.string()
    .min(100)
    .messages({
      'string.min': 'History must be at least 100 characters long'
    }),
  significanceAndArchitecture: joi.string()
    .min(100)
    .messages({
      'string.min': 'Significance and Architecture must be at least 100 characters long'
    }),
  state: joi.string(),
  city: joi.string(),
  locationUrl: joi.string()
    .pattern(/^https:\/\/www\.google\.com\/maps/)
    .allow('')
    .messages({
      'string.pattern.base': 'Please enter a valid Google Maps URL'
    }),
  deity: joi.string()
    .max(100)
    .messages({
      'string.max': 'Deity cannot exceed 100 characters'
    }),
  guidelines: joi.string().allow(''),
  tags: joi.array().items(joi.string()),
  showOnHomepage: joi.boolean(),
  services: joi.array().items(
    joi.string().valid('PHYSICAL_POOJA', 'PHYSICAL_DARSHAN', 'VIRTUAL_POOJA')
  ),
  templeAdmins: joi.array().items(joi.object({
    firstName: joi.string().max(50).required(),
    lastName: joi.string().max(50).required(),
    email: joi.string().email().required(),
    phoneNumber: joi.string().pattern(/^\+[1-9]\d{1,14}$/),
    whatsappEnabled: joi.boolean().default(false),
    status: joi.string().valid('ACTIVE', 'INACTIVE', 'BLOCKED')
  })),
  images: joi.array().items(
    joi.string()
  ).max(10),
  video: joi.string().optional().allow(null),
  operatingHours: joi.object({
    openTime: joi.string()
      .pattern(timePattern)
      .messages({
        'string.pattern.base': 'Invalid time format. Use HH:MM AM/PM format'
      }),
    closeTime: joi.string()
      .pattern(timePattern)
      .messages({
        'string.pattern.base': 'Invalid time format. Use HH:MM AM/PM format'
      })
  }),
  aartiTimings: joi.string(),
  configurableField: joi.object({
    name: joi.string().allow(''),
    description: joi.string().allow(''),
  }),
  payoutConfiguration: joi.array().items(payoutComponentSchema).optional()
}).min(1); // Ensure at least one field is being updated

const uploadUrlSchema = joi.object({
  extension: joi.string()
    .valid('jpeg', 'jpg', 'png', 'mp4', 'mov', 'hevc', 'flv', 'webm', 'mpeg')
    .required()
    .messages({
      'any.required': 'File extension is required',
      'any.only': 'Invalid file extension. Only JPEG, JPG, PNG, MP4, MOV, HEVC, FLV, WEBM and MPEG are allowed'
    })
});

const updateDashboardTemplesSchema = joi.object({
  templeIds: joi.array()
    .items(joi.string().regex(/^[0-9a-fA-F]{24}$/))
    .max(6)
    .required()
    .messages({
      'array.max': 'Cannot select more than 6 temples for dashboard',
      'array.base': 'Temple IDs must be an array',
      'string.pattern.base': 'Invalid temple ID format'
    })
});

const searchTempleSchema = joi.object({
  search: joi.string(),
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(10),
  sortBy: joi.string().valid('name', 'deity', 'status', 'createdAt', 'updatedAt').default('createdAt'),
  sortOrder: joi.number().valid(1, -1).default(-1)
});

const checkAvailabilitySchema = joi.object({
  templeId: joi.string()
    .custom(validateObjectId)
    .required()
    .messages({
      'any.invalid': 'Invalid temple ID format',
      'any.required': 'Temple ID is required'
    }),
  date: joi.string()
    .required()
    .messages({
      'any.required': 'Date is required'
    }),
  time: joi.string()
    .pattern(timePattern)
    .required()
    .messages({
      'string.pattern.base': 'Invalid time format. Use HH:MM AM/PM format',
      'any.required': 'Time is required'
    })
});

const checkServiceSchema = joi.object({
  date: joi.string()
    .required()
    .messages({
      'any.required': 'Date is required'
    }),
  time: joi.string()
    .pattern(timePattern)
    .required()
    .messages({
      'string.pattern.base': 'Invalid time format. Use HH:MM AM/PM format',
      'any.required': 'Time is required'
    }),
  search: joi.string(),
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(10),
  sortBy: joi.string().valid('name', 'deity', 'createdAt', 'updatedAt').default('createdAt'),
  sortOrder: joi.number().valid(1, -1).default(-1)
});

const getTemplesServiceCodeSchema = joi.object({
  temple: joi.string()
    .custom(validateObjectId)
    .required()
    .messages({
      'any.invalid': 'Invalid temple ID format',
      'any.required': 'Temple ID is required'
    }),
  type: joi.string()
    .valid('PHYSICAL_POOJA', 'PHYSICAL_DARSHAN', 'VIRTUAL_POOJA', 'EVENT')
    .required()
    .messages({
      'any.required': 'Service type is required',
      'any.only': 'Invalid service type'
    })
});

module.exports = {
  createTempleSchema,
  updateTempleSchema,
  uploadUrlSchema,
  updateDashboardTemplesSchema,
  searchTempleSchema,
  validateId,
  checkAvailabilitySchema,
  checkServiceSchema,
  getTemplesServiceCodeSchema
};
