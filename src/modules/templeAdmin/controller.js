const { apiResponse, errorApiResponse } = require('../../config/responseHandler');
const { commonConstants } = require('../../constants/common');
const { messages } = require('../../messages');
const templeAdminService = require('./service');
const { 
  createTempleAdminSchema, 
  updateTempleAdminSchema,
  searchTempleAdminSchema,
  updateStatusSchema,
  loginSchema,
  assignPujariToBookingSchema
} = require('./validation');
const { saveAuditLog } = require('../../utils/auditLogger');
const { auditLogAction } = require('../../constants/dbEnums');

const createTempleAdmin = async (req, res) => {
  try {
    const { value, error } = createTempleAdminSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await templeAdminService.createTempleAdmin(value);

    //* Save audit log 
    const detail = `Temple admin ${data.name} created successfully`;
    const model = 'TempleAdmin';

    await saveAuditLog(req, req.user.id, auditLogAction.TEMPLE_ADMIN_CREATED, detail, model);

    return apiResponse({ 
      res, 
      code: commonConstants.SUCCESS.CODE, 
      message: 'Temple admin created successfully', 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateTempleAdmin = async (req, res) => {
  try {
    const { error } = updateTempleAdminSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await templeAdminService.updateTempleAdmin(req.params.id, req.body);

    //* Save audit log 
    const detail = `Temple admin ${data.name} updated successfully`;
    const model = 'TempleAdmin';

    await saveAuditLog(req, req.user.id, auditLogAction.TEMPLE_ADMIN_UPDATED, detail, model);

    return apiResponse({ 
      res, 
      code: commonConstants.SUCCESS.CODE, 
      message: 'Temple admin updated successfully', 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateAdminStatus = async (req, res) => {
  try {
    const { error } = updateStatusSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await templeAdminService.updateAdminStatus(req.params.id, req.body.status);

    //* Save audit log 
    const detail = `Temple admin ${data.name} ${req.body.status.toLowerCase()} successfully`;
    const model = 'TempleAdmin';

    await saveAuditLog(req, req.user.id, auditLogAction.TEMPLE_ADMIN_UPDATED, detail, model);

    return apiResponse({ 
      res, 
      code: commonConstants.SUCCESS.CODE, 
      message: `Temple admin ${req.body.status.toLowerCase()}d successfully`, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const resetPassword = async (req, res) => {
  try {
    const data = await templeAdminService.resetPassword(req.params.id);

    //* Save audit log 
    const detail = `Temple admin ${data.name} password reset successfully`;
    const model = 'TempleAdmin';

    await saveAuditLog(req, req.user.id, auditLogAction.TEMPLE_ADMIN_UPDATED, detail, model);

    return apiResponse({ 
      res, 
      code: commonConstants.SUCCESS.CODE, 
      message: 'Password reset successfully', 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getTempleAdmins = async (req, res) => {
  try {
    const { error } = searchTempleAdminSchema.validate(req.query);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await templeAdminService.searchTempleAdmins(req.query);

    return apiResponse({ 
      res, 
      code: commonConstants.SUCCESS.CODE, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getTempleAdminById = async (req, res) => {
  try {
    const data = await templeAdminService.getTempleAdminById(req.params.id);

    return apiResponse({ 
      res, 
      code: commonConstants.SUCCESS.CODE, 
      message: 'Temple admin retrieved successfully', 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const login = async (req, res) => {
  try {
    const { value, error } = loginSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await templeAdminService.login(value);

    return apiResponse({ 
      res, 
      code: commonConstants.SUCCESS.CODE, 
      message: messages.SUCCESS, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* Function to assign pujari to booking manually 
const assignPujariToBookingManually = async (req, res) => {
  try {
    const { error } = assignPujariToBookingSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await templeAdminService.assignPujariToBookingManually(req.body, req.user.id);

    return apiResponse({ 
      res, 
      code: commonConstants.SUCCESS.CODE, 
      message: 'Pujari assigned successfully', 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createTempleAdmin,
  updateTempleAdmin,
  updateAdminStatus,
  resetPassword,
  login,
  getTempleAdmins,
  getTempleAdminById,
  assignPujariToBookingManually
};
