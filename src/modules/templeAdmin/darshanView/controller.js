const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const { getDarshanSchedulesSchema, validateIdSchema } = require('./validation');
const darshanViewService = require('./service');

/**
 * Get all darshan schedules for the temple admin's temple
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDarshanSchedules = async (req, res) => {
  try {
    const { error, value } = getDarshanSchedulesSchema.validate(req.query);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await darshanViewService.getDarshanSchedules(value, req.user);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get a specific darshan schedule by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDarshanScheduleById = async (req, res) => {
  try {
    const { error } = validateIdSchema.validate({ id: req.params.id });

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await darshanViewService.getDarshanScheduleById(req.params.id, req.user);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getDarshanSchedules,
  getDarshanScheduleById
};
