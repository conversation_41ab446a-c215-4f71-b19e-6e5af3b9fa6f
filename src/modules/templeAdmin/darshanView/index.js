const express = require('express');
const router = express.Router();
const darshanViewController = require('./controller');
const auth = require('../../../middleware/auth');
const { isTempleAdmin } = require('../../../middleware/roleCheck');

// Route to get all darshan schedules for the temple admin's temple
router.get('/', auth, isTempleAdmin, darshanViewController.getDarshanSchedules);

// Route to get a specific darshan schedule by ID
router.get('/:id', auth, isTempleAdmin, darshanViewController.getDarshanScheduleById);

module.exports = router;
