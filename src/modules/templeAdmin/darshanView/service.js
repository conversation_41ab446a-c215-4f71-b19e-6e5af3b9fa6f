const DarshanSchedule = require('../../../models/DarshanSchedule');
const TempleAdmin = require('../../../models/TempleAdmin');
const { throwBadRequestError } = require('../../../errors');
const moment = require('moment');

/**
 * Get all darshan schedules for the temple admin's temple
 * @param {Object} params - Query parameters
 * @param {Object} user - User object from request
 * @returns {Object} - Darshan schedules and pagination info
 */
const getDarshanSchedules = async (params, user) => {
  // Get the temple admin's temple
  const templeAdmin = await TempleAdmin.findById(user.id);

  if (!templeAdmin) {
    throwBadRequestError('Unauthorized access');
  }

  const {
    page = 1,
    limit = 10,
    search,
    dateFilter,
    startDate,
    endDate,
    specificDate,
    sortBy = 'createdAt',
    sortOrder = -1
  } = params;

  const filter = { temple: templeAdmin.temple, status: 'ACTIVE' };

  // Create search conditions if provided
  const searchConditions = search && search.trim() ? [
    { name: { $regex: search, $options: 'i' } },
    { description: { $regex: search, $options: 'i' } }
  ] : null;

  // Use local time instead of UTC
  const today = moment().format('YYYY-MM-DD') + 'T00:00:00.000Z';

  // Apply date filtering
  let dateConditions = [];

  if (dateFilter === 'TODAY') {
    // For specific date schedules that match today
    const todayEnd = moment().format('YYYY-MM-DD') + 'T23:59:59.999Z';

    dateConditions = [
      // SPECIFIC_DATE schedules for today
      {
        dateType: 'SPECIFIC_DATE',
        specificDate: {
          $gte: today,
          $lte: todayEnd
        }
      },
      // DATE_RANGE schedules that include today
      {
        dateType: 'DATE_RANGE',
        'dateRange.startDate': { $lte: todayEnd },
        'dateRange.endDate': { $gte: today }
      }
    ];
  } else if (dateFilter === 'PAST') {
    // For schedules in the past
    dateConditions = [
      // SPECIFIC_DATE schedules in the past
      {
        dateType: 'SPECIFIC_DATE',
        specificDate: { $lt: today }
      },
      // DATE_RANGE schedules that ended in the past
      {
        dateType: 'DATE_RANGE',
        'dateRange.endDate': { $lt: today }
      }
    ];
  } else if (dateFilter === 'FUTURE') {
    // For schedules in the future
    const tomorrow = new Date(today);

    tomorrow.setDate(tomorrow.getDate() + 1);

    dateConditions = [
      // SPECIFIC_DATE schedules in the future
      {
        dateType: 'SPECIFIC_DATE',
        specificDate: { $gte: tomorrow }
      },
      // DATE_RANGE schedules that start in the future
      {
        dateType: 'DATE_RANGE',
        'dateRange.startDate': { $gte: tomorrow }
      }
    ];
  } else if (dateFilter === 'CUSTOM' && startDate && endDate) {
    // For custom date range
    const startDateObj = moment(startDate).format('YYYY-MM-DD') + 'T00:00:00.000Z';
    const endDateObj = moment(endDate).format('YYYY-MM-DD') + 'T23:59:59.999Z';

    dateConditions = [
      // SPECIFIC_DATE schedules within the range
      {
        dateType: 'SPECIFIC_DATE',
        specificDate: {
          $gte: startDateObj,
          $lte: endDateObj
        }
      },
      // DATE_RANGE schedules that overlap with the range
      {
        dateType: 'DATE_RANGE',
        $and: [
          { 'dateRange.startDate': { $lte: endDateObj } },
          { 'dateRange.endDate': { $gte: startDateObj } }
        ]
      }
    ];
  } else if (specificDate) {
    // For a specific date
    const specificDateObj = moment(specificDate).format('YYYY-MM-DD') + 'T00:00:00.000Z';
    const specificDateEnd = moment(specificDate).format('YYYY-MM-DD') + 'T23:59:59.999Z';

    dateConditions = [
      // SPECIFIC_DATE schedules for the specific date
      {
        dateType: 'SPECIFIC_DATE',
        specificDate: {
          $gte: specificDateObj,
          $lte: specificDateEnd
        }
      },
      // DATE_RANGE schedules that include the specific date
      {
        dateType: 'DATE_RANGE',
        'dateRange.startDate': { $lte: specificDateEnd },
        'dateRange.endDate': { $gte: specificDateObj }
      }
    ];
  }

  // Combine search and date conditions
  if (searchConditions && dateConditions.length > 0) {
    // If both search and date filters are applied
    filter.$and = [
      { $or: searchConditions },
      { $or: dateConditions }
    ];
  } else if (searchConditions) {
    // If only search filter is applied
    filter.$or = searchConditions;
  } else if (dateConditions.length > 0) {
    // If only date filter is applied
    filter.$or = dateConditions;
  }

  // Set up sorting
  const sort = {};

  sort[sortBy] = parseInt(sortOrder);

  // Calculate pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Get darshan schedules
  const schedules = await DarshanSchedule.find(filter)
    .populate('temple', 'name location')
    .sort(sort).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit));

  // Calculate total count for pagination
  const total = await DarshanSchedule.countDocuments(filter);

  // Process schedules to add status
  const currentDate = new Date();
  const processedSchedules = schedules.map(schedule => {
    const scheduleObj = schedule.toObject();

    // Determine schedule status based on dates
    let status;

    if (scheduleObj.dateType === 'SPECIFIC_DATE') {
      const scheduleDate = new Date(scheduleObj.specificDate);

      if (scheduleDate.toDateString() === currentDate.toDateString()) {
        status = 'ACTIVE';
      } else if (scheduleDate < currentDate) {
        status = 'COMPLETED';
      } else {
        status = 'UPCOMING';
      }
    } else if (scheduleObj.dateType === 'DATE_RANGE') {
      const startDate = new Date(scheduleObj.dateRange.startDate);
      const endDate = new Date(scheduleObj.dateRange.endDate);

      if (currentDate >= startDate && currentDate <= endDate) {
        status = 'ACTIVE';
      } else if (currentDate > endDate) {
        status = 'COMPLETED';
      } else {
        status = 'UPCOMING';
      }
    }

    return {
      ...scheduleObj,
      scheduleStatus: status
    };
  });

  return {
    schedules: processedSchedules,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

/**
 * Get a specific darshan schedule by ID
 * @param {string} scheduleId - Darshan schedule ID
 * @param {Object} user - User object from request
 * @returns {Object} - Darshan schedule with status
 */
const getDarshanScheduleById = async (scheduleId, user) => {
  // Get the temple admin's temple
  const templeAdmin = await TempleAdmin.findById(user.id);

  if (!templeAdmin) {
    throwBadRequestError('Unauthorized access');
  }

  // Get the darshan schedule
  const schedule = await DarshanSchedule.findOne({
    _id: scheduleId,
    temple: templeAdmin.temple
  }).populate('temple', 'name location');

  // Check if schedule exists and belongs to the temple admin's temple
  if (!schedule) {
    throwBadRequestError('Darshan schedule not found or not associated with your temple');
  }

  // Process schedule to add status
  const currentDate = new Date();
  const scheduleObj = schedule.toObject();

  // Determine schedule status based on dates
  let status;

  if (scheduleObj.dateType === 'SPECIFIC_DATE') {
    const scheduleDate = new Date(scheduleObj.specificDate);

    if (scheduleDate.toDateString() === currentDate.toDateString()) {
      status = 'ACTIVE';
    } else if (scheduleDate < currentDate) {
      status = 'COMPLETED';
    } else {
      status = 'UPCOMING';
    }
  } else if (scheduleObj.dateType === 'DATE_RANGE') {
    const startDate = new Date(scheduleObj.dateRange.startDate);
    const endDate = new Date(scheduleObj.dateRange.endDate);

    if (currentDate >= startDate && currentDate <= endDate) {
      status = 'ACTIVE';
    } else if (currentDate > endDate) {
      status = 'COMPLETED';
    } else {
      status = 'UPCOMING';
    }
  }

  return {
    ...scheduleObj,
    scheduleStatus: status
  };
};

module.exports = {
  getDarshanSchedules,
  getDarshanScheduleById
};
