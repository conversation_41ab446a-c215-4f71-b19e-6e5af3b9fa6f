const express = require('express');
const router = express.Router();
const templeAdminController = require('./controller');
const authController = require('../user/auth/controller');
const auth = require('../../middleware/auth');
const liveDarshanRoutes = require('./liveDarshan');
const darshanViewRoutes = require('./darshanView');
const poojaViewRoutes = require('./poojaView');
const settlementRoutes = require('./settlement');
const { isAdminOrSuperAdmin } = require('../../middleware/roleCheck');

router.use('/live-darshan', liveDarshanRoutes);
router.use('/darshan-view', darshanViewRoutes);
router.use('/pooja-view', poojaViewRoutes);
router.use('/settlement', settlementRoutes);

router.post('/', auth, isAdminOrSuperAdmin, templeAdminController.createTempleAdmin);
router.get('/', auth, isAdminOrSuperAdmin, templeAdminController.getTempleAdmins);
router.get('/:id', auth, isAdminOrSuperAdmin, templeAdminController.getTempleAdminById);
router.put('/:id', auth, isAdminOrSuperAdmin, templeAdminController.updateTempleAdmin);
router.put('/:id/status', auth, isAdminOrSuperAdmin, templeAdminController.updateAdminStatus);
router.post('/:id/reset-password', auth, isAdminOrSuperAdmin, templeAdminController.resetPassword);
router.post('/login', templeAdminController.login);
router.delete('/logout', auth, authController.logout);

module.exports = router;
