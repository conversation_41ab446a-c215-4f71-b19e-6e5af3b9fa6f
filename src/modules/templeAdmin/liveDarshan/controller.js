const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const { createLiveDarshanSchema, updateLiveDarshanSchema, validateId, getLiveDarshanListSchema, updateLiveDarshanStatusSchema } = require('./validation');
const liveDarshanService = require('./service');

//* 1. Function to create live darshan
const createLiveDarshan = async (req, res) => {
  try {
    const { error } = createLiveDarshanSchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const liveDarshan = await liveDarshanService.createLiveDarshan(req.body, req.user);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.LIVE_DARSHAN_CREATED,
      data: liveDarshan
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 2. Function to update live darshan
const updateLiveDarshan = async (req, res) => {
  try {
    const { error: idError } = validateId.validate(req.params.id);
    
    if (idError) {
      return res.status(400).json({ message: idError.details[0].message });
    }

    const { error: updateError } = updateLiveDarshanSchema.validate(req.body);
    
    if (updateError) {
      return res.status(400).json({ message: updateError.details[0].message });
    }

    const liveDarshan = await liveDarshanService.updateLiveDarshan(req.params.id, req.body, req.user);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.LIVE_DARSHAN_UPDATED,
      data: liveDarshan
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 3. Function to delete live darshan 
const deleteLiveDarshan = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);
    
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const liveDarshan = await liveDarshanService.deleteLiveDarshan(req.params.id);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.LIVE_DARSHAN_DELETED,
      data: liveDarshan
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 4. Function to get all live darshans
const getAllLiveDarshans = async (req, res) => {
  try {
    const { error, value } = getLiveDarshanListSchema.validate(req.query);
    
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const liveDarshans = await liveDarshanService.getAllLiveDarshans(value, req.user);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data: liveDarshans
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 5. Function to get live darshan by id
const getLiveDarshanById = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);
    
    if (error) {
      return res.status(400).json({ 
        status: false,
        message: error.details[0].message 
      });
    }

    const liveDarshan = await liveDarshanService.getLiveDarshanById(req.params.id, req.user);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data: liveDarshan
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateStatusByAdmin = async (req, res) => {
  try {
    const { error: idError } = validateId.validate(req.params.id);
    
    if (idError) {
      return res.status(400).json({ message: idError.details[0].message });
    }

    const { error: updateError } = updateLiveDarshanStatusSchema.validate(req.body);
    
    if (updateError) {
      return res.status(400).json({ message: updateError.details[0].message });
    }

    const liveDarshan = await liveDarshanService.updateLiveDarshanStatus(req.params.id, req.body);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.LIVE_DARSHAN_UPDATED,
      data: liveDarshan
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getAllLiveDarshans,
  getLiveDarshanById,
  createLiveDarshan,
  updateLiveDarshan,
  updateStatusByAdmin,
  deleteLiveDarshan
};
