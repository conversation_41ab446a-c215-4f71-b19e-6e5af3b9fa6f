const express = require('express');
const router = express.Router();
const liveDarshanController = require('./controller');
const auth = require('../../../middleware/auth');
const { isTempleAdmin } = require('../../../middleware/roleCheck');

router.get('/', auth, liveDarshanController.getAllLiveDarshans);
router.get('/:id', auth, isTempleAdmin, liveDarshanController.getLiveDarshanById);
router.post('/', auth, isTempleAdmin, liveDarshanController.createLiveDarshan);
router.put('/:id', auth, isTempleAdmin, liveDarshanController.updateLiveDarshan);
router.delete('/:id', auth, isTempleAdmin, liveDarshanController.deleteLiveDarshan);

module.exports = router;