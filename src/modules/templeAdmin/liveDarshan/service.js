const LiveDarshan = require('../../../models/LiveDarshan');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const TempleAdmin = require('../../../models/TempleAdmin');
const { darshanStatus, userTypeValue, notificationType } = require('../../../constants/dbEnums');
const { translateDataForStore } = require('../../../utils/translateInput');
const { transformTranslatedFields } = require('../../../utils/localizer');
const User = require('../../../models/User');
const Language = require('../../../models/Language');
const Notification = require('../../../models/Notification');
const Admin = require('../../../models/Admin');

//* 1. Function to create live darshan
const createLiveDarshan = async (liveDarshanData, user) => {
  const templeAdmin = await TempleAdmin.findById(user.id);

  if (!templeAdmin) {
    throwBadRequestError(messages.TEMPLE_ADMIN_NOT_FOUND);
  }

  liveDarshanData.temple = templeAdmin.temple;
  liveDarshanData.templeAdmin = user.id;
  liveDarshanData.status = darshanStatus.PENDING;

  // Translate fields
  const translatedFields = [ 'name', 'description' ];
  const translatedData = await translateDataForStore(translatedFields, liveDarshanData);

  translatedFields.forEach(field => {
    if (liveDarshanData[field]) {
      delete liveDarshanData[field];
    }
  });

  liveDarshanData = {
    ...liveDarshanData,
    ...translatedData
  };

  const liveDarshan = await LiveDarshan.create(liveDarshanData);

  if (!liveDarshan) {
    throwBadRequestError(messages.LIVE_DARSHAN_CREATION_FAILED);
  }

  //* Send notification to admin
  const admin = await Admin.findOne({ userType: userTypeValue.SUPER_ADMIN }).select('name userType');

  await Notification.create({
    userId: admin._id,
    title: 'Live Darshan Created',
    body: 'A new live darshan has been created. Please review and approve.',
    type: userTypeValue.SUPER_ADMIN,
    redirectPath: '/admin/live-darshans',
    metaData: {
      liveDarshanId: liveDarshan._id,
      liveDarshanName: liveDarshan.name.en || liveDarshan.name
    },
    notificationType: notificationType.LIVE_DARSHAN_APPROVAL
  });

  return liveDarshan;
};

//* 2. Function to update live darshan
const updateLiveDarshan = async (id, updateData, user) => {

  const liveDarshan = await LiveDarshan.findOne({
    _id: id,
    templeAdmin: user.id
  });

  if (!liveDarshan) {
    throwBadRequestError(messages.LIVE_DARSHAN_NOT_FOUND);
  }

  updateData.status = darshanStatus.PENDING;

  // Translate fields if provided
  const translatedFields = [ 'name', 'description' ];
  const hasTranslatableFields = translatedFields.some(field => updateData[field]);

  if (hasTranslatableFields) {
    const translatedData = await translateDataForStore(translatedFields, updateData);

    translatedFields.forEach(field => {
      if (updateData[field]) {
        delete updateData[field];
      }
    });

    updateData = {
      ...updateData,
      ...translatedData
    };
  }

  return await LiveDarshan.findByIdAndUpdate(
    id,
    updateData,
    { new: true }
  );
};

const updateLiveDarshanStatus = async (id, updateData) => {
  const liveDarshan = await LiveDarshan.findById(
    id
  );

  if (!liveDarshan) {
    throwBadRequestError(messages.LIVE_DARSHAN_NOT_FOUND);
  }

  const updatedLiveDarshan = await LiveDarshan.findByIdAndUpdate(
    id,
    updateData,
    { new: true }
  );

  return updatedLiveDarshan;
};

//* 3. Function to delete live darshan
const deleteLiveDarshan = async (id) => {
  const liveDarshan = await LiveDarshan.findById(id);

  if (!liveDarshan) {
    throwBadRequestError(messages.LIVE_DARSHAN_NOT_FOUND);
  }

  if (liveDarshan.deletedAt) {
    throwBadRequestError('Live darshan already deleted');
  }

  //* Soft delete live darshan
  liveDarshan.deletedAt = new Date();
  await liveDarshan.save();

  return liveDarshan;
};

//* 4. Function to get all live darshans
const getAllLiveDarshans = async (params, user) => {
  const { page = 1, limit = 10, sortBy, sortOrder } = params;
  const skip = (parseInt(page) - 1) * parseInt(limit);

  let language = { code: 'en' };

  if (user) {
    const loggedInUser = await User.findById(user.id);

    if (loggedInUser && loggedInUser?.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  const queryObj = {};

  queryObj.deletedAt = null;

  if (user.userType === userTypeValue.TEMPLE_ADMIN) {
    queryObj.templeAdmin = user.id;
  } else if (user.userType === userTypeValue.USER) {
    const today = new Date();
    const startOfToday = new Date(today.setUTCHours(0, 0, 0, 0));
    const startOfTomorrow = new Date(today.setUTCDate(today.getUTCDate() + 1));

    startOfTomorrow.setUTCHours(0, 0, 0, 0);
    queryObj.scheduledAt = {
      $gte: startOfToday,
      $lt: startOfTomorrow
    };
    queryObj.status = darshanStatus.APPROVED;
  }

  const sortOptions = {
    createdAt: -1
  };

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const liveDarshans = await LiveDarshan.find(queryObj).populate([
    { path: 'temple' },
    { path: 'templeAdmin', select: '-password' }
  ])
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit)).lean();

  const total = await LiveDarshan.countDocuments(queryObj);

  return {
    records: await transformTranslatedFields(liveDarshans, language.code),
    pagination: {
      total: total || 0,
      page: parseInt(page),
      pages: Math.ceil(total / parseInt(limit)),
      limit: parseInt(limit),
    }
  };
};

//* 5. Function to get live darshan by id
const getLiveDarshanById = async (id, user) => {
  const liveDarshan = await LiveDarshan.findById(id).populate([
    { path: 'temple' },
    { path: 'templeAdmin', select: '-password' }
  ]).lean();

  if (!liveDarshan) {
    throwBadRequestError(messages.LIVE_DARSHAN_NOT_FOUND);
  }

  let language = { code: 'en' };

  if (user) {
    const loggedInUser = await User.findById(user.id);

    language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  }
  return await transformTranslatedFields(liveDarshan, language.code);
};

module.exports = {
  getAllLiveDarshans,
  getLiveDarshanById,
  createLiveDarshan,
  updateLiveDarshan,
  updateLiveDarshanStatus,
  deleteLiveDarshan
};
