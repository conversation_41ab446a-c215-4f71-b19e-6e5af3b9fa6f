const joi = require('joi');
const mongoose = require('mongoose');
const { darshanStatus } = require('../../../constants/dbEnums');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const createLiveDarshanSchema = joi.object({
  url: joi.string()
    .uri({
      scheme: [ 'http', 'https' ]
    })
    .required()
    .messages({
      'string.uri': 'Please provide a valid URL (must start with http:// or https://)',
      'string.empty': 'URL is required',
      'any.required': 'URL is required'
    }),
  name: joi.string()
    .max(100)
    .allow('', null)
    .messages({
      'string.max': 'Name cannot exceed 100 characters'
    }),
  description: joi.string()
    .max(500)
    .optional()
    .allow('', null)
    .messages({
      'string.max': 'Description cannot exceed 500 characters'
    }),
  scheduledAt: joi.date().iso().required().messages({
    'date.greater': 'Scheduled date must be in the future',
    'date.format': 'Scheduled date must be in ISO format (YYYY-MM-DD)',
    'any.required': 'Scheduled date is required',
  }),
});

const updateLiveDarshanSchema = joi.object({
  url: joi.string()
    .uri({
      scheme: [ 'http', 'https' ]
    })
    .messages({
      'string.uri': 'Please provide a valid URL (must start with http:// or https://)',
      'string.empty': 'URL is required'
    }),
  name: joi.string()
    .max(100)
    .allow('', null)
    .messages({
      'string.max': 'Name cannot exceed 100 characters'
    }),
  description: joi.string()
    .max(500)
    .allow('', null)
    .messages({
      'string.max': 'Description cannot exceed 500 characters'
    }),
  scheduledAt: joi.date().iso().messages({
    'date.greater': 'Scheduled date must be in the future',
    'date.format': 'Scheduled date must be in ISO format (YYYY-MM-DD)',
  }),
}).min(1); // Ensure at least one field is being updated       

const updateLiveDarshanStatusSchema = joi.object({
  status: joi.string().valid(darshanStatus.APPROVED , darshanStatus.REJECTED, darshanStatus.PENDING).messages({
    'any.required': 'Status is required',
    'any.only': 'Status must be either APPROVED, REJECTED or PENDING'
  }),
  rejectionReason: joi.string().optional()
}).min(1);

const validateId = joi.string().custom(validateObjectId).required().messages({
  'any.invalid': 'Invalid ID format',
  'any.required': 'ID is required'
});

const getLiveDarshanListSchema = joi.object({
  page: joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be greater than or equal to 1'
    }),
  limit: joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be greater than or equal to 1',
      'number.max': 'Limit cannot exceed 100'
    }),
  sortBy: joi.string().valid('createdAt', 'price', 'name').default('createdAt'),
  sortOrder: joi.number().valid(1, -1).default(-1)
});

module.exports = {
  createLiveDarshanSchema,
  updateLiveDarshanSchema,
  validateId,
  getLiveDarshanListSchema,
  updateLiveDarshanStatusSchema
};
