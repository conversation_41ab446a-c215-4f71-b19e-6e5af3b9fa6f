const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const { getPoojaSchedulesSchema, validateIdSchema } = require('./validation');
const poojaViewService = require('./service');

/**
 * Get all pooja schedules for the temple admin's temple
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getPoojaSchedules = async (req, res) => {
  try {
    const { error, value } = getPoojaSchedulesSchema.validate(req.query);
    
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await poojaViewService.getPoojaSchedules(value, req.user);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get a specific pooja schedule by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getPoojaScheduleById = async (req, res) => {
  try {
    const { error } = validateIdSchema.validate({ id: req.params.id });
    
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await poojaViewService.getPoojaScheduleById(req.params.id, req.user);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getPoojaSchedules,
  getPoojaScheduleById
};
