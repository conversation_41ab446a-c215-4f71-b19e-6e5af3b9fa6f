const express = require('express');
const router = express.Router();
const poojaViewController = require('./controller');
const auth = require('../../../middleware/auth');
const { isTempleAdmin } = require('../../../middleware/roleCheck');

// Route to get all pooja schedules for the temple admin's temple
router.get('/', auth, isTempleAdmin, poojaViewController.getPoojaSchedules);

// Route to get a specific pooja schedule by ID
router.get('/:id', auth, isTempleAdmin, poojaViewController.getPoojaScheduleById);

module.exports = router;
