const bcrypt = require('bcryptjs');
const { generatePassword } = require('../../utils/passwordGenerator');
const TempleAdmin = require('../../models/TempleAdmin');
const { throwBadRequestError } = require('../../errors');
const { messages } = require('../../messages');
const { sendWhatsAppMessage } = require('../../utils/whatsappService');
const { userStatusValue, userTypeValue } = require('../../constants/dbEnums');
const { generateToken } = require('../../utils/jwt');
const { createToken } = require('../../database/queries/accessToken.query');
const path = require('path');
const { sendMail } = require('../../utils/sendMail');
const { emailSubjects } = require('../../messages/emailSubjects');
const Temple = require('../../models/Temple');
const Booking = require('../../models/Booking');
const Pujari = require('../../models/Pujari');
const { assignmentMode } = require('../../constants/dbEnums');
const { transformTranslatedFields } = require('../../utils/localizer');

const createTempleAdmin = async (adminData) => {
  // Check if admin already exists
  const existingAdmin = await TempleAdmin.findOne({ 
    $or: [
      { email: adminData.email },
      { phoneNumber: adminData.phoneNumber }
    ]
  });

  if (existingAdmin) {
    if (existingAdmin.email === adminData.email) {
      throwBadRequestError(messages.EMAIL_ALREADY_EXISTS);
    }
    throwBadRequestError(messages.PHONE_NUMBER_ALREADY_EXISTS);
  }

  // Generate temporary password
  const tempPassword = generatePassword();
  const hashedPassword = await bcrypt.hash(tempPassword, 10);

  // Create temple admin
  const templeAdmin = await TempleAdmin.create({
    ...adminData,
    password: hashedPassword,
    passwordChangeRequired: true
  });

  const temple = await Temple.findById(templeAdmin.temple);

  // Send credentials via email
  const email = adminData.email;
  const subject = emailSubjects.TEMPLE_ADMIN_ACCOUNT_CREDENTIALS;
  const templatePath = path.join(__dirname, '../../views/templeAdminLoginInfo.html');
  const data = {
    name: `${adminData.firstName} ${adminData.lastName}`,
    username: adminData.email,
    password: tempPassword,
    templeName: temple.name.en || temple.name || '',
    loginUrl: process.env.TEMPLE_ADMIN_LOGIN_URL,
    supportContact: process.env.SUPPORT_CONTACT,
    contactDetails: process.env.CONTACT_DETAILS
  };

  await sendMail(email, subject, templatePath, data);

  // // Send WhatsApp message if enabled
  // if (adminData.whatsappEnabled) {
  //   sendWhatsAppMessage(adminData.phoneNumber, {
  //     username: adminData.email,
  //     password: tempPassword
  //   });
  // }

  // Remove password from response
  const response = templeAdmin.toObject();

  delete response.password;

  return { ...response , password: tempPassword };
};

const updateTempleAdmin = async (adminId, updateData) => {
  const admin = await TempleAdmin.findById(adminId);
  
  if (!admin) {
    throwBadRequestError(messages.TEMPLE_ADMIN_NOT_FOUND);
  }

  // Don't allow email updates
  if (updateData.email) {
    delete updateData.email;
  }

  // Check if phone number already exists for another admin
  if (updateData.phoneNumber) {
    const existingAdminWithPhone = await TempleAdmin.findOne({ 
      phoneNumber: updateData.phoneNumber,
      _id: { $ne: adminId } // Exclude current admin from check
    });

    if (existingAdminWithPhone) {
      throwBadRequestError(messages.PHONE_NUMBER_ALREADY_EXISTS);
    }
  }

  Object.assign(admin, updateData);
  await admin.save();

  const response = admin.toObject();

  delete response.password;

  return response;
};

const updateAdminStatus = async (adminId, status) => {
  const admin = await TempleAdmin.findById(adminId);
  
  if (!admin) {
    throwBadRequestError(messages.TEMPLE_ADMIN_NOT_FOUND);
  }

  admin.status = status;
  await admin.save();

  // Send email notification based on status
  if (status === userStatusValue.ACTIVE) {
    
    //* Send activation email
    const subject = emailSubjects.ACCOUNT_ACTIVATED;
    const templatePath = path.join(__dirname, '../../views/templeAdminActivation.html');
    const data = {
      name: `${admin.firstName} ${admin.lastName}`
    };

    await sendMail(admin.email, subject, templatePath, data);

  } else if (status === userStatusValue.INACTIVE) {
    
    //* Send deactivation email
    const subject = emailSubjects.ACCOUNT_DEACTIVATED;
    const templatePath = path.join(__dirname, '../../views/templeAdminDeactivation.html');
    const data = {
      name: `${admin.firstName} ${admin.lastName}`
    };

    await sendMail(admin.email, subject, templatePath, data);
  }

  const response = admin.toObject();

  delete response.password;

  return response;
};

const resetPassword = async (adminId) => {
  const admin = await TempleAdmin.findById(adminId);
  
  if (!admin) {
    throwBadRequestError(messages.TEMPLE_ADMIN_NOT_FOUND);
  }

  // Generate new temporary password
  const tempPassword = generatePassword();
  const hashedPassword = await bcrypt.hash(tempPassword, 10);

  admin.password = hashedPassword;
  admin.passwordChangeRequired = true;
  await admin.save();

  // Send new credentials
  const email = admin.email;
  const subject = emailSubjects.PASSWORD_RESET_NOTIFICATION;
  const templatePath = path.join(__dirname, '../../views/templeAdminPasswordReset.html');
  const data = {
    name: `${admin.firstName} ${admin.lastName}`,
    password: tempPassword
  };

  await sendMail(email, subject, templatePath, data);

  // if (admin.whatsappEnabled) {
  //   sendWhatsAppMessage(admin.phoneNumber, {
  //     password: tempPassword
  //   });
  // }

  return admin.toObject();
};

const searchTempleAdmins = async (query) => {
  const { 
    page = 1, 
    limit = 10, 
    sortBy = 'createdAt', 
    sortOrder = -1,
    status,
    search
  } = query;
  
  const aggregatePipeline = [];

  // Lookup temple details
  aggregatePipeline.push({
    $lookup: {
      from: 'temples',
      localField: 'temple',
      foreignField: '_id',
      as: 'templeDetails'
    }
  });

  // Unwind temple details
  aggregatePipeline.push({
    $unwind: '$templeDetails'
  });

  // Match conditions
  const matchConditions = {};

  const language = { code: 'en' };

  if (search) {
    matchConditions.$or = [
      { firstName: { $regex: search, $options: 'i' } },
      { lastName: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } },
      { [`templeDetails.name.${language.code}`]: { $regex: search, $options: 'i' } },
      {
        $expr: {
          $regexMatch: {
            input: { $concat: [ '$firstName', ' ', '$lastName' ] },
            regex: search,
            options: 'i'
          }
        }
      }
    ];
  }

  if (status) {
    matchConditions.status = status;
  }

  if (Object.keys(matchConditions).length > 0) {
    aggregatePipeline.push({ $match: matchConditions });
  }

  // Handle sorting
  const sortMongoOrder = parseInt(sortOrder) || -1; // Ensure sortOrder is a number
  
  if (sortBy === 'name') {
    aggregatePipeline.push({
      $sort: {
        'firstName': sortMongoOrder,
        'lastName': sortMongoOrder
      }
    });
  } else if (sortBy === 'temple') {
    aggregatePipeline.push({
      $sort: {
        'templeDetails.name.en': sortMongoOrder
      }
    });
  } else {
    aggregatePipeline.push({
      $sort: {
        [sortBy]: sortMongoOrder
      }
    });
  }

  // Add facet for pagination
  aggregatePipeline.push({
    $facet: {
      data: [
        { $skip: (page - 1) * limit },
        { $limit: parseInt(limit) },
        {
          $project: {
            firstName: 1,
            lastName: 1,
            email: 1,
            phoneNumber: 1,
            status: 1,
            createdAt: 1,
            updatedAt: 1,
            templeName: '$templeDetails.name',
            templeId: '$templeDetails._id'
          }
        }
      ],
      total: [{ $count: 'count' }]
    }
  });

  const [ result ] = await TempleAdmin.aggregate(aggregatePipeline).collation({ locale: 'en', strength: 1 });

  // Transform the result to match the expected format
  const transformedData = result.data.map(admin => ({
    ...admin,
    temple: {
      _id: admin.templeId,
      name: admin.templeName
    }
  }));

  delete transformedData.templeId;
  delete transformedData.templeName;

  return {
    templeAdmins: await transformTranslatedFields(transformedData, language.code),
    pagination: {
      total: result.total[0]?.count || 0,
      page: parseInt(page),
      pages: Math.ceil((result.total[0]?.count || 0) / limit)
    }
  };
};

const getTempleAdminById = async (adminId) => {
  const admin = await TempleAdmin.findById(adminId)
    .select('-password')
    .populate('temple', 'name').lean();

  if (!admin) {
    throwBadRequestError(messages.TEMPLE_ADMIN_NOT_FOUND);
  }

  const adminObj = admin;

  adminObj.name = `${admin.firstName} ${admin.lastName}`;

  const language = { code: 'en' };

  return await transformTranslatedFields(adminObj, language.code);
};

const login = async ({ email, password }) => {
  const templeAdmin = await TempleAdmin.findOne({ email, status: userStatusValue.ACTIVE }).lean();

  if (!templeAdmin) {
    throwBadRequestError(messages.INVALID_CREDENTIALS);
  }

  const isMatch = await bcrypt.compare(password, templeAdmin.password);

  if (!isMatch) {
    throwBadRequestError(messages.INVALID_CREDENTIALS);
  }

  const token = await generateToken({ ...templeAdmin, phone: templeAdmin.phoneNumber, userType: userTypeValue.TEMPLE_ADMIN });
  const expireTime = process.env.JWT_EXPIRY;
  const days = expireTime.toLowerCase().replace('d', '');

  await createToken({
    token: token,
    userType: userTypeValue.TEMPLE_ADMIN,
    userId: templeAdmin._id,
    expiresAt: new Date().getTime() + (parseInt(days) * 24 * 60 * 60 * 1000),
  });

  delete templeAdmin.password;

  return {
    templeAdmin,
    token
  };
};

const assignPujariToBookingManually = async (body, templeAdminId) => {
  const { booking, pujari } = body;

  const templeAdmin = await TempleAdmin.findById(templeAdminId).populate('temple');

  if (!templeAdmin) {
    throwBadRequestError('Temple admin not found');
  }

  if (!templeAdmin.temple.pujariBookingAssignmentMode || templeAdmin.temple.pujariBookingAssignmentMode !== assignmentMode.MANUAL) {
    throwBadRequestError('Pujari assignment mode is not manual for this temple');
  }

  const bookingObj = await Booking.findById(booking);

  if (!bookingObj) {
    throwBadRequestError('Booking not found');
  }

  if (bookingObj.bookingNumber.startsWith('EVENT')) {
    throwBadRequestError('Cannot assign pujari to event booking');
  }

  if (bookingObj.status !== 'COMPLETED') {
    throwBadRequestError('Cannot assign pujari to booking that is not completed');
  }

  if (bookingObj.assignedPujari) {
    throwBadRequestError('Booking already has an assigned pujari');
  }

  const pujariObj = await Pujari.findById(pujari);

  if (!pujariObj) {
    throwBadRequestError('Pujari not found');
  }

  if (pujariObj.status !== 'ACTIVE') {
    throwBadRequestError('Pujari is not active');
  }

  if (pujariObj.approvalStatus !== 'APPROVED') {
    throwBadRequestError('Pujari is not approved');
  }

  if (pujariObj.temple.toString() !== bookingObj.temple.toString()) {
    throwBadRequestError('Pujari does not belong to the same temple as the booking');
  }

  const updatedBooking = await Booking.findByIdAndUpdate(
    booking,
    { assignedPujari: pujari },
    { new: true }
  ).populate('assignedPujari');

  return updatedBooking;
};

module.exports = {
  createTempleAdmin,
  updateTempleAdmin,
  updateAdminStatus,
  resetPassword,
  searchTempleAdmins,
  getTempleAdminById,
  login,
  assignPujariToBookingManually
};
