const express = require('express');
const router = express.Router();
const settlementController = require('./controller');
const auth = require('../../../middleware/auth');
const { isTempleAdmin } = require('../../../middleware/roleCheck');

// All routes require vendor authentication
router.use(auth, isTempleAdmin);

// Settlement routes
router.get('/settled', settlementController.getSettlementBookings);

module.exports = router;
