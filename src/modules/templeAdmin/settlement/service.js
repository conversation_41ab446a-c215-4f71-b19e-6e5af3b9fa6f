const ProductShipment = require('../../../models/ProductShipment');
const { status: bookingStatusValue, settlementStatusValue } = require('../../../constants/dbEnums');
const mongoose = require('mongoose');
const Booking = require('../../../models/Booking');
const TempleAdmin = require('../../../models/TempleAdmin');

/**
 * Get settled shipments for a vendor
 * @param {Object} params - Query parameters
 * @returns {Object} Settled shipments with pagination
 */
const getSettlementBookings = async ({ templeAdminId, page, limit, startDate, endDate, sortBy, sortOrder, status }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const templeAdmin = await TempleAdmin.findById(templeAdminId);

  // Build the query
  const query = {
    temple: new mongoose.Types.ObjectId(templeAdmin.temple),
    status: bookingStatusValue.COMPLETED,
  };

  if (status) {
    if (status === 'PENDING') {
      query.$or = [
        { settlementStatus: settlementStatusValue.PENDING },
        { settlementStatus: { $exists: false } }
      ];
    } else {
      query.settlementStatus = status;
    }
  }

  // Add date range filter if provided
  if (startDate && endDate) {
    query.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(new Date(endDate).setUTCHours(23, 59, 59, 999))
    };
  }

  // Get settled shipments
  const bookings = await Booking.find(query)
    .populate('temple poojaSchedule event darshanSchedule')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit);

  // Get total count
  const total = await ProductShipment.countDocuments(query);

  return {
    bookings,
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

module.exports = {
  getSettlementBookings,
};
