const { status: bookingStatusValue, settlementStatusValue } = require('../../../constants/dbEnums');
const mongoose = require('mongoose');
const Booking = require('../../../models/Booking');
const TempleAdmin = require('../../../models/TempleAdmin');
const { transformTranslatedFields } = require('../../../utils/localizer');
const moment = require('moment');

/**
 * Get settled shipments for a vendor
 * @param {Object} params - Query parameters
 * @returns {Object} Settled shipments with pagination
 */
const getSettlementBookings = async ({ templeAdminId, page, limit, startDate, endDate, sortBy, sortOrder, status }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const templeAdmin = await TempleAdmin.findById(templeAdminId);

  // Build the query
  const query = {
    temple: new mongoose.Types.ObjectId(templeAdmin.temple),
    status: bookingStatusValue.COMPLETED,
  };

  if (status) {
    if (status === 'PENDING') {
      query.$or = [
        { settlementStatus: settlementStatusValue.PENDING },
        { settlementStatus: { $exists: false } }
      ];
    } else {
      query.settlementStatus = status;
    }
  }

  // Add date range filter if provided
  if (startDate && endDate) {
    query.createdAt = {
      $gte: moment(startDate).format('YYYY-MM-DD') + 'T00:00:00.000Z',
      $lte: moment(endDate).format('YYYY-MM-DD') + 'T23:59:59.999Z'
    };
  }

  // Get settled shipments
  const bookings = await Booking.find(query)
    .populate('temple poojaSchedule event darshanSchedule')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  bookings.forEach((booking) => {
    const finalPricingDetails = {
      gstPercentage: 0,
      gstCharges: 0,
      discountAmount: 0,
      totalTaxableAmount: 0,
      totalAmount: 0
    };

    if (booking.darshanSchedule !== null) {
      const totalTaxableAmount = (parseFloat(booking.darshanSchedule.pricing.individualTaxablePrice || 0) * parseFloat(booking.individual || 0)) + (parseFloat(booking.darshanSchedule.pricing.coupleTaxablePrice || 0) * parseFloat(booking.couple || 0)) + (parseFloat(booking.darshanSchedule.pricing.familyTaxablePrice || 0) * parseFloat(booking.family || 0));
      
      const gstCharges = ((parseFloat(totalTaxableAmount) - ((booking.discountAmount) ? parseFloat(booking.discountAmount || 0) : 0)) * booking.darshanSchedule.pricing.gstPercentage || 0) / 100;

      finalPricingDetails.gstPercentage = booking.darshanSchedule.pricing.gstPercentage;
      finalPricingDetails.gstCharges = gstCharges;
      finalPricingDetails.discountAmount = (booking.discountAmount) ? booking.discountAmount : 0;
      finalPricingDetails.totalTaxableAmount = totalTaxableAmount;
      finalPricingDetails.totalAmount = booking.totalAmount;
    } else if (booking.poojaSchedule !== null) {
      const totalTaxableAmount = (parseFloat(booking.poojaSchedule.pricing.individualTaxablePrice || 0) * parseFloat(booking.individual || 0)) + (parseFloat(booking.poojaSchedule.pricing.coupleTaxablePrice || 0) * parseFloat(booking.couple || 0)) + (parseFloat(booking.poojaSchedule.pricing.familyTaxablePrice || 0) * parseFloat(booking.family || 0));
      
      const gstCharges = ((parseFloat(totalTaxableAmount) - ((booking.discountAmount) ? parseFloat(booking.discountAmount || 0) : 0)) * booking.poojaSchedule.pricing.gstPercentage || 0) / 100;

      finalPricingDetails.gstPercentage = booking.poojaSchedule.pricing.gstPercentage;
      finalPricingDetails.gstCharges = gstCharges;
      finalPricingDetails.discountAmount = (booking.discountAmount) ? booking.discountAmount : 0;
      finalPricingDetails.totalTaxableAmount = totalTaxableAmount;
      finalPricingDetails.totalAmount = booking.totalAmount;
    } else if (booking.event !== null) {
      if (booking.event.dateType === 'SPECIFIC_DATE') {
        const totalTaxableAmount = (parseFloat(booking.event.pricing.individualTaxablePrice || 0) * parseFloat(booking.individual || 0)) + (parseFloat(booking.event.pricing.coupleTaxablePrice || 0) * parseFloat(booking.couple || 0)) + (parseFloat(booking.event.pricing.familyTaxablePrice || 0) * parseFloat(booking.family || 0));

        const gstCharges = (parseFloat(booking.event.pricing.individualGstPrice || 0) * parseFloat(booking.individual || 0)) + (parseFloat(booking.event.pricing.coupleGstPrice || 0) * parseFloat(booking.couple || 0)) + (parseFloat(booking.event.pricing.familyGstPrice || 0) * parseFloat(booking.family || 0));

        finalPricingDetails.gstPercentage = booking.event.pricing.gstPercentage;
        finalPricingDetails.gstCharges = gstCharges;
        finalPricingDetails.discountAmount = (booking.discountAmount) ? booking.discountAmount : 0;
        finalPricingDetails.totalTaxableAmount = totalTaxableAmount;
        finalPricingDetails.totalAmount = booking.totalAmount;
      } else if (booking.event.dateType === 'DATE_RANGE') {
        let totalTaxableAmount = 0;
        let gstCharges = 0;
        const individual = booking.individual;
        const couple = booking.couple;
        const family = booking.family;

        const formatDate = (date) => new Date(date).toISOString().split('T')[0];

        booking.eventDates.forEach((eventDate) => {
          const priceDate = booking.event.datePricing.find(
            (p) => formatDate(p.date) === formatDate(eventDate.date)
          );

          totalTaxableAmount = totalTaxableAmount + (priceDate.pricing.individualTaxablePrice * individual) + (priceDate.pricing.coupleTaxablePrice * couple) + (priceDate.pricing.familyTaxablePrice * family);
          gstCharges = gstCharges + (priceDate.pricing.individualGstPrice * individual) + (priceDate.pricing.coupleGstPrice * couple) + (priceDate.pricing.familyGstPrice * family);
        });

        finalPricingDetails.gstPercentage = booking.event.datePricing[0].pricing.gstPercentage;
        finalPricingDetails.gstCharges = gstCharges;
        finalPricingDetails.discountAmount = (booking.discountAmount) ? booking.discountAmount : 0;
        finalPricingDetails.totalTaxableAmount = totalTaxableAmount;
        finalPricingDetails.totalAmount = booking.totalAmount;
      }
    }

    booking.finalPricingDetails = finalPricingDetails;
    return booking;
  });

  // Get total count
  const total = await Booking.countDocuments(query);

  const language = { code: 'en' };

  return {
    bookings: await transformTranslatedFields(bookings, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

module.exports = {
  getSettlementBookings,
};
