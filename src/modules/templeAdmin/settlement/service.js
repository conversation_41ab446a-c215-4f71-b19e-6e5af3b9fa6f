const { status: bookingStatusValue, settlementStatusValue } = require('../../../constants/dbEnums');
const mongoose = require('mongoose');
const Booking = require('../../../models/Booking');
const TempleAdmin = require('../../../models/TempleAdmin');
const { transformTranslatedFields } = require('../../../utils/localizer');
const moment = require('moment');

/**
 * Get settled shipments for a vendor
 * @param {Object} params - Query parameters
 * @returns {Object} Settled shipments with pagination
 */
const getSettlementBookings = async ({ templeAdminId, page, limit, startDate, endDate, sortBy, sortOrder, status }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  const templeAdmin = await TempleAdmin.findById(templeAdminId);

  // Build the query
  const query = {
    temple: new mongoose.Types.ObjectId(templeAdmin.temple),
    status: bookingStatusValue.COMPLETED,
  };

  if (status) {
    if (status === 'PENDING') {
      query.$or = [
        { settlementStatus: settlementStatusValue.PENDING },
        { settlementStatus: { $exists: false } }
      ];
    } else {
      query.settlementStatus = status;
    }
  }

  // Add date range filter if provided
  if (startDate && endDate) {
    query.createdAt = {
      $gte: moment(startDate).format('YYYY-MM-DD') + 'T00:00:00.000Z',
      $lte: moment(endDate).format('YYYY-MM-DD') + 'T23:59:59.999Z'
    };
  }

  // Get settled shipments
  const bookings = await Booking.find(query)
    .populate('temple poojaSchedule event darshanSchedule')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  // Get total count
  const total = await Booking.countDocuments(query);

  const language = { code: 'en' };

  return {
    bookings: await transformTranslatedFields(bookings, language.code),
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

module.exports = {
  getSettlementBookings,
};
