const joi = require('joi');
const { userStatusValue } = require('../../constants/dbEnums');
const mongoose = require('mongoose');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const createTempleAdminSchema = joi.object({
  firstName: joi.string().max(50).required(),
  lastName: joi.string().max(50).required(),
  email: joi.string().email().lowercase().required(),
  phoneNumber: joi.string().pattern(/^\+[1-9]\d{1,14}$/),
  whatsappEnabled: joi.boolean().default(false),
  temple: joi.string().required(),
});

const updateTempleAdminSchema = joi.object({
  firstName: joi.string().max(50),
  lastName: joi.string().max(50),
  phoneNumber: joi.string().pattern(/^\+[1-9]\d{1,14}$/),
  whatsappEnabled: joi.boolean(),
  temple: joi.string(),
  status: joi.string().valid('ACTIVE', 'INACTIVE', 'BLOCKED')
}).min(1);

const searchTempleAdminSchema = joi.object({
  search: joi.string(),
  status: joi.string().valid('ACTIVE', 'INACTIVE', 'BLOCKED'),
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(10),
  sortBy: joi.string().valid('name', 'email', 'phoneNumber', 'temple', 'status', 'createdAt', 'updatedAt').default('createdAt'),
  sortOrder: joi.number().valid(1, -1).default(-1)
});

const updateStatusSchema = joi.object({
  status: joi.string()
    .valid(userStatusValue.ACTIVE, userStatusValue.INACTIVE)
    .required()
    .messages({
      'any.required': 'Status is required',
      'any.only': 'Status must be either ACTIVE or INACTIVE'
    })
});

const loginSchema = joi.object({
  email: joi.string().email().lowercase().required().messages({
    'string.email': 'Please enter a valid email',
    'any.required': 'Email is required'
  }),
  password: joi.string().required().messages({
    'any.required': 'Password is required'
  })
});

const assignPujariToBookingSchema = joi.object({
  booking: joi.string().custom(validateObjectId).required().messages({
    'string.empty': 'Booking ID is required',
    'any.required': 'Booking ID is required'
  }),
  pujari: joi.string().custom(validateObjectId).required().messages({
    'string.empty': 'Pujari ID is required',
    'any.required': 'Pujari ID is required'
  })
});

module.exports = {
  createTempleAdminSchema,
  updateTempleAdminSchema,
  searchTempleAdminSchema,
  loginSchema,
  updateStatusSchema,
  assignPujariToBookingSchema
};
