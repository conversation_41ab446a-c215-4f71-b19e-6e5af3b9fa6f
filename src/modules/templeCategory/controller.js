const { apiResponse, errorApiResponse } = require('../../config/responseHandler');
const { commonConstants } = require('../../constants/common');
const templeCategoryService = require('./service');
const { createCategorySchema, validateId, updateCategorySchema, listCategoriesSchema } = require('./validation');
const { messages } = require('../../messages');
const { getPresignedUrl } = require('../../utils/s3Service');
const { saveAuditLog } = require('../../utils/auditLogger');
const { auditLogAction } = require('../../constants/dbEnums');
const { authenticate } = require('../../utils/authenticate');

//* 1. Function to create category
const createCategory = async (req, res) => {
  try {
    const { error } = createCategorySchema.validate(req.body);
    
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const category = await templeCategoryService.createCategory(req.body, req.user.id);

    //* Save audit log 
    const detail = `Temple category ${category.name.en}created successfully`;
    const model = 'TempleCategory';

    await saveAuditLog(req, req.user.id, auditLogAction.TEMPLE_CATEGORY_CREATED, detail, model);

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.TEMPLE_CATEGORY_CREATED,
      data: category
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 2. Function to get all categories
const getAllCategories = async (req, res) => {
  try {
    const { error, value } = listCategoriesSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    // Get user ID if authenticated
    const user = await authenticate(req);

    const data = await templeCategoryService.getAllCategories(value, user);
  
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.TEMPLE_CATEGORIES_RETRIEVED,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 3. Function to get category by id
const getCategoryById = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);
      
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }
  
    const category = await templeCategoryService.getCategoryById(req.params.id, req.user);
  
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.TEMPLE_CATEGORY_RETRIEVED,
      data: category
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};
  
//* 4. Function to update category
const updateCategory = async (req, res) => {
  try {
    // Validate ID
    const { error: idError } = validateId.validate(req.params.id);

    if (idError) {
      return res.status(400).json({ message: idError.details[0].message });
    }
  
    // Validate body
    const { error: bodyError } = updateCategorySchema.validate(req.body);

    if (bodyError) {
      return res.status(400).json({ message: bodyError.details[0].message });
    }
  
    const category = await templeCategoryService.updateCategory(req.params.id, req.body);

    //* Save audit log 
    const detail = `Temple category ${category.name.en}updated successfully`;
    const model = 'TempleCategory';

    await saveAuditLog(req, req.user.id, auditLogAction.TEMPLE_CATEGORY_UPDATED, detail, model);
  
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.TEMPLE_CATEGORY_UPDATED,
      data: category
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 5. Function to delete category
const deleteCategory = async (req, res) => {
  try {
    const { error } = validateId.validate(req.params.id);
      
    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }
  
    const category = await templeCategoryService.deleteCategory(req.params.id);

    //* Save audit log 
    const detail = `Temple category ${category.name.en}deleted successfully`;
    const model = 'TempleCategory';

    await saveAuditLog(req, req.user.id, auditLogAction.TEMPLE_CATEGORY_DELETED, detail, model);
  
    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.TEMPLE_CATEGORY_DELETED,
      data: category
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }

    const { extension } = req.body;
    
    const uploadData = await getPresignedUrl(extension, 'temple-categories');

    return apiResponse({
      res,
      code: commonConstants.SUCCESS.CODE,
      message: messages.SUCCESS,
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};
  
module.exports = {
  createCategory,
  getAllCategories,
  getCategoryById,
  updateCategory,
  deleteCategory,
  getUploadUrl
};