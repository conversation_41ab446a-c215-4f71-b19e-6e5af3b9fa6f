const express = require('express');
const router = express.Router();
const templeCategoryController = require('./controller');
const auth = require('../../middleware/auth');
const { isAdminOrSuperAdmin } = require('../../middleware/roleCheck');

router.post('/create', auth, isAdminOrSuperAdmin, templeCategoryController.createCategory);
router.get('/all', auth, templeCategoryController.getAllCategories);
router.get('/:id', auth, templeCategoryController.getCategoryById);
router.put('/:id', auth, isAdminOrSuperAdmin, templeCategoryController.updateCategory);
router.delete('/:id', auth, isAdminOrSuperAdmin, templeCategoryController.deleteCategory);

router.post('/upload-url', auth, isAdminOrSuperAdmin, templeCategoryController.getUploadUrl);

module.exports = router;