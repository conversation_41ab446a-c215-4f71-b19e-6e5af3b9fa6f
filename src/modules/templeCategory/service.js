const TempleCategory = require('../../models/TempleCategory');
const { throwBadRequestError } = require('../../errors');
const { translateDataForStore } = require('../../utils/translateInput');
const { transformTranslatedFields } = require('../../utils/localizer');
const User = require('../../models/User');
const Language = require('../../models/Language');

//* 1. Function to create category
const createCategory = async (categoryData, adminId) => {
  // Check if category already exists
  const existingCategory = await TempleCategory.findOne({
    'name.en': categoryData.name
  });

  if (existingCategory) {
    throwBadRequestError('Temple category already exists');
  }

  if (categoryData.image) {
    // Convert S3 URL to CloudFront URL if needed
    const cloudFrontUrl = `${process.env.MEDIA_URL}/${categoryData.image}`;

    categoryData.image = cloudFrontUrl;
  }

  const translatedData = await translateDataForStore([ 'name' ], categoryData);

  delete categoryData.name;

  categoryData = {
    ...categoryData,
    ...translatedData
  };

  // Create new category
  const category = await TempleCategory.create({
    ...categoryData,
    createdBy: adminId
  });

  return category;
};

//* 2. Function to get all categories
const getAllCategories = async (queryParams, user) => {
  const { page = 1, limit = 10, search, sortBy = 'createdAt', sortOrder = -1 } = queryParams;

  const loggedInUser = await User.findById(user.id);
  const language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  
  // Build filter
  const filter = {};

  if (search) {
    filter[`name.${language.code}`] = { $regex: search, $options: 'i' };
  }

  // Build sort object
  const sort = {};

  sort[sortBy] = parseInt(sortOrder);

  // Calculate skip value for pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Get categories with pagination
  const categories = await TempleCategory.find(filter)
    .sort(sort).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit)).lean();

  // Get total count for pagination
  const total = await TempleCategory.countDocuments(filter);

  const localizedCategories = await transformTranslatedFields(categories, language.code);

  return {
    categories: localizedCategories,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

//* 3. Function to get category by id
const getCategoryById = async (categoryId, user) => {
  const category = await TempleCategory.findById(categoryId).lean();
  const loggedInUser = await User.findById(user.id);
  const language = await Language.findOne({ name: loggedInUser.preferredLanguage });

  if (!category) {
    throwBadRequestError('Temple category not found');
  }

  const localizedCategory = await transformTranslatedFields(category, language.code);

  return localizedCategory;
};

//* 4. Function to update category
const updateCategory = async (categoryId, updateData) => {
  // Check if category exists
  const category = await TempleCategory.findById(categoryId);

  if (!category) {
    throwBadRequestError('Temple category not found');
  }

  // Translate fields if provided
  if (updateData.name) {

    const translatedData = await translateDataForStore([ 'name' ], updateData);

    delete updateData.name;

    updateData = {
      ...updateData,
      ...translatedData
    };
  }

  // Update category
  const updatedCategory = await TempleCategory.findByIdAndUpdate(
    categoryId,
    { ...updateData },
    { new: true }
  );

  return updatedCategory;
};

//* 5. Function to delete category
const deleteCategory = async (categoryId) => {
  const category = await TempleCategory.findById(categoryId);

  if (!category) {
    throwBadRequestError('Temple category not found');
  }

  await category.deleteOne();
  return category;
};

module.exports = {
  createCategory,
  getAllCategories,
  getCategoryById,
  updateCategory,
  deleteCategory
};