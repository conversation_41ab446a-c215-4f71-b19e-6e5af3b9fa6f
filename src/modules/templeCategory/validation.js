const joi = require('joi');
const mongoose = require('mongoose');

const createCategorySchema = joi.object({
  name: joi.string().required().trim(),
  image: joi.string().trim()
});

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string()
  .custom(validateObjectId)
  .required()
  .messages({
    'any.invalid': 'Invalid category ID format',
    'any.required': 'Category ID is required'
  });

const updateCategorySchema = joi.object({
  name: joi.string().trim(),
  image: joi.string().trim()
}).min(1);

const listCategoriesSchema = joi.object({
  page: joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be greater than or equal to 1'
    }),
  limit: joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be greater than or equal to 1',
      'number.max': 'Limit cannot exceed 100 records per page'
    }),
  search: joi.string()
    .trim()
    .allow('')
    .max(100)
    .messages({
      'string.base': 'Search term must be a string',
      'string.max': 'Search term cannot exceed 100 characters'
    }),
  sortBy: joi.string()
    .valid('name', 'createdAt', 'updatedAt')
    .default('createdAt')
    .messages({
      'string.base': 'Sort field must be a string',
      'any.only': 'Sort field must be one of: name, createdAt, or updatedAt'
    }),
  sortOrder: joi.number()
    .valid(1, -1)
    .default(-1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    }),
});

module.exports = {
  createCategorySchema,
  validateId,
  updateCategorySchema,
  listCategoriesSchema
};