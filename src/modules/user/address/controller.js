const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const { searchEntitiesSchema } = require('../validation');
const addressService = require('./service');
const { addressSchema, updateAddressSchema } = require('./validation');
const { SUCCESS } = commonConstants;

const addAddress = async (req, res) => {
  try {
    const { error } = addressSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const data = await addressService.createAddress(req.user.id, req.body);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getAddresses = async (req, res) => {
  try {
    const { error } = searchEntitiesSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const data = await addressService.getUserAddresses(req.user.id, req.query);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getAddressById = async (req, res) => {
  try {

    const data = await addressService.getAddressById(req.params.id, req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateAddress = async (req, res) => {
  try {
    const { error } = updateAddressSchema.validate({ ...req.params, ...req.body });

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const data = await addressService.updateAddress(req.params.id, req.user.id, req.body);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const deleteAddress = async (req, res) => {
  try {
    await addressService.deleteAddress(req.params.id, req.user.id);
    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  addAddress,
  getAddresses,
  updateAddress,
  deleteAddress,
  getAddressById,
};