const Address = require('../../../models/Address');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const { default: mongoose } = require('mongoose');

const createAddress = async (userId, body) => {
  const { isDefault, ...rest } = body;

  if (isDefault && isDefault === true) {
    await Address.updateMany(
      { userId: userId, isDefault: true, deletedAt: null },
      { isDefault: false }
    );
  }

  const address = await Address.create({
    ...rest,
    userId,
    isDefault,
  });

  return address;
};

const getUserAddresses = async (userId, query) => {
  const { page = 1, limit = 10 } = query;
  
  const filter = {
    userId: userId,
    deletedAt: null
  };
  
  const addresses = await Address.find(filter)
    .sort({ isDefault: -1, createdAt: -1 }).collation({ locale: 'en', strength: 1 })
    .skip((parseInt(page) - 1) * parseInt(limit))
    .limit(parseInt(limit));
  
  const total = await Address.countDocuments(filter);
  
  return {
    addresses,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

const getAddressById = async (addressId, userId) => {

  if (!mongoose.Types.ObjectId.isValid(addressId)) {
    throwBadRequestError(messages.INVALID_MONGO_ID);
  }

  const address = await Address.findOne({
    _id: addressId,
    userId: userId,
    deletedAt: null
  });
  
  if (!address) {
    throwBadRequestError(messages.ADDRESS_NOT_FOUND);
  }

  return address;
};

const updateAddress = async (addressId, userId, body) => {
  const { isDefault, ...rest } = body;

  if (!mongoose.Types.ObjectId.isValid(addressId)) {
    throwBadRequestError(messages.INVALID_MONGO_ID);
  }

  const address = await Address.findOne({
    _id: addressId,
    userId: userId,
    deletedAt: null
  });

  if (!address) {
    throwBadRequestError(messages.ADDRESS_NOT_FOUND);
  }

  if (isDefault && isDefault === true) {

    await Address.updateMany(
      { userId: userId, isDefault: true },
      { isDefault: false }
    );
  }

  const updatedAddress = await Address.findByIdAndUpdate(
    addressId,
    {
      ...rest,
      isDefault,
    },
    { new: true }
  );

  return updatedAddress;
};

const deleteAddress = async (addressId, userId) => {
  const address = await Address.findOneAndUpdate(
    {
      _id: addressId,
      userId: userId,
      deletedAt: null
    },
    { deletedAt: new Date() },
    { new: true }
  );

  if (!address) {
    throwBadRequestError(messages.ADDRESS_NOT_FOUND);
  }
};

module.exports = {
  createAddress,
  getUserAddresses,
  updateAddress,
  deleteAddress,
  getAddressById,
};