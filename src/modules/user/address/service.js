const Address = require('../../../models/Address');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const { default: mongoose } = require('mongoose');
const { translateDataForStore } = require('../../../utils/translateInput');
const { transformTranslatedFields } = require('../../../utils/localizer');
const User = require('../../../models/User');
const Language = require('../../../models/Language');

const createAddress = async (userId, body) => {
  const { isDefault, ...rest } = body;

  if (isDefault && isDefault === true) {
    await Address.updateMany(
      { userId: userId, isDefault: true, deletedAt: null },
      { isDefault: false }
    );
  }

  const translatedData = await translateDataForStore([ 'addressLine1', 'addressLine2', 'city', 'state' ], rest);

  rest.addressLine1 = translatedData.addressLine1;
  rest.addressLine2 = translatedData.addressLine2;
  rest.city = translatedData.city;
  rest.state = translatedData.state;

  const address = await Address.create({
    ...rest,
    userId,
    isDefault,
  });

  return address;
};

const getUserAddresses = async (userId, query) => {
  const { page = 1, limit = 10 } = query;
  
  const filter = {
    userId: userId,
    deletedAt: null
  };
  
  const addresses = await Address.find(filter)
    .sort({ isDefault: -1, createdAt: -1 }).collation({ locale: 'en', strength: 1 })
    .skip((parseInt(page) - 1) * parseInt(limit))
    .limit(parseInt(limit)).lean();
  
  const total = await Address.countDocuments(filter);

  const loggedInUser = await User.findById(userId);

  const language = await Language.findOne({ name: loggedInUser.preferredLanguage });

  const localizedAddresses = await transformTranslatedFields(addresses, language.code);

  return {
    addresses: localizedAddresses,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

const getAddressById = async (addressId, userId) => {

  if (!mongoose.Types.ObjectId.isValid(addressId)) {
    throwBadRequestError(messages.INVALID_MONGO_ID);
  }

  const address = await Address.findOne({
    _id: addressId,
    userId: userId,
    deletedAt: null
  });
  
  if (!address) {
    throwBadRequestError(messages.ADDRESS_NOT_FOUND);
  }

  const loggedInUser = await User.findById(userId);
  const language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  
  return await transformTranslatedFields(address, language.code);
};

const updateAddress = async (addressId, userId, body) => {
  const { isDefault, ...rest } = body;

  if (!mongoose.Types.ObjectId.isValid(addressId)) {
    throwBadRequestError(messages.INVALID_MONGO_ID);
  }

  const address = await Address.findOne({
    _id: addressId,
    userId: userId,
    deletedAt: null
  });

  if (!address) {
    throwBadRequestError(messages.ADDRESS_NOT_FOUND);
  }

  if (isDefault && isDefault === true) {

    await Address.updateMany(
      { userId: userId, isDefault: true },
      { isDefault: false }
    );
  }

  if (rest.addressLine1) {
    const translatedData = await translateDataForStore([ 'addressLine1' ], rest);

    rest.addressLine1 = translatedData.addressLine1;
  }

  if (rest.addressLine2) {
    const translatedData = await translateDataForStore([ 'addressLine2' ], rest);

    rest.addressLine2 = translatedData.addressLine2;
  }

  if (rest.city) {
    const translatedData = await translateDataForStore([ 'city' ], rest);

    rest.city = translatedData.city;
  }

  if (rest.state) {
    const translatedData = await translateDataForStore([ 'state' ], rest);

    rest.state = translatedData.state;
  }

  const updatedAddress = await Address.findByIdAndUpdate(
    addressId,
    {
      ...rest,
      isDefault,
    },
    { new: true }
  );

  return updatedAddress;
};

const deleteAddress = async (addressId, userId) => {
  const address = await Address.findOneAndUpdate(
    {
      _id: addressId,
      userId: userId,
      deletedAt: null
    },
    { deletedAt: new Date() },
    { new: true }
  );

  if (!address) {
    throwBadRequestError(messages.ADDRESS_NOT_FOUND);
  }
};

module.exports = {
  createAddress,
  getUserAddresses,
  updateAddress,
  deleteAddress,
  getAddressById,
};