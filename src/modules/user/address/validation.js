const joi = require('joi');

const addressSchema = joi.object({
  addressLine1: joi.string().required().messages({
    'string.empty': 'House number, floor, apartment name is required',
    'any.required': 'House number, floor, apartment name is required'
  }),
  addressLine2: joi.string().allow('').optional(),
  city: joi.string().required().messages({
    'string.empty': 'City is required',
    'any.required': 'City is required'
  }),
  state: joi.string().required().messages({
    'string.empty': 'State is required',
    'any.required': 'State is required'
  }),
  country: joi.string().trim().optional().messages({
    'string.base': 'Country must be a string',
    'string.empty': 'Country cannot be empty',
  }),
  postalCode: joi.string().required().messages({
    'string.empty': 'Postal code is required',
    'any.required': 'Postal code is required'
  }),
  latitude: joi.number().required().min(-90).max(90).messages({
    'number.base': 'Latitude must be a number',
    'number.min': 'Invalid latitude',
    'number.max': 'Invalid latitude',
    'any.required': 'Latitude is required'
  }),
  longitude: joi.number().required().min(-180).max(180).messages({
    'number.base': 'Longitude must be a number',
    'number.min': 'Invalid longitude',
    'number.max': 'Invalid longitude',
    'any.required': 'Longitude is required'
  }),
  isDefault: joi.boolean().default(false)
});

const updateAddressSchema = joi.object({
  id: joi.string().required().messages({
    'string.empty': 'Address ID is required',
    'any.required': 'Address ID is required'
  }),
  addressLine1: joi.string().optional().messages({
    'string.empty': 'House number, floor, apartment name is required',
    'any.required': 'House number, floor, apartment name is required'
  }),
  addressLine2: joi.string().allow('').optional(),
  city: joi.string().optional().messages({
    'string.empty': 'City is required',
    'any.required': 'City is required'
  }),
  state: joi.string().optional().messages({
    'string.empty': 'State is required',
    'any.required': 'State is required'
  }),
  country: joi.string().trim().optional().messages({
    'string.base': 'Country must be a string',
    'string.empty': 'Country cannot be empty',
  }),
  postalCode: joi.string().optional().messages({
    'string.empty': 'Postal code is required'
  }),
  latitude: joi.number().optional().min(-90).max(90).messages({
    'number.base': 'Latitude must be a number',
    'number.min': 'Invalid latitude',
    'number.max': 'Invalid latitude'
  }),
  longitude: joi.number().optional().min(-180).max(180).messages({
    'number.base': 'Longitude must be a number',
    'number.min': 'Invalid longitude',
    'number.max': 'Invalid longitude'
  }),
  isDefault: joi.boolean().valid(true, false).optional()
});

module.exports = {
  addressSchema,
  updateAddressSchema,
};