const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const authService = require('./service');
const { loginSchema, userSignUpSchema,verifyOtpSchema, verifyEmailSchema, sendOtpSchema } = require('./validation');
const { SUCCESS } = commonConstants;

//* 1. Function for login 
const login = async (req, res) => {
  try {
    const { value, error } = loginSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await authService.loginUser(value);

    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 2. Function for signup 
const signup = async (req, res) => {
  try {
    const { value, error } = userSignUpSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await authService.userSignup(value);
    
    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 3. Function for verify email
const verifyEmail = async (req, res) => {
  try {
    const { value, error } = verifyEmailSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await authService.verifyEmail(value);
    
    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const sendOtp = async (req, res) => {
  try {
    const { error } = sendOtpSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await authService.sendOtp(req.body);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: messages.OTP_SENT, 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 4. Function for verify otp
const verifyOtp = async (req, res) => {
  try {
    const { error } = verifyOtpSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await authService.verifyOtp(req.body);
    
    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const logout = async (req, res) => {
  try {
    await authService.logout(req.user.id, req.header('Authorization').replace('Bearer ', ''), req.user.userType);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  verifyEmail,
  verifyOtp,
  signup,
  login,
  sendOtp,
  logout,
};
