const express = require('express');
const router = express.Router();
const authController = require('./controller');
const auth = require('../../../middleware/auth');
const { rateLimiter } = require('../../../utils/rateLimiter');

router.post('/verify-otp', authController.verifyOtp);
router.post('/send-otp', rateLimiter, authController.sendOtp);
router.delete('/logout', auth, authController.logout);

module.exports = router;
