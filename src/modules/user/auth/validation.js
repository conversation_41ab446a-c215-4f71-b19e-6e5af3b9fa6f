const joi = require('joi');
const { parsePhoneNumberFromString } = require('libphonenumber-js');

const loginSchema = joi.object({
  email: joi.string().email().lowercase().required(),
  password: joi.string().required(),
});

const userSignUpSchema = joi.object().keys({
  name: joi.string().min(3).max(50).trim(true).required(),
  businessName: joi.string().min(3).max(100).trim(true).required(),
  email: joi.string().trim(true).email().lowercase().required(),
  phoneNumber: joi.string()
    .pattern(/^\d+$/) // Ensures only digits
    .length(10) // Ensures exactly 10 characters
    .required()
    .messages({
      'string.pattern.base': 'Phone number must contain only digits',
      'string.length': 'Phone number must be exactly 10 digits',
      'string.empty': 'Phone number is required'
    }),
  work: joi.string().min(3).trim(true).required(),
  referralCode: joi.string().trim(true).allow(null, ''),
  userType: joi.string().valid('USER', 'ADMIN').default('USER'),
  profilePicture: joi.string().allow(null, ''),
  password: joi.string().min(4).max(16)
    .pattern(/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d).+$/)
    .required()
    .messages({
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number.'
    }),
  confirmPassword: joi.string().valid(joi.ref('password'))
    .required()
    .messages({
      'any.only': 'Confirm password must match password'
    }),
  city: joi.string().min(2).max(100).trim(true).required(),
  state: joi.string().min(2).max(100).trim(true).required(),
  pinCode: joi.number()
    .required()
    .messages({
      'any.required': 'Pin code is required',
      'number.base': 'Pin code must be a number'
    })
    .integer()
    .messages({
      'number.integer': 'Pin code must be an integer'
    })
    .min(100000)
    .max(999999)
    .messages({
      'number.min': 'Pin code must be a 6-digit number',
      'number.max': 'Pin code must be a 6-digit number'
    }),
});

const verifyOtpSchema = joi.object({
  countryCode: joi.string()
    .trim()
    .pattern(/^\+\d{1,3}$/)
    .required()
    .messages({
      'string.empty': 'Country code is required',
      'string.pattern.base': 'Country code must start with + followed by 1 to 3 digits',
    }),

  phone: joi.string()
    .required()
    .custom((value, helpers) => {
      const { countryCode } = helpers.state.ancestors[0];

      const fullNumber = `${countryCode}${value}`;
      const parsed = parsePhoneNumberFromString(fullNumber);

      if (!parsed || !parsed.isValid()) {
        return helpers.message('Invalid phone number for the selected country');
      }

      return value; // or `parsed.number` if you want to store in E.164 format
    })
    .messages({
      'string.empty': 'Phone number is required',
    }),

  otp: joi.string()
    .length(6)
    .pattern(/^[0-9]+$/)
    .required()
    .messages({
      'string.pattern.base': 'OTP must contain only numbers',
      'string.length': 'OTP must be exactly 6 digits',
      'any.required': 'OTP is required',
    }),

  fcmToken: joi.string().optional().allow('', null),
});

const sendOtpSchema = joi.object({
  countryCode: joi.string()
    .trim()
    .pattern(/^\+\d{1,3}$/)
    .required()
    .messages({
      'string.empty': 'Country code is required',
      'string.pattern.base': 'Country code must start with + followed by 1 to 3 digits',
    }),

  phone: joi.string()
    .required()
    .custom((value, helpers) => {
      const { countryCode } = helpers.state.ancestors[0];

      const fullNumber = `${countryCode}${value}`;
      const parsed = parsePhoneNumberFromString(fullNumber);

      if (!parsed || !parsed.isValid()) {
        return helpers.message('Invalid phone number for the selected country');
      }

      return value; // Or return `parsed.number` to replace with E.164 format
    })
    .messages({
      'string.empty': 'Phone number is required',
    }),

  preferredLanguage: joi.string()
    .valid('English', 'Hindi', '', null)
    .optional()
    .messages({
      'any.only': 'Preferred language must be either English or Hindi',
    }),

  // Uncomment when needed
  // userType: joi.string().valid(1, 2, 3, 4).required().messages({
  //   'any.only': 'User type must be one of 1, 2, 3, or 4',
  // }),
});

const verifyEmailSchema = joi.object({
  email: joi.string().email().lowercase().required(),
});

module.exports = {
  loginSchema,
  userSignUpSchema,
  verifyOtpSchema,
  verifyEmailSchema,
  sendOtpSchema,
};
