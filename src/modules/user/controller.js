const { apiResponse, errorApiResponse } = require('../../config/responseHandler');
const { commonConstants } = require('../../constants/common');
const { messages } = require('../../messages');
const userService = require('./service');
const { userRegistrationSchema, searchEntitiesSchema, updateUserSchema, getNotificationsSchema, notificationIdSchema, verifyOtpSchema, languageSchema } = require('./validation');
const { SUCCESS } = commonConstants;

const getAllUsers = async (req, res) => {
  try {
    const users = await userService.getAllUsers();

    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data: users });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getUserById = async (req, res) => {
  try {
    const data = await userService.getUserById(req.user.id);

    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateUser = async (req, res) => {
  try {

    if (Object.keys(req.body).length === 1 && req.body?.preferredLanguage) {
      const { error, value } = languageSchema.validate(req.body);

      if (error) {
        return res.status(400).json({
          message: error.details[0].message,
          status: false
        });
      }

      const data = await userService.updateUser(req.user.id, value);

      return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
    }

    const { value, error } = updateUserSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ message: error.details[0].message });
    }

    const data = await userService.updateUser(req.user.id, value);

    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const checkDeletionEligibility = async (req, res) => {
  try {
    const data = await userService.checkDeletionEligibility(req.user.id);
    const result = { isAllowed: !data };

    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data: result });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Generate OTP for account deletion
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
// const generateDeletionOTP = async (req, res) => {
//   try {
//     const userId = req.user.id;
    
//     const data = await userService.generateDeletionOTP(userId);
    
//     return apiResponse({
//       res,
//       code: SUCCESS.CODE,
//       message: messages.OTP_SENT,
//       status: true,
//       data
//     });
//   } catch (error) {
//     return errorApiResponse(res, error);
//   }
// };

const deleteUser = async (req, res) => {
  try {
    // const { error } = verifyOtpSchema.validate(req.body);

    // if (error) {
    //   return res.status(400).json({ 
    //     message: error.details[0].message,
    //     status: false 
    //   });
    // }

    const data = await userService.deleteUser(req.user.id, req.user.userType, req.user.tokenId);

    return apiResponse({ res, code: SUCCESS.CODE, message: messages.SUCCESS, status: true, data });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const registerUser = async (req, res) => {
  try {
  
    const { value, error } = userRegistrationSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await userService.addUser(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.USER_REGISTERED_SUCCESSFULLY,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getHomePageDetails = async (req, res) => {
  try {

    const data = await userService.homePageDetails(req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getHomePageEntities = async (req , res) => {
  try {

    const { error } = searchEntitiesSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await userService.homePageEntities(req.user, req.query);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getAllTemples = async (req, res) => {

  try {
    const { error } = searchEntitiesSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await userService.allTemples(req.user, req.query);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getNotifications = async (req, res) => {
  try {
    const { error } = getNotificationsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await userService.getNotifications(req.user.id, req.query);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const markNotificationAsRead = async (req, res) => {
  try {
    const { error } = notificationIdSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    await userService.markNotificationAsRead(req.body, req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: {}
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updateFCMToken = async (req, res) => {
  try {
    const { fcmToken } = req.body;

    await userService.updateFCMToken(req.user, req.headers.authorization.replace('Bearer ', ''), fcmToken);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: {}
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getAllUsers,
  getUserById,
  updateUser,
  checkDeletionEligibility,
  // generateDeletionOTP,
  deleteUser,
  registerUser,
  getNotifications,
  getHomePageDetails,
  getHomePageEntities,
  markNotificationAsRead,
  getAllTemples,
  updateFCMToken,
};
