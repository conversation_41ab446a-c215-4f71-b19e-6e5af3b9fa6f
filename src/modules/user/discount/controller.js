const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const userDiscountService = require('./service');
const { applyDiscountSchema, listDiscountsSchema } = require('./validation');
const { SUCCESS } = commonConstants;

//* 1. Function to list all discounts
const listDiscounts = async (req, res) => {
  try {
    const { error } = listDiscountsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await userDiscountService.listDiscounts(req.query);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.DISCOUNTS_RETRIEVED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }   
};

//* 2. Function to apply discount
const applyDiscount = async (req, res) => {
  try {
    const { error } = applyDiscountSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const userId = req.user.id;

    const data = await userDiscountService.applyDiscount(userId, req.body, req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: req.user?.preferredLanguage === 'Hindi' ? messages.DISCOUNT_APPLIED_HI : messages.DISCOUNT_APPLIED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  applyDiscount,
  listDiscounts
};