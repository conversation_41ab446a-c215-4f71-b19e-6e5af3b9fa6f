const express = require('express');
const router = express.Router();
const userDiscountController = require('./controller');
const auth = require('../../../middleware/auth');
const { isUser } = require('../../../middleware/roleCheck');

router.get('/list', auth, isUser, userDiscountController.listDiscounts);
router.post('/apply', auth, isUser, userDiscountController.applyDiscount);

module.exports = router;