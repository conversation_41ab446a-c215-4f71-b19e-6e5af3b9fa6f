const UserDiscount = require('../../../models/UserDiscount');
const Discount = require('../../../models/Discount');
const Temple = require('../../../models/Temple');
const { throwBadRequestError } = require('../../../errors');
const moment = require('moment');
const PoojaSchedule = require('../../../models/PoojaSchedule');
const DarshanSchedule = require('../../../models/DarshanSchedule');
const { transformTranslatedFields } = require('../../../utils/localizer');

//* 1. Function to list all discounts
const listDiscounts = async (body) => {
  const { page = 1, limit = 10, search, discountType, applicabilityType, sortBy = 'createdAt', sortOrder = -1, temple, poojaSchedule, darshanSchedule, products } = body;

  const skip = (parseInt(page) - 1) * parseInt(limit);
  const query = {
    discountStatus: 'ACTIVE'
  };
  const sortOptions = {};
  
  sortOptions[sortBy] = parseInt(sortOrder);

  // Add search filter if provided
  if (search) {
    query.code = { $regex: search, $options: 'i' };
  }

  // Add discount type filter if provided
  if (discountType) {
    query.discountType = discountType;
  }

  // Add applicability type filter if provided
  if (applicabilityType) {
    query.applicabilityType = applicabilityType;
  }

  // Add temple filter if provided
  if (temple) {
    query.$or = [
      { applicabilityType: 'GENERAL' },
      { temple: temple }
    ];
  }

  // Add puja schedule filter if provided
  if (poojaSchedule) {
    query.$or = [
      { applicabilityType: 'GENERAL' },
      { poojaSchedules: { $in: [ poojaSchedule ] } }
    ];
  }

  // Add darshan schedule filter if provided
  if (darshanSchedule) {
    query.$or = [
      { applicabilityType: 'GENERAL' },
      { darshanSchedules: { $in: [ darshanSchedule ] } }
    ];
  }

  // Add product filter if provided
  if (products && products.length) {
    query.$or = [
      { applicabilityType: 'GENERAL' },
      { products: { $all: products } }
    ];
  }
  
  // Get discounts with pagination
  const discounts = await Discount.find(query)
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(parseInt(limit))
    .lean();
  
  // Get total count for pagination
  const total = await Discount.countDocuments(query);
  
  const language = { code: 'en' };

  return {
    discounts: await transformTranslatedFields(discounts, language.code),
    pagination: {
      total,
      page: parseInt(page),
      pages: Math.ceil(total / parseInt(limit)),
      limit: parseInt(limit)
    }
  };
};

//* 2. Function to apply discount
const applyDiscount = async (userId, body, user) => {
  const { discountCode, temple, darshanSchedule, poojaSchedule, products, totalAmount } = body;

  //* 1. Check if discount exists
  const discountExists = await Discount.findOne({
    code: discountCode.toUpperCase(),
    discountStatus: 'ACTIVE'
  });

  if (!discountExists) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'डिस्काउंट नहीं मिला या अक्टिव नहीं है।' : 'Discount not found or expired');
  }

  //* 2. Check if discount is active
  if (discountExists.discountStatus !== 'ACTIVE') {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'डिस्काउंट अक्टिव नहीं है या अक्टिव नहीं है।' : 'Discount is not active or expired');
  }

  //* 3. Check if discount is applicable  
  if (discountExists.applicabilityType === 'SPECIFIC_PRODUCTS') {
    if (!products || !products.length) {
      throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस डिस्काउंट को उत्पादों के लिए ही लागू होता है।' : 'This discount is only applicable for products');
    }
    if (temple) {
      throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस डिस्काउंट को मंदिरों के लिए लागू नहीं है।' : 'This discount is not applicable for temples');
    }
    if (poojaSchedule) {
      throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस डिस्काउंट को पूजा अनुसूचिकाओं के लिए लागू नहीं है।' : 'This discount is not applicable for puja schedules');
    }
    if (darshanSchedule) {
      throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस डिस्काउंट को दर्शन अनुसूचिकाओं के लिए लागू नहीं है।' : 'This discount is not applicable for darshan schedules');
    }

    discountExists.products = discountExists.products.map(item => item.toString());
    if (!products.every(item => discountExists.products.includes(item))) {
      throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस डिस्काउंट को इन उत्पादों के लिए लागू नहीं है।' : 'Discount is not applicable for these products');
    }
  }

  if (discountExists.applicabilityType === 'SPECIFIC_TEMPLES') {
    if (!temple) {
      throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस डिस्काउंट के लिए मंदिर आईडी आवश्यक है।' : 'Temple ID is required for this discount');
    }
    if (products && products.length) {
      throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस डिस्काउंट को मंदिरों के लिए ही लागू होता है।' : 'This discount is only applicable for temples');
    }
  
    if (discountExists.temple && discountExists.temple.toString() !== temple) {
      throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस डिस्काउंट को इस मंदिर के लिए लागू नहीं है।' : 'Discount is not applicable for this temple');
    }

    const templeExists = await Temple.findById(temple);

    if (!templeExists) {
      throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'मंदिर नहीं मिला।' : 'Temple not found');
    }

    if (poojaSchedule) {
      const poojaScheduleExists = await PoojaSchedule.findOne({
        _id: poojaSchedule,
        temple
      });

      if (!poojaScheduleExists) {
        throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस मंदिर के लिए पूजा अनुसूचिका नहीं मिली।' : 'Puja schedule not found for this temple');
      }
      
      discountExists.poojaSchedules = discountExists.poojaSchedules.map(item => item.toString());
      if (!discountExists.poojaSchedules.includes(poojaSchedule)) {
        throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस डिस्काउंट को इस पूजा अनुसूचिका के लिए लागू नहीं है।' : 'Discount is not applicable for this puja schedule');
      }
    }

    if (darshanSchedule) {
      const darshanScheduleExists = await DarshanSchedule.findOne({
        _id: darshanSchedule,
        temple
      });

      if (!darshanScheduleExists) {
        throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस मंदिर के लिए दर्शन अनुसूचिका नहीं मिली।' : 'Darshan schedule not found for this temple');
      }
      
      discountExists.darshanSchedules = discountExists.darshanSchedules.map(item => item.toString());
      if (!discountExists.darshanSchedules.includes(darshanSchedule)) {
        throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस डिस्काउंट को इस दर्शन अनुसूचिका के लिए लागू नहीं है।' : 'Discount is not applicable for this darshan schedule');
      }
    }
  }
  
  //* 4. Check total usage limit 
  const totalDiscountUsage = await UserDiscount.countDocuments({
    discount: discountExists._id
  });

  if (totalDiscountUsage >= discountExists.totalUsageLimit) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस डिस्काउंट के लिए कुल उपयोग सीमा पूरी हो गई है।' : 'The total usage limit for this discount has been reached');
  }

  //* 5. Check user usage limit
  const userDiscountCount = await UserDiscount.countDocuments({
    user: userId,
    discount: discountExists._id
  });

  if (userDiscountCount >= discountExists.usageLimitPerUser) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस डिस्काउंट के लिए उपयोग सीमा पूरी हो गई है।' : 'You have reached the usage limit for this discount');
  }  

  //* 6. Check validity of discount based on date and time
  let currentDate = moment().format('YYYY-MM-DD') + 'T00:00:00.000Z';

  currentDate = new Date(currentDate);

  const currentTime = new Date().toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });

  if (currentDate.toISOString().split('T')[0] < discountExists.startDate.toISOString().split('T')[0] || currentDate.toISOString().split('T')[0] > discountExists.endDate.toISOString().split('T')[0]) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस तिथि पर डिस्काउंट मान्य नहीं है।' : 'Discount is not valid on this date');
  } else if (currentDate.toISOString().split('T')[0] === discountExists.startDate.toISOString().split('T')[0] && convertTo24Hour(currentTime) < convertTo24Hour(discountExists.startTime)) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस समय डिस्काउंट मान्य नहीं है।' : 'Discount is not valid at this time');
  } else if (currentDate.toISOString().split('T')[0] === discountExists.endDate.toISOString().split('T')[0] && convertTo24Hour(currentTime) >= convertTo24Hour(discountExists.endTime)) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? 'इस समय डिस्काउंट मान्य नहीं है।' : 'Discount is not valid at this time');
  }

  //* 7. Check if minimum purchase amount is met
  if (parseFloat(totalAmount) < parseFloat(discountExists.minimumPurchaseAmount)) {
    throwBadRequestError(user.preferredLanguage === 'Hindi' ? `इस डिस्काउंट को लागू करने के लिए न्यूनतम खरीदारी राशि ${discountExists.minimumPurchaseAmount} होनी चाहिए।` : `Minimum purchase amount of ${discountExists.minimumPurchaseAmount} is required to apply this discount`);
  }

  let discountAmount = 0;

  if (discountExists.discountType === 'FIXED_AMOUNT') {
    discountAmount = parseFloat(discountExists.discountValue);
  } else {
    discountAmount = (parseFloat(totalAmount) * parseFloat(discountExists.discountValue)) / 100;
    if (discountExists.maxDiscount && discountAmount > parseFloat(discountExists.maxDiscount)) {
      discountAmount = parseFloat(discountExists.maxDiscount);
    }
  }

  return {
    discountAmount,
    isValid: true,
    discount: discountExists
  };
};

//* 2. Convert times to 24-hour format for comparison
const convertTo24Hour = (timeStr) => {
  const [ time, period ] = timeStr.split(' ');
  let [ hours, minutes ] = time.split(':').map(Number);
  
  hours = parseInt(hours);
  minutes = parseInt(minutes);
  if (period === 'PM' && hours < 12) {
    hours += 12;
  }
  if (period === 'AM' && hours === 12) {
    hours = 0;
  }
  
  return hours * 60 + minutes; // Return minutes since midnight
};

module.exports = {
  applyDiscount,
  listDiscounts
};