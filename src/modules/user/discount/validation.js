const joi = require('joi');
const mongoose = require('mongoose');
const { discountType, discountStatus, applicabilityType } = require('../../../constants/dbEnums');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string()
  .custom(validateObjectId)
  .required()
  .messages({
    'any.invalid': 'Invalid discount ID format',
    'any.required': 'Discount ID is required'
  });

const applyDiscountSchema = joi.object({
  discountCode: joi.string()
    .required()
    .trim()
    .uppercase()
    .messages({
      'string.empty': 'Discount code is required',
      'any.required': 'Discount code is required'
    }),
  temple: joi.string()
    .custom(validateObjectId)
    .optional()
    .messages({
      'string.empty': 'Temple ID is required',
      'any.required': 'Temple ID is required',
      'any.invalid': 'Invalid temple ID format'
    }),
  poojaSchedule: joi.string()
    .custom(validateObjectId)
    .optional()
    .messages({
      'string.empty': 'Puja schedule ID is required',
      'any.required': 'Puja schedule ID is required',
      'any.invalid': 'Invalid puja schedule ID format'
    }),
  darshanSchedule: joi.string()
    .custom(validateObjectId)
    .optional()
    .messages({
      'string.empty': 'Darshan schedule ID is required',
      'any.required': 'Darshan schedule ID is required',
      'any.invalid': 'Invalid darshan schedule ID format'
    }),
  products: joi.array().items(joi.string()
    .custom(validateObjectId)
    .optional()
    .messages({
      'string.empty': 'Product ID is required',
      'any.required': 'Product ID is required',
      'any.invalid': 'Invalid product ID format'
    })),
  totalAmount: joi.number()
    .required()
    .min(0)
    .messages({
      'number.base': 'Total amount must be a number',
      'number.min': 'Total amount cannot be negative',
      'any.required': 'Total amount is required for discount calculation'
    })
});

const listDiscountsSchema = joi.object({
  page: joi.number().integer().min(1).default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
  limit: joi.number().integer().min(1).max(100).default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
  search: joi.string().trim().allow('').optional()
    .messages({ 
      'string.base': 'Search query must be a string'
    }),
  status: joi.string().valid(...Object.values(discountStatus)).allow('').optional()
    .messages({
      'any.only': `Status must be one of: ${Object.values(discountStatus).join(', ')}`
    }),
  applicabilityType: joi.string().valid(...Object.values(applicabilityType)).allow('').optional()
    .messages({
      'any.only': `Applicability type must be one of: ${Object.values(applicabilityType).join(', ')}`
    }),
  discountType: joi.string().valid(...Object.values(discountType)).allow('').optional()
    .messages({
      'any.only': `Discount type must be one of: ${Object.values(discountType).join(', ')}`
    }),
  sortBy: joi.string().valid('createdAt', 'code', 'discountValue', 'status', 'totalUsageLimit', 'usageLimitPerUser', 'minimumPurchaseAmount', 'totalUsageCount', 'applicabilityType', 'discountType').default('createdAt')
    .messages({
      'any.only': 'Sort by field must be one of: createdAt, code, discountValue, status, totalUsageLimit, usageLimitPerUser, minimumPurchaseAmount, totalUsageCount, applicabilityType, discountType'
    }),
  sortOrder: joi.number().valid(1, -1).default(-1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    }),
  temple: joi.string().custom(validateObjectId).allow('').optional()
    .messages({
      'any.invalid': 'Invalid temple ID format'
    }),
  poojaSchedule: joi.string().custom(validateObjectId).allow('').optional()
    .messages({
      'any.invalid': 'Invalid puja schedule ID format'
    }),
  darshanSchedule: joi.string().custom(validateObjectId).allow('').optional()
    .messages({
      'any.invalid': 'Invalid darshan schedule ID format'
    }),
  products: joi.array().items(joi.string().custom(validateObjectId).allow('').optional()
    .messages({
      'any.invalid': 'Invalid product ID format'
    }))
});

module.exports = {
  validateId,
  applyDiscountSchema,
  listDiscountsSchema
};
