const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const favoriteProductService = require('./service');
const { toggleFavoriteSchema } = require('./validation');
const { SUCCESS } = commonConstants;

/**
 * Toggle favorite status for a product or product variant
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} - Response object
 */
const toggleFavorite = async (req, res) => {
  try {
    const { error } = toggleFavoriteSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await favoriteProductService.toggleFavoriteProduct(req.user.id, req.body, req.user);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: data.message || messages.SUCCESS, 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get all favorite products for a user
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} - Response object
 */
const getFavorites = async (req, res) => {
  try {
    const data = await favoriteProductService.getFavoriteProducts(req.user.id, req.query);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: messages.SUCCESS, 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  toggleFavorite,
  getFavorites
};
