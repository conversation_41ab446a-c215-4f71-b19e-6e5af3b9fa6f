const express = require('express');
const router = express.Router();
const favoriteProductController = require('./controller');
const auth = require('../../../middleware/auth');

// All routes require authentication
router.use(auth);

// Handle favorite/unfavorite product
router.post('/toggle', favoriteProductController.toggleFavorite);

// Get all favorite products
router.get('/', favoriteProductController.getFavorites);

module.exports = router;
