const FavoriteProduct = require('../../../models/FavoriteProduct');
const Product = require('../../../models/Product');
const ProductVariant = require('../../../models/ProductVariant');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');
const mongoose = require('mongoose');

/**
 * Toggle favorite status for a product or product variant
 * @param {string} userId - User ID
 * @param {Object} data - Request data containing productId, variantId, and isFavorite
 * @returns {Object} - Response data
 */
const toggleFavoriteProduct = async (userId, data) => {
  const { productId, variantId, isFavorite } = data;

  // Validate product ID
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  // Check if product exists
  const product = await Product.findById(productId);

  if (!product) {
    throwBadRequestError(messages.PRODUCT_NOT_FOUND);
  }

  // If variantId is provided, validate it
  if (variantId) {
    if (!mongoose.Types.ObjectId.isValid(variantId)) {
      throwBadRequestError(messages.INVALID_VARIANT_ID);
    }

    // Check if variant exists and belongs to the product
    const variant = await ProductVariant.findOne({
      _id: variantId,
      product: productId
    });

    if (!variant) {
      throwBadRequestError(messages.VARIANT_NOT_FOUND);
    }
  }

  // Create query to find existing favorite
  const query = {
    user: userId,
    product: productId
  };

  if (variantId) {
    query.variant = variantId;
  } else {
    query.variant = null;
  }

  // Check if product is already in favorites
  const existingFavorite = await FavoriteProduct.findOne(query);

  // If isFavorite is true and product is not in favorites, add it
  if (isFavorite && !existingFavorite) {
    await FavoriteProduct.create(query);
    return {
      success: true,
      message: variantId ? messages.VARIANT_ADDED_TO_FAVORITES : messages.PRODUCT_ADDED_TO_FAVORITES
    };
  }

  // If isFavorite is false and product is in favorites, remove it
  if (!isFavorite && existingFavorite) {
    await FavoriteProduct.findOneAndDelete(query);
    return {
      success: true,
      message: variantId ? messages.VARIANT_REMOVED_FROM_FAVORITES : messages.PRODUCT_REMOVED_FROM_FAVORITES
    };
  }

  // If product is already in the desired state, return appropriate message
  return {
    success: true,
    message: isFavorite
      ? (variantId ? messages.VARIANT_ALREADY_IN_FAVORITES : messages.PRODUCT_ALREADY_IN_FAVORITES)
      : (variantId ? messages.VARIANT_NOT_IN_FAVORITES : messages.PRODUCT_NOT_IN_FAVORITES)
  };
};

/**
 * Get all favorite products for a user
 * @param {string} userId - User ID
 * @param {Object} query - Query parameters including search, pagination, and sorting
 * @returns {Object} - Object containing favorite products array and pagination details
 */
const getFavoriteProducts = async (userId, query) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, search } = query;

  if (!mongoose.Types.ObjectId.isValid(userId)) {
    throwBadRequestError(messages.INVALID_USER_ID);
  }

  const skip = (parseInt(page) - 1) * parseInt(limit);

  const sort = {};

  sort[sortBy] = parseInt(sortOrder) || -1;

  // Build aggregation pipeline
  const pipeline = [
    // Match favorites for this user
    {
      $match: {
        user: new mongoose.Types.ObjectId(userId)
      }
    },

    // Lookup product details
    {
      $lookup: {
        from: 'products',
        localField: 'product',
        foreignField: '_id',
        as: 'product'
      }
    },

    // Unwind product array to get a single object
    {
      $unwind: '$product'
    },

    // Lookup variant details if present
    // Collection name 'productvariants' is correct - it's the MongoDB collection for ProductVariant model
    {
      $lookup: {
        from: 'productvariants',
        localField: 'variant',
        foreignField: '_id',
        as: 'variant'
      }
    },

    // Unwind variant array (preserving null values)
    {
      $unwind: {
        path: '$variant',
        preserveNullAndEmptyArrays: true
      }
    },

    // Lookup category details
    {
      $lookup: {
        from: 'categories',
        localField: 'product.category',
        foreignField: '_id',
        as: 'product.category'
      }
    },

    // Unwind category array
    {
      $unwind: {
        path: '$product.category',
        preserveNullAndEmptyArrays: true
      }
    },
  ];

  // Add search filter if provided
  if (search) {
    pipeline.push({
      $match: {
        'product.name': { $regex: search, $options: 'i' }
      }
    });
  }

  // Create a copy of the pipeline for counting total documents
  const countPipeline = [ ...pipeline ];

  // Add count stage to the count pipeline
  countPipeline.push({ $count: 'total' });

  // Get total count for pagination
  const totalDocs = await FavoriteProduct.aggregate(countPipeline);
  const total = totalDocs.length > 0 ? totalDocs[0].total : 0;

  // Add sorting, pagination to the main pipeline
  pipeline.push(
    { $sort: sort },
    { $skip: skip },
    { $limit: parseInt(limit) }
  );

  // Execute aggregation
  const favorites = await FavoriteProduct.aggregate(pipeline);

  const productIds = favorites.map(fav => fav.product._id);

  const productVariants = await ProductVariant.find({
    product: { $in: productIds }
  });

  favorites.forEach(favorite => {
    favorite.product.variants = productVariants.filter(variant => variant.product.toString() === favorite.product._id.toString());
  });

  return {
    favorites,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

module.exports = {
  toggleFavoriteProduct,
  getFavoriteProducts
};
