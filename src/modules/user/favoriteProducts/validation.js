const joi = require('joi');

const toggleFavoriteSchema = joi.object({
  productId: joi.string().required().messages({
    'string.empty': 'Product ID is required',
    'any.required': 'Product ID is required'
  }),
  variantId: joi.string().allow(null, ''),
  isFavorite: joi.boolean().required().messages({
    'boolean.base': 'isFavorite must be a boolean',
    'any.required': 'isFavorite is required'
  })
});

module.exports = {
  toggleFavoriteSchema
};
