const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const { searchEntitiesSchema } = require('../validation');
const favoriteService = require('./service');
const { addFavoriteSchema } = require('./validation');
const { SUCCESS } = commonConstants;

const handleFavorite = async (req, res) => {
  try {
    const { error } = addFavoriteSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await favoriteService.handleFavouriteTemple(req.user.id, req.body);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: messages.SUCCESS, 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getFavorites = async (req, res) => {
  try {
    const { error } = searchEntitiesSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await favoriteService.getFavoriteTemples(req.user.id, req.query);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: messages.SUCCESS, 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  handleFavorite,
  getFavorites,
};