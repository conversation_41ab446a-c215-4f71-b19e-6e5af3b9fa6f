const FavoriteTemple = require('../../../models/FavoriteTemple');
const Temple = require('../../../models/Temple');
const { throwBadRequestError } = require('../../../errors');
const { messages } = require('../../../messages');

const handleFavouriteTemple = async (userId, body) => {
  const temple = await Temple.findById(body.templeId);

  if (!temple) {
    throwBadRequestError(messages.TEMPLE_NOT_FOUND);
  }

  if (body.status === 1) {
    const isFavorite = await FavoriteTemple.findOne({
      user: userId,
      temple: body.templeId
    });

    if (isFavorite) {
      return [];
    }

    await FavoriteTemple.create({
      user: userId,
      temple: body.templeId
    });

  } else if (body.status === 2) {
    await FavoriteTemple.findOneAndDelete({
      user: userId,
      temple: body.templeId
    });
  }

  return [];
};

const getFavoriteTemples = async (userId, query) => {

  const { page = 1, limit = 10 } = query;
  
  const filter = { user: userId };

  const favorites = await FavoriteTemple.find(filter)
    .populate('temple')
    .sort({ createdAt: -1 }).collation({ locale: 'en', strength: 1 })
    .skip((parseInt(page) - 1) * parseInt(limit))
    .limit(parseInt(limit));

  const total = await FavoriteTemple.countDocuments(filter);

  return {
    records: favorites,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

module.exports = {
  handleFavouriteTemple,
  getFavoriteTemples,
};