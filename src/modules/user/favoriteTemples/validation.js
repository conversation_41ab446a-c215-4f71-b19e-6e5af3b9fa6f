const joi = require('joi');

const addFavoriteSchema = joi.object({
  templeId: joi.string().required().messages({
    'string.empty': 'Temple ID is required',
    'any.required': 'Temple ID is required'
  }),
  status: joi.number().valid(1, 2).required().messages({
    'any.only': 'Status must be either 1 or 2',
    'any.required': 'Status is required'
  })
});

module.exports = {
  addFavoriteSchema
};