const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const helicopterBookingService = require('./service');
const { createHelicopterBookingSchema } = require('./validation');
const { SUCCESS } = commonConstants;

const createHelicopterBooking = async (req, res) => {
  try {
    const { error } = createHelicopterBookingSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await helicopterBookingService.createHelicopterBooking(req.user.id, req.body);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: messages.HELICOPTER_BOOKING_CREATED, 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

// const listHelicopterBookings = async (req, res) => {
//   try {
//     const { error } = listHelicopterBookingsSchema.validate(req.query);

//     if (error) {
//       return res.status(400).json({ 
//         message: error.details[0].message,
//         status: false 
//       });
//     }

//     const data = await helicopterBookingService.listHelicopterBookings(req.user.id, req.query);
    
//     return apiResponse({ 
//       res, 
//       code: SUCCESS.CODE, 
//       message: messages.SUCCESS, 
//       status: true, 
//       data 
//     });
//   } catch (error) {
//     return errorApiResponse(res, error);
//   }
// };

module.exports = {
  createHelicopterBooking,
  // listHelicopterBookings
};