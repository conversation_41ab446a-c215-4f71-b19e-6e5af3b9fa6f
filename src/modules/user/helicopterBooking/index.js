const express = require('express');
const router = express.Router();
const helicopterBookingController = require('./controller');
const auth = require('../../../middleware/auth');
const { isUser } = require('../../../middleware/roleCheck');

router.post('/create', auth, isUser, helicopterBookingController.createHelicopterBooking);
// router.get('/list', auth, isUser, helicopterBookingController.listHelicopterBookings);

module.exports = router;