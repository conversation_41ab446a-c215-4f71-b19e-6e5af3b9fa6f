const HelicopterQuery = require('../../../models/HelicopterQuery');
const Helicopter = require('../../../models/Helicopter');
const Booking = require('../../../models/Booking');
const { translateDataForStore } = require('../../../utils/translateInput');
const { throwBadRequestError } = require('../../../errors');
const User = require('../../../models/User');
const moment = require('moment');
const { transformTranslatedFields } = require('../../../utils/localizer');

const createHelicopterBooking = async (userId, bookingData) => {
  // Validate user
  const user = await User.findById(userId);

  if (!user) {
    throwBadRequestError('User not found');
  }

  if (user.isGuestUser) {
    throwBadRequestError('Please complete your profile to book helicopter service');
  }

  // Validate helicopter
  const helicopter = await Helicopter.findById(bookingData.helicopter);

  if (!helicopter) {
    throwBadRequestError('Helicopter not found');
  }

  if (helicopter.deletedAt || helicopter.status === 'INACTIVE') {
    throwBadRequestError('Helicopter already deleted or inactive');
  }

  // Validate date
  const bookingDate = new Date(bookingData.date);
  let today = moment().format('YYYY-MM-DD') + 'T00:00:00.000Z';

  today = new Date(today);

  if (bookingDate < today) {
    throwBadRequestError('Booking date cannot be in the past');
  }

  // Validate availability
  const availability = helicopter.availability.find(a => a.date.toISOString().split('T')[0] === bookingDate.toISOString().split('T')[0]);

  if (!availability) {
    throwBadRequestError('No availability on the selected date');
  }
  if (availability.availableSeats < bookingData.numberOfPeople) {
    throwBadRequestError('Not enough seats available on the selected date');
  }

  const amount = bookingData.numberOfPeople * helicopter.basePrice;

  bookingData.amount = amount;

  // 1. Translate primary devotee's full name
  const translatedPrimary = await translateDataForStore([ 'fullName' ], bookingData.primaryDevoteeDetails);

  bookingData.primaryDevoteeDetails.fullName = translatedPrimary.fullName;

  // 2. Translate each otherDevotee's full name
  bookingData.otherDevotees = await Promise.all(
    bookingData.otherDevotees.map(async (devotee) => {
      const translated = await translateDataForStore([ 'fullName' ], devotee);

      return {
        ...devotee,
        fullName: translated.fullName
      };
    })
  );

  // 3. Create HelicopterQuery
  const booking = await HelicopterQuery.create({
    ...bookingData,
    user: userId
  });

  //* Save the data into booking table, with status as SUBMITTED
  await Booking.create({
    user: userId,
    helicopter: bookingData.helicopter,
    type: 'HELICOPTER_BOOKING',
    date: bookingData.date,
    individual: bookingData.numberOfPeople,
    primaryDevoteeDetails: bookingData.primaryDevoteeDetails,
    otherDevotees: bookingData.otherDevotees,
    status: 'SUBMITTED',
    totalAmount: amount,
    bookingNumber: `HELICOPTER-${Date.now()}-${Math.floor(Math.random() * 1000)}`
  });

  //* Update helicopter availability for the booked date
  availability.availableSeats -= bookingData.numberOfPeople;
  await helicopter.save();

  return booking;
};

const listHelicopterBookings = async (userId, query) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, status, date, search } = query;

  const filter = {
    user: userId
  };

  if (status) {
    filter.status = status;
  }

  if (date) {
    filter.date = new Date(date);
  }

  if (search) {
    filter.$or = [
      { 'primaryDevoteeDetails.fullName.en': { $regex: search, $options: 'i' } },
      { 'primaryDevoteeDetails.fullName.hi': { $regex: search, $options: 'i' } },
      { 'otherDevotees.fullName.en': { $regex: search, $options: 'i' } },
      { 'otherDevotees.fullName.hi': { $regex: search, $options: 'i' } }
    ];
  }

  const language = { code: 'en' };

  const bookings = await HelicopterQuery.find(filter)
    .sort({ [sortBy]: parseInt(sortOrder) })
    .collation({ locale: 'en', strength: 1 })
    .skip((parseInt(page) - 1) * parseInt(limit))
    .limit(parseInt(limit))
    .lean();

  const total = await HelicopterQuery.countDocuments(filter);

  return {
    bookings: await transformTranslatedFields(bookings, language.code),
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

module.exports = {
  createHelicopterBooking,
  listHelicopterBookings
};