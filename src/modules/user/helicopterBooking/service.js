const HelicopterBooking = require('../../../models/HelicopterBooking');
// const { translateDataForStore } = require('../../../utils/translateInput');
const { throwBadRequestError } = require('../../../errors');
const User = require('../../../models/User');
// const { transformTranslatedFields } = require('../../../utils/localizer');
// const Language = require('../../../models/Language');
const { emailSubjects } = require('../../../messages/emailSubjects');
const path = require('path');
const { sendMail } = require('../../../utils/sendMail');

const createHelicopterBooking = async (userId, bookingData) => {
  // Validate user
  const user = await User.findById(userId);

  if (!user) {
    throwBadRequestError('User not found');
  }

  if (user.isGuestUser) {
    throwBadRequestError('Please complete your profile to submit helicopter query');
  }

  // Create HelicopterQuery
  const booking = await HelicopterBooking.create({
    ...bookingData,
    user: userId
  });

  //* Send Email to Admin 
  const subject = emailSubjects.HELICOPTER_BOOKING_QUERY;
  const templatePath = path.join(__dirname, '../../../views/helicopterQuery.html');
  const data = {
    name: bookingData.fullName,
    email: bookingData.email, 
    destination: bookingData.destination,
    date: bookingData.date,
    number_of_devotees: bookingData.numberOfPeople
  };
  const email = process.env.SUPPORT_CONTACT;

  await sendMail(email, subject, templatePath, data);

  return booking;
};

// const listHelicopterBookings = async (userId, query) => {
//   const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = -1, status, date, search } = query;

//   const filter = {
//     user: userId
//   };

//   if (status) {
//     filter.status = status;
//   }

//   if (date) {
//     filter.date = new Date(date);
//   }

//   let language = { code: 'en' };

//   if (userId) {
//     const loggedInUser = await User.findById(userId);

//     if (loggedInUser && loggedInUser.preferredLanguage) {
//       const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

//       if (userLanguage) {
//         language = userLanguage;
//       }
//     }
//   }

//   if (search) {
//     filter.$or = [
//       { [`primaryDevoteeDetails.fullName.${language.code}`]: { $regex: search, $options: 'i' } },
//       { [`otherDevotees.fullName.${language.code}`]: { $regex: search, $options: 'i' } }
//     ];
//   }

//   const bookings = await HelicopterBooking.find(filter)
//     .sort({ [sortBy]: parseInt(sortOrder) })
//     .collation({ locale: 'en', strength: 1 })
//     .skip((parseInt(page) - 1) * parseInt(limit))
//     .limit(parseInt(limit))
//     .lean();

//   const total = await HelicopterBooking.countDocuments(filter);

//   return {
//     bookings: await transformTranslatedFields(bookings, language.code),
//     pagination: {
//       total,
//       page: parseInt(page),
//       limit: parseInt(limit),
//       pages: Math.ceil(total / parseInt(limit))
//     }
//   };
// };

module.exports = {
  createHelicopterBooking,
  // listHelicopterBookings
};