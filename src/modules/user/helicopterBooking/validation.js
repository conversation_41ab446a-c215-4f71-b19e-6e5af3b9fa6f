const joi = require('joi');
// const mongoose = require('mongoose');
// const { helicopterBookingStatus } = require('../../../constants/dbEnums');

// const validateObjectId = (value, helpers) => {
//   if (!mongoose.Types.ObjectId.isValid(value)) {
//     return helpers.error('any.invalid');
//   }
//   return value;
// };

const createHelicopterBookingSchema = joi.object({
  date: joi.date().min('now').iso().required().messages({
    'date.base': 'Date must be a valid ISO date (YYYY-MM-DD)',
    'date.format': 'Date must be in ISO format (YYYY-MM-DD)',
    'any.required': 'Date is required'
  }),

  numberOfPeople: joi.number().min(1).max(6).required().messages({
    'number.min': 'Number of people must be at least 1',
    'number.max': 'Number of people cannot exceed 6',
    'any.required': 'Number of people is required'
  }),

  fullName: joi.string().trim().required().messages({
    'string.empty': 'Full name cannot be empty',
    'any.required': 'Full name is required'
  }),

  email: joi.string().email().required().messages({
    'string.email': 'Email must be valid',
    'any.required': 'Email is required'
  }),

  destination: joi.string().trim().required().messages({
    'string.empty': 'Destination cannot be empty',
    'any.required': 'Destination is required'
  })
});

module.exports = {
  createHelicopterBookingSchema,
};