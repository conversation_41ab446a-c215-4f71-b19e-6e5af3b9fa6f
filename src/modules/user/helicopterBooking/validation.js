const joi = require('joi');
const mongoose = require('mongoose');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const createHelicopterBookingSchema = joi.object({
  helicopter: joi.string().custom(validateObjectId).required().messages({
    'any.invalid': 'Invalid helicopter ID format',
    'any.required': 'Helicopter ID is required'
  }),

  date: joi.date().min('now').iso().required().messages({
    'date.base': 'Date must be a valid ISO date (YYYY-MM-DD)',
    'date.format': 'Date must be in ISO format (YYYY-MM-DD)',
    'any.required': 'Date is required'
  }),

  numberOfPeople: joi.number().min(1).max(6).required().messages({
    'number.min': 'Number of people must be at least 1',
    'number.max': 'Number of people cannot exceed 6',
    'any.required': 'Number of people is required'
  }),

  primaryDevoteeDetails: joi.object({
    fullName: joi.string().required().messages({
      'string.empty': 'Full name is required',
      'any.required': 'Full name is required'
    }),
    gender: joi.string().valid('Male', 'Female', 'Other').required().messages({
      'any.only': 'Gender must be Male, Female, or Other',
      'any.required': 'Gender is required'
    }),
    aadharNumber: joi.string().optional(),
    aadharImage: joi.string().optional(),
    weight: joi.number().min(1).optional().messages({
      'number.min': 'Weight cannot be less than 1'
    }),
    phoneNumber: joi.string().required().messages({
      'string.empty': 'Phone number is required',
      'any.required': 'Phone number is required'
    }),
    whatsappNumber: joi.string().optional()
  }).required().messages({
    'any.required': 'Primary devotee details are required'
  }),
  
  otherDevotees: joi.array().items(
    joi.object({
      fullName: joi.string().required().messages({
        'string.empty': 'Full name is required',
        'any.required': 'Full name is required'
      }),
      gender: joi.string().valid('Male', 'Female', 'Other').required().messages({
        'any.only': 'Gender must be Male, Female, or Other',
        'any.required': 'Gender is required'
      }),
      aadharNumber: joi.string().optional(),
      aadharImage: joi.string().optional(),
      weight: joi.number().min(1).optional().messages({
        'number.min': 'Weight cannot be less than 1'
      })
    })
  ).max(5).messages({
    'array.max': 'You cannot add more than 5 additional devotees'
  })
}).custom((value, helpers) => {
  const totalDevotees = 1 + (value.otherDevotees?.length || 0);

  if (totalDevotees > 6) {
    return helpers.message('Total number of devotees (including primary) cannot exceed 6');
  }
  if (value.numberOfPeople !== totalDevotees) {
    return helpers.message('Number of people must match the number of devotees');
  }
  return value;
});

const listHelicopterBookingsSchema = joi.object({
  page: joi.number().integer().min(1).default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
  limit: joi.number().integer().min(1).max(100).default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
  sortBy: joi.string().valid('createdAt', 'date', 'status', 'totalAmount').default('createdAt')
    .messages({
      'string.base': 'Sort field must be a string',
      'any.only': 'Sort field must be one of: createdAt, date, status, totalAmount'
    }),
  sortOrder: joi.number().valid(1, -1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    }),
  search: joi.string().trim().allow('').optional()
    .messages({
      'string.base': 'Search query must be a string'
    }),
  status: joi.string().valid('SUBMITTED', 'APPROVED', 'REJECTED', 'CANCELLED', 'COMPLETED').allow('').optional()
    .messages({
      'any.only': 'Status must be one of: SUBMITTED, APPROVED, REJECTED, CANCELLED, or COMPLETED'
    }),
  date: joi.date().iso().allow('').optional()
    .messages({
      'date.base': 'Date must be a valid date',
      'date.format': 'Date must be in ISO format (YYYY-MM-DD)'
    })
});

module.exports = {
  createHelicopterBookingSchema,
  listHelicopterBookingsSchema
};