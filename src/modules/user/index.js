const express = require('express');
const router = express.Router();
const userController = require('./controller');
const favoriteController = require('./favoriteTemples/controller');
const addressController = require('./address/controller');
const auth = require('../../middleware/auth');
const favoriteProductRoutes = require('./favoriteProducts');
const { sendFirebasePushMulticast } = require('../../utils/firebase_cm');
const discountRoutes = require('./discount');
const { translateText } = require('../../utils/translator');

// Auth Sub-Module :
router.use('/auth', require('./auth'));

router.post('/register', userController.registerUser);
router.get('/profile', auth, userController.getUserById);
router.patch('/profile', auth, userController.updateUser);
router.get('/deletion-allowed', auth, userController.checkDeletionEligibility);
router.post('/generate-deletion-otp', auth, userController.generateDeletionOTP);
router.post('/delete', auth, userController.deleteUser);
router.post('/update/fcm-token', auth, userController.updateFCMToken);

// Favourite Temple :
router.post('/handle-favourite-temple', auth , favoriteController.handleFavorite);
router.get('/favourite-temples', auth, favoriteController.getFavorites);

router.get('/home', auth, userController.getHomePageDetails);
router.get('/home/<USER>', auth, userController.getHomePageEntities);
router.get('/temples', auth, userController.getAllTemples);

// Address :
router.post('/address', auth, addressController.addAddress);
router.delete('/address/:id', auth, addressController.deleteAddress);
router.get('/address', auth, addressController.getAddresses);
router.patch('/address/:id', auth, addressController.updateAddress);
router.get('/address/:id', auth, addressController.getAddressById);

// Favourite Products :
router.use('/favorite-products', favoriteProductRoutes);

// Notifications :
router.get('/notifications', auth, userController.getNotifications);
router.post('/notification/mark', auth, userController.markNotificationAsRead);

router.post('/fcm-test', async (req, res) => {
  const { tokens } = req.body;

  const message = 'This is a test message from One God.';

  await sendFirebasePushMulticast(message , 'One God', tokens);

  return res.status(200).json({ message: 'Success' });
});

router.post('/translation-test', async (req, res) => {
  const { text, targetLanguage } = req.body;

  const translatedText = await translateText(text, targetLanguage);

  return res.status(200).json({ translatedText });
});

router.use('/discount', discountRoutes);

module.exports = router;