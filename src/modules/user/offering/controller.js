const offeringService = require('./service');
const { listOfferingsSchema } = require('./validation');
const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { SUCCESS } = commonConstants;
const { messages } = require('../../../messages');

const listOfferings = async (req, res) => {
  try {
    const { error } = listOfferingsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        status: false,
        message: error.details[0].message
      });
    }

    const data = await offeringService.listOfferings(req.query, req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.OFFERINGS_RETRIEVED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  listOfferings
};