const panchangService = require('./service');
const { getMuhuratDaySchema, getDurMuhuratSchema, getHoraSchema } = require('./validation');
const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { SUCCESS } = commonConstants;
const { messages } = require('../../../messages');

//* 1. Function to get muhurat day
const getMuhuratDay = async (req, res) => {
  try {
    const { error } = getMuhuratDaySchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await panchangService.getMuhuratDay(req.query, req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.MUHURAT_RETRIEVED_SUCCESSFULLY,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 2. Function to get durmuhurat
const getDurmuhurat = async (req, res) => {
  try {
    const { error } = getDurMuhuratSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await panchangService.getDurmuhurat(req.query, req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.DURMUHURAT_RETRIEVED_SUCCESSFULLY,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

//* 3. Function to get hora
const getHora = async (req, res) => {
  try {
    const { error } = getHoraSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await panchangService.getHora(req.query, req.user.id);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.HORA_RETRIEVED_SUCCESSFULLY,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getMuhuratDay,
  getDurmuhurat,
  getHora
};