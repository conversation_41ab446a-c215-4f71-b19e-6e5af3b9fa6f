const express = require('express');
const router = express.Router();
const panchangController = require('./controller');
const auth = require('../../../middleware/auth');
const { isUser } = require('../../../middleware/roleCheck');

router.get('/muhurat-day', auth, isUser, panchangController.getMuhuratDay);
router.get('/durmuhurat', auth, isUser, panchangController.getDurmuhurat);
router.get('/hora', auth, isUser, panchangController.getHora);

module.exports = router;