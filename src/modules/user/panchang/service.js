const axios = require('axios');
const User = require('../../../models/User');
const Language = require('../../../models/Language');
const { throwBadRequestError } = require('../../../errors');

//* 1. Function to get muhurat day
const getMuhuratDay = async (query, userId) => {
  let language = { code: 'en' }; // Default to English

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  const params = {
    muhurat: query.muhurat,
    date: query.date,
    month: query.month,
    year: query.year,
    tz: query.tz,
    userid: process.env.USER_ID,
    authcode: process.env.AUTH_CODE,
    lat: query.lat,
    lon: query.lon,
    lang: query.lang || language.code || 'en'
  };

  const { data } = await axios.get(`${process.env.PANCHANG_API_URL}muhurat-day`, { params });

  if (!data) {
    throwBadRequestError('No muhurat found for the given date');
  }

  return data;
};

//* 2. Function to get durmuhurat
const getDurmuhurat = async (query, userId) => {
  let language = { code: 'en' }; // Default to English

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  const params = {
    durmuhurat: query.durmuhurat,
    date: query.date,
    tz: query.tz,
    time: query.time,
    userid: process.env.USER_ID,
    authcode: process.env.AUTH_CODE,
    lat: query.lat,
    lon: query.lon,
    lang: query.lang || language.code || 'en'
  };

  const { data } = await axios.get(`${process.env.PANCHANG_API_URL}durmuhurat`, { params });

  if (!data) {
    throwBadRequestError('No durmuhurat found for the given date');
  }

  return data;
};

//* 3. Function to get hora
const getHora = async (query, userId) => {
  let language = { code: 'en' }; // Default to English

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  const params = {
    date: query.date,
    time: query.time,
    tz: query.tz,
    userid: process.env.USER_ID,
    authcode: process.env.AUTH_CODE,
    lat: query.lat,
    lon: query.lon,
    lang: query.lang || language.code || 'en'
  };

  const { data } = await axios.get(`${process.env.PANCHANG_API_URL}hora`, { params });

  if (!data) {
    throwBadRequestError('No hora found for the given date');
  }

  return data;
};

module.exports = {
  getMuhuratDay,
  getDurmuhurat,
  getHora
};