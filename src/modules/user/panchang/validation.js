const joi = require('joi');

const getMuhuratDaySchema = joi.object({
  muhurat: joi.string()
    .valid('vivah', 'grihpravesh', 'vaahan', 'sampatti')
    .required()
    .messages({
      'any.only': 'Invalid muhurat type. Allowed values are: vivah, grihpravesh, vaahan, sampatti',
      'string.empty': 'Muhurat is required',
      'any.required': 'Muhurat is required'
    }),

  date: joi.string()
    .pattern(/^(0[1-9]|[12][0-9]|3[01])$/)
    .required()
    .messages({
      'string.pattern.base': 'Date must be a valid two-digit number between 01 and 31',
      'string.empty': 'Date is required',
      'any.required': 'Date is required'
    }),

  month: joi.string()
    .pattern(/^(0[1-9]|1[0-2])$/)
    .required()
    .messages({
      'string.pattern.base': 'Month must be a valid two-digit number between 01 and 12',
      'string.empty': 'Month is required',
      'any.required': 'Month is required'
    }),

  year: joi.number()
    .integer()
    .min(1930)
    .max(2030)
    .required()
    .messages({
      'number.base': 'Year must be a number',
      'number.min': 'Year must be at least 1930',
      'number.max': 'Year must be at most 2030',
      'any.required': 'Year is required'
    }),

  lat: joi.number()
    .required()
    .messages({
      'number.base': 'Latitude must be a decimal number',
      'any.required': 'Latitude is required'
    }),

  lon: joi.number()
    .required()
    .messages({
      'number.base': 'Longitude must be a decimal number',
      'any.required': 'Longitude is required'
    }),

  tz: joi.number()
    .required()
    .messages({
      'number.base': 'Timezone must be a decimal number',
      'any.required': 'Timezone is required'
    }),

  lang: joi.string()
    .optional()
    .valid('en', 'hi') 
    .messages({
      'any.only': 'Language must be "en" or "hi"',
      'string.base': 'Language must be a string'
    })
});

const getDurMuhuratSchema = joi.object({
  durmuhurat: joi.string()
    .valid('rahukaal', 'gulikkaal', 'yamgantakkaal', 'varjyam', 'kaalvela', 'vaarvela')
    .required()
    .messages({
      'any.only': 'Invalid durmuhurat type. Allowed values are: rahukaal, gulikkaal, yamgantakkaal, varjyam, kaalvela, vaarvela',
      'string.empty': 'Durmuhurat is required',
      'any.required': 'Durmuhurat is required'
    }),
  
  date: joi.string()
    .pattern(/^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/(19[4-9][0-9]|20[0-4][0-9])$/)
    .required()
    .messages({
      'string.pattern.base': 'Date must be in DD/MM/YYYY format and between 01/01/1949 and 31/12/2049',
      'string.empty': 'Date is required',
      'any.required': 'Date is required'
    }),

  time: joi.string()
    .pattern(/^([01]\d|2[0-3]):([0-5]\d)$/)
    .required()
    .messages({
      'string.pattern.base': 'Time must be in HH:MM format (24-hour clock), for example 20:50 (for 08:50 PM)',
      'string.empty': 'Time is required',
      'any.required': 'Time is required'
    }),
  
  lat: joi.number()
    .required()
    .messages({
      'number.base': 'Latitude must be a decimal number',
      'any.required': 'Latitude is required'
    }),
  
  lon: joi.number()
    .required()
    .messages({
      'number.base': 'Longitude must be a decimal number',
      'any.required': 'Longitude is required'
    }),
  
  tz: joi.number()
    .required()
    .messages({
      'number.base': 'Timezone must be a decimal number',
      'any.required': 'Timezone is required'
    }),
  
  lang: joi.string()
    .optional()
    .valid('en', 'hi')
    .messages({
      'any.only': 'Language must be "en" or "hi"',
      'string.base': 'Language must be a string'
    })
});

const getHoraSchema = joi.object({
  date: joi.string()
    .pattern(/^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/(19[4-9][0-9]|20[0-4][0-9])$/)
    .required()
    .messages({
      'string.pattern.base': 'Date must be in DD/MM/YYYY format and between 01/01/1949 and 31/12/2049',
      'string.empty': 'Date is required',
      'any.required': 'Date is required'
    }),
  
  time: joi.string()
    .pattern(/^([01]\d|2[0-3]):([0-5]\d)$/)
    .required()
    .messages({
      'string.pattern.base': 'Time must be in HH:MM format (24-hour clock), for example 20:50 (for 08:50 PM)',
      'string.empty': 'Time is required',
      'any.required': 'Time is required'
    }),
  
  lat: joi.number()
    .required()
    .messages({
      'number.base': 'Latitude must be a decimal number',
      'any.required': 'Latitude is required'
    }),
  
  lon: joi.number()
    .required()
    .messages({
      'number.base': 'Longitude must be a decimal number',
      'any.required': 'Longitude is required'
    }),
  
  tz: joi.number()
    .required()
    .messages({
      'number.base': 'Timezone must be a decimal number',
      'any.required': 'Timezone is required'
    }),

  lang: joi.string()
    .optional()
    .valid('en', 'hi')
    .messages({
      'any.only': 'Language must be "en" or "hi"',
      'string.base': 'Language must be a string'
    })
});

module.exports = {
  getMuhuratDaySchema,
  getDurMuhuratSchema,
  getHoraSchema
};