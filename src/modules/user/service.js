const { throwBadRequestError } = require('../../errors');
const { messages } = require('../../messages');
const User = require('../../models/User');
const { createToken } = require('../../database/queries/accessToken.query');
const { generateToken } = require('../../utils/jwt');
const { findOtp, findAndUpdateOtp, deleteOtps, createOtp } = require('../../database/queries/otp.query');
const { otpTypeValue, userTypeValue, darshanStatus, productStatusValue } = require('../../constants/dbEnums');
const { findUser, createUser } = require('../../database/queries/user.query');
const Temple = require('../../models/Temple');
const Banner = require('../../models/Banner');
const Product = require('../../models/Product');
const { default: mongoose } = require('mongoose');
const LiveDarshan = require('../../models/LiveDarshan');
const Event = require('../../models/Event');
const Booking = require('../../models/Booking');
const Token = require('../../models/Token');
const { type, status } = require('../../constants/dbEnums');
const Notification = require('../../models/Notification');
const Order = require('../../models/Order');
const { orderStatusValue } = require('../../constants/dbEnums');
const moment = require('moment');
const { translateDataForStore } = require('../../utils/translateInput');
const { transformTranslatedFields } = require('../../utils/localizer');
const Language = require('../../models/Language');
const { sendSMS } = require('../../utils/sms');

const getAllUsers = async () => {
  return User.find().select('-password');
};

const getUserById = async (id) => {
  const user = await User.findById(id).select('-password');

  if (!user) {
    throwBadRequestError(messages.USER_NOT_FOUND);
  }

  const language = await Language.findOne({ name: user.preferredLanguage });
  
  const localizedUser = await transformTranslatedFields(user, language.code);

  return localizedUser;
};

const updateUser = async (userId, userData) => {
  const user = await User.findById(userId).select('-password');

  if (!user) {
    throwBadRequestError(messages.USER_NOT_FOUND);
  }

  if (userData.isGuestUser) {
    userData.isGuestUser = false;
  }

  if (userData.firstName) {
    const translatedData = await translateDataForStore([ 'firstName' ], userData);

    userData.firstName = translatedData.firstName;
  }

  if (userData.lastName) {
    const translatedData = await translateDataForStore([ 'lastName' ], userData);

    userData.lastName = translatedData.lastName;
  }

  Object.keys(userData).forEach(key => {
    user[key] = userData[key];
  });

  await user.save();

  // Localize user data
  const language = await Language.findOne({ name: user.preferredLanguage });
  
  const localizedUser = await transformTranslatedFields(user, language.code);

  return localizedUser;
};

const checkDeletionEligibility = async (userId) => {
  //* 1. Check if user exists
  const user = await User.findById({
    _id: userId,
    deletedAt: null
  });

  if (!user) {
    throwBadRequestError(messages.USER_NOT_FOUND);
  }

  //* 2. Check for upcoming bookings
  const now = new Date();

  now.setUTCHours(0, 0, 0, 0); // Midnight UTC for date-only comparison

  const bookings = await Booking.find({
    user: userId,
    status: status.COMPLETED,
    $or: [
      //* Future date bookings (darshan/pooja)
      {
        type: { $in: [ type.PHYSICAL_DARSHAN, type.PHYSICAL_POOJA, type.VIRTUAL_POOJA ] },
        date: { $gte: now }
      },
      //* Event bookings - future dates
      {
        type: type.EVENT,
        'eventDates': {
          $elemMatch: { date: { $gte: now } }
        }
      }
    ]
  });

  const hasUpcomingBooking = bookings.some((booking) => {
    if (booking.type !== type.EVENT) {
      const bookingDate = new Date(booking.date);

      // Future date bookings
      if (bookingDate.getTime() > now.getTime()) {
        return true;
      }

      // Booking is for today, check time
      const [ time, period ] = booking.timeSlot.endTime.split(' ');
      const [ hours, minutes ] = time.split(':');
      let h = parseInt(hours);

      if (period === 'PM' && h !== 12) {
        h += 12;
      }
      if (period === 'AM' && h === 12) {
        h = 0;
      }

      const bookingEndTime = new Date(now);

      bookingEndTime.setHours(h, parseInt(minutes), 0, 0);

      return bookingEndTime > new Date();
    } else {
      // Handle Event booking
      const todayMatch = booking.eventDates.find((event) => {
        return new Date(event.date).getTime() === now.getTime();
      });

      if (todayMatch) {
        const [ time, period ] = booking.timeSlot.endTime.split(' ');
        const [ hours, minutes ] = time.split(':');
        let h = parseInt(hours);

        if (period === 'PM' && h !== 12) {
          h += 12;
        }
        if (period === 'AM' && h === 12) {
          h = 0;
        }

        const eventEndTime = new Date(now);

        eventEndTime.setHours(h, parseInt(minutes), 0, 0);

        return eventEndTime > new Date();
      }

      // If today not found, but future dates exist, count as upcoming
      return booking.eventDates.some((event) => new Date(event.date).getTime() > now.getTime());
    }
  });

  if (hasUpcomingBooking) {
    return true;
  }

  //* 3. Check for active orders
  const hasActiveOrder = await Order.find({
    user: userId, 
    orderStatus: {
      $in: [
        orderStatusValue.PROCESSING,
        orderStatusValue.PARTIALLY_SHIPPED,
        orderStatusValue.SHIPPED
      ]
    }
  });

  if (hasActiveOrder.length > 0) {
    return true;
  }
  return false;
};

const deleteUser = async (userId, userType, token, otp) => {
  //* 1. Check if user exists
  const user = await User.findById({
    _id: userId,
    deletedAt: null
  });

  if (!user) {
    throwBadRequestError(messages.USER_NOT_FOUND);
  }

  //* 2 Verify OTP
  const otpRecord = await findOtp({
    phoneNumber: user.phoneNumber,
    countryCode: user.countryCode,
    type: otpTypeValue.PHONE_NUMBER,
    userType: userTypeValue.USER,
    deletedAt: null,
    isVerified: false,
  });

  if (!otpRecord) {
    throwBadRequestError(messages.INVALID_OTP);
  }

  if (new Date(otpRecord.expiresAt) < new Date()) {
    await deleteOtps({
      phoneNumber: user.phoneNumber,
      countryCode: user.countryCode,
      type: otpTypeValue.PHONE_NUMBER,
      userType: userTypeValue.USER,
      deletedAt: null,
      isVerified: false,
    });
    throwBadRequestError(messages.OTP_EXPIRED);
  }

  const receivedOtp = String(otp).trim();
  const storedOtp = String(otpRecord.otp).trim();

  if (process.env.APP_ENV && process.env.APP_ENV === 'development') {
    if (receivedOtp !== '797979' && receivedOtp !== storedOtp) {
      throwBadRequestError(messages.INVALID_OTP);
    }
  } else {
    if (receivedOtp !== storedOtp) {
      throwBadRequestError(messages.INVALID_OTP);
    }
  }

  await deleteOtps({
    phoneNumber: user.phoneNumber,
    countryCode: user.countryCode,
    type: otpTypeValue.PHONE_NUMBER,
    userType: userTypeValue.USER
  });

  //* 3. Check for upcoming bookings or orders
  const hasUpcomingBookingOrOrder = await checkDeletionEligibility(userId);

  if (hasUpcomingBookingOrOrder) {
    throwBadRequestError('Cannot delete user with upcoming bookings or active orders');
  }

  user.deletedAt = new Date();
  await user.save();

  await Token.deleteOne({
    userId,
    token,
    userType
  });

  return user;
};

/**
 * Create a new user and generate a token
 * @param {Object} userData - User data to create
 * @returns {Object} User data and token
 */
const addUser = async (userData) => {
  // Check if user already exists with the same phone number
  const existingUser = await findUser({
    phoneNumber: userData.phoneNumber,
    countryCode: userData.countryCode,
    deletedAt: null
  });

  if (existingUser) {
    throwBadRequestError(messages.USER_ALREADY_EXISTS);
  }

  const otpRecord = await findOtp({
    phoneNumber: userData.phoneNumber,
    countryCode: userData.countryCode,
    type: otpTypeValue.PHONE_NUMBER,
    userType: userTypeValue.USER,
    deletedAt: null,
    isVerified: true,
  });

  if (!otpRecord) {
    throwBadRequestError(messages.VERIFY_PHONE_NUMBER);
  }

  await findAndUpdateOtp({ _id: otpRecord._id }, { deletedAt: new Date() });

  const translatedData = await translateDataForStore([ 'firstName', 'lastName' ], userData);

  userData.firstName = translatedData.firstName;
  userData.lastName = translatedData.lastName;

  // Create the user
  let user = await createUser(userData);

  // Generate token
  user = user.toObject();
  user.userType = userTypeValue.USER;
  const token = await generateToken(user);

  // Save token in AccessTokens collection
  await createToken({
    token: token,
    userType: userTypeValue.USER,
    fcmToken: userData?.fcmToken,
    userId: user._id,
    expiresAt: new Date().getTime() + 5 * 60 * 1000
  });

  // Localize user data
  const language = await Language.findOne({ name: user.preferredLanguage });
  
  const localizedUser = await transformTranslatedFields(user, language.code);

  return {
    user: localizedUser,
    token
  };
};

const homePageEntities = async (user, query) => {
  const { search, page = 1, limit = 10 } = query;

  const parsedLimit = parseInt(limit);
  const skip = (parseInt(page) - 1) * parsedLimit;
  const now = new Date();

  // Get user's preferred language
  const loggedInUser = await User.findById(user.id);
  const language = await Language.findOne({ name: loggedInUser.preferredLanguage });

  // Search in temples
  const templesPipeline = [
    // Match by name if search is provided
    {
      $match: search ? { [`name.${language.code}`]: { $regex: search, $options: 'i' } } : {}
    },
    // Lookup favorites
    {
      $lookup: {
        from: 'favoritetemples',
        let: { templeId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: [ '$temple', '$$templeId' ] },
                  { $eq: [ '$user', user.id ? new mongoose.Types.ObjectId(user.id) : null ] }
                ]
              }
            }
          }
        ],
        as: 'favorites'
      }
    },
    // Add favorite flag
    {
      $addFields: {
        isFavourite: {
          $cond: {
            if: { $gt: [{ $size: '$favorites' }, 0 ] },
            then: true,
            else: false
          }
        }
      }
    },
    // Remove favorites array
    {
      $project: {
        favorites: 0
      }
    },
    {
      $facet: {
        data: [
          { $skip: skip },
          { $limit: parseInt(limit) }
        ],
        total: [
          { $count: 'count' }
        ]
      }
    }
  ];

  // Search in events
  const eventsPipeline = [
    // First, lookup temple data for temple name search
    {
      $lookup: {
        from: 'temples',
        localField: 'temple',
        foreignField: '_id',
        as: 'templeData'
      }
    },
    // Match by event name or temple name, and ensure events are active and not ended
    {
      $match: {
        status: 'ACTIVE',
        $or: [
          // Specific date events that are today or in the future
          { dateType: 'SPECIFIC_DATE', specificDate: { $gte: now } },
          // Date range events where the end date is today or in the future
          { dateType: 'DATE_RANGE', 'dateRange.endDate': { $gte: now } }
        ],
        ...(search ? {
          $or: [
            { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
            { [`templeData.name.${language.code}`]: { $regex: search, $options: 'i' } }
          ]
        } : {})
      }
    },
    // Lookup related data
    {
      $lookup: {
        from: 'temples',
        localField: 'temple',
        foreignField: '_id',
        as: 'templeData'
      }
    },
    // Unwind arrays to objects
    {
      $addFields: {
        temple: { $arrayElemAt: [ '$templeData', 0 ] },
      }
    },
    {
      $project: {
        templeData: 0,
      }
    },
    // Sort by date
    {
      $sort: { 'dateRange.startDate': 1, specificDate: 1 }
    },
    // Pagination
    {
      $facet: {
        data: [
          { $skip: skip },
          { $limit: parseInt(limit) } // Half the limit for events
        ],
        total: [
          { $count: 'count' }
        ]
      }
    }
  ];

  const productsPipeline = [
    // Match by name if search is provided
    {
      $match: search ? {
        [`name.${language.code}`]: { $regex: search, $options: 'i' },
        status: productStatusValue.ACTIVE
      } : {
        status: productStatusValue.ACTIVE
      }
    },
    // Lookup product variants
    {
      $lookup: {
        from: 'productvariants',
        let: { productId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: [ '$product', '$$productId' ] },
                  { $eq: [ '$isActive', true ] }
                ]
              }
            }
          },
          {
            $sort: { isDefault: -1 } // Sort to get default variants first
          }
        ],
        as: 'variants'
      }
    },
    // Lookup favorites
    {
      $lookup: {
        from: 'favoriteproducts',
        let: { productId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: [ '$product', '$$productId' ] },
                  { $eq: [ '$user', user.id ? new mongoose.Types.ObjectId(user.id) : null ] }
                ]
              }
            }
          }
        ],
        as: 'favorites'
      }
    },
    // Add favorite flag
    {
      $addFields: {
        isFavourite: {
          $cond: {
            if: { $gt: [{ $size: '$favorites' }, 0 ] },
            then: true,
            else: false
          }
        }
      }
    },
    // Remove favorites array
    {
      $project: {
        favorites: 0
      }
    },
    {
      $facet: {
        data: [
          { $skip: skip },
          { $limit: parseInt(limit) }
        ],
        total: [
          { $count: 'count' }
        ]
      }
    }
  ];

  // Execute both pipelines in parallel
  const [ templesResult, eventsResult, productsResult ] = await Promise.all([
    Temple.aggregate(templesPipeline),
    Event.aggregate(eventsPipeline),
    Product.aggregate(productsPipeline)
  ]);

  // Extract data
  const temples = templesResult[0]?.data || [];
  const templeCount = templesResult[0]?.total[0]?.count || 0;

  const events = eventsResult[0]?.data || [];
  const eventCount = eventsResult[0]?.total[0]?.count || 0;

  const products = productsResult[0]?.data || [];
  const productCount = productsResult[0]?.total[0]?.count || 0;

  // Localize results

  const [ localizedTemples, localizedEvents, localizedProducts ] = await Promise.all([
    transformTranslatedFields(temples, language.code),
    transformTranslatedFields(events, language.code),
    transformTranslatedFields(products, language.code)
  ]);

  return {
    temples: {
      data: localizedTemples,
      pagination: {
        total: templeCount,
        page: parseInt(page),
        pages: Math.ceil(templeCount / (parsedLimit)),
        limit: parsedLimit
      }
    },
    events: {
      data: localizedEvents,
      pagination: {
        total: eventCount,
        page: parseInt(page),
        pages: Math.ceil(eventCount / (parsedLimit)),
        limit: parsedLimit
      }
    },
    products: {
      data: localizedProducts,
      pagination: {
        total: productCount,
        page: parseInt(page),
        pages: Math.ceil(productCount / (parsedLimit)),
        limit: parsedLimit
      }
    },
    totalResults: templeCount + eventCount + productCount
  };
};

const homePageDetails = async (user) => {
  // Get user's preferred language
  const loggedInUser = await User.findById(user.id);
  const language = await Language.findOne({ name: loggedInUser.preferredLanguage });

  // const counts = {
  //   popularTempleCount: 6,
  //   eventCount: 6,
  //   bannerCount: 6,
  //   productCount: 6,
  //   liveDarshanCount: 6,
  // };

  const temples = Temple.aggregate([
    // { $limit: counts.popularTempleCount },
    { $match: { showOnHomepage: true } },
    {
      $lookup: {
        from: 'favoritetemples',
        let: { templeId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: [ '$temple', '$$templeId' ] },
                  { $eq: [ '$user', user.id ? new mongoose.Types.ObjectId(user.id) : null ] }
                ]
              }
            }
          }
        ],
        as: 'favorites'
      }
    },
    {
      $addFields: {
        isFavourite: {
          $cond: {
            if: { $gt: [{ $size: '$favorites' }, 0 ] },
            then: true,
            else: false
          }
        }
      }
    },
    {
      $project: {
        favorites: 0
      }
    }
  ]);

  const now = new Date();

  const banners = Banner.find({ status: 'ACTIVE', startDate: { $lte: now }, endDate: { $gte: now }, isActive: true });
  const queryObj = { status: darshanStatus.APPROVED };

  const today = new Date();
  const startOfToday = new Date(today.setUTCHours(0, 0, 0, 0));
  const startOfTomorrow = new Date(today.setUTCDate(today.getUTCDate() + 1));

  startOfTomorrow.setUTCHours(0, 0, 0, 0);
  queryObj.scheduledAt = {
    $gte: startOfToday,
    $lt: startOfTomorrow
  };

  const liveDarshans = LiveDarshan.find(queryObj).populate([
    { path: 'temple' },
    { path: 'templeAdmin', select: '-password' }
  ]).sort({ createdAt: -1 });

  // Get active events for homepage
  const events = Event.find({
    status: 'ACTIVE',
    showOnHomepage: true,
    $or: [
      // Specific date events that are today or in the future
      { dateType: 'SPECIFIC_DATE', specificDate: { $gte: now } },
      // Date range events where the end date is today or in the future
      { dateType: 'DATE_RANGE', 'dateRange.endDate': { $gte: now } }
    ]
  }).populate('temple').sort({ 'dateRange.startDate': 1, specificDate: 1 });

  // Get products for homepage
  const products = Product.aggregate([
    // { $limit: counts.popularTempleCount },
    { $match: {
      showOnHomepage: true,
      status: productStatusValue.ACTIVE
    } },
    // Lookup product variants
    {
      $lookup: {
        from: 'productvariants',
        let: { productId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: [ '$product', '$$productId' ] },
                  { $eq: [ '$isActive', true ] }
                ]
              }
            }
          },
          {
            $sort: { isDefault: -1 } // Sort to get default variants first
          }
        ],
        as: 'variants'
      }
    },
    // Lookup favorites
    {
      $lookup: {
        from: 'favoriteproducts',
        let: { productId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: [ '$product', '$$productId' ] },
                  { $eq: [ '$user', user.id ? new mongoose.Types.ObjectId(user.id) : null ] }
                ]
              }
            }
          }
        ],
        as: 'favorites'
      }
    },
    {
      $addFields: {
        isFavourite: {
          $cond: {
            if: { $gt: [{ $size: '$favorites' }, 0 ] },
            then: true,
            else: false
          }
        }
      }
    },
    {
      $project: {
        favorites: 0
      }
    }
  ]);

  const [ templesResult, bannersResult, liveDarshansResult, eventsResult, productsResult ] = await Promise.all([
    temples, banners, liveDarshans, events, products
  ]);

  // Localize results
  const localizedTemples = await transformTranslatedFields(templesResult || [], language.code);
  const localizedEvents = await transformTranslatedFields(eventsResult || [], language.code);
  const localizedProducts = await transformTranslatedFields(productsResult || [], language.code);
  const localizedBanners = await transformTranslatedFields(bannersResult || [], language.code);
  const localizedLiveDarshans = await transformTranslatedFields(liveDarshansResult || [], language.code);

  const result = {
    popularTemple: localizedTemples,
    event: localizedEvents,
    banner: localizedBanners,
    product: localizedProducts,
    liveDarshan: localizedLiveDarshans,
  };

  return result;
};

const allTemples = async (user, query) => {

  const { search, page = 1, limit = 10, category } = query;

  const parsedLimit = parseInt(limit);
  const skip = (parseInt(page) - 1) * parsedLimit;

  const templeQuery = {};

  const loggedInUser = await User.findById(user.id);
  const language = await Language.findOne({ name: loggedInUser.preferredLanguage });

  if (search) {
    templeQuery[`name.${language.code}`] = { $regex: search, $options: 'i' };
  }

  if (category) {
    templeQuery.category = new mongoose.Types.ObjectId(category);
  }

  const temples = await Temple.aggregate([
    {
      $match: {
        ...templeQuery
      }
    },
    {
      $lookup: {
        from: 'favoritetemples',
        let: { templeId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: [ '$temple', '$$templeId' ] },
                  { $eq: [ '$user', user.id ? new mongoose.Types.ObjectId(user.id) : null ] }
                ]
              }
            }
          }
        ],
        as: 'favorites'
      }
    },
    {
      $addFields: {
        isFavourite: {
          $cond: {
            if: { $gt: [{ $size: '$favorites' }, 0 ] },
            then: true,
            else: false
          }
        }
      }
    },
    {
      $project: {
        favorites: 0
      }
    },
    {
      $facet: {
        data: [
          { $skip: skip },
          { $limit: parseInt(limit) }
        ],
        total: [
          { $count: 'count' }
        ]
      }
    }
  ]);

  const localizedTemples = await transformTranslatedFields(temples[0]?.data || [], language.code);

  return {
    records: localizedTemples || [],
    pagination: {
      total: temples[0]?.total[0]?.count || 0,
      page: parseInt(page),
      pages: Math.ceil((temples[0]?.total[0]?.count || 0) / parsedLimit),
      limit: parsedLimit
    }
  };
};

const getNotifications = async (userId, query) => {
  const { page = 1, limit = 10 } = query;
  const queryObj = { userId };

  const promises = [];

  promises.push(await Notification.find(queryObj).sort({ createdAt: -1 }).skip((parseInt(page) - 1) * parseInt(limit)).limit(parseInt(limit)).lean(), await Notification.countDocuments(queryObj), await Notification.countDocuments({ userId, isRead: false }));

  const [ notifications, total, unreadCount ] = await Promise.all(promises);
  
  const loggedInUser = await User.findById(userId);
  const language = await Language.findOne({ name: loggedInUser.preferredLanguage });
  
  const localizedNotifications = await transformTranslatedFields(notifications, language.code);

  return {
    notifications: localizedNotifications,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    },
    unreadCount
  };
};

const markNotificationAsRead = async (body, userId) => {

  if (body?.id) {
    const notification = await Notification.find({ _id: body.id, userId });

    if (!notification) {
      throwBadRequestError(messages.NOTIFICATION_NOT_FOUND);
    }

    await Notification.updateOne(
      { _id: body.id, userId },
      { isRead: true }
    );
  } else {
    await Notification.updateMany(
      { userId },
      { isRead: true }
    );
  }

  return;
};

/**
 * Generate OTP for account deletion
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Success message
 */
const generateDeletionOTP = async (userId) => {
  //* 1. Check if user exists
  const user = await User.findById({
    _id: userId,
    deletedAt: null
  });

  if (!user) {
    throwBadRequestError(messages.USER_NOT_FOUND);
  }

  //* 2. Delete any existing OTPs for this user
  await deleteOtps({
    email: user.phoneNumber,
    type: otpTypeValue.PHONE_NUMBER,
    userType: userTypeValue.USER
  });

  //* 3. Generate new OTP
  const otp = String(Math.floor(100000 + Math.random() * 900000));
  const expiredAt = moment().add(5, 'minutes').toDate();

  //* 4. Save OTP in database
  await createOtp({
    phoneNumber: user.phoneNumber,
    email: user.email,
    countryCode: user.countryCode,
    type: otpTypeValue.PHONE_NUMBER,
    otp,
    userType: userTypeValue.USER,
    expiresAt: expiredAt
  });

  //* 5. Send OTP via SMS (you would implement this)
  // TODO: Implement SMS sending logic here
  // For now, we'll just return success

  // const countryCodeValue = user.countryCode.replace('+', '') ?? '91';

  // const recipients = [
  //   {
  //     mobiles: `${countryCodeValue}${user.phoneNumber}`,
  //     var1: otp,
  //     var2: 5
  //   }
  // ];

  // const template = user.preferredLanguage === 'Hindi' ? process.env.HINDI_SMS_TEMPLATE_ID : process.env.ENGLISH_SMS_TEMPLATE_ID;

  // await sendSMS(recipients, template);

  return { otp };
};

const updateFCMToken = async (user, accessToken, fcmToken) => {

  if (!fcmToken) {
    throwBadRequestError(messages.FCM_TOKEN_REQUIRED);
  }

  return await Token.updateOne(
    { userId: user.id, token: accessToken },
    { fcmToken }
  );
};

module.exports = {
  getAllUsers,
  getUserById,
  updateUser,
  checkDeletionEligibility,
  deleteUser,
  addUser,
  homePageDetails,
  homePageEntities,
  getNotifications,
  allTemples,
  markNotificationAsRead,
  generateDeletionOTP,
  updateFCMToken,
};
