const joi = require('joi');
const { default: mongoose } = require('mongoose');
const { addressSchema } = require('./address/validation');
const { parsePhoneNumberFromString } = require('libphonenumber-js');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const userRegistrationSchema = joi.object({
  isGuestUser: joi.boolean().default(false),
  firstName: joi.string().min(3).max(50).trim(true).when('isGuestUser', {
    is: false,
    then: joi.required(),
    otherwise: joi.optional()
  })
    .messages({
      'string.min': 'First name must be at least 3 characters',
      'string.max': 'First name cannot exceed 50 characters',
      'any.required': 'First name is required'
    }),
  lastName: joi.string().min(3).max(50).trim(true).when('isGuestUser', {
    is: false,
    then: joi.required(),
    otherwise: joi.optional()
  })
    .messages({
      'string.min': 'Last name must be at least 3 characters',
      'string.max': 'Last name cannot exceed 50 characters',
      'any.required': 'Last name is required'
    }),

  countryCode: joi.string()
    .trim()
    .pattern(/^\+\d{1,3}$/)
    .required()
    .messages({
      'string.empty': 'Country code is required',
      'string.pattern.base': 'Country code must start with + followed by 1 to 3 digits',
    }),

  phoneNumber: joi.string().required().custom((value, helpers) => {
    const { countryCode } = helpers.state.ancestors[0];

    const full = `${countryCode}${value}`;
    const parsed = parsePhoneNumberFromString(full);

    if (!parsed || !parsed.isValid()) {
      return helpers.message('Invalid phone number for the selected country');
    }

    return value; // Or return parsed.number for E.164 storage
  }).messages({
    'string.empty': 'Phone number is required',
  }),

  email: joi.string().email().lowercase().when('isGuestUser', {
    is: false,
    then: joi.required(),
    otherwise: joi.optional()
  })
    .messages({
      'string.email': 'Please provide a valid email address',
      'string.empty': 'Email is required'
    }),
  ageGroup: joi.string().valid('Under 18', '18-35', '36-55', '55+').when('isGuestUser', {
    is: false,
    then: joi.required(),
    otherwise: joi.optional()
  })
    .messages({
      'any.only': 'Age group must be one of: Under 18, 18-35, 36-55, 55+',
      'any.required': 'Age group is required'
    }),
  gender: joi.string().valid('Male', 'Female').when('isGuestUser', {
    is: false,
    then: joi.required(),
    otherwise: joi.optional()
  })
    .messages({
      'any.only': 'Gender must be either Male or Female',
      'any.required': 'Gender is required'
    }),
  // locationPermission: joi.number().valid(1, 2, 3, 4).required()
  //   .messages({
  //     'any.only': 'Location permission must be one of: 1, 2, 3, 4',
  //     'any.required': 'Location permission is required'
  //   }),
  latitude: joi.number().optional().allow(null),
  longitude: joi.number().optional().allow(null),
  preferredLanguage: joi.string().valid('English', 'Hindi').required()
    .messages({
      'any.only': 'Preferred language must be either English or Hindi'
    }),
  // address: joi.when('isGuestUser', {
  //   is: false,
  //   then: addressSchema.required(),
  //   otherwise: addressSchema.optional()
  // }),
  address: addressSchema.optional(),

  fcmToken: joi.string().optional().allow(null, ''),
});

const searchEntitiesSchema = joi.object({
  search: joi.string().trim(true).optional().allow(null, ''),
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).default(10),
  category: joi.string().optional().allow(null, '')
});

const whatsappNumberSchema = joi.object({
  countryCode: joi.string()
    .trim()
    .pattern(/^\+\d{1,3}$/)
    .required()
    .messages({
      'string.empty': 'Country code is required',
      'string.pattern.base': 'Country code must start with + followed by 1 to 3 digits',
    }),
    
  phoneNumber: joi.string()
    .required()
    .custom((value, helpers) => {
      const { countryCode } = helpers.state.ancestors[0];
      const fullNumber = `${countryCode}${value}`;
      const parsed = parsePhoneNumberFromString(fullNumber);

      if (!parsed || !parsed.isValid()) {
        return helpers.message('Invalid phone number for the selected country');
      }

      return value;
    })
    .messages({
      'string.empty': 'Phone number is required',
    }),

  booking: joi.string()
    .required()
    .custom(validateObjectId)
    .messages({
      'any.required': 'Booking ID is required',
      'any.invalid': 'Invalid booking ID',
    }),
});

const updateUserSchema = joi.object({
  isGuestUser: joi.boolean().default(false),
  firstName: joi.string().min(3).max(50).trim(true).when('isGuestUser', {
    is: true,
    then: joi.required(),
    otherwise: joi.optional()
  })
    .messages({
      'string.min': 'First name must be at least 3 characters',
      'string.max': 'First name cannot exceed 50 characters',
      'string.empty': 'First name should not be empty'
    }),
  lastName: joi.string().min(3).max(50).trim(true).when('isGuestUser', {
    is: true,
    then: joi.required(),
    otherwise: joi.optional()
  })
    .messages({
      'string.min': 'Last name must be at least 3 characters',
      'string.max': 'Last name cannot exceed 50 characters',
      'any.required': 'Last name should not be empty'
    }),
  email: joi.string().email().lowercase().when('isGuestUser', {
    is: true,
    then: joi.required(),
    otherwise: joi.optional()
  })
    .messages({
      'string.email': 'Please provide a valid email address',
      'string.empty': 'Email should not be empty'
    }),
  ageGroup: joi.string().valid('Under 18', '18-35', '36-55', '55+').when('isGuestUser', {
    is: true,
    then: joi.required(),
    otherwise: joi.optional()
  })
    .messages({
      'any.only': 'Age group must be one of: Under 18, 18-35, 36-55, 55+'
    }),
  gender: joi.string().valid('Male', 'Female').optional()
    .messages({
      'any.only': 'Gender must be either Male or Female'
    }),
  latitude: joi.number().optional().allow(null),
  longitude: joi.number().optional().allow(null),
  preferredLanguage: joi.string().valid('English', 'Hindi').optional()
    .messages({
      'any.only': 'Preferred language must be either English or Hindi'
    }),
  
  address: addressSchema.optional(),

  whatsappNumber: whatsappNumberSchema.optional(),
}).min(1);

const languageSchema = joi.object({
  preferredLanguage: joi.string().valid('English', 'Hindi').required()
    .messages({
      'any.only': 'Preferred language must be either English or Hindi'
    })
});

const getNotificationsSchema = joi.object({
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(10)
});

const notificationIdSchema = joi.object({
  id: joi.string().custom(validateObjectId).optional()
    .messages({
      'string.empty': 'Recording ID is required',
      'any.required': 'Recording ID is required'
    })
});

const verifyOtpSchema = joi.object({
  otp: joi.string().length(6).pattern(/^[0-9]+$/).required().messages({
    'string.pattern.base': 'OTP must contain only numbers',
    'string.length': 'OTP must be exactly 6 digits',
    'any.required': 'OTP is required'
  })
});

const receiveContactInquirySchema = joi.object({
  name: joi.string().trim().required().messages({
    'string.empty': 'Name is required'
  }),

  email: joi.string().email().trim().required().messages({
    'string.empty': 'Email is required',
    'string.email': 'Please provide a valid email address'
  }),

  phone: joi.string().allow('', null).optional(),

  message: joi.string().trim().required().messages({
    'string.empty': 'Message is required'
  })
});

module.exports = {
  userRegistrationSchema,
  searchEntitiesSchema,
  updateUserSchema,
  getNotificationsSchema,
  notificationIdSchema,
  verifyOtpSchema,
  languageSchema,
  receiveContactInquirySchema
};
