const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { SUCCESS } = require('../../../constants/common').commonConstants;
const authService = require('./service');
const { loginSchema, changePasswordSchema } = require('./validation');
const { messages } = require('../../../messages');

/**
 * Login a vendor
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const login = async (req, res) => {
  try {
    const { error, value } = loginSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const data = await authService.login(value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Login successful',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Change vendor password
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const changePassword = async (req, res) => {
  try {
    const { error, value } = changePasswordSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    await authService.changePassword(req.user.id, value);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Password changed successfully',
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Logout a vendor
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const logout = async (req, res) => {
  try {
    await authService.logout(req.user.id, req.headers.authorization.replace('Bearer ', ''));

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.LOGOUT_SUCCESS,
      status: true
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  login,
  changePassword,
  logout
};
