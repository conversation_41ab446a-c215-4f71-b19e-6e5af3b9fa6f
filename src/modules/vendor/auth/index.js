const express = require('express');
const router = express.Router();
const authController = require('./controller');
const auth = require('../../../middleware/auth');
const { isVendor } = require('../../../middleware/roleCheck');

router.post('/login', authController.login);
router.post('/change-password', auth, isVendor, authController.changePassword);
router.delete('/logout', auth, isVendor, authController.logout);

module.exports = router;
