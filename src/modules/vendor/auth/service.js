const bcrypt = require('bcryptjs');
const Vendor = require('../../../models/Vendor');
const Token = require('../../../models/Token');
const { throwBadRequestError, throwNotFoundError } = require('../../../errors');
const { messages } = require('../../../messages');
const { userTypeValue } = require('../../../constants/dbEnums');
const { generateToken } = require('../../../utils/jwt');
const { createToken } = require('../../../database/queries/accessToken.query');

/**
 * Login a vendor
 * @param {Object} loginData - Login credentials
 * @returns {Object} Vendor data and token
 */
const login = async (loginData) => {
  // Find vendor by email
  const vendor = await Vendor.findOne({ email: loginData.email.toLowerCase() });
  
  if (!vendor) {
    throwBadRequestError(messages.INVALID_CREDENTIALS);
  }

  // Check if vendor is active
  if (vendor.status !== 'ACTIVE') {
    throwBadRequestError(messages.USER_INACTIVE);
  }

  // Verify password
  const isPasswordValid = await bcrypt.compare(loginData.password, vendor.password);
  
  if (!isPasswordValid) {
    throwBadRequestError(messages.INVALID_CREDENTIALS);
  }

  // Generate token
  const token = await generateToken({ 
    _id: vendor._id,
    email: vendor.email,
    businessName: vendor.businessName,
    userType: userTypeValue.VENDOR
  });

  // Save token
  const expireTime = process.env.JWT_EXPIRY;
  const days = expireTime.toLowerCase().replace('d', '');

  await createToken({
    token: token,
    userType: userTypeValue.VENDOR,
    userId: vendor._id,
    expiresAt: new Date().getTime() + (parseInt(days) * 24 * 60 * 60 * 1000),
  });

  // Return vendor data and token
  return {
    vendor: {
      _id: vendor._id,
      businessName: vendor.businessName?.en || vendor.businessName || '',
      name: vendor.name?.en || vendor.name || '',
      email: vendor.email,
      phoneNumber: vendor.phoneNumber,
      contactPersonName: vendor.contactPersonName,
      status: vendor.status,
      passwordChangeRequired: vendor.passwordChangeRequired
    },
    token
  };
};

/**
 * Change vendor password
 * @param {string} vendorId - Vendor ID
 * @param {Object} passwordData - Password data
 */
const changePassword = async (vendorId, passwordData) => {
  const vendor = await Vendor.findById(vendorId);
  
  if (!vendor) {
    throwNotFoundError(messages.VENDOR_NOT_FOUND);
  }

  // Verify current password
  const isPasswordValid = await bcrypt.compare(passwordData.currentPassword, vendor.password);
  
  if (!isPasswordValid) {
    throwBadRequestError('Current password is incorrect');
  }

  // Hash new password
  const hashedPassword = await bcrypt.hash(passwordData.newPassword, 10);

  // Update vendor password
  await Vendor.findByIdAndUpdate(vendorId, {
    password: hashedPassword,
    passwordChangeRequired: false
  });
};

/**
 * Logout a vendor
 * @param {string} vendorId - Vendor ID
 * @param {string} token - JWT token
 */
const logout = async (vendorId, token) => {
  return await Token.deleteOne({ 
    userId: vendorId,
    token,
    userType: userTypeValue.VENDOR
  });
};

module.exports = {
  login,
  changePassword,
  logout
};
