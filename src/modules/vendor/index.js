const express = require('express');
const router = express.Router();
const vendorController = require('./controller');
const auth = require('../../middleware/auth');
const { isAdminOrSuperAdmin, isSuperAdmin } = require('../../middleware/roleCheck');

// Auth Sub-Module
router.use('/auth', require('./auth'));

// Product Sub-Module
router.use('/products', require('./product'));

// Shipping Sub-Module
router.use('/shipping', require('./shipping'));

// Settlement Sub-Module
router.use('/settlements', require('./settlement'));

// Super Admin routes
router.post('/', auth, isSuperAdmin, vendorController.createVendor);
router.get('/', auth, isAdminOrSuperAdmin, vendorController.listVendors);
router.get('/:id', auth, isAdminOrSuperAdmin, vendorController.getVendorById);
router.put('/:id', auth, isSuperAdmin, vendorController.updateVendor);
router.delete('/:id', auth, isSuperAdmin, vendorController.deleteVendor);

// Document upload routes
router.post('/:documentName/upload-url', auth, isSuperAdmin, vendorController.getUploadUrl);
router.delete('/document/:key(*)', auth, vendorController.deleteDocument);

module.exports = router;
