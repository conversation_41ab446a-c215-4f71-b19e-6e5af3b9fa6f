const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const { getPresignedUrl } = require('../../../utils/s3Service');
const productService = require('./service');
const { createProductSchema, updateProductSchema, uploadUrlSchema, validateId, stockUpdateRequestSchema } = require('./validation');
const { SUCCESS } = commonConstants;

/**
 * Get all products for the vendor
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getVendorProducts = async (req, res) => {
  try {
    const vendorId = req.user.id;
    const { page = 1, limit = 10, search = '', status = '', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

    const data = await productService.getVendorProducts({
      vendorId,
      page: parseInt(page),
      limit: parseInt(limit),
      search,
      status,
      sortBy,
      sortOrder
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.PRODUCTS_RETRIEVED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Create a new product
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const createProduct = async (req, res) => {
  try {
    const { error } = createProductSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const vendorId = req.user.id;

    const data = await productService.createProduct({
      vendorId,
      ...req.body
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.PRODUCT_CREATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get product by ID
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getProductById = async (req, res) => {
  try {
    const { error } = validateId.validate({ id: req.params.id });

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const vendorId = req.user.id;
    const productId = req.params.id;

    const data = await productService.getProductById(productId, vendorId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.PRODUCT_RETRIEVED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Update product
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const updateProduct = async (req, res) => {
  try {
    const { error: idError } = validateId.validate({ id: req.params.id });

    if (idError) {
      return res.status(400).json({
        message: idError.details[0].message,
        status: false
      });
    }

    const { error } = updateProductSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const vendorId = req.user.id;
    const productId = req.params.id;

    const data = await productService.updateProduct({
      vendorId,
      productId,
      ...req.body
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.PRODUCT_UPDATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Delete product
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const deleteProduct = async (req, res) => {
  try {
    const { error } = validateId.validate({ id: req.params.id });

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const vendorId = req.user.id;
    const productId = req.params.id;

    const data = await productService.deleteProduct(productId, vendorId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.PRODUCT_DELETED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get upload URL for product images
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getUploadUrl = async (req, res) => {
  try {
    if (req.body.extension) {
      req.body.extension = req.body.extension.toLowerCase();
    }
    
    const { error } = uploadUrlSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const { extension } = req.body;

    const uploadData = await getPresignedUrl(extension, 'products');

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data: uploadData
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get variant types for a category
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getVariantTypesForCategory = async (req, res) => {
  try {
    const { categoryId } = req.params;

    const data = await productService.getVariantTypesForCategory(categoryId, req.user);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get variant types for a subcategory
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getVariantTypesForSubCategory = async (req, res) => {
  try {
    const { subCategoryId } = req.params;

    const data = await productService.getVariantTypesForSubCategory(subCategoryId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Request stock update for a product
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const requestStockUpdate = async (req, res) => {
  try {
    const { error } = stockUpdateRequestSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const vendorId = req.user.id;
    const { productId, variantId, requestedStock } = req.body;

    const data = await productService.requestStockUpdate({
      vendorId,
      productId,
      variantId: variantId || null,
      requestedStock
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Stock update submitted for approval',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get stock update requests for a vendor
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getStockUpdateRequests = async (req, res) => {
  try {
    const vendorId = req.user.id;
    const { page = 1, limit = 10, status = '', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

    const data = await productService.getStockUpdateRequests({
      vendorId,
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      sortBy,
      sortOrder
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: 'Stock update requests retrieved successfully',
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getVendorProducts,
  createProduct,
  getProductById,
  updateProduct,
  deleteProduct,
  getUploadUrl,
  getVariantTypesForCategory,
  getVariantTypesForSubCategory,
  requestStockUpdate,
  getStockUpdateRequests
};
