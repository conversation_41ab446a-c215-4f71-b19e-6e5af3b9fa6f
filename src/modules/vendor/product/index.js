const express = require('express');
const router = express.Router();
const productController = require('./controller');
const auth = require('../../../middleware/auth');
const { isVendor, isAdminOrSuperAdmin } = require('../../../middleware/roleCheck');

// Super Admin routes
router.get('/category/variant-types/:categoryId', auth, isAdminOrSuperAdmin, productController.getVariantTypesForCategory);

// All routes require vendor authentication
router.use(auth, isVendor);

// Product management routes
router.get('/', productController.getVendorProducts);
router.post('/', productController.createProduct);
router.get('/:id', productController.getProductById);
router.put('/:id', productController.updateProduct);
router.delete('/:id', productController.deleteProduct);

// Category and subcategory variant types routes
router.get('/category/:categoryId/variant-types', productController.getVariantTypesForCategory);
router.get('/subcategory/:subCategoryId/variant-types', productController.getVariantTypesForSubCategory);

// Image upload route
router.post('/upload-url', productController.getUploadUrl);

// Stock management routes
router.post('/stock-update', productController.requestStockUpdate);
router.get('/stock-update/requests', productController.getStockUpdateRequests);

module.exports = router;
