const mongoose = require('mongoose');
const Product = require('../../../models/Product');
const ProductVariant = require('../../../models/ProductVariant');
const StockUpdateRequest = require('../../../models/StockUpdateRequest');
const Category = require('../../../models/Category');
const SubCategory = require('../../../models/SubCategory');
const { productStatusValue, stockUpdateStatusValue } = require('../../../constants/dbEnums');
const { messages } = require('../../../messages');
const { withTransaction, sessionOptions } = require('../../../utils/transactionHelper');
const { generateSKU } = require('../../../utils/skuGenerator');
const { throwBadRequestError, throwNotFoundError, throwUnAuthorizedError } = require('../../../errors');
const AttributeOption = require('../../../models/AttributeOption');
const { deleteFile } = require('../../../utils/s3Service');
const { translateDataForStore } = require('../../../utils/translateInput');

/**
 * Process images to ensure they have media URL
 * @param {Array} images - Array of image paths
 * @returns {Array} Processed image paths
 */
const processImages = (images) => {
  if (!images || !images.length) {
    return [];
  }

  return images.map(image => {
    // If image already has media URL, return as is
    if (image.startsWith('http')) {
      return image;
    }

    // Otherwise, add media URL
    const mediaUrl = process.env.MEDIA_URL;

    return `${mediaUrl}/${image}`;
  });
};

/**
 * Get all products for a vendor
 * @param {Object} options - Query options
 * @returns {Object} Products and pagination info
 */
const getVendorProducts = async ({ vendorId, page, limit, search, status, sortBy, sortOrder }) => {
  const skip = (page - 1) * limit;
  const sort = {};

  sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

  // Build query
  const query = {
    createdBy: vendorId,
    createdByModel: 'Vendor',
    deletedAt: null
  };

  // Add status filter if provided
  if (status) {
    query.status = status;
  }

  // Add search filter if provided
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { sku: { $regex: search, $options: 'i' } }
    ];
  }

  // Get products
  const products = await Product.find(query)
    .populate('category', 'name')
    .populate('subCategory', 'name')
    .populate({
      path: 'variants',
      match: { isActive: true }
    })
    .sort(sort).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit);

  // Get total count
  const total = await Product.countDocuments(query);

  // Get pending stock update requests for these products
  const productIds = products.map(product => product._id);
  const stockUpdateRequests = await StockUpdateRequest.find({
    product: { $in: productIds },
    variant: null,
    status: stockUpdateStatusValue.PENDING
  });

  // Create a map of product IDs to stock update requests
  const stockUpdateMap = {};

  stockUpdateRequests.forEach(request => {
    stockUpdateMap[request.product.toString()] = request;
  });

  // Get pending stock update requests for variants
  const variantIds = products.flatMap(product =>
    product.variants ? product.variants.map(variant => variant._id) : []
  );

  const variantStockUpdateRequests = await StockUpdateRequest.find({
    variant: { $in: variantIds },
    status: stockUpdateStatusValue.PENDING
  });

  // Create a map of variant IDs to stock update requests
  const variantStockUpdateMap = {};

  variantStockUpdateRequests.forEach(request => {
    variantStockUpdateMap[request.variant.toString()] = request;
  });

  // Add stock status information to products
  const productsWithStockInfo = products.map(product => {
    const productObj = product.toObject();

    // Check if product has a pending stock update request
    if (stockUpdateMap[product._id.toString()]) {
      productObj.stockUpdateRequest = stockUpdateMap[product._id.toString()];
      productObj.stockUpdateStatus = 'PENDING';
    }

    // Check if product stock is below threshold
    productObj.isLowStock = product.stock <= product.lowStockThreshold;

    // Add stock status information to variants
    if (productObj.variants && productObj.variants.length > 0) {
      productObj.variants = productObj.variants.map(variant => {
        const variantObj = variant;

        // Check if variant has a pending stock update request
        if (variantStockUpdateMap[variant._id.toString()]) {
          variantObj.stockUpdateRequest = variantStockUpdateMap[variant._id.toString()];
          variantObj.stockUpdateStatus = 'PENDING';
        }

        // Check if variant stock is below threshold
        variantObj.isLowStock = variant.stock <= variant.lowStockThreshold;

        return variantObj;
      });
    }

    return productObj;
  });

  return {
    products: productsWithStockInfo,
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Create a new product
 * @param {Object} productData - Product data
 * @returns {Object} Created product
 */
const createProduct = async ({ vendorId, name, description, price, discountPrice, images, category, subCategory, stock, sku, hasVariants, variantAttributes, variants }) => {
  // Check if category exists
  if (!mongoose.Types.ObjectId.isValid(category)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID);
  }

  const categoryExists = await Category.findById(category).populate('variantTypes', 'name values');

  if (!categoryExists) {
    throwBadRequestError(messages.CATEGORY_NOT_FOUND);
  }

  // Check if subcategory exists
  if (!mongoose.Types.ObjectId.isValid(subCategory)) {
    throwBadRequestError(messages.INVALID_SUBCATEGORY_ID || 'Invalid subcategory ID');
  }

  const subCategoryExists = await SubCategory.findById(subCategory);

  if (!subCategoryExists) {
    throwBadRequestError(messages.SUBCATEGORY_NOT_FOUND || 'Subcategory not found');
  }

  // Check if subcategory belongs to the selected category
  if (subCategoryExists.category.toString() !== category) {
    throwBadRequestError(messages.SUBCATEGORY_NOT_BELONG_TO_CATEGORY || 'Subcategory does not belong to the selected category');
  }

  // If product has variants, validate variant attributes against category's variant types
  if (hasVariants) {
    // Get allowed variant types for this category
    const allowedVariantTypes = categoryExists.variantTypes || [];

    if (allowedVariantTypes.length === 0) {
      throwBadRequestError('This category does not support variants');
    }

    // Check if all variant attributes are allowed for this category
    const allowedVariantTypeNames = allowedVariantTypes.map(vt => vt.name);

    for (const attr of variantAttributes) {
      if (!allowedVariantTypeNames.includes(attr)) {
        throwBadRequestError(`Variant type "${attr}" is not allowed for this category`);
      }
    }

    // Validate variant attribute values
    if (variants && variants.length > 0) {
      // Create a map of allowed values for each variant type
      const allowedValuesMap = {};

      allowedVariantTypes.forEach(vt => {
        allowedValuesMap[vt.name] = vt.values;
      });

      // Check each variant's attributes
      variants.forEach(variant => {
        for (const [ attrName, attrValue ] of Object.entries(variant.attributes)) {
          // Check if attribute name is allowed
          if (!allowedVariantTypeNames.includes(attrName)) {
            throwBadRequestError(`Variant type "${attrName}" is not allowed for this category`);
          }

          // Check if attribute value is allowed
          if (!allowedValuesMap[attrName].includes(attrValue)) {
            throwBadRequestError(`Value "${attrValue}" is not allowed for variant type "${attrName}"`);
          }
        }
      });
    }
  }

  // Process images
  const processedImages = processImages(images);

  // Generate SKU if not provided
  const productSku = sku || generateSKU('P', name);

  // Translate fields
  const translatedFields = ['name', 'description', 'rejectionReason'];
  const productData = { name, description };
  const translatedData = await translateDataForStore(translatedFields, productData);

  translatedFields.forEach(field => {
    if (productData[field]) {
      delete productData[field];
    }
  });

  const finalProductData = {
    ...productData,
    ...translatedData
  };

  // Use transaction helper
  return await withTransaction(async (session) => {
    // Create product
    const product = new Product({
      ...finalProductData,
      price,
      discountPrice,
      images: processedImages,
      category,
      subCategory,
      stock,
      sku: productSku,
      status: productStatusValue.PENDING, // Vendor-created products are pending by default
      createdBy: vendorId,
      createdByModel: 'Vendor',
      hasVariants: hasVariants || false,
      variantAttributes: hasVariants ? variantAttributes : []
    });

    await product.save(sessionOptions(session));

    // If product has variants, create them
    if (hasVariants && variants && variants.length > 0) {
      const variantPromises = variants.map(variant => {
        // Process variant images if provided
        const variantImages = variant.images && variant.images.length > 0 ?
          processImages(variant.images) : [];

        // Generate SKU for variant if not provided
        const variantSku = variant.sku || generateSKU('V', `${name}-${Object.values(variant.attributes).join('-')}`);

        const productVariant = new ProductVariant({
          product: product._id,
          attributes: new Map(Object.entries(variant.attributes)),
          price: variant.price,
          discountPrice: variant.discountPrice,
          stock: variant.stock,
          sku: variantSku,
          images: variantImages,
          isDefault: variant.isDefault || false
        });

        return productVariant.save(sessionOptions(session));
      });

      await Promise.all(variantPromises);
    }

    // Populate active variants for return
    await product.populate({
      path: 'variants',
      match: { isActive: true }
    });

    return product;
  });
};

/**
 * Get variant types for a category
 * @param {string} categoryId - Category ID
 * @returns {Array} Variant types with their values
 */
const getVariantTypesForCategory = async (categoryId) => {
  // Check if category ID is valid
  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    throwBadRequestError(messages.INVALID_CATEGORY_ID);
  }

  // Get category with variant types
  const category = await Category.findById(categoryId)
    .populate('variantTypes', 'name values');

  if (!category) {
    throwNotFoundError(messages.CATEGORY_NOT_FOUND);
  }

  return category.variantTypes || [];
};

/**
 * Get variant types for a subcategory
 * @param {string} subCategoryId - SubCategory ID
 * @returns {Array} Variant types with their values
 */
const getVariantTypesForSubCategory = async (subCategoryId) => {
  // Check if subcategory ID is valid
  if (!mongoose.Types.ObjectId.isValid(subCategoryId)) {
    throwBadRequestError(messages.INVALID_SUBCATEGORY_ID || 'Invalid subcategory ID');
  }

  // Get subcategory
  const subCategory = await SubCategory.findById(subCategoryId);

  if (!subCategory) {
    throwNotFoundError(messages.SUBCATEGORY_NOT_FOUND || 'Subcategory not found');
  }

  // Get variant types from subcategory
  if (subCategory.variantTypes && subCategory.variantTypes.length > 0) {
    // If subcategory has its own variant types, return those
    return await AttributeOption.find({
      _id: { $in: subCategory.variantTypes }
    }).select('name values');
  } else {
    // Otherwise, get variant types from parent category
    const category = await Category.findById(subCategory.category)
      .populate('variantTypes', 'name values');

    if (!category) {
      throwNotFoundError(messages.CATEGORY_NOT_FOUND);
    }

    return category.variantTypes || [];
  }
};

/**
 * Get product by ID
 * @param {string} productId - Product ID
 * @param {string} vendorId - Vendor ID
 * @returns {Object} Product
 */
const getProductById = async (productId, vendorId) => {
  // Check if product ID is valid
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  // Get product with variants
  const product = await Product.findById(productId)
    .populate({
      path: 'variants',
      match: { isActive: true }
    })
    .populate({
      path: 'category',
      populate: {
        path: 'variantTypes',
        select: 'name values'
      }
    })
    .populate('subCategory', 'name');

  if (!product) {
    throwNotFoundError(messages.PRODUCT_NOT_FOUND);
  }

  // Check if product belongs to vendor
  if (product.createdBy.toString() !== vendorId || product.createdByModel !== 'Vendor') {
    throwUnAuthorizedError(messages.UNAUTHORIZED_ACCESS);
  }

  return product;
};

/**
 * Update product
 * @param {Object} updateData - Update data
 * @returns {Object} Updated product
 */
const updateProduct = async ({ vendorId, productId, name, description, price, discountPrice, images, category, subCategory, stock, sku, hasVariants, variantAttributes, addVariants, updateVariants, removeVariants }) => {
  // Check if product ID is valid
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  // Get product
  const product = await Product.findById(productId);

  if (!product) {
    throwNotFoundError(messages.PRODUCT_NOT_FOUND);
  }

  // Check if product belongs to vendor
  if (product.createdBy.toString() !== vendorId || product.createdByModel !== 'Vendor') {
    throwUnAuthorizedError(messages.UNAUTHORIZED_ACCESS);
  }

  // Check if product can be updated (only pending or rejected products can be updated)
  if (product.status !== productStatusValue.PENDING && product.status !== productStatusValue.REJECTED) {
    throwBadRequestError('Only pending or rejected products can be updated');
  }

  // Check if category exists if provided
  let categoryData;

  if (category) {
    if (!mongoose.Types.ObjectId.isValid(category)) {
      throwBadRequestError(messages.INVALID_CATEGORY_ID);
    }

    categoryData = await Category.findById(category).populate('variantTypes', 'name values');

    if (!categoryData) {
      throwBadRequestError(messages.CATEGORY_NOT_FOUND);
    }
  } else {
    // If category is not being updated, get the current category
    categoryData = await Category.findById(product.category).populate('variantTypes', 'name values');
  }

  // Check if subcategory exists if provided
  let subCategoryData;

  if (subCategory) {
    if (!mongoose.Types.ObjectId.isValid(subCategory)) {
      throwBadRequestError(messages.INVALID_SUBCATEGORY_ID || 'Invalid subcategory ID');
    }

    subCategoryData = await SubCategory.findById(subCategory);

    if (!subCategoryData) {
      throwBadRequestError(messages.SUBCATEGORY_NOT_FOUND || 'Subcategory not found');
    }

    // Check if subcategory belongs to the selected category
    const categoryToCheck = category || product.category.toString();

    if (subCategoryData.category.toString() !== categoryToCheck) {
      throwBadRequestError(messages.SUBCATEGORY_NOT_BELONG_TO_CATEGORY || 'Subcategory does not belong to the selected category');
    }
  }

  // If product has variants or is being updated to have variants, validate variant attributes
  if ((hasVariants !== undefined && hasVariants) || (hasVariants === undefined && product.hasVariants)) {
    // Get allowed variant types for this category
    const allowedVariantTypes = categoryData.variantTypes || [];

    if (allowedVariantTypes.length === 0) {
      throwBadRequestError('This category does not support variants');
    }

    // Check if all variant attributes are allowed for this category
    const allowedVariantTypeNames = allowedVariantTypes.map(vt => vt.name);
    const attributesToCheck = variantAttributes || product.variantAttributes;

    for (const attr of attributesToCheck) {
      if (!allowedVariantTypeNames.includes(attr)) {
        throwBadRequestError(`Variant type "${attr}" is not allowed for this category`);
      }
    }

    // Create a map of allowed values for each variant type
    const allowedValuesMap = {};

    allowedVariantTypes.forEach(vt => {
      allowedValuesMap[vt.name] = vt.values;
    });

    // Validate new variants if provided
    if (addVariants && addVariants.length > 0) {
      addVariants.forEach(variant => {
        for (const [ attrName, attrValue ] of Object.entries(variant.attributes)) {
          // Check if attribute name is allowed
          if (!allowedVariantTypeNames.includes(attrName)) {
            throwBadRequestError(`Variant type "${attrName}" is not allowed for this category`);
          }

          // Check if attribute value is allowed
          if (!allowedValuesMap[attrName].includes(attrValue)) {
            throwBadRequestError(`Value "${attrValue}" is not allowed for variant type "${attrName}"`);
          }
        }
      });
    }
  }

  // Process images if provided
  const processedImages = images ? processImages(images) : undefined;

  // Translate fields if provided
  const translatedFields = ['name', 'description'];
  const updateData = { name, description };
  const hasTranslatableFields = translatedFields.some(field => updateData[field]);

  let translatedData = {};

  if (hasTranslatableFields) {
    translatedData = await translateDataForStore(translatedFields, updateData);
  }

  // Use transaction helper
  return await withTransaction(async (session) => {
    // Update product
    const updateFields = {
      updatedBy: vendorId,
      updatedByModel: 'Vendor',
      status: productStatusValue.PENDING // Reset to pending when updated
    };

    if (name) {
      updateFields.name = translatedData.name || name;
    }
    if (description) {
      updateFields.description = translatedData.description || description;
    }
    if (price) {
      updateFields.price = price;
    }
    if (discountPrice !== undefined) {
      updateFields.discountPrice = discountPrice;
    }
    if (processedImages) {
      updateFields.images = processedImages;
    }
    if (category) {
      updateFields.category = category;
    }
    if (subCategory) {
      updateFields.subCategory = subCategory;
    }
    if (stock !== undefined) {
      updateFields.stock = stock;
    }
    if (sku) {
      updateFields.sku = sku;
    }
    if (hasVariants !== undefined) {
      updateFields.hasVariants = hasVariants;
    }
    if (variantAttributes) {
      updateFields.variantAttributes = variantAttributes;
    }

    // Update product
    const updatedProduct = await Product.findByIdAndUpdate(
      productId,
      { $set: updateFields },
      { new: true, session }
    );

    // Handle variants if product has them
    if (updatedProduct.hasVariants) {
      // Add new variants
      if (addVariants && addVariants.length > 0) {
        const addVariantPromises = addVariants.map(variant => {
          // Process variant images if provided
          const variantImages = variant.images && variant.images.length > 0 ?
            processImages(variant.images) : [];

          // Generate SKU for variant if not provided
          const variantSku = variant.sku || generateSKU('V', `${updatedProduct.name}-${Object.values(variant.attributes).join('-')}`);

          const productVariant = new ProductVariant({
            product: updatedProduct._id,
            attributes: new Map(Object.entries(variant.attributes)),
            price: variant.price,
            discountPrice: variant.discountPrice,
            stock: variant.stock,
            sku: variantSku,
            images: variantImages,
            isDefault: variant.isDefault || false
          });

          return productVariant.save(sessionOptions(session));
        });

        await Promise.all(addVariantPromises);
      }

      // Update existing variants
      if (updateVariants && updateVariants.length > 0) {
        const updateVariantPromises = updateVariants.map(async variant => {
          // Process variant images if provided
          const variantImages = variant.images ? processImages(variant.images) : undefined;

          const updateFields = {};

          if (variant.price) {
            updateFields.price = variant.price;
          }
          if (variant.discountPrice !== undefined) {
            updateFields.discountPrice = variant.discountPrice;
          }
          if (variant.stock !== undefined) {
            updateFields.stock = variant.stock;
          }
          if (variant.sku) {
            updateFields.sku = variant.sku;
          }
          if (variantImages) {
            updateFields.images = variantImages;
          }
          if (variant.isDefault !== undefined) {
            updateFields.isDefault = variant.isDefault;
          }

          return ProductVariant.findByIdAndUpdate(
            variant.id,
            { $set: updateFields },
            { new: true, session }
          );
        });

        await Promise.all(updateVariantPromises);
      }

      // Deactivate variants instead of removing them
      if (removeVariants && removeVariants.length > 0) {
        await ProductVariant.updateMany(
          {
            _id: { $in: removeVariants },
            product: updatedProduct._id
          },
          { $set: { isActive: false } },
          sessionOptions(session)
        );
      }
    }

    // Populate active variants for return
    await updatedProduct.populate({
      path: 'variants',
      match: { isActive: true }
    });

    return updatedProduct;
  });
};

/**
 * Delete product
 * @param {string} productId - Product ID
 * @param {string} vendorId - Vendor ID
 * @returns {Object} Success status
 */
const deleteProduct = async (productId, vendorId) => {
  // Check if product ID is valid
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  // Get product with variants
  const product = await Product.findById(productId).populate('variants');

  if (!product) {
    throwNotFoundError(messages.PRODUCT_NOT_FOUND);
  }

  // Check if product belongs to vendor
  if (product.createdBy.toString() !== vendorId || product.createdByModel !== 'Vendor') {
    throwUnAuthorizedError(messages.UNAUTHORIZED_ACCESS);
  }

  // Check if product can be deleted (only pending or rejected products can be deleted)
  if (product.status !== productStatusValue.PENDING && product.status !== productStatusValue.REJECTED) {
    throwBadRequestError('Only pending or rejected products can be deleted');
  }

  // Use transaction helper
  return await withTransaction(async (session) => {

    // Delete variants and their images if product has them
    if (product.hasVariants && product.variants && product.variants.length > 0) {
      // Add variant images to deletion queue
      const deletePromises = [];

      product.variants.forEach(variant => {
        if (variant.images && variant.images.length > 0) {
          variant.images.forEach(image => {
            const mediaUrl = process.env.MEDIA_URL;
            
            deletePromises.push(
              deleteFile(image.replace(`${mediaUrl}/`, ''))
            );
          });
        }
      });

      await ProductVariant.deleteMany(
        { product: productId },
        sessionOptions(session)
      );
    }

    await Product.deleteOne(
      { _id: productId },
      sessionOptions(session)
    );

    return { success: true };
  });
};

/**
 * Request stock update for a product
 * @param {Object} requestData - Stock update request data
 * @returns {Object} Created stock update request
 */
const requestStockUpdate = async ({ vendorId, productId, variantId, requestedStock }) => {
  // Check if product ID is valid
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throwBadRequestError(messages.INVALID_PRODUCT_ID);
  }

  // Get product
  const product = await Product.findById(productId);

  if (!product) {
    throwNotFoundError(messages.PRODUCT_NOT_FOUND);
  }

  // Check if product belongs to vendor
  if (product.createdBy.toString() !== vendorId || product.createdByModel !== 'Vendor') {
    throwUnAuthorizedError(messages.UNAUTHORIZED_ACCESS);
  }

  // Check if product is active
  if (product.status !== productStatusValue.ACTIVE) {
    throwBadRequestError('Only active products can have stock updates');
  }

  // If variant ID is provided, check if it's valid
  let variant = null;

  if (variantId) {
    if (!mongoose.Types.ObjectId.isValid(variantId)) {
      throwBadRequestError('Invalid variant ID');
    }

    // Get variant
    variant = await ProductVariant.findById(variantId);

    if (!variant) {
      throwNotFoundError('Variant not found');
    }

    // Check if variant belongs to product
    if (variant.product.toString() !== productId) {
      throwBadRequestError('Variant does not belong to this product');
    }

    // Check if variant is active
    if (!variant.isActive) {
      throwBadRequestError('Cannot update stock for inactive variant');
    }

    // Check if there's already a pending stock update request for this variant
    const existingVariantRequest = await StockUpdateRequest.findOne({
      variant: variantId,
      status: stockUpdateStatusValue.PENDING
    });

    if (existingVariantRequest) {
      throwBadRequestError('There is already a pending stock update request for this variant');
    }
  } else {
    // Check if there's already a pending stock update request for this product
    const existingProductRequest = await StockUpdateRequest.findOne({
      product: productId,
      variant: null,
      status: stockUpdateStatusValue.PENDING
    });

    if (existingProductRequest) {
      throwBadRequestError('There is already a pending stock update request for this product');
    }
  }

  // Create stock update request
  const stockUpdateRequest = new StockUpdateRequest({
    product: productId,
    variant: variantId,
    currentStock: variantId ? variant.stock : product.stock,
    requestedStock,
    requestedBy: vendorId
  });

  await stockUpdateRequest.save();

  // Update product or variant stockUpdateStatus
  if (variantId) {
    await ProductVariant.findByIdAndUpdate(variantId, { stockUpdateStatus: 'PENDING' });
  } else {
    await Product.findByIdAndUpdate(productId, { stockUpdateStatus: 'PENDING' });
  }

  return stockUpdateRequest;
};

/**
 * Get stock update requests for a vendor
 * @param {Object} options - Query options
 * @returns {Object} Stock update requests and pagination info
 */
const getStockUpdateRequests = async ({ vendorId, page, limit, status, sortBy, sortOrder }) => {
  const skip = (page - 1) * limit;
  const sort = {};

  sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

  // Build query
  const query = {
    requestedBy: vendorId
  };

  // Add status filter if provided
  if (status) {
    query.status = status;
  }

  // Get stock update requests
  const stockUpdateRequests = await StockUpdateRequest.find(query)
    .populate('product variant')
    .sort(sort).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit);

  // Get total count
  const total = await StockUpdateRequest.countDocuments(query);

  return {
    stockUpdateRequests,
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

module.exports = {
  getVendorProducts,
  createProduct,
  getProductById,
  updateProduct,
  deleteProduct,
  getVariantTypesForCategory,
  getVariantTypesForSubCategory,
  requestStockUpdate,
  getStockUpdateRequests
};
