const Joi = require('joi');
const { extension } = require('../../../constants/dbEnums');

const addressSchema = Joi.object({
  addressLine1: Joi.string().allow('').optional(),
  addressLine2: Joi.string().allow('').optional(),
  city: Joi.string().allow('').optional(),
  state: Joi.string().allow('').optional(),
  postalCode: Joi.string().allow('').optional(),
  country: Joi.string().default('India'),
  phone: Joi.string().allow('').optional()
});

// Validation schema for creating a new product
const createProductSchema = Joi.object({
  name: Joi.string().trim().max(100).required()
    .messages({
      'string.empty': 'Product name is required',
      'string.max': 'Product name cannot exceed 100 characters'
    }),
  gstPercentage: Joi.number().min(0).optional(),
  isCommissionBasedProduct: Joi.boolean().default(false),
  isOneGodProduct: Joi.boolean().default(true),
  commissionType: Joi.string().valid('FIXED', 'PERCENTAGE'),
  pickupAddress: addressSchema.optional(),
  category: Joi.string().trim().required()
    .messages({
      'string.empty': 'Category is required'
    }),
  subCategory: Joi.string().trim().required()
    .messages({
      'string.empty': 'Sub-category is required'
    }),
  hasVariants: Joi.boolean().default(false),
  variantAttributes: Joi.when('hasVariants', {
    is: true,
    then: Joi.array().items(Joi.string().trim()).min(1).required()
      .messages({
        'array.min': 'At least one variant attribute is required when variants are enabled'
      }),
    otherwise: Joi.array().items(Joi.string().trim()).default([])
  }),
  variants: Joi.when('hasVariants', {
    is: true,
    then: Joi.array().items(
      Joi.object({
        attributes: Joi.object().required()
          .messages({
            'object.base': 'Variant attributes must be an object'
          }),
        description: Joi.string().optional(),
        price: Joi.number().min(1).required()
          .messages({
            'number.base': 'Variant price must be a number',
            'number.min': 'Variant price must be greater than 0'
          }),
        gstPercentage: Joi.number().min(0).optional(),
        taxablePrice: Joi.number().min(0).optional(),
        gstPrice: Joi.number().min(0).optional(),
        pickupAddress: addressSchema.optional(),
        shipRocketPickupLocationName: Joi.string().trim().optional(),
        gstIn: Joi.string().trim().optional(),
        unit: Joi.number().min(0).optional(),
        hsn: Joi.string().trim().optional(),
        commissionPrice: Joi.number().min(0).optional(),
        discountPrice: Joi.number().min(0).less(Joi.ref('price')).allow(null)
          .messages({
            'number.base': 'Variant discount price must be a number',
            'number.min': 'Variant discount price must be greater than or equal to 0',
            'number.less': 'Variant discount price must be less than regular price'
          }),
        stock: Joi.number().integer().min(1).required()
          .messages({
            'number.base': 'Variant stock quantity must be a number',
            'number.integer': 'Variant stock quantity must be an integer',
            'number.min': 'Variant stock quantity must be greater than 0'
          }),
        sku: Joi.string().trim().allow('', null),
        images: Joi.array().items(Joi.string().trim()).min(0).max(5)
          .messages({
            'array.max': 'Maximum 5 variant images/videos are allowed'
          }),
        isDefault: Joi.boolean().default(false),

        weight: Joi.number().min(0).required()
          .messages({
            'number.base': 'Variant weight must be a number',
            'number.min': 'Variant weight must be 0 or greater'
          }),

        dimensions: Joi.object({
          length: Joi.number().min(0).required()
            .messages({
              'number.base': 'Length must be a number',
              'number.min': 'Length must be 0 or greater',
              'any.required': 'Length is required for variant dimensions'
            }),
          width: Joi.number().min(0).required()
            .messages({
              'number.base': 'Width must be a number',
              'number.min': 'Width must be 0 or greater',
              'any.required': 'Width is required for variant dimensions'
            }),
          height: Joi.number().min(0).required()
            .messages({
              'number.base': 'Height must be a number',
              'number.min': 'Height must be 0 or greater',
              'any.required': 'Height is required for variant dimensions'
            }),
        }).required()
          .messages({
            'object.base': 'Variant dimensions must be an object',
            'any.required': 'Variant dimensions are required'
          }),

        faq: Joi.string().optional().allow(null),

        highlight: Joi.string().optional().allow(null),

        barcode: Joi.string()
          .trim()
          .required() 
          .messages({
            'string.base': 'Barcode must be a string',
            'string.empty': 'Barcode cannot be empty',
            'any.required': 'Barcode is required',
          }),
      }),
    ).min(1).required()
      .messages({
        'array.min': 'At least one variant is required when variants are enabled'
      }),
    otherwise: Joi.array().items(Joi.any()).default([])
  })
});

// Validation schema for updating a product
const updateProductSchema = Joi.object({
  name: Joi.string().trim().max(100)
    .messages({
      'string.max': 'Product name cannot exceed 100 characters'
    }),
  gstPercentage: Joi.number().min(0).optional(),
  isCommissionBasedProduct: Joi.boolean().default(false),
  isOneGodProduct: Joi.boolean().default(true),
  commissionType: Joi.string().valid('FIXED', 'PERCENTAGE'),
  pickupAddress: addressSchema.optional(),
  category: Joi.string().trim(),
  subCategory: Joi.string().trim(),
  hasVariants: Joi.boolean(),
  variantAttributes: Joi.array().items(Joi.string().trim()),
  // For adding new variants
  addVariants: Joi.array().items(
    Joi.object({
      attributes: Joi.object().required()
        .messages({
          'object.base': 'Variant attributes must be an object'
        }),
      description: Joi.string().optional(),
      price: Joi.number().min(1).required()
        .messages({
          'number.base': 'Variant price must be a number',
          'number.min': 'Variant price must be greater than 0'
        }),
      gstPercentage: Joi.number().min(0).optional(),
      taxablePrice: Joi.number().min(0).optional(),
      gstPrice: Joi.number().min(0).optional(),
      pickupAddress: addressSchema.optional(),
      shipRocketPickupLocationName: Joi.string().trim().optional(),
      gstIn: Joi.string().trim().optional(),
      unit: Joi.number().min(0).optional(),
      hsn: Joi.string().trim().optional(),
      commissionPrice: Joi.number().min(0).optional(),
      discountPrice: Joi.number().min(0).less(Joi.ref('price')).allow(null)
        .messages({
          'number.base': 'Variant discount price must be a number',
          'number.min': 'Variant discount price must be greater than or equal to 0',
          'number.less': 'Variant discount price must be less than regular price'
        }),
      stock: Joi.number().integer().min(1).required()
        .messages({
          'number.base': 'Variant stock quantity must be a number',
          'number.integer': 'Variant stock quantity must be an integer',
          'number.min': 'Variant stock quantity must be greater than 0'
        }),
      sku: Joi.string().trim().allow('', null),
      images: Joi.array().items(Joi.string().trim()).min(0).max(5)
        .messages({
          'array.max': 'Maximum 5 variant images/videos are allowed'
        }),
      isDefault: Joi.boolean().default(false),

      weight: Joi.number().min(0).required()
        .messages({
          'number.base': 'Variant weight must be a number',
          'number.min': 'Variant weight must be 0 or greater'
        }),

      dimensions: Joi.object({
        length: Joi.number().min(0).required()
          .messages({
            'number.base': 'Length must be a number',
            'number.min': 'Length must be 0 or greater',
            'any.required': 'Length is required for variant dimensions'
          }),
        width: Joi.number().min(0).required()
          .messages({
            'number.base': 'Width must be a number',
            'number.min': 'Width must be 0 or greater',
            'any.required': 'Width is required for variant dimensions'
          }),
        height: Joi.number().min(0).required()
          .messages({
            'number.base': 'Height must be a number',
            'number.min': 'Height must be 0 or greater',
            'any.required': 'Height is required for variant dimensions'
          }),
      }).required()
        .messages({
          'object.base': 'Variant dimensions must be an object',
          'any.required': 'Variant dimensions are required'
        }),

      faq: Joi.string().optional().allow(null),

      highlight: Joi.string().optional().allow(null),

      barcode: Joi.string()
        .trim()
        .required() 
        .messages({
          'string.base': 'Barcode must be a string',
          'string.empty': 'Barcode cannot be empty',
          'any.required': 'Barcode is required',
        }),
    })
  ),
  // For updating existing variants
  updateVariants: Joi.array().items(
    Joi.object({
      id: Joi.string().trim().required()
        .messages({
          'string.empty': 'Variant ID is required for updating'
        }),
      description: Joi.string().optional(),
      price: Joi.number().min(1)
        .messages({
          'number.base': 'Variant price must be a number',
          'number.min': 'Variant price must be greater than 0'
        }),
      gstPercentage: Joi.number().min(0).optional(),
      taxablePrice: Joi.number().min(0).optional(),
      gstPrice: Joi.number().min(0).optional(),
      pickupAddress: addressSchema.optional(),
      shipRocketPickupLocationName: Joi.string().trim().optional(),
      gstIn: Joi.string().trim().optional(),
      unit: Joi.number().min(0).optional(),
      hsn: Joi.string().trim().optional(),
      commissionPrice: Joi.number().min(0).optional(),
      discountPrice: Joi.number().min(0).less(Joi.ref('price')).allow(null)
        .messages({
          'number.base': 'Variant discount price must be a number',
          'number.min': 'Variant discount price must be greater than or equal to 0',
          'number.less': 'Variant discount price must be less than regular price'
        }),
      stock: Joi.number().integer().min(0)
        .messages({
          'number.base': 'Variant stock quantity must be a number',
          'number.integer': 'Variant stock quantity must be an integer',
          'number.min': 'Variant stock quantity must be greater than or equal to 0'
        }),
      sku: Joi.string().trim().allow('', null),
      images: Joi.array().items(Joi.string().trim()).min(0).max(5)
        .messages({
          'array.max': 'Maximum 5 variant images/videos are allowed'
        }),
      isDefault: Joi.boolean(),

      weight: Joi.number().min(0).optional()
        .messages({
          'number.base': 'Variant weight must be a number',
          'number.min': 'Variant weight must be 0 or greater'
        }),

      dimensions: Joi.object({
        length: Joi.number().min(0).optional()
          .messages({
            'number.base': 'Length must be a number',
            'number.min': 'Length must be 0 or greater',
          }),
        width: Joi.number().min(0).optional()
          .messages({
            'number.base': 'Width must be a number',
            'number.min': 'Width must be 0 or greater',
          }),
        height: Joi.number().min(0).optional()
          .messages({
            'number.base': 'Height must be a number',
            'number.min': 'Height must be 0 or greater',
          }),
      }).optional()
        .messages({
          'object.base': 'Variant dimensions must be an object'
        }),

      faq: Joi.string().optional().allow(null),
      
      highlight: Joi.string().optional().allow(null),

      barcode: Joi.string()
        .trim()
        .optional()
        .messages({
          'string.base': 'Barcode must be a string',
        }),
    })
  ),
  // For removing variants
  removeVariants: Joi.array().items(Joi.string().trim())
});

// Validation schema for upload URL
const uploadUrlSchema = Joi.object({
  extension: Joi.string()
    .valid(extension.JPG, extension.JPEG, extension.PNG, extension.PDF, extension.WEBP, extension.MP4, extension.MOV, extension.HEVC, extension.WEBM, extension.MPEG)
    .required()
    .messages({
      'any.required': 'File extension is required',
      'any.only': 'Invalid file extension. Only JPG, JPEG, PNG, PDF, MP4, MOV, HEVC, WEBM, WEBP and MPEG are allowed'
    })
});

// Validation schema for product ID
const validateId = Joi.object({
  id: Joi.string().trim().required()
    .messages({
      'string.empty': 'Product ID is required'
    })
});

// Validation schema for stock update request
const stockUpdateRequestSchema = Joi.object({
  productId: Joi.string().trim().required()
    .messages({
      'string.empty': 'Product ID is required',
      'any.required': 'Product ID is required'
    }),
  variantId: Joi.string().trim().allow(null, '')
    .messages({
      'string.empty': 'Variant ID must be a valid ID or empty'
    }),
  requestedStock: Joi.number().integer().min(0).required()
    .messages({
      'number.base': 'Stock quantity must be a number',
      'number.integer': 'Stock quantity must be an integer',
      'number.min': 'Stock quantity must be at least 0',
      'any.required': 'Stock quantity is required'
    })
});

module.exports = {
  createProductSchema,
  updateProductSchema,
  uploadUrlSchema,
  validateId,
  stockUpdateRequestSchema
};
