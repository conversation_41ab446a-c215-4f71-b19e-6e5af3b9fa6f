const bcrypt = require('bcryptjs');
const Vendor = require('../../models/Vendor');
const { throwBadRequestError, throwNotFoundError } = require('../../errors');
const { messages } = require('../../messages');
const { generatePassword } = require('../../utils/passwordGenerator');
const { deleteFile } = require('../../utils/s3Service');
const { sendMail } = require('../../utils/sendMail');
// const { sendWhatsAppMessage } = require('../../utils/whatsappService');
const { emailSubjects } = require('../../messages/emailSubjects');
const path = require('path');
const { translateDataForStore } = require('../../utils/translateInput');

/**
 * Helper function to ensure a path has the media URL prefix
 * @param {string} path - The file path
 * @returns {string} Path with media URL prefix
 */
const ensureMediaUrl = (path) => {
  if (!path) {
    return path;
  }

  const mediaUrl = process.env.MEDIA_URL;

  if (path.startsWith(mediaUrl)) {
    return path;
  }
  return `${mediaUrl}/${path}`;
};

/**
 * Helper function to remove media URL prefix for S3 operations
 * @param {string} path - The file path with media URL
 * @returns {string} Path without media URL prefix
 */
const removeMediaUrl = (path) => {
  if (!path) {
    return path;
  }

  const mediaUrl = process.env.MEDIA_URL;

  if (path.startsWith(mediaUrl)) {
    return path.replace(`${mediaUrl}/`, '');
  }
  return path;
};

/**
 * Create a new vendor
 * @param {Object} vendorData - Vendor data
 * @param {Object} adminUser - Admin user creating the vendor
 * @returns {Object} Created vendor
 */
const createVendor = async (vendorData, adminUser) => {
  // Check if vendor already exists with the same email or GST number
  const existingVendor = await Vendor.findOne({
    $or: [
      { email: vendorData.email.toLowerCase() },
      { gstNumber: vendorData.gstNumber.toUpperCase() }
    ]
  });

  if (existingVendor) {
    if (existingVendor.email === vendorData.email.toLowerCase()) {
      throwBadRequestError(messages.EMAIL_ALREADY_EXISTS);
    }
    throwBadRequestError('GST Number already exists');
  }

  // If PAN number is provided, check if it already exists
  if (vendorData.panNumber) {
    const vendorWithPan = await Vendor.findOne({ panNumber: vendorData.panNumber.toUpperCase() });

    if (vendorWithPan) {
      throwBadRequestError('PAN Number already exists');
    }
  }

  // Generate temporary password
  const tempPassword = generatePassword();
  const hashedPassword = await bcrypt.hash(tempPassword, 10);

  // Process document paths to ensure they have media URL
  const processedData = {
    ...vendorData,
    email: vendorData.email.toLowerCase(),
    gstNumber: vendorData.gstNumber.toUpperCase(),
    panNumber: vendorData.panNumber ? vendorData.panNumber.toUpperCase() : '',
    password: hashedPassword,
    passwordChangeRequired: true,
    createdBy: adminUser.id
  };

  // Ensure GST certificate has media URL
  if (processedData.gstCertificate) {
    processedData.gstCertificate = ensureMediaUrl(processedData.gstCertificate);
  }

  // Ensure PAN card has media URL
  if (processedData.panCard) {
    processedData.panCard = ensureMediaUrl(processedData.panCard);
  }

  // Ensure other documents have media URL
  if (processedData.otherDocuments && processedData.otherDocuments.length > 0) {
    processedData.otherDocuments = processedData.otherDocuments.map(doc => ({
      name: doc.name,
      image: ensureMediaUrl(doc.image)
    }));
  }

  const translatedData = await translateDataForStore([ 'name', 'businessName' ], processedData);

  processedData.name = translatedData.name;
  processedData.businessName = translatedData.businessName;

  // Create vendor with processed data
  const vendor = await Vendor.create(processedData);

  // Send welcome email with credentials
  const email = vendorData.email;
  const subject = emailSubjects.VENDOR_ACCOUNT_CREDENTIALS;
  const templatePath = path.join(__dirname, '../../views/welcomeVendor.html');
  const data = {
    name: vendorData.vendorName,
    email: vendorData.email,
    password: tempPassword,
    loginUrl: '',
    supportContact: process.env.SUPPORT_CONTACT,
    contactDetails: process.env.CONTACT_DETAILS
  };
  
  await sendMail(email, subject, templatePath, data);

  // Send WhatsApp message if enabled
  // sendVendorWelcomeWhatsApp(vendor, tempPassword);

  delete vendor.password;

  vendor.password = tempPassword;

  return vendor;
};

/**
 * Get vendor by ID
 * @param {string} id - Vendor ID
 * @returns {Object} Vendor
 */
const getVendorById = async (id) => {
  const vendor = await Vendor.findById(id);

  if (!vendor) {
    throwNotFoundError(messages.VENDOR_NOT_FOUND);
  }

  return vendor;
};

/**
 * Update vendor
 * @param {string} id - Vendor ID
 * @param {Object} updateData - Data to update
 * @param {Object} adminUser - Admin user updating the vendor
 * @returns {Object} Updated vendor
 */
const updateVendor = async (id, updateData, adminUser) => {
  const vendor = await Vendor.findById(id);

  if (!vendor) {
    throwNotFoundError(messages.VENDOR_NOT_FOUND);
  }

  // Check if email is being updated and if it already exists
  if (updateData.email && updateData.email.toLowerCase() !== vendor.email) {
    const existingVendor = await Vendor.findOne({ email: updateData.email.toLowerCase() });

    if (existingVendor) {
      throwBadRequestError(messages.EMAIL_ALREADY_EXISTS);
    }
  }

  // Check if GST number is being updated and if it already exists
  if (updateData.gstNumber && updateData.gstNumber.toUpperCase() !== vendor.gstNumber) {
    const existingVendor = await Vendor.findOne({ gstNumber: updateData.gstNumber.toUpperCase() });

    if (existingVendor) {
      throwBadRequestError('GST Number already exists');
    }
  }

  // Check if PAN number is being updated and if it already exists
  if (updateData.panNumber && updateData.panNumber.toUpperCase() !== vendor.panNumber) {
    const existingVendor = await Vendor.findOne({ panNumber: updateData.panNumber.toUpperCase() });

    if (existingVendor) {
      throwBadRequestError('PAN Number already exists');
    }
  }

  // Process document paths to ensure they have media URL
  const processedData = {
    ...updateData,
    email: updateData.email ? updateData.email.toLowerCase() : vendor.email,
    gstNumber: updateData.gstNumber ? updateData.gstNumber.toUpperCase() : vendor.gstNumber,
    panNumber: updateData.panNumber ? updateData.panNumber.toUpperCase() : vendor.panNumber,
    updatedBy: adminUser.id
  };

  // Ensure GST certificate has media URL
  if (processedData.gstCertificate) {
    processedData.gstCertificate = ensureMediaUrl(processedData.gstCertificate);
  }

  // Ensure PAN card has media URL
  if (processedData.panCard) {
    processedData.panCard = ensureMediaUrl(processedData.panCard);
  }

  // Ensure other documents have media URL
  if (processedData.otherDocuments && processedData.otherDocuments.length > 0) {
    processedData.otherDocuments = processedData.otherDocuments.map(doc => ({
      name: doc.name,
      image: ensureMediaUrl(doc.image)
    }));
  }

  if (processedData.name) {
    const translatedData = await translateDataForStore([ 'name' ], { name: processedData.name });

    processedData.name = translatedData.name;
  }

  if (processedData.businessName) {
    const translatedData = await translateDataForStore([ 'businessName' ], { businessName: processedData.businessName });

    processedData.businessName = translatedData.businessName;
  }

  // Update vendor
  const updatedVendor = await Vendor.findByIdAndUpdate(
    id,
    processedData,
    { new: true }
  );

  return updatedVendor;
};

/**
 * Delete vendor
 * @param {string} id - Vendor ID
 * @returns {Object} Deleted vendor
 */
const deleteVendor = async (id) => {
  const vendor = await Vendor.findById(id);

  if (!vendor) {
    throwNotFoundError(messages.VENDOR_NOT_FOUND);
  }

  // Delete vendor documents from S3 in parallel
  const deletePromises = [];

  // Collect all document deletion promises
  if (vendor.gstCertificate) {
    deletePromises.push(
      deleteFile(removeMediaUrl(vendor.gstCertificate))
    );
  }

  if (vendor.panCard) {
    deletePromises.push(
      deleteFile(removeMediaUrl(vendor.panCard))
    );
  }

  if (vendor.otherDocuments && vendor.otherDocuments.length > 0) {
    // Add deletion promises for each document in otherDocuments
    vendor.otherDocuments.forEach(doc => {
      deletePromises.push(
        deleteFile(removeMediaUrl(doc.image))
      );
    });
  }

  // Execute all deletion operations in parallel
  if (deletePromises.length > 0) {
    await Promise.all(deletePromises);
  }

  // Delete vendor
  await Vendor.findByIdAndDelete(id);

  return vendor;
};

/**
 * List vendors with pagination and filters
 * @param {Object} options - Pagination and filter options
 * @returns {Object} Vendors and pagination info
 */
const listVendors = async (options) => {
  const { page = 1, limit = 10, search = '', status = '', sortBy = 'createdAt', sortOrder = 'desc' } = options;

  // Build query
  const query = {};

  // Add status filter if provided
  if (status) {
    query.status = status;
  }

  // Add search filter if provided
  if (search) {
    query.$or = [
      { businessName: { $regex: search, $options: 'i' } },
      { name: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } },
      { gstNumber: { $regex: search, $options: 'i' } },
      { 'contactPersonName': { $regex: search, $options: 'i' } }
    ];
  }

  // Count total documents
  const total = await Vendor.countDocuments(query);

  // Build sort object
  const sort = {};

  sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

  // Get vendors
  const vendors = await Vendor.find(query)
    .sort(sort).collation({ locale: 'en', strength: 1 })
    .skip((page - 1) * limit)
    .limit(limit);

  return {
    vendors,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

/**
 * Send welcome email to vendor with credentials
 * @param {Object} vendor - Vendor object
 * @param {string} password - Plain text password
 */

/**
 * Send welcome WhatsApp message to vendor with credentials
 * @param {Object} vendor - Vendor object
 * @param {string} password - Plain text password
 */
// const sendVendorWelcomeWhatsApp = async (vendor, password) => {
//   const message = `
// Welcome to One God Platform!

// Your vendor account for ${vendor.businessName} has been created successfully.

// Login Credentials:
// Email: ${vendor.email}
// Password: ${password}

// Please login and change your password.

// Thank you,
// One God Team
//   `;

//   await sendWhatsAppMessage(vendor.phoneNumber, message);
// };

module.exports = {
  createVendor,
  getVendorById,
  updateVendor,
  deleteVendor,
  listVendors
};
