const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { SUCCESS } = require('../../../constants/common').commonConstants;
const { messages } = require('../../../messages');
const settlementService = require('./service');
const { getShipmentsSchema } = require('./validation');

/**
 * Get settled shipments for vendor
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
const getSettlementShipments = async (req, res) => {
  try {
    const { error, value } = getShipmentsSchema.validate(req.query);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const vendorId = req.user.id;
    const { page = 1, limit = 10, startDate, endDate, sortBy = 'settledAt', sortOrder = -1, status } = value;

    const data = await settlementService.getSettlementShipments({
      vendorId,
      page: parseInt(page),
      limit: parseInt(limit),
      startDate,
      endDate,
      sortBy,
      sortOrder,
      status
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getSettlementShipments,
};
