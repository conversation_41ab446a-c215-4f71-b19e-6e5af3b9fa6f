const express = require('express');
const router = express.Router();
const settlementController = require('./controller');
const auth = require('../../../middleware/auth');
const { isVendor } = require('../../../middleware/roleCheck');

// All routes require vendor authentication
router.use(auth, isVendor);

// Settlement routes
router.get('/settled', settlementController.getSettlementShipments);

module.exports = router;
