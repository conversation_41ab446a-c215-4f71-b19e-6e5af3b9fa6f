const ProductShipment = require('../../../models/ProductShipment');
const { shipmentStatusValue, settlementStatusValue } = require('../../../constants/dbEnums');
const mongoose = require('mongoose');

/**
 * Get settlement summary for a vendor
 * @param {String} vendorId - Vendor ID
 * @param {Object} dateRange - Date range for filtering
 * @returns {Object} Settlement summary
 */
const getSettlementSummary = async (vendorId, dateRange = {}) => {
  const { startDate, endDate } = dateRange;

  // Build the query
  const query = {
    vendor: new mongoose.Types.ObjectId(vendorId),
    vendorModel: 'Vendor',
    status: shipmentStatusValue.DELIVERED
  };

  // Add date range filter if provided
  if (startDate && endDate) {
    query.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(new Date(endDate).setUTCHours(23, 59, 59, 999))
    };
  }

  // Get total delivered orders
  const totalDeliveredOrders = await ProductShipment.countDocuments({
    ...query
  });

  // Get amount pending settlement
  const pendingSettlementAggregation = await ProductShipment.aggregate([
    {
      $match: {
        ...query,
        $or: [
          { settlementStatus: settlementStatusValue.PENDING },
          { settlementStatus: { $exists: false } }
        ]
      }
    },
    {
      $unwind: '$orderItems'
    },
    {
      $group: {
        _id: null,
        pendingAmount: { $sum: '$orderItems.subtotal' }
      }
    }
  ]);

  const amountPendingSettlement = pendingSettlementAggregation.length > 0 ? pendingSettlementAggregation[0].pendingAmount : 0;

  // Get amount settled
  const settledAggregation = await ProductShipment.aggregate([
    {
      $match: {
        ...query,
        settlementStatus: settlementStatusValue.SETTLED
      }
    },
    {
      $unwind: '$orderItems'
    },
    {
      $group: {
        _id: null,
        settledAmount: { $sum: '$orderItems.subtotal' }
      }
    }
  ]);

  const amountSettled = settledAggregation.length > 0 ? settledAggregation[0].settledAmount : 0;

  return {
    totalDeliveredOrders,
    amountPendingSettlement,
    amountSettled
  };
};

/**
 * Get settled shipments for a vendor
 * @param {Object} params - Query parameters
 * @returns {Object} Settled shipments with pagination
 */
const getSettlementShipments = async ({ vendorId, page, limit, startDate, endDate, sortBy, sortOrder, status }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  // Build the query
  const query = {
    vendor: new mongoose.Types.ObjectId(vendorId),
    vendorModel: 'Vendor',
    status: shipmentStatusValue.DELIVERED,
  };

  if (status) {
    if (status === 'PENDING') {
      query.$or = [
        { settlementStatus: settlementStatusValue.PENDING },
        { settlementStatus: { $exists: false } }
      ];
    } else {
      query.settlementStatus = status;
    }
  }

  // Add date range filter if provided
  if (startDate && endDate) {
    query.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(new Date(endDate).setUTCHours(23, 59, 59, 999))
    };
  }

  // Get settled shipments
  const shipments = await ProductShipment.find(query)
    .populate({
      path: 'order',
      select: 'orderNumber createdAt'
    })
    .populate({
      path: 'settledBy',
      select: 'name email'
    })
    .populate({
      path: 'invoice',
    })
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit);

  // Get total count
  const total = await ProductShipment.countDocuments(query);

  const settlementSummary = await getSettlementSummary(vendorId, { startDate, endDate });

  return {
    settlementSummary,
    shipments,
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

module.exports = {
  getSettlementSummary,
  getSettlementShipments,
};
