const Joi = require('joi');

// Validation schema for getting shipments list
const getShipmentsSchema = Joi.object({
  page: Joi.number().integer().min(1),
  limit: Joi.number().integer().min(1).max(100),
  startDate: Joi.date().iso(),
  endDate: Joi.date().iso().min(Joi.ref('startDate')),
  sortBy: Joi.string().valid('createdAt', 'deliveryDate', 'settledAt', 'status'),
  sortOrder: Joi.number().valid(1, -1),
  status: Joi.string().valid('PENDING', 'SETTLED')
});

module.exports = {
  getShipmentsSchema
};
