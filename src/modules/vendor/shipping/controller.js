const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const { messages } = require('../../../messages');
const shippingService = require('./service');
const { updateShipmentDimensionsSchema, generateAWBSchema, shipOrderSchema } = require('./validation');
const { SUCCESS } = commonConstants;

/**
 * Get vendor's orders that need shipping
 */
const getVendorOrders = async (req, res) => {
  try {
    const vendorId = req.user.id;
    const { page = 1, limit = 10, status, search, sortBy = 'createdAt', sortOrder = -1 } = req.query;

    const data = await shippingService.getVendorOrders({
      vendorId,
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      search,
      sortBy,
      sortOrder
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get specific order details with shipments
 */
const getOrderDetails = async (req, res) => {
  try {
    const vendorId = req.user.id;
    const { orderId } = req.params;

    const data = await shippingService.getOrderDetails(orderId, vendorId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Create ShipRocket order for a shipment
 */
const createShiprocketOrder = async (req, res) => {
  try {
    // Validate package details
    const { error } = updateShipmentDimensionsSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const vendorId = req.user.id;
    const { shipmentId } = req.params;
    const { weight, dimensions } = req.body;

    // Create package details object
    const packageDetails = {
      weight,
      dimensions
    };

    const data = await shippingService.createShiprocketOrder(shipmentId, vendorId, packageDetails);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SHIPROCKET_ORDER_CREATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get courier recommendations for a shipment
 */
const getCourierRecommendations = async (req, res) => {
  try {
    const vendorId = req.user.id;
    const { shipmentId } = req.params;

    const data = await shippingService.getCourierRecommendations(shipmentId, vendorId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Generate AWB for a shipment
 */
const generateAWB = async (req, res) => {
  try {
    const { error } = generateAWBSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const vendorId = req.user.id;
    const { shipmentId } = req.params;
    const { courierId } = req.body;

    const data = await shippingService.generateAWB(shipmentId, vendorId, courierId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.AWB_GENERATED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Request pickup for a shipment
 */
const requestPickup = async (req, res) => {
  try {
    const vendorId = req.user.id;
    const { shipmentId } = req.params;
    const { pickUpDate } = req.query;

    const data = await shippingService.requestPickup(shipmentId, vendorId, pickUpDate);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.PICKUP_REQUESTED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Ship order (combined generate AWB and request pickup)
 */
const shipOrder = async (req, res) => {
  try {
    const { error } = shipOrderSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        message: error.details[0].message,
        status: false
      });
    }

    const vendorId = req.user.id;
    const { shipmentId } = req.params;
    const { courierId } = req.body;

    const data = await shippingService.shipOrder(shipmentId, vendorId, courierId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.ORDER_SHIPPED,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Track shipment
 */
const trackShipment = async (req, res) => {
  try {
    const vendorId = req.user.id;
    const { shipmentId } = req.params;

    const data = await shippingService.trackShipment(shipmentId, vendorId);

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

/**
 * Get all shipments for vendor
 */
const getVendorShipments = async (req, res) => {
  try {
    const vendorId = req.user.id;
    const { page = 1, limit = 10, status, search, sortBy = 'createdAt', sortOrder = -1 } = req.query;

    const data = await shippingService.getVendorShipments({
      vendorId,
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      search,
      sortBy,
      sortOrder
    });

    return apiResponse({
      res,
      code: SUCCESS.CODE,
      message: messages.SUCCESS,
      status: true,
      data
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  getVendorOrders,
  getOrderDetails,
  createShiprocketOrder,
  getCourierRecommendations,
  generateAWB,
  requestPickup,
  shipOrder,
  trackShipment,
  getVendorShipments
};
