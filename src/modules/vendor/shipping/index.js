const express = require('express');
const router = express.Router();
const shippingController = require('./controller');
const auth = require('../../../middleware/auth');
const { isVendor } = require('../../../middleware/roleCheck');

// All routes require vendor authentication
router.use(auth, isVendor);

router.get('/orders', shippingController.getVendorOrders);
router.get('/orders/:orderId', shippingController.getOrderDetails);
router.post('/shipments/:shipmentId/create-order', shippingController.createShiprocketOrder);
router.get('/shipments/:shipmentId/couriers', shippingController.getCourierRecommendations);
router.post('/shipments/:shipmentId/generate-awb', shippingController.generateAWB);
router.post('/shipments/:shipmentId/request-pickup', shippingController.requestPickup);
router.post('/shipments/:shipmentId/ship', shippingController.shipOrder);
router.get('/shipments/:shipmentId/track', shippingController.trackShipment);
router.get('/shipments', shippingController.getVendorShipments);

module.exports = router;
