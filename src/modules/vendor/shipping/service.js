const Order = require('../../../models/Order');
const ProductShipment = require('../../../models/ProductShipment');
const Vendor = require('../../../models/Vendor');
const { throwBadRequestError, throwNotFoundError, throwUnauthorizedError, throwInternalServerError } = require('../../../errors');
const { messages } = require('../../../messages');
const { shipmentStatusValue, orderStatusValue } = require('../../../constants/dbEnums');
const mongoose = require('mongoose');
const { withTransaction, sessionOptions } = require('../../../utils/transactionHelper');
const { logShippingEvent } = require('../../../utils/logger');
const shiprocket = require('../../../services/shiprocket');
const adminShippingService = require('../../admin/shop/shipping/service');

/**
 * Get vendor's orders that need shipping
 * @param {Object} params - Query parameters
 */
const getVendorOrders = async ({ vendorId, page, limit, status, search, sortBy, sortOrder }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  // Build the query
  const query = {
    'shipments.vendor': new mongoose.Types.ObjectId(vendorId),
    'shipments.vendorModel': 'Vendor',
    paymentStatus: 'COMPLETED' // Only show paid orders
  };

  // Add status filter if provided
  if (status) {
    query.orderStatus = status;
  } else {
    // By default, only show orders that need shipping action
    query.orderStatus = { $in: [ orderStatusValue.PROCESSING, orderStatusValue.PARTIALLY_SHIPPED ] };
  }

  // Add search filter if provided
  if (search) {
    // We'll handle product name search separately in a later stage
    query.$or = [
      { orderNumber: { $regex: search, $options: 'i' } },
      // { 'user.name': { $regex: search, $options: 'i' } },
      { 'user.email': { $regex: search, $options: 'i' } },
      { 'shipments.orderItems.name': { $regex: search, $options: 'i' } },
      {
        $expr: {
          $regexMatch: {
            input: { $concat: [ '$user.firstName', ' ', '$user.lastName' ] },
            regex: search,
            options: 'i'
          }
        }
      }
    ];
  }

  // Build the pipeline
  const pipeline = [
    // Match orders with shipments for this vendor
    {
      $lookup: {
        from: 'productshipments',
        localField: '_id',
        foreignField: 'order',
        as: 'shipments'
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'userDetails'
      }
    },
    {
      $addFields: {
        user: { $arrayElemAt: [ '$userDetails', 0 ] }
      }
    },
    // Filter shipments to only include those for this vendor
    {
      $addFields: {
        shipments: {
          $filter: {
            input: '$shipments',
            as: 'shipment',
            cond: {
              $and: [
                { $eq: [ '$$shipment.vendor', new mongoose.Types.ObjectId(vendorId) ] },
                { $eq: [ '$$shipment.vendorModel', 'Vendor' ] }
              ]
            }
          }
        }
      }
    },
    {
      $match: query
    },
    // Lookup product details for each shipment's orderItems
    {
      $lookup: {
        from: 'productshipments',
        let: { orderId: '$_id', vendorId: new mongoose.Types.ObjectId(vendorId) },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: [ '$order', '$$orderId' ] },
                  { $eq: [ '$vendor', '$$vendorId' ] },
                  { $eq: [ '$vendorModel', 'Vendor' ] }
                ]
              }
            }
          },
          // Lookup product details for each orderItem
          {
            $lookup: {
              from: 'products',
              localField: 'orderItems.product',
              foreignField: '_id',
              as: 'productDetails'
            }
          },
          // Lookup variant details for each orderItem
          {
            $lookup: {
              from: 'productvariants',
              localField: 'orderItems.variant',
              foreignField: '_id',
              as: 'variantDetails'
            }
          }
        ],
        as: 'shipmentDetails'
      }
    }
  ];

  // Add sorting, pagination, and projection
  pipeline.push(
    { $sort: sortOptions },
    { $skip: skip },
    { $limit: limit },
    // Project only needed fields
    
  );

  // Execute the aggregation pipeline
  const orders = await Order.aggregate(pipeline).collation({ locale: 'en', strength: 1 });

  // Build the count pipeline
  const countPipeline = [
    {
      $lookup: {
        from: 'productshipments',
        localField: '_id',
        foreignField: 'order',
        as: 'shipments'
      }
    },
    {
      $match: query
    }
  ];

  // Add product name search to count pipeline if search is provided
  if (search) {
    countPipeline.push({
      $lookup: {
        from: 'products',
        let: { orderItems: '$items' },
        pipeline: [
          {
            $match: {
              $expr: {
                $in: [ '$_id', { $map: { input: '$$orderItems', as: 'item', in: '$$item.product' } }]
              },
              name: { $regex: search, $options: 'i' }
            }
          }
        ],
        as: 'matchingProducts'
      }
    });

    countPipeline.push({
      $match: {
        $or: [
          { $or: query.$or }, // Original search criteria
          { matchingProducts: { $ne: [] } } // Orders with matching products
        ]
      }
    });
  }

  // Add count stage
  countPipeline.push({ $count: 'total' });

  // Execute count pipeline
  const totalCount = await Order.aggregate(countPipeline).collation({ locale: 'en', strength: 1 });

  const total = totalCount.length > 0 ? totalCount[0].total : 0;

  const orderDetails = orders.map(order => {
    const shipment = order.shipments[0];

    shipment.orderItems = shipment.orderItems.map(item => {
      return {
        ...item,
        product: order.shipmentDetails[0].productDetails.find(product => product._id.toString() === item.product.toString()),
        variant: order.shipmentDetails[0].variantDetails.find(variant => variant._id.toString() === item.variant.toString())
      };
    });

    return {
      _id: order._id,
      orderNumber: order.orderNumber,
      orderStatus: order.orderStatus,
      createdAt: order.createdAt,
      user: order.user,
      shipments: shipment,
      subtotal: order.subtotal,
      shippingCost: order.shippingCost,
      total: order.total,
      tax: order.tax,
      discount: order.discount,
      shippingAddress: order.shippingAddress,
      billingAddress: order.billingAddress,
      paymentMethod: order.paymentMethod,
      paymentStatus: order.paymentStatus,
      notes: order.notes,
      trackingNumber: order.trackingNumber,
      shippingProvider: order.shippingProvider,
      estimatedDelivery: order.estimatedDelivery,
      cancelReason: order.cancelReason,
      cancelledAt: order.cancelledAt,
      deliveredAt: order.deliveredAt
    };
  });

  return {
    orders: orderDetails,
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

/**
 * Get specific order details with shipments
 * @param {String} orderId - Order ID
 * @param {String} vendorId - Vendor ID
 */
const getOrderDetails = async (orderId, vendorId) => {
  // Check if order ID is valid
  if (!mongoose.Types.ObjectId.isValid(orderId)) {
    throwBadRequestError(messages.INVALID_ORDER_ID);
  }

  // Get order with shipments
  const order = await Order.findById(orderId)
    .populate('user', 'name email phoneNumber')
    .populate({
      path: 'items.product'
    })
    .populate({
      path: 'items.variant'
    }).lean();

  if (!order) {
    throwNotFoundError(messages.ORDER_NOT_FOUND);
  }

  // Get shipments for this vendor
  const shipments = await ProductShipment.findOne({
    order: orderId,
    vendor: vendorId,
    vendorModel: 'Vendor'
  }).populate({
    path: 'orderItems.product'
  }).populate({
    path: 'orderItems.variant'
  });

  if (!shipments) {
    throwUnauthorizedError(messages.UNAUTHORIZED_ACCESS);
  }

  order.shipments = shipments;

  return {
    order
  };
};

/**
 * Create ShipRocket order for a shipment
 * @param {String} shipmentId - Shipment ID
 * @param {String} vendorId - Vendor ID
 * @param {Object} packageDetails - Package weight and dimensions
 */
const createShiprocketOrder = async (shipmentId, vendorId, packageDetails) => {
  // Check if shipment ID is valid
  if (!mongoose.Types.ObjectId.isValid(shipmentId)) {
    throwBadRequestError('Invalid shipment ID');
  }

  // Get shipment
  const shipment = await ProductShipment.findById(shipmentId);

  if (!shipment) {
    throwNotFoundError('Shipment not found');
  }

  // Check if shipment belongs to this vendor
  if (shipment.vendor.toString() !== vendorId || shipment.vendorModel !== 'Vendor') {
    throwUnauthorizedError(messages.UNAUTHORIZED_ACCESS);
  }

  // Check if shipment already has a ShipRocket order ID
  if (shipment.shiprocketOrderId) {
    throwBadRequestError('Shipment already has a ShipRocket order');
  }

  // Validate package details
  if (!packageDetails || !packageDetails.weight || !packageDetails.dimensions) {
    throwBadRequestError('Package weight and dimensions are required');
  }

  const { weight, dimensions } = packageDetails;

  // Validate weight and dimensions
  if (!weight || weight <= 0) {
    throwBadRequestError('Package weight must be greater than 0');
  }

  if (!dimensions || !dimensions.length || !dimensions.width || !dimensions.height) {
    throwBadRequestError('Package dimensions (length, width, height) are required');
  }

  // Get order
  const order = await Order.findById(shipment.order)
    .populate('user')
    .populate({
      path: 'items.product',
      select: 'name images sku'
    })
    .populate({
      path: 'items.variant',
      select: 'attributes images sku'
    });

  if (!order) {
    throwNotFoundError(messages.ORDER_NOT_FOUND);
  }

  // Get vendor details
  const vendor = await Vendor.findById(vendorId);

  if (!vendor) {
    throwNotFoundError(messages.VENDOR_NOT_FOUND);
  }

  // Format order data for ShipRocket
  const shiprocketOrderData = await shiprocket.formatOrderData(order, shipment, order.user, vendor);

  if (!shiprocketOrderData || !shiprocketOrderData.order_id) {
    throwInternalServerError('Failed to format order data for ShipRocket');
  }

  // Add package details to the ShipRocket order data
  shiprocketOrderData.weight = weight;
  shiprocketOrderData.length = dimensions.length;
  shiprocketOrderData.breadth = dimensions.width;
  shiprocketOrderData.height = dimensions.height;

  // Create ShipRocket order
  const shiprocketResponse = await shiprocket.createOrder(shiprocketOrderData);

  // Update shipment with ShipRocket details and package information
  await withTransaction(async (session) => {
    shipment.shiprocketOrderId = shiprocketResponse.order_id.toString();
    shipment.shiprocketShipmentId = shiprocketResponse.shipment_id.toString();
    shipment.status = shipmentStatusValue.PROCESSING;

    // Store package details in the shipment
    shipment.weight = weight;
    shipment.dimensions = dimensions;

    // Store API response
    shipment.apiResponses = shipment.apiResponses || [];
    shipment.apiResponses.push({
      endpoint: 'create_order',
      response: shiprocketResponse,
      timestamp: new Date()
    });

    await shipment.save(sessionOptions(session));
  });

  logShippingEvent('VENDOR_SHIPROCKET_ORDER_CREATED', {
    vendorId,
    orderId: order._id,
    orderNumber: order.orderNumber,
    shipmentId: shipment._id,
    shiprocketOrderId: shiprocketResponse.order_id,
    shiprocketShipmentId: shiprocketResponse.shipment_id,
    packageDetails: { weight, dimensions }
  });

  return {
    shipmentId: shipment._id,
    shiprocketOrderId: shiprocketResponse.order_id,
    shiprocketShipmentId: shiprocketResponse.shipment_id,
    status: shipment.status,
    packageDetails: { weight, dimensions }
  };
};

/**
 * Get courier recommendations for a shipment
 * @param {String} shipmentId - Shipment ID
 * @param {String} vendorId - Vendor ID
 */
const getCourierRecommendations = async (shipmentId, vendorId) => {
  // Check if shipment ID is valid
  if (!mongoose.Types.ObjectId.isValid(shipmentId)) {
    throwBadRequestError('Invalid shipment ID');
  }

  // Get shipment
  const shipment = await ProductShipment.findById(shipmentId);

  if (!shipment) {
    throwNotFoundError('Shipment not found');
  }

  // Check if shipment belongs to this vendor
  if (shipment.vendor.toString() !== vendorId || shipment.vendorModel !== 'Vendor') {
    throwUnauthorizedError(messages.UNAUTHORIZED_ACCESS);
  }

  // Check if shipment has a ShipRocket order ID
  if (!shipment.shiprocketOrderId) {
    throwBadRequestError('Shipment does not have a ShipRocket order. Create one first.');
  }

  // Check if weight and dimensions are set
  if (!shipment.weight || !shipment.dimensions || !shipment.dimensions.length || !shipment.dimensions.width || !shipment.dimensions.height) {
    throwBadRequestError('Shipment weight and dimensions must be set before getting courier recommendations');
  }

  // Get courier recommendations from ShipRocket
  const recommendations = await adminShippingService.getCourierRecommendations(shipmentId);

  return recommendations;
};

/**
 * Generate AWB for a shipment
 * @param {String} shipmentId - Shipment ID
 * @param {String} vendorId - Vendor ID
 * @param {String} courierId - Courier ID
 */
const generateAWB = async (shipmentId, vendorId, courierId) => {
  // Check if shipment ID is valid
  if (!mongoose.Types.ObjectId.isValid(shipmentId)) {
    throwBadRequestError('Invalid shipment ID');
  }

  // Get shipment
  const shipment = await ProductShipment.findById(shipmentId);

  if (!shipment) {
    throwNotFoundError('Shipment not found');
  }

  // Check if shipment belongs to this vendor
  if (shipment.vendor.toString() !== vendorId || shipment.vendorModel !== 'Vendor') {
    throwUnauthorizedError(messages.UNAUTHORIZED_ACCESS);
  }

  // Check if shipment has a ShipRocket order ID
  if (!shipment.shiprocketOrderId) {
    throwBadRequestError('Shipment does not have a ShipRocket order. Create one first.');
  }

  // Check if AWB is already generated
  if (shipment.trackingId) {
    throwBadRequestError('AWB already generated for this shipment');
  }

  // Generate AWB
  const awbResult = await adminShippingService.generateAWB(shipmentId, courierId);

  return awbResult;
};

/**
 * Request pickup for a shipment
 * @param {String} shipmentId - Shipment ID
 * @param {String} vendorId - Vendor ID
 */
const requestPickup = async (shipmentId, vendorId) => {
  // Check if shipment ID is valid
  if (!mongoose.Types.ObjectId.isValid(shipmentId)) {
    throwBadRequestError('Invalid shipment ID');
  }

  // Get shipment
  const shipment = await ProductShipment.findById(shipmentId);

  if (!shipment) {
    throwNotFoundError('Shipment not found');
  }

  // Check if shipment belongs to this vendor
  if (shipment.vendor.toString() !== vendorId || shipment.vendorModel !== 'Vendor') {
    throwUnauthorizedError(messages.UNAUTHORIZED_ACCESS);
  }

  // Check if shipment has a ShipRocket order ID
  if (!shipment.shiprocketOrderId) {
    throwBadRequestError('Shipment does not have a ShipRocket order. Create one first.');
  }

  // Check if AWB is generated
  if (!shipment.trackingId) {
    throwBadRequestError('AWB not generated for this shipment. Generate AWB first.');
  }

  // Request pickup
  const pickupResult = await adminShippingService.requestPickup(shipmentId);

  return pickupResult;
};

/**
 * Ship order (combined generate AWB and request pickup)
 * @param {String} shipmentId - Shipment ID
 * @param {String} vendorId - Vendor ID
 * @param {String} courierId - Courier ID
 */
const shipOrder = async (shipmentId, vendorId, courierId) => {
  // Check if shipment ID is valid
  if (!mongoose.Types.ObjectId.isValid(shipmentId)) {
    throwBadRequestError('Invalid shipment ID');
  }

  // Get shipment
  const shipment = await ProductShipment.findById(shipmentId);

  if (!shipment) {
    throwNotFoundError('Shipment not found');
  }

  // Check if shipment belongs to this vendor
  if (shipment.vendor.toString() !== vendorId || shipment.vendorModel !== 'Vendor') {
    throwUnauthorizedError(messages.UNAUTHORIZED_ACCESS);
  }

  // Check if shipment has a ShipRocket order ID
  if (!shipment.shiprocketOrderId) {
    throwBadRequestError('Shipment does not have a ShipRocket order. Create one first.');
  }

  // Ship order
  const shipResult = await adminShippingService.shipOrder(shipmentId, courierId);

  return shipResult;
};

/**
 * Track shipment
 * @param {String} shipmentId - Shipment ID
 * @param {String} vendorId - Vendor ID
 */
const trackShipment = async (shipmentId, vendorId) => {
  // Check if shipment ID is valid
  if (!mongoose.Types.ObjectId.isValid(shipmentId)) {
    throwBadRequestError('Invalid shipment ID');
  }

  // Get shipment
  const shipment = await ProductShipment.findById(shipmentId);

  if (!shipment) {
    throwNotFoundError('Shipment not found');
  }

  // Check if shipment belongs to this vendor
  if (shipment.vendor.toString() !== vendorId || shipment.vendorModel !== 'Vendor') {
    throwUnauthorizedError(messages.UNAUTHORIZED_ACCESS);
  }

  // Check if shipment has a ShipRocket order ID
  if (!shipment.shiprocketOrderId) {
    throwBadRequestError('Shipment does not have a ShipRocket order');
  }

  // Track shipment
  const trackingData = await shiprocket.trackShipment(shipment.shiprocketOrderId);

  // Update shipment with tracking information
  await withTransaction(async (session) => {
    // Update tracking URL if available
    if (trackingData.tracking_data && trackingData.tracking_data.track_url) {
      shipment.trackingUrl = trackingData.tracking_data.track_url;
    }

    // Update estimated delivery date if available
    if (trackingData.tracking_data && trackingData.tracking_data.shipment_track && trackingData.tracking_data.shipment_track.etd) {
      shipment.estimatedDeliveryDate = new Date(trackingData.tracking_data.shipment_track.etd);
    }

    // Update courier information if available
    if (trackingData.tracking_data && trackingData.tracking_data.shipment_track) {
      const track = trackingData.tracking_data.shipment_track;

      if (track.courier_name) {
        shipment.courier = {
          courierName: track.courier_name,
          courierId: track.courier_id || shipment.courier?.courierId
        };
      }
    }

    // Update tracking history
    if (trackingData.trackingHistory && trackingData.trackingHistory.length > 0) {
      // Clear existing tracking history to avoid duplicates
      shipment.trackingHistory = [];

      // Add new tracking history
      shipment.trackingHistory = trackingData.trackingHistory;
    }

    // Store API response
    shipment.apiResponses = shipment.apiResponses || [];
    shipment.apiResponses.push({
      endpoint: 'track_shipment',
      response: trackingData,
      timestamp: new Date()
    });

    await shipment.save(sessionOptions(session));
  });

  return {
    shipmentId: shipment._id,
    trackingId: shipment.trackingId,
    trackingUrl: shipment.trackingUrl,
    estimatedDeliveryDate: shipment.estimatedDeliveryDate,
    courier: shipment.courier,
    trackingHistory: shipment.trackingHistory
  };
};

/**
 * Get all shipments for vendor
 * @param {Object} params - Query parameters
 */
const getVendorShipments = async ({ vendorId, page, limit, status, search, sortBy, sortOrder }) => {
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder) || -1;

  // Build the query
  const query = {
    vendor: new mongoose.Types.ObjectId(vendorId),
    vendorModel: 'Vendor'
  };

  // Add status filter if provided
  if (status) {
    query.status = status;
  }

  // Add search filter if provided
  if (search) {
    query.$or = [
      { trackingId: { $regex: search, $options: 'i' } },
      { shiprocketOrderId: { $regex: search, $options: 'i' } },
      { 'courier.courierName': { $regex: search, $options: 'i' } }
    ];
  }

  // Get shipments
  const shipments = await ProductShipment.find(query)
    .populate({
      path: 'order',
      select: 'orderNumber orderStatus paymentStatus totalAmount createdAt'
    })
    .populate({
      path: 'orderItems.product',
    })
    .populate({
      path: 'orderItems.variant',
    })
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit);

  // Get total count
  const total = await ProductShipment.countDocuments(query);

  return {
    shipments,
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit),
      limit
    }
  };
};

module.exports = {
  getVendorOrders,
  getOrderDetails,
  createShiprocketOrder,
  getCourierRecommendations,
  generateAWB,
  requestPickup,
  shipOrder,
  trackShipment,
  getVendorShipments
};
