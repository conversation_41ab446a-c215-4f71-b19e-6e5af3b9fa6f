const joi = require('joi');

const updateShipmentDimensionsSchema = joi.object({
  weight: joi.number().min(0.001).required()
    .messages({
      'number.base': 'Weight must be a number',
      'number.min': 'Weight must be at least 0.001 kg'
    }),
  dimensions: joi.object({
    length: joi.number().min(0.1).required()
      .messages({
        'number.base': 'Length must be a number',
        'number.min': 'Length must be at least 0.1 cm',
        'any.required': 'Length is required'
      }),
    width: joi.number().min(0.1).required()
      .messages({
        'number.base': 'Width must be a number',
        'number.min': 'Width must be at least 0.1 cm',
        'any.required': 'Width is required'
      }),
    height: joi.number().min(0.1).required()
      .messages({
        'number.base': 'Height must be a number',
        'number.min': 'Height must be at least 0.1 cm',
        'any.required': 'Height is required'
      })
  }).required()
});

const generateAWBSchema = joi.object({
  courierId: joi.string().required()
    .messages({
      'string.base': 'Courier ID must be a string',
      'any.required': 'Courier ID is required'
    })
});

const shipOrderSchema = joi.object({
  courierId: joi.string().required()
    .messages({
      'string.base': 'Courier ID must be a string',
      'any.required': 'Courier ID is required'
    })
});

module.exports = {
  updateShipmentDimensionsSchema,
  generateAWBSchema,
  shipOrderSchema
};
