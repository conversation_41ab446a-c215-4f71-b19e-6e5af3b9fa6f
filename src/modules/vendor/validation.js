const joi = require('joi');

// Validation schema for creating a vendor
const createVendorSchema = joi.object({
  // Business Information
  name: joi.string().trim(),
  businessName: joi.string().trim().required().messages({
    'string.empty': 'Business name is required',
    'any.required': 'Business name is required'
  }),
  address: joi.object({
    fullAddress: joi.string().trim().required().messages({
      'string.empty': 'Full address is required',
      'any.required': 'Full address is required'
    }),
    pincode: joi.string().trim().pattern(/^\d{6}$/).required().messages({
      'string.empty': 'Pincode is required',
      'string.pattern.base': 'Pincode must be 6 digits',
      'any.required': 'Pincode is required'
    })
  }).required(),

  // Contact Information
  contactPersonName: joi.string().trim().required().messages({
    'string.empty': 'Contact person name is required',
    'any.required': 'Contact person name is required'
  }),
  email: joi.string().trim().email().required().messages({
    'string.empty': 'Email is required',
    'string.email': 'Please enter a valid email',
    'any.required': 'Email is required'
  }),
  phoneNumber: joi.string().pattern(/^\+[1-9]\d{1,14}$/),

  // Tax Information & Documents
  gstNumber: joi.string().trim().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).required().messages({
    'string.empty': 'GST number is required',
    'string.pattern.base': 'Please enter a valid GST number',
    'any.required': 'GST number is required'
  }),
  gstCertificate: joi.string().trim().required().messages({
    'string.empty': 'GST certificate is required',
    'any.required': 'GST certificate is required'
  }),
  panNumber: joi.string().trim().pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/).allow('').messages({
    'string.pattern.base': 'Please enter a valid PAN number'
  }),
  panCard: joi.string().trim().when('panNumber', {
    is: joi.string().trim().pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/),
    then: joi.string().trim().required().messages({
      'string.empty': 'PAN card is required when PAN number is provided',
      'any.required': 'PAN card is required when PAN number is provided'
    }),
    otherwise: joi.string().trim().allow('')
  }),
  otherDocuments: joi.array().items(
    joi.object({
      name: joi.string().trim().required().messages({
        'string.empty': 'Document name is required',
        'any.required': 'Document name is required'
      }),
      image: joi.string().trim().required().messages({
        'string.empty': 'Document image is required',
        'any.required': 'Document image is required'
      })
    })
  ).optional(),

  // Bank Details
  bankDetails: joi.object({
    accountHolderName: joi.string().trim().required().messages({
      'string.empty': 'Account holder name is required',
      'any.required': 'Account holder name is required'
    }),
    accountNumber: joi.string().trim().required().messages({
      'string.empty': 'Account number is required',
      'any.required': 'Account number is required'
    }),
    ifscCode: joi.string().trim().pattern(/^[A-Z]{4}0[A-Z0-9]{6}$/).required().messages({
      'string.empty': 'IFSC code is required',
      'string.pattern.base': 'Please enter a valid IFSC code',
      'any.required': 'IFSC code is required'
    }),
    bankName: joi.string().trim().required().messages({
      'string.empty': 'Bank name is required',
      'any.required': 'Bank name is required'
    }),
    branchName: joi.string().trim().required().messages({
      'string.empty': 'Branch name is required',
      'any.required': 'Branch name is required'
    })
  }).required(),

  // Account Status
  status: joi.string().valid('ACTIVE', 'INACTIVE').default('ACTIVE'),
  shipRocketPickupLocationName: joi.string().trim()
});

// Validation schema for updating a vendor
const updateVendorSchema = joi.object({
  // Business Information
  name: joi.string().trim(),
  businessName: joi.string().trim(),
  address: joi.object({
    fullAddress: joi.string().trim(),
    pincode: joi.string().trim().pattern(/^\d{6}$/).messages({
      'string.pattern.base': 'Pincode must be 6 digits'
    })
  }),

  // Contact Information
  contactPersonName: joi.string().trim(),
  email: joi.string().trim().email().messages({
    'string.email': 'Please enter a valid email'
  }),
  phoneNumber: joi.string().pattern(/^\+[1-9]\d{1,14}$/),

  // Tax Information & Documents
  gstNumber: joi.string().trim().pattern(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/).messages({
    'string.pattern.base': 'Please enter a valid GST number'
  }),
  gstCertificate: joi.string().trim(),
  panNumber: joi.string().trim().pattern(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/).allow('').messages({
    'string.pattern.base': 'Please enter a valid PAN number'
  }),
  panCard: joi.string().trim(),
  otherDocuments: joi.array().items(
    joi.object({
      name: joi.string().trim().required().messages({
        'string.empty': 'Document name is required',
        'any.required': 'Document name is required'
      }),
      image: joi.string().trim().required().messages({
        'string.empty': 'Document image is required',
        'any.required': 'Document image is required'
      })
    })
  ).optional(),

  // Bank Details
  bankDetails: joi.object({
    accountHolderName: joi.string().trim(),
    accountNumber: joi.string().trim(),
    ifscCode: joi.string().trim().pattern(/^[A-Z]{4}0[A-Z0-9]{6}$/).messages({
      'string.pattern.base': 'Please enter a valid IFSC code'
    }),
    bankName: joi.string().trim(),
    branchName: joi.string().trim()
  }),

  // Account Status
  status: joi.string().valid('ACTIVE', 'INACTIVE'),
  shipRocketPickupLocationName: joi.string().trim()
});

// Validation schema for listing vendors with pagination and filters
const listVendorsSchema = joi.object({
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(10),
  search: joi.string().trim().allow(''),
  status: joi.string().valid('ACTIVE', 'INACTIVE').allow(''),
  sortBy: joi.string().valid('createdAt', 'businessName', 'email', 'status').default('createdAt'),
  sortOrder: joi.string().valid('asc', 'desc').default('desc')
});

// Validation schema for upload URL
const uploadUrlSchema = joi.object({
  extension: joi.string().required().valid('jpg', 'jpeg', 'png', 'pdf').messages({
    'string.empty': 'File extension is required',
    'any.required': 'File extension is required',
    'any.only': 'File extension must be jpg, jpeg, png, or pdf'
  })
});

// Validation for MongoDB ID
const validateId = joi.object({
  id: joi.string().required().messages({
    'string.empty': 'ID is required',
    'any.required': 'ID is required',
    'any.custom': 'Invalid ID format'
  })
});

module.exports = {
  createVendorSchema,
  updateVendorSchema,
  listVendorsSchema,
  uploadUrlSchema,
  validateId
};
