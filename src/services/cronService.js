const cron = require('node-cron');
const { expirePendingOrders } = require('../jobs/expireOrders');
const { logOrderEvent } = require('../utils/logger');
const { sendBookingReminders } = require('../crons/bookingReminders');

/**
 * Initialize all cron jobs
 */
const initCronJobs = () => {
  // Schedule order expiration job to run every hour
  // Cron format: minute hour day-of-month month day-of-week
  // '0 * * * *' = Run at the start of every hour
  cron.schedule('0 * * * *', async () => {
    try {
      logOrderEvent('CRON_JOB_STARTED', {
        job: 'expirePendingOrders',
        timestamp: new Date()
      });
      
      const result = await expirePendingOrders();
      
      logOrderEvent('CRON_JOB_COMPLETED', {
        job: 'expirePendingOrders',
        result,
        timestamp: new Date()
      });
    } catch (error) {
      logOrderEvent('CRON_JOB_ERROR', {
        job: 'expirePendingOrders',
        error: error.message,
        timestamp: new Date()
      });
    }
  });

  cron.schedule('0 0 * * *', async () => {
    await sendBookingReminders();
  });
  
  // You can add more scheduled jobs here
  
  logOrderEvent('CRON_JOBS_INITIALIZED', {
    timestamp: new Date()
  });
};

module.exports = {
  initCronJobs
};
