const axios = require('axios');
const { logShippingEvent } = require('../utils/logger');
const moment = require('moment');

// ShipRocket API base URL
const SHIPROCKET_API_BASE_URL = 'https://apiv2.shiprocket.in/v1/external';

/**
 * Helper function to get a short ID from a MongoDB ObjectId or string
 * @param {Object|String} id - MongoDB ObjectId or string
 * @returns {String} - Short ID (last 6 characters)
 */
const getShortId = (id) => {
  if (!id) {
    return 'UNKNOWN';
  }

  // If it's an object with _id property (populated document)
  if (id._id) {
    return id._id.toString().slice(-6);
  }

  // If it's an object with toString method (ObjectId)
  if (id.toString) {
    return id.toString().slice(-6);
  }

  // If it's a string
  if (typeof id === 'string') {
    return id.slice(-6);
  }

  // Fallback
  return 'UNKNOWN';
};

// Cache for auth token
let authToken = null;
let tokenExpiry = null;

/**
 * Authenticate with ShipRocket API and get token
 */
const authenticate = async () => {
  try {
    // Check if we have a valid token
    if (authToken && tokenExpiry && new Date() < tokenExpiry) {
      return authToken;
    }

    // Use direct API token if provided
    // if (process.env.SHIPROCKET_API_TOKEN) {
    //   // Set token and expiry (assuming token is valid for a long time)
    //   authToken = process.env.SHIPROCKET_API_TOKEN;
    //   tokenExpiry = new Date();
    //   tokenExpiry.setDate(tokenExpiry.getDate() + 30); // Set expiry to 30 days

    //   return authToken;
    // }

    // Fallback to email/password authentication if API token is not provided
    const response = await axios.post(`${SHIPROCKET_API_BASE_URL}/auth/login`, {
      email: process.env.SHIPROCKET_EMAIL,
      password: process.env.SHIPROCKET_PASSWORD
    });

    // Set token and expiry (token valid for 24 hours)
    authToken = response.data.token;
    tokenExpiry = new Date();
    tokenExpiry.setHours(tokenExpiry.getHours() + 23); // Set expiry to 23 hours to be safe

    return authToken;
  } catch (error) {
    logShippingEvent('SHIPROCKET_AUTH_ERROR', {
      error: error.response?.data || error.message
    });
    throw new Error(`ShipRocket authentication failed: ${error.message}`);
  }
};

/**
 * Create a ShipRocket order
 * @param {Object} orderData - Order data for ShipRocket
 */
const createOrder = async (orderData) => {
  try {
    const token = await authenticate();

    const response = await axios.post(
      `${SHIPROCKET_API_BASE_URL}/orders/create/adhoc`,
      orderData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    logShippingEvent('SHIPROCKET_ORDER_CREATED', {
      orderId: orderData.order_id,
      shiprocketOrderId: response.data.order_id,
      response: response.data
    });

    return response.data;
  } catch (error) {
    logShippingEvent('SHIPROCKET_ORDER_ERROR', {
      orderId: orderData.order_id,
      error: error.response?.data || error.message
    });
    throw new Error(`Failed to create ShipRocket order: ${error.message}`);
  }
};

/**
 * Generate pickup for a ShipRocket order
 * @param {String} shiprocketOrderId - ShipRocket order ID
 * @param {String} pickUpDate - Pickup date (optional)
 */
const generatePickup = async (shiprocketShipmentId, pickUpDate = null) => {
  try {
    const token = await authenticate();
    const body = {
      shipment_id: [ shiprocketShipmentId ]
    };

    if (pickUpDate) {
      pickUpDate = moment(pickUpDate).format('YYYY-MM-DD');
      body.pickup_date = [ pickUpDate ];
    }

    const response = await axios.post(
      `${SHIPROCKET_API_BASE_URL}/courier/generate/pickup`,
      body,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    logShippingEvent('SHIPROCKET_PICKUP_GENERATED', {
      shiprocketShipmentId,
      response: response.data
    });

    return response.data;
  } catch (error) {
    logShippingEvent('SHIPROCKET_PICKUP_ERROR', {
      shiprocketShipmentId,
      error: error.response?.data || error.message
    });
    throw new Error(`Failed to generate pickup for ShipRocket order: ${error.message}`);
  }
};

/**
 * Track a ShipRocket shipment
 * @param {String} shiprocketOrderId - ShipRocket order ID
 */
const trackShipment = async (shiprocketOrderId) => {
  try {
    const token = await authenticate();

    const response = await axios.get(
      `${SHIPROCKET_API_BASE_URL}/courier/track/shipment/${shiprocketOrderId}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    // Process tracking data to extract history
    const trackingData = response.data;
    let trackingHistory = [];

    if (trackingData.tracking_data && trackingData.tracking_data.shipment_track) {
      const shipmentTrack = trackingData.tracking_data.shipment_track;

      // Extract tracking history from different possible formats
      if (shipmentTrack.tracks && Array.isArray(shipmentTrack.tracks)) {
        // Format 1: Array of track objects
        trackingHistory = shipmentTrack.tracks.map(track => ({
          status: track.status || 'Unknown',
          location: track.location || 'Unknown',
          timestamp: track.date ? new Date(track.date) : new Date(),
          description: track.activity || track.status || 'Status update'
        }));
      } else if (shipmentTrack.track_activities && Array.isArray(shipmentTrack.track_activities)) {
        // Format 2: Array of track_activities
        trackingHistory = shipmentTrack.track_activities.map(activity => ({
          status: activity.status || 'Unknown',
          location: activity.location || 'Unknown',
          timestamp: activity.date ? new Date(activity.date) : new Date(),
          description: activity.activity || activity.status || 'Status update'
        }));
      } else if (shipmentTrack.shipment_status_array && Array.isArray(shipmentTrack.shipment_status_array)) {
        // Format 3: shipment_status_array
        trackingHistory = shipmentTrack.shipment_status_array.map(status => ({
          status: status.status || 'Unknown',
          location: status.location || 'Unknown',
          timestamp: status.date ? new Date(status.date) : new Date(),
          description: status.activity || status.status || 'Status update'
        }));
      }
    }

    // Add tracking history to the response
    trackingData.trackingHistory = trackingHistory;

    return trackingData;
  } catch (error) {
    logShippingEvent('SHIPROCKET_TRACKING_ERROR', {
      shiprocketOrderId,
      error: error.response?.data || error.message
    });
    throw new Error(`Failed to track ShipRocket shipment: ${error.message}`);
  }
};

/**
 * Get available couriers for a shipment
 * @param {Object} shipmentData - Shipment data for courier selection
 */
const getAvailableCouriers = async (shipmentData) => {
  try {
    const token = await authenticate();

    const response = await axios.post(
      `${SHIPROCKET_API_BASE_URL}/courier/serviceability`,
      shipmentData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response.data;
  } catch (error) {
    logShippingEvent('SHIPROCKET_COURIERS_ERROR', {
      pickup_postcode: shipmentData.pickup_postcode,
      delivery_postcode: shipmentData.delivery_postcode,
      error: error.response?.data || error.message
    });
    throw new Error(`Failed to get available couriers: ${error.message}`);
  }
};

/**
 * Cancel a ShipRocket order
 * @param {String} shiprocketOrderId - ShipRocket order ID
 */
const cancelOrder = async (shiprocketOrderId) => {
  try {
    const token = await authenticate();

    const response = await axios.post(
      `${SHIPROCKET_API_BASE_URL}/orders/cancel`,
      { ids: [ shiprocketOrderId ] },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    logShippingEvent('SHIPROCKET_ORDER_CANCELLED', {
      shiprocketOrderId,
      response: response.data
    });

    return response.data;
  } catch (error) {
    logShippingEvent('SHIPROCKET_CANCEL_ERROR', {
      shiprocketOrderId,
      error: error.response?.data || error.message
    });
    throw new Error(`Failed to cancel ShipRocket order: ${error.message}`);
  }
};

/**
 * Get pickup location name for a vendor
 * @param {Object} vendor - Vendor document
 * @param {String} vendorModel - Vendor model type ('Admin' or 'Vendor')
 */
const getPickupLocationName = async (vendor, vendorModel) => {
  if (!vendor) {
    return 'Primary'; // Default fallback
  }

  // For admin, use a default location name
  if (vendorModel === 'Admin') {
    return 'AdminLocation';
  }

  // For vendors, use their business name or ID as the location name
  return `Vendor-${vendor.businessName.en || vendor.businessName || vendor._id.toString().slice(-6)}`;
};

/**
 * Add pickup location to ShipRocket
 * @param {Object} vendor - Vendor document
 * @param {String} vendorModel - Vendor model type ('Admin' or 'Vendor')
 */
const addPickupLocation = async (vendor, vendorModel) => {
  try {
    if (!vendor) {
      throw new Error('Vendor not provided');
    }

    const token = await authenticate();

    // Get pickup location name
    const pickupName = await getPickupLocationName(vendor, vendorModel);

    // Format pickup address based on vendor model
    let pickupAddress = {};

    if (vendorModel === 'Admin') {
      // Use default admin address from environment variables
      // Ensure address has house/flat number as required by ShipRocket
      const adminAddress = process.env.ADMIN_ADDRESS || 'Admin Office';
      const formattedAddress = adminAddress.includes('No.') ||
                              adminAddress.includes('#') ||
                              adminAddress.includes('Flat') ||
                              adminAddress.includes('House') ?
        adminAddress : `House No. 123, ${adminAddress}`;

      pickupAddress = {
        pickup_location: pickupName,
        name: 'Admin',
        email: process.env.ADMIN_EMAIL || '<EMAIL>',
        phone: process.env.ADMIN_PHONE || '9999999999',
        address: formattedAddress,
        address_2: '',
        city: process.env.ADMIN_CITY || 'Mumbai',
        state: process.env.ADMIN_STATE || 'Maharashtra',
        country: 'India',
        pin_code: process.env.ADMIN_PINCODE || '400001'
      };
    } else if (vendorModel === 'Vendor' && vendor.address) {
      // Use vendor's address
      // Ensure address has house/flat number as required by ShipRocket
      const vendorAddress = vendor.address.addressLine1;
      const formattedAddress = vendorAddress.includes('No.') ||
                             vendorAddress.includes('#') ||
                             vendorAddress.includes('Flat') ||
                             vendorAddress.includes('House') ?
        vendorAddress : `House No. 123, ${vendorAddress}`;

      pickupAddress = {
        pickup_location: pickupName,
        name: vendor.businessName.en || vendor.businessName || vendor.ownerName,
        email: vendor.email,
        phone: vendor.phoneNumber,
        address: formattedAddress,
        address_2: vendor.address.addressLine2 || '',
        city: vendor.address.city,
        state: vendor.address.state,
        country: vendor.address.country || 'India',
        pin_code: vendor.address.postalCode
      };
    } else {
      throw new Error('Invalid vendor or missing address information');
    }

    // Check if pickup location already exists
    try {
      const pickupLocationsResponse = await axios.get(
        `${SHIPROCKET_API_BASE_URL}/settings/company/pickup`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      // Log the response for debugging
      logShippingEvent('PICKUP_LOCATIONS_RESPONSE', {
        response: pickupLocationsResponse.data
      });

      // The API response format has changed, so we need to handle it differently
      // If there are recent_addresses, check if our location exists
      if (pickupLocationsResponse.data.data &&
          pickupLocationsResponse.data.data.recent_addresses &&
          Array.isArray(pickupLocationsResponse.data.data.recent_addresses)) {

        const existingLocations = pickupLocationsResponse.data.data.recent_addresses;
        const locationExists = existingLocations.some(loc => loc.pickup_location === pickupName);

        if (locationExists) {
          // Location already exists, no need to add it again
          return { success: true, message: 'Pickup location already exists', pickupName };
        }
      }

      // If we can't determine if the location exists, proceed to add it
    } catch (error) {
      // If we can't check existing locations, just proceed to add the new one
      logShippingEvent('CHECK_PICKUP_LOCATIONS_ERROR', {
        error: error.response?.data || error.message
      });
      // Continue with adding the location
    }

    // Add new pickup location
    try {
      const response = await axios.post(
        `${SHIPROCKET_API_BASE_URL}/settings/company/addpickup`,
        pickupAddress,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      logShippingEvent('PICKUP_LOCATION_ADDED', {
        pickupName,
        vendorId: vendor._id,
        vendorModel,
        response: response.data
      });

      return { success: true, pickupName, response: response.data };
    } catch (addError) {
      // If adding the pickup location fails, log the error but still return the pickup name
      // so we can use it in the order creation
      logShippingEvent('ADD_PICKUP_LOCATION_ERROR', {
        pickupName,
        vendorId: vendor._id,
        vendorModel,
        error: addError.response?.data || addError.message
      });

      // If the error indicates the location already exists, consider it a success
      const errorMessage = addError.response?.data?.message || '';

      if (errorMessage.includes('already exists') ||
          errorMessage.includes('already added') ||
          errorMessage.includes('duplicate')) {
        return { success: true, pickupName, message: 'Pickup location already exists' };
      }

      // If there's a validation error with the address, log it specifically
      if (errorMessage.includes('Address line') ||
          (addError.response?.data?.status_code === 422 &&
           addError.response?.data?.message?.includes('address'))) {
        logShippingEvent('ADDRESS_VALIDATION_ERROR', {
          pickupName,
          vendorId: vendor._id,
          vendorModel,
          error: addError.response?.data || addError.message,
          address: pickupAddress.address
        });
      }

      // For other errors, return success=true anyway so we can proceed with order creation
      // using the default pickup location
      return { success: true, pickupName, message: 'Using default pickup location' };
    }
  } catch (error) {
    logShippingEvent('ADD_PICKUP_LOCATION_ERROR', {
      vendorId: vendor?._id,
      vendorModel,
      error: error.response?.data || error.message
    });

    // Return the pickup name even if there's an error, so we can still use it
    const pickupName = await getPickupLocationName(vendor, vendorModel);

    // Return success=true anyway so we can proceed with order creation
    // using the default pickup location
    return { success: true, pickupName, message: 'Using default pickup location due to error' };
  }
};

/**
 * Format order data for ShipRocket API
 * @param {Object} order - Order document
 * @param {Object} shipment - Shipment document
 * @param {Object} user - User document
 * @param {Object} vendor - Vendor document
 */
const formatOrderData = async (order, shipment, user) => {

  // Format order items
  const orderItems = shipment.orderItems.map(item => {
    // Use the price information directly from the shipment item
    return {
      name: item.name.en || item.name,
      // Create a shorter SKU by using just the last 6 characters of the IDs
      // Ensure total length is under 50 characters
      sku: `SKU-${getShortId(item.product && item.product._id ? item.product._id : item.product)}${item.variant ? `-${getShortId(item.variant && item.variant._id ? item.variant._id : item.variant)}` : ''}`.substring(0, 45),
      units: item.quantity,
      selling_price: item.price,
      discount: 0,
      tax: 0,
      hsn: 0
    };
  });

  // Calculate order total
  const orderTotal = orderItems.reduce((total, item) =>
    total + (item.selling_price * item.units), 0
  );

  // Format delivery address
  const deliveryAddress = shipment.deliveryAddress;

  // Get pickup location name based on vendor
  // const pickupLocationName = await getPickupLocationName(vendor, shipment.vendorModel);
  const pickupLocationName = shipment.shipRocketPickupLocationName || 'AdminLocation';

  // Use shipment dimensions if available, otherwise use default values
  const packageWeight = shipment.weight || 0.5; // Default to 0.5 kg
  const packageLength = shipment.dimensions?.length || 10; // Default to 10 cm
  const packageWidth = shipment.dimensions?.width || 10; // Default to 10 cm
  const packageHeight = shipment.dimensions?.height || 10; // Default to 10 cm

  return {
    order_id: `${order.orderNumber}-${shipment._id.toString().slice(-6)}`,
    order_date: order.createdAt.toISOString().split('T')[0],
    pickup_location: pickupLocationName,
    channel_id: '',
    comment: order.notes || 'Order from Ek Ishwar',
    billing_customer_name: deliveryAddress.name ? deliveryAddress.name.split(' ')[0] : '',
    billing_last_name: deliveryAddress.name ? deliveryAddress.name.split(' ')[1] : '',
    billing_address: deliveryAddress.addressLine1,
    billing_address_2: deliveryAddress.addressLine2 || '123',
    billing_city: deliveryAddress.city,
    billing_pincode: deliveryAddress.postalCode,
    billing_state: deliveryAddress.state,
    billing_country: deliveryAddress.country || 'India',
    billing_email: deliveryAddress.email || user.email,
    billing_phone: deliveryAddress.phone,
    shipping_is_billing: true,
    shipping_customer_name: deliveryAddress.name ? deliveryAddress.name.split(' ')[0] : '',
    shipping_last_name: deliveryAddress.name ? deliveryAddress.name.split(' ')[1] : '',
    shipping_address: deliveryAddress.addressLine1,
    shipping_address_2: deliveryAddress.addressLine2 || '123',
    shipping_city: deliveryAddress.city,
    shipping_pincode: deliveryAddress.postalCode,
    shipping_state: deliveryAddress.state,
    shipping_country: deliveryAddress.country || 'India',
    shipping_email: deliveryAddress.email || user.email,
    shipping_phone: deliveryAddress.phone,
    order_items: orderItems,
    payment_method: 'Prepaid',
    shipping_charges: order.shippingCost?.totalShipping || order.shippingCost,
    giftwrap_charges: 0,
    transaction_charges: 0,
    total_discount: 0,
    sub_total: orderTotal,
    length: packageLength,
    breadth: packageWidth,
    height: packageHeight,
    weight: packageWeight
  };
};

/**
 * Generate AWB (tracking number) for a shipment
 * @param {String} shiprocketOrderId - ShipRocket order ID
 * @param {String} courierId - Courier ID to use for the shipment
 */
const generateAWB = async (shiprocketShipmentId, courierId) => {
  try {
    const token = await authenticate();

    const response = await axios.post(
      `${SHIPROCKET_API_BASE_URL}/courier/assign/awb`,
      {
        shipment_id: shiprocketShipmentId,
        courier_id: courierId
      },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    logShippingEvent('SHIPROCKET_AWB_GENERATED', {
      shiprocketShipmentId,
      courierId,
      response: response.data
    });

    return response.data;
  } catch (error) {
    logShippingEvent('SHIPROCKET_AWB_ERROR', {
      shiprocketShipmentId,
      courierId,
      error: error.response?.data || error.message
    });
    throw new Error(`Failed to generate AWB for ShipRocket order: ${error.message}`);
  }
};

/**
 * Get courier recommendations for a shipment
 * @param {String} shiprocketOrderId - ShipRocket order ID
 */
const getCourierRecommendations = async (shiprocketOrderId) => {
  try {
    const token = await authenticate();

    // First, get the order details to extract pickup and delivery pincodes
    const orderDetailsResponse = await axios.get(
      `${SHIPROCKET_API_BASE_URL}/orders/show/${shiprocketOrderId}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    const orderDetails = orderDetailsResponse.data;

    if (!orderDetails || !orderDetails.data) {
      throw new Error(`Order details not found for order ID: ${shiprocketOrderId}`);
    }

    const order = orderDetails.data;
    const pickupPincode = order.pickup_address?.pin_code || order.pickup_pincode;

    const deliveryPincode = order.billing_pincode;

    const weight = order.weight || 0.5; // Default to 0.5 kg if not specified
    const cod = order.payment_method === 'COD' ? 1 : 0;

    if (!pickupPincode || !deliveryPincode) {
      throw new Error('Pickup or delivery pincode not found in order details');
    }

    // Now get courier serviceability
    const serviceabilityResponse = await axios.get(
      `${SHIPROCKET_API_BASE_URL}/courier/serviceability?pickup_postcode=${pickupPincode}&delivery_postcode=${deliveryPincode}&weight=${weight}&cod=${cod}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    logShippingEvent('SHIPROCKET_COURIER_RECOMMENDATIONS', {
      shiprocketOrderId,
      pickupPincode,
      deliveryPincode,
      weight,
      cod,
      response: serviceabilityResponse.data
    });

    return serviceabilityResponse.data;
  } catch (error) {
    logShippingEvent('SHIPROCKET_COURIER_RECOMMENDATIONS_ERROR', {
      shiprocketOrderId,
      error: error.response?.data || error.message
    });
    throw new Error(`Failed to get courier recommendations for ShipRocket order: ${error.message}`);
  }
};

/**
 * Update pickup location for a ShipRocket order
 * @param {String} shiprocketOrderId - ShipRocket order ID
 * @param {String} pickupLocationName - Name of the pickup location
 */
const updatePickupLocation = async (shiprocketOrderId, pickupLocationName) => {
  try {
    const token = await authenticate();

    // First, get the order details
    const orderDetailsResponse = await axios.get(
      `${SHIPROCKET_API_BASE_URL}/orders/show/${shiprocketOrderId}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    const orderDetails = orderDetailsResponse.data;

    if (!orderDetails || !orderDetails.data) {
      throw new Error(`Order details not found for order ID: ${shiprocketOrderId}`);
    }

    // Update the pickup location
    const response = await axios.patch(
      `${SHIPROCKET_API_BASE_URL}/orders/address/pickup`,
      {
        order_id: [ shiprocketOrderId ],
        pickup_location: pickupLocationName
      },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    logShippingEvent('SHIPROCKET_PICKUP_LOCATION_UPDATED', {
      shiprocketOrderId,
      pickupLocationName,
      response: response.data
    });

    return response.data;
  } catch (error) {
    logShippingEvent('SHIPROCKET_UPDATE_PICKUP_LOCATION_ERROR', {
      shiprocketOrderId,
      pickupLocationName,
      error: error.response?.data || error.message
    });
    throw new Error(`Failed to update pickup location for ShipRocket order: ${error.message}`);
  }
};

module.exports = {
  authenticate,
  createOrder,
  generatePickup,
  trackShipment,
  getAvailableCouriers,
  cancelOrder,
  formatOrderData,
  addPickupLocation,
  getPickupLocationName,
  generateAWB,
  getCourierRecommendations,
  updatePickupLocation
};
