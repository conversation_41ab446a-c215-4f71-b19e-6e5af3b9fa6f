const AuditLog = require('../models/AuditLog');

const getClientIp = (req) => {
  const forwarded = req.headers['x-forwarded-for'];

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  return req.connection?.remoteAddress || req.socket?.remoteAddress || req.ip;
};

const saveAuditLog = async (req, admin, action, detail, model) => {
  try {
    await AuditLog.create({
      admin,
      action,
      detail,
      ipAddress: getClientIp(req),
      model,  
      metaData: {
        originalUrl: req.originalUrl,
        method: req.method,
        remoteConnection: req.connection.remoteAddress,
        headers: req.headers,
      }
    });
  } catch (error) {
    console.error('Error saving audit log:', error);
  }
};

module.exports = {
  saveAuditLog,
  getClientIp
};