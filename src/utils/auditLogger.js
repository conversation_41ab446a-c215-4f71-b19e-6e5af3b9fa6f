const AuditLog = require('../models/AuditLog');

const saveAuditLog = async (req, admin, action, detail, model) => {
  try {
    await AuditLog.create({
      admin,
      action,
      detail,
      ipAddress: req.ip,
      model,
      metaData: {
        originalUrl: req.originalUrl,
        method: req.method,
        remoteConnection: req.connection.remoteAddress,
        headers: req.headers,
      }
    });
  } catch (error) {
    console.error('Error saving audit log:', error);
  }
};

module.exports = {
  saveAuditLog
};