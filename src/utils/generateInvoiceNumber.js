const states = require('../../data/states.json');
const Booking = require('../models/Booking');

//? Function to generate invoice for temple bookings
const getInvoiceNumber = async (updatedBooking) => {
  //* 1. Fetch the temple state
  const templeState = updatedBooking?.temple?.state?.en;

  //* 2. Verify state exists in your states.json
  const stateData = states.find(
    (s) => s.name.trim().toLowerCase() === templeState.trim().toLowerCase()
  );

  const stateId = stateData.id;

  //* 3. Find last booking for this state to determine the next number
  const lastBooking = await Booking.findOne({
    invoiceNumber: new RegExp(`INV-${stateId}-`, 'i')
  })
    .sort({ _id: -1 })
    .lean();

  let nextNumber = 1;

  if (lastBooking?.invoiceNumber) {
    const lastNumber = parseInt(lastBooking.invoiceNumber.split('-')[2], 10);

    nextNumber = lastNumber + 1;
  }

  //* 4. Determine padding (switch to 6 digits after 99999)
  const padLength = nextNumber > 99999 ? 6 : 5;

  const formattedNumber = String(nextNumber).padStart(padLength, '0');

  //* 5. Build invoice number
  const invoiceNumber = `INV-${stateData.id}-${formattedNumber}`;

  //* 6. Return invoice number
  return invoiceNumber;
};

module.exports = getInvoiceNumber;
