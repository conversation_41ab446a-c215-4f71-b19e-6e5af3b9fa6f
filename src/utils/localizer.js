const { default: mongoose } = require('mongoose');

// const transformTranslatedFields = (obj, lang) => {
  
//   if (Array.isArray(obj)) {
//     return obj.map(item => transformTranslatedFields(item, lang));
//   }

//   if (obj && typeof obj === 'object' && obj !== null && !mongoose.Types.ObjectId.isValid(obj) && !(obj instanceof mongoose.Types.ObjectId) && !(obj instanceof Date)) {

//     const result = {};

//     for (const key in obj) {
//       const value = obj[key];

//       if (
//         value &&
//         typeof value === 'object' &&
//         typeof value[lang] === 'string'
//       ) {
//         // This is a translatable field
//         result[key] = value[lang] || value.en;

//       } else {
//         // Recurse deeper if it's an object or array
//         result[key] = transformTranslatedFields(value, lang);
//       }
//     }

//     return result;
//   }

//   // Primitive value (string, number, null, etc.)
//   return obj;
// };

const transformTranslatedFields = (obj, lang) => {
  if (Array.isArray(obj)) {
    return obj.map(item => transformTranslatedFields(item, lang));
  }

  if (
    obj &&
    typeof obj === 'object' &&
    obj !== null &&
    !(obj instanceof Date) &&
    !(obj instanceof mongoose.Types.ObjectId) &&
    !(mongoose.Types.ObjectId.isValid(obj))
  ) {
    const result = {};

    for (const key in obj) {
      const value = obj[key];

      if (
        value &&
        typeof value === 'object' &&
        !Array.isArray(value) &&
        typeof value[lang] === 'string'
      ) {
        result[key] = value[lang] || value.en || null;
      } else {
        result[key] = transformTranslatedFields(value, lang);
      }
    }

    return result;
  }

  return obj; // Primitive value or ObjectId/Date
};

module.exports = {
  transformTranslatedFields
};