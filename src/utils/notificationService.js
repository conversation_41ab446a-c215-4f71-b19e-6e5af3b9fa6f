const { sendEmail } = require('./sesEmailService');
const { sendWhatsAppMessage } = require('./whatsappService');

/**
 * Send product approval notification to vendor
 * @param {Object} vendor - Vendor object
 * @param {Object} product - Product object
 * @returns {Promise} Promise that resolves when notifications are sent
 */
const sendProductApprovalNotification = async (vendor, product) => {
  try {
    // Email notification
    const emailSubject = 'Product Approved: ' + product.name;
    const emailHtml = `
      <h2>Congratulations!</h2>
      <p>Your product "${product.name}" has been approved and is now live on the platform.</p>
      <p>Product details:</p>
      <ul>
        <li><strong>Product Name:</strong> ${product.name}</li>
        <li><strong>SKU:</strong> ${product.sku}</li>
        <li><strong>Price:</strong> ₹${product.price}</li>
      </ul>
      <p>You can now view your product in your vendor dashboard.</p>
      <p>Thank you for your contribution to our marketplace!</p>
      <p>Best regards,<br>One God Team</p>
    `;

    await sendEmail(
      process.env.AWS_SES_SENDER_EMAIL,
      vendor.email,
      emailHtml,
      emailSubject
    );

    // WhatsApp notification (if vendor has phone number)
    if (vendor.phoneNumber) {
      const whatsappMessage = `
Congratulations!

Your product "${product.name}" has been approved and is now live on the platform.

Product details:
- Product Name: ${product.name}
- SKU: ${product.sku}
- Price: ₹${product.price}

You can now view your product in your vendor dashboard.

Thank you for your contribution to our marketplace!

Best regards,
One God Team
      `;

      await sendWhatsAppMessage(vendor.phoneNumber, whatsappMessage);
    }

    return { success: true };
  } catch (error) {
    console.error('Error sending product approval notification:', error);
    // Continue execution even if notification fails
    return { success: false, error: error.message };
  }
};

/**
 * Send product rejection notification to vendor
 * @param {Object} vendor - Vendor object
 * @param {Object} product - Product object
 * @param {string} rejectionReason - Reason for rejection
 * @returns {Promise} Promise that resolves when notifications are sent
 */
const sendProductRejectionNotification = async (vendor, product, rejectionReason) => {
  try {
    // Email notification
    const emailSubject = 'Product Needs Revision: ' + product.name;
    const emailHtml = `
      <h2>Product Review Update</h2>
      <p>Your product "${product.name}" requires some revisions before it can be approved.</p>
      <p><strong>Reason for revision:</strong></p>
      <p>${rejectionReason}</p>
      <p>Product details:</p>
      <ul>
        <li><strong>Product Name:</strong> ${product.name}</li>
        <li><strong>SKU:</strong> ${product.sku}</li>
      </ul>
      <p>Please log in to your vendor dashboard to make the necessary changes and resubmit for approval.</p>
      <p>If you have any questions, please contact our support team.</p>
      <p>Best regards,<br>One God Team</p>
    `;

    await sendEmail(
      process.env.AWS_SES_SENDER_EMAIL,
      vendor.email,
      emailHtml,
      emailSubject
    );

    // WhatsApp notification (if vendor has phone number)
    if (vendor.phoneNumber) {
      const whatsappMessage = `
Product Review Update

Your product "${product.name}" requires some revisions before it can be approved.

Reason for revision:
${rejectionReason}

Product details:
- Product Name: ${product.name}
- SKU: ${product.sku}

Please log in to your vendor dashboard to make the necessary changes and resubmit for approval.

If you have any questions, please contact our support team.

Best regards,
One God Team
      `;

      await sendWhatsAppMessage(vendor.phoneNumber, whatsappMessage);
    }

    return { success: true };
  } catch (error) {
    console.error('Error sending product rejection notification:', error);
    // Continue execution even if notification fails
    return { success: false, error: error.message };
  }
};

module.exports = {
  sendProductApprovalNotification,
  sendProductRejectionNotification
};
