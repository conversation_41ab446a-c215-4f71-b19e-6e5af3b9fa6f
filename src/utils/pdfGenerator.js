const fs = require('fs');
const path = require('path');
const os = require('os');
const { v4: uuidv4 } = require('uuid');
const AWS = require('aws-sdk');
const htmlPdf = require('html-pdf-node');
const util = require('util');
const readFile = util.promisify(fs.readFile);
const Handlebars = require('handlebars');

// Configure AWS S3
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION
});

/**
 * Format date to a readable string
 * @param {Date} date - Date to format
 * @returns {String} Formatted date string
 */
const formatDate = (date) => {
  const d = new Date(date);

  return d.toLocaleDateString('en-IN', {
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  });
};

/**
 * Format number to currency
 * @param {Number} num - Number to format
 * @returns {String} Formatted currency string
 */
const formatCurrency = (num) => {
  return new Intl.NumberFormat('en-IN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(num);
};

/**
 * Helper function to format variant attributes into a readable string
 * @param {Map} attributes - Map of variant attributes
 * @returns {String} Formatted attributes string
 */
const formatVariantAttributes = (attributes) => {
  if (!attributes || attributes.size === 0) {
    return '';
  }

  const attributeArray = [];

  attributes.forEach((value, key) => {
    attributeArray.push(`${key}: ${value}`);
  });

  return attributeArray.join(', ');
};

/**
 * Generate an order invoice PDF for a customer
 * @param {Object} orderData - Order data
 * @returns {Promise<String>} S3 key of the generated PDF
 */
const generateTicketDetails = async (ticketDetails) => {

  // Create a temporary file path
  const tempFilePath = path.join(os.tmpdir(), `ticket-details-${uuidv4()}.pdf`);

  // Read the HTML template
  const templatePath = path.join(__dirname, '../views/bookingTickets.html');
  const templateSource = await readFile(templatePath, 'utf8');

  // Compile the template with Handlebars
  const template = Handlebars.compile(templateSource);

  // Generate the HTML from the template and data
  const htmlTemplate = template(ticketDetails);

  // Generate PDF from HTML
  const options = {
    format: 'A4',
    printBackground: true,
    margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' }
  };

  const file = { content: htmlTemplate };

  const pdfBuffer = await htmlPdf.generatePdf(file, options);

  // Write the PDF to a temporary file
  fs.writeFileSync(tempFilePath, pdfBuffer);

  // Upload the PDF to S3
  const s3Key = `booking-tickets/${ticketDetails.bookingNumber}.pdf`;

  await s3.putObject({
    Bucket: process.env.AWS_S3_BUCKET,
    Key: s3Key,
    Body: fs.createReadStream(tempFilePath),
    ContentType: 'application/pdf'
  }).promise();

  // Delete the temporary file
  fs.unlinkSync(tempFilePath);

  return s3Key;
};

/**
 * Generate a commission invoice PDF for a shipment
 * @param {Object} invoiceData - Invoice data
 * @returns {Promise<String>} S3 key of the generated PDF
 */
const generateCommissionInvoice = async (invoiceData) => {
  const {
    invoiceNumber,
    invoiceDate,
    vendorDetails,
    platformDetails,
    settlementPeriod,
    commissionAmount,
    taxAmount,
    totalAmount,
    orderNumber = 'N/A', // Default value if not provided
    orderItems = [] // Default empty array if not provided
  } = invoiceData;

  // Create a temporary file path
  const tempFilePath = path.join(os.tmpdir(), `invoice-${uuidv4()}.pdf`);

  // Read the HTML template
  const templatePath = path.join(__dirname, '../views/invoice-template.html');
  const templateSource = await readFile(templatePath, 'utf8');

  // Compile the template with Handlebars
  const template = Handlebars.compile(templateSource);

  // Prepare the data for the template
  const templateData = {
    invoiceNumber,
    invoiceDate: formatDate(invoiceDate),
    platformDetails: {
      businessName: platformDetails.businessName,
      address: platformDetails.address,
      gstNumber: platformDetails.gstNumber
    },
    vendorDetails: {
      businessName: vendorDetails.businessName,
      address: vendorDetails.address,
      gstNumber: vendorDetails.gstNumber
    },
    settlementPeriodStart: formatDate(settlementPeriod.startDate),
    settlementPeriodEnd: formatDate(settlementPeriod.endDate),
    orderNumber,
    orderItems: orderItems || [],
    commissionAmount: formatCurrency(commissionAmount),
    taxAmount: formatCurrency(taxAmount),
    totalAmount: formatCurrency(totalAmount)
  };

  // Generate the HTML from the template and data
  const htmlTemplate = template(templateData);

  // Generate PDF from HTML
  const options = {
    format: 'A4',
    margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' }
  };

  const file = { content: htmlTemplate };

  const pdfBuffer = await htmlPdf.generatePdf(file, options);

  // Write the PDF to a temporary file
  fs.writeFileSync(tempFilePath, pdfBuffer);

  // Upload the PDF to S3
  const s3Key = `invoices/${invoiceNumber}.pdf`;

  await s3.putObject({
    Bucket: process.env.AWS_S3_BUCKET,
    Key: s3Key,
    Body: fs.createReadStream(tempFilePath),
    ContentType: 'application/pdf'
  }).promise();

  // Delete the temporary file
  fs.unlinkSync(tempFilePath);

  return s3Key;
};

/**
 * Generate an order invoice PDF for a customer
 * @param {Object} orderData - Order data
 * @returns {Promise<String>} S3 key of the generated PDF
 */
const generateOrderInvoice = async (orderData) => {
  const {
    orderNumber,
    orderDate,
    customerName,
    billingAddress,
    shippingAddress,
    items,
    subtotal,
    tax,
    shippingCost,
    discountAmount,
    total,
    paymentMethod,
    paymentStatus
  } = orderData;

  // Create a temporary file path
  const tempFilePath = path.join(os.tmpdir(), `order-invoice-${uuidv4()}.pdf`);

  // Read the HTML template
  const templatePath = path.join(__dirname, '../views/order-invoice-template.html');
  const templateSource = await readFile(templatePath, 'utf8');

  // Compile the template with Handlebars
  const template = Handlebars.compile(templateSource);

  // Format items to include variant attributes as text
  const formattedItems = items.map(item => ({
    ...item,
    price: formatCurrency(item.price),
    subtotal: formatCurrency(item.subtotal),
    variantAttributesText: item.variantAttributes ? formatVariantAttributes(item.variantAttributes) : ''
  }));

  let logoBase64 = '';

  try {
    const logoPath = path.join(__dirname, '../../public/images/logo.png');
    const logoBuffer = await readFile(logoPath);

    logoBase64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;
  } catch (error) {
    console.log('Error reading logo file:', error);
  }

  // Prepare the data for the template
  const templateData = {
    orderNumber,
    orderDate: formatDate(orderDate),
    customerName,
    billingAddress,
    shippingAddress,
    items: formattedItems,
    subtotal: formatCurrency(subtotal),
    tax: formatCurrency(tax),
    shippingCost: formatCurrency(shippingCost),
    discountAmount: discountAmount ? formatCurrency(discountAmount) : null,
    total: formatCurrency(total),
    paymentMethod,
    paymentStatus,
    logo: logoBase64
  };

  // Generate the HTML from the template and data
  const htmlTemplate = template(templateData);

  // Generate PDF from HTML
  const options = {
    format: 'A4',
    margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' }
  };

  const file = { content: htmlTemplate };

  const pdfBuffer = await htmlPdf.generatePdf(file, options);

  // Write the PDF to a temporary file
  fs.writeFileSync(tempFilePath, pdfBuffer);

  // Upload the PDF to S3
  const s3Key = `order-invoices/${orderNumber}.pdf`;

  await s3.putObject({
    Bucket: process.env.AWS_S3_BUCKET,
    Key: s3Key,
    Body: fs.createReadStream(tempFilePath),
    ContentType: 'application/pdf'
  }).promise();

  // Delete the temporary file
  fs.unlinkSync(tempFilePath);

  return s3Key;
};

/**
 * Generate a booking confirmation PDF
 * @param {Object} bookingData - Booking data
 * @returns {Promise<String>} S3 key of the generated PDF
 */
const generateBookingConfirmationPDF = async (bookingData, bookingType, bookingDates, individualPrice, couplePrice, familyPrice) => {
  //* 1. Create a temporary file path
  const tempFilePath = path.join(os.tmpdir(), `booking-${uuidv4()}.pdf`);

  //* 2. Read the HTML template
  const templatePath = path.join(__dirname, '../views/booking-confirmation-pdf.html');
  const templateSource = await readFile(templatePath, 'utf8');

  //* 3. Compile the template with Handlebars
  const template = Handlebars.compile(templateSource);

  //* 4. Extract data from the booking object
  const { bookingNumber, temple, timeSlot, primaryDevoteeDetails, otherDevotees = [], offerings = [], promotionalKitCount, promotionalKitCost, offeringsTotal } = bookingData;

  //* 5. Populate offerings with offering names
  const populatedOfferings = await Promise.all(offerings.map(async (offering) => {
    try {
      // Import the Offering model if not already imported
      const Offering = require('../models/Offering');
      const offeringDetails = await Offering.findById(offering.offering);

      return {
        quantity: offering.quantity,
        amount: offering.amount,
        subtotal: offering.subtotal,
        offeringName: offeringDetails ? offeringDetails.name : 'Unknown Offering'
      };
    } catch (error) {
      // Error handled silently, returning default value
      return {
        ...offering,
        offeringName: 'Unknown Offering'
      };
    }
  }));

  //* 6. Format date strings to YYYY-MM-DD
  const formatToYMD = (dateStr) => {
    const d = new Date(dateStr);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  };
  
  //* 7. Merge pricing data by date
  const mergedPricing = bookingDates.map(dateStr => {
    const date = formatToYMD(dateStr); // convert to 'YYYY-MM-DD' in local time
  
    const findInList = (list) => {
      return list.find(item => {
        const itemDate = formatToYMD(item.date);

        return itemDate === date;
      }) || { price: 0, quantity: 0, subtotal: 0 };
    };

    const individual = findInList(individualPrice);
    const couple = findInList(couplePrice);
    const family = findInList(familyPrice);
    const dayTotal = parseFloat(individual.subtotal || 0) + parseFloat(couple.subtotal || 0) + parseFloat(family.subtotal || 0);
  
    return {
      date,
      individual,
      couple,
      family,
      dayTotal
    };
  });

  const pricingTotal = mergedPricing.reduce((sum, curr) => sum + curr.dayTotal, 0);

  let logoBase64 = '';

  try {
    const logoPath = path.join(__dirname, '../../public/images/logo.png');
    const logoBuffer = await readFile(logoPath);

    logoBase64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;
  } catch (error) {
    console.log('Error reading logo file:', error);
  }

  //* 8. Prepare the data for the template
  const templateData = {
    bookingId: bookingNumber,
    bookingType,
    templeName: temple.name,
    bookingDates: bookingDates.join(', '),
    timeSlot: `${timeSlot.startTime} - ${timeSlot.endTime}`,
    numberOfPeople: 1 + otherDevotees.length,
    devoteeName: primaryDevoteeDetails.fullName,
    devoteePhone: primaryDevoteeDetails.phoneNumber,
    otherDevotees,
    supportContact: process.env.SUPPORT_CONTACT,
    contactDetails: process.env.CONTACT_DETAILS,
    offerings: populatedOfferings,
    offeringsTotal,
    promotionalKitCount,
    promotionalKitCost,
    offeringsLength: offerings.length,
    promotionalKitTotalAmount: promotionalKitCount * promotionalKitCost,
    discountAmount: bookingData.discountAmount,
    totalAmount: bookingData.totalAmount,
    pricingDetails: mergedPricing,
    pricingTotal,
    logo: logoBase64
  };
  
  //* 9. Generate the HTML from the template and data
  const htmlTemplate = template(templateData);

  //* 10. Generate PDF from HTML
  const options = {
    format: 'A4',
    margin: { top: '20px', right: '20px', bottom: '20px', left: '20px' }
  };

  const file = { content: htmlTemplate };

  const pdfBuffer = await htmlPdf.generatePdf(file, options);

  //* 11. Write the PDF to a temporary file
  fs.writeFileSync(tempFilePath, pdfBuffer);

  //* 12. Upload the PDF to S3
  const s3Key = `booking-confirmations/${bookingNumber}.pdf`;

  await s3.putObject({
    Bucket: process.env.AWS_S3_BUCKET,
    Key: s3Key,
    Body: fs.createReadStream(tempFilePath),
    ContentType: 'application/pdf'
  }).promise();

  //* 13. Delete the temporary file
  fs.unlinkSync(tempFilePath);

  return s3Key;
};

module.exports = {
  generateCommissionInvoice,
  generateTicketDetails,
  generateOrderInvoice,
  generateBookingConfirmationPDF
};
