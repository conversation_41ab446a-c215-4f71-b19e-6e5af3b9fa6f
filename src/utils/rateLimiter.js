const rateLimit = require('express-rate-limit');
const { messages } = require('../messages');
const { getClientIp } = require('./auditLogger');

const rateLimiter = rateLimit({
  windowMs: 60 * 1000 * (process.env.RATE_LIMIT_WINDOW_MS ?? 60), // 60 minutes
  max: process.env.RATE_LIMIT_MAX_VALUE ?? 5, // limit each IP to 100 requests per windowMs
  message: messages.TOO_MANY_REQUESTS ?? 'Too many requests. Please try again later.',
  keyGenerator: (req) => getClientIp(req)
}); 

module.exports = {
  rateLimiter
};