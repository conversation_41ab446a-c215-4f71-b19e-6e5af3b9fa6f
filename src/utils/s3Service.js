const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

// Configure AWS
AWS.config.update({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  signatureVersion: 'v4',
  apiVersion: '2006-03-01'
});

const s3 = new AWS.S3();

/**
 * Generates a pre-signed URL for S3 upload
 * @param {string} fileType - MIME type of the file
 * @param {string} folder - S3 folder path (e.g., 'banners', 'profiles')
 * @returns {Promise<{presignedUrl: string, fileKey: string}>}
 */
const getPresignedUrl = async (extension, folder, userId) => {
  // Validate file type
  const allowedExtensions = [ 'jpeg', 'jpg', 'png', 'pdf', 'mp4', 'mov', 'flv', 'hevc', 'webm', 'mpeg', 'webp', 'mp3', 'srt' ];

  if (!allowedExtensions.includes(extension)) {
    throw new Error('Invalid file extension. Only JPEG, JPG, PNG, PDF, MP4, MOV, HEVC, WEBM, MPEG, WEBP, MP3, FLV and SRT are allowed.');
  }

  const key = `${folder}/${userId ? userId + '/' + Date.now() : uuidv4() }.${extension}`;
  
  let contentType;

  switch (extension) {
    case 'jpeg':
      contentType = 'image/jpeg';
      break;
    case 'jpg':
      contentType = 'image/jpeg';
      break;
    case 'png':
      contentType = 'image/png';
      break;
    case 'pdf':
      contentType = 'application/pdf';
      break;
    case 'mp4':
      contentType = 'video/mp4';
      break;
    case 'mov':
      contentType = 'video/quicktime';
      break;
    case 'hevc':
      contentType = 'video/mp4';
      break;
    case 'flv':
      contentType = 'video/x-flv';
      break;
    case 'webm':
      contentType = 'video/webm';
      break;
    case 'webp':
      contentType = 'image/webp';
      break;
    case 'mpeg':
      contentType = 'video/mpeg';
      break;
    case 'mp3':
      contentType = 'audio/mp3';
      break;
    case 'srt':
      contentType = 'application/x-subrip';
      break;
  }

  const params = {
    Bucket: process.env.AWS_S3_BUCKET,
    Key: key,
    ContentType: contentType,
    Expires: 300 // URL expires in 5 minutes
  };

  try {
    const uploadUrl = s3.getSignedUrl('putObject', params);

    return {
      uploadUrl,
      key,
      contentType
    };
  } catch (error) {
    throw new Error(`Failed to generate pre-signed URL: ${error.message}`);
  }
};

const deleteFile = async (key) => {
  const params = {
    Bucket: process.env.AWS_S3_BUCKET,
    Key: key
  };

  try {
    await s3.deleteObject(params).promise();
  } catch (error) {
    throw new Error(`Failed to delete file from S3: ${error.message}`);
  }
};

module.exports = {
  getPresignedUrl,
  deleteFile
};
