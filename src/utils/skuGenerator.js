/**
 * Generate a unique SKU based on product name and a prefix
 * @param {string} prefix - Prefix for the SKU (e.g., 'P' for product, 'V' for variant)
 * @param {string} name - Product or variant name
 * @returns {string} Generated SKU
 */
const generateSKU = (prefix, name) => {
  // Convert name to uppercase and remove special characters
  const cleanName = name.toUpperCase().replace(/[^A-Z0-9]/g, '');
  
  // Take first 3 characters of clean name (or less if name is shorter)
  const namePrefix = cleanName.substring(0, Math.min(3, cleanName.length));
  
  // Generate a timestamp-based suffix
  const timestamp = Date.now().toString();
  
  // Combine parts to create SKU
  return `${prefix}-${namePrefix}-${timestamp}`;
};

module.exports = {
  generateSKU
};
