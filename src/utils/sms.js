const axios = require('axios');

const sendSMS = async (recipients, template) => {
  try {

    const options = {
      method: 'POST',
      url: 'https://control.msg91.com/api/v5/flow',
      headers: {
        authkey: process.env.MSG_91_SMS_API_KEY,
        accept: 'application/json',
        'content-type': 'application/json'
      },
      data: JSON.stringify({
        template_id: template,
        short_url: '0',
        recipients: recipients
      }),
    };

    const response = await axios.request(options);

    if (response?.status !== 200) {
      throw new Error(`Failed to send SMS: ${response?.statusText}`);
    }

    return response?.data;

  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error sending SMS:', error);
  }
};

module.exports = {
  sendSMS
};