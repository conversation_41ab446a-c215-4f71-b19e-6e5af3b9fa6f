/**
 * Utility to handle MongoDB transactions conditionally based on environment
 */
const mongoose = require('mongoose');

// Check if transactions are enabled in environment
const useTransactions = process.env.MONGODB_USE_TRANSACTIONS === 'true';

/**
 * Executes a function within a transaction if transactions are enabled,
 * otherwise executes it normally
 * 
 * @param {Function} callback - Async function that receives session as parameter
 * @returns {Promise<any>} - Result of the callback function
 */
const withTransaction = async (callback) => {
  // If transactions are disabled, execute without transaction
  if (!useTransactions) {
    return await callback(null);
  }

  // Use transactions
  const session = await mongoose.startSession();

  session.startTransaction();

  try {
    const result = await callback(session);

    await session.commitTransaction();
    session.endSession();
    return result;
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    throw error;
  }
};

/**
 * Helper to conditionally add session to options object
 * 
 * @param {Object|null} session - MongoDB session or null
 * @returns {Object} - Options object with session if available
 */
const sessionOptions = (session) => {
  return session ? { session } : {};
};

module.exports = {
  withTransaction,
  sessionOptions,
  useTransactions
};
