const Language = require('../models/Language');
const { translateText } = require('./translator');

const translateDataForStore = async (translatedFields, data) => {
  const languages = await Language.find();
  const languageCodes = languages.map(lang => lang.code);

  const translatedData = {};

  await Promise.all(translatedFields.map(async field => {
    if (data[field]) {
      const translations = await Promise.all(languageCodes.map(async code => {
        const translatedText = await translateText(data[field], code);

        return { code, translatedText };
      }));
  
      translatedData[field] = translations.reduce((acc, { code, translatedText }) => {
        acc[code] = translatedText;
        return acc;
      }, {});
    }
  }));

  return translatedData;
};

module.exports = {
  translateDataForStore
};