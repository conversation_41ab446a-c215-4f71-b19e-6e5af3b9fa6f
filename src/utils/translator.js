const { TranslationServiceClient } = require('@google-cloud/translate').v3;

const client = new TranslationServiceClient(
  {
    keyFilename: './service_account_key.json'
  }
);

const PROJECT_ID = process.env.GCP_PROJECT_ID;
const LOCATION = process.env.GCP_LOCATION;

const translateText = async (text, targetLanguage) => {
  const request = {
    parent: `projects/${PROJECT_ID}/locations/${LOCATION}`,
    contents: [ text ],
    mimeType: 'text/plain',
    targetLanguageCode: targetLanguage
  };

  const [ response ] = await client.translateText(request);

  return response.translations[0].translatedText;
};

module.exports = {
  translateText
};