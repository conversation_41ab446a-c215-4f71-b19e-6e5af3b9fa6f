const { TranslationServiceClient } = require('@google-cloud/translate').v3;

const client = new TranslationServiceClient(
  {
    keyFilename: './service_account_key.json'
  }
);

const PROJECT_ID = process.env.GCP_PROJECT_ID;
const LOCATION = process.env.GCP_LOCATION;

const translateText = async (text, targetLanguage) => {
  const request = {
    parent: `projects/${PROJECT_ID}/locations/${LOCATION}`,
    contents: [ text ],
    mimeType: 'text/plain',
    targetLanguageCode: targetLanguage
  };

  const [ response ] = await client.translateText(request);

  if (targetLanguage === 'hi' && response.translations[0].translatedText === text) {
    return forceTranslateWord(text);
  }

  return response.translations[0].translatedText;
};

const forceTranslateWord = async (text) => {

  const contextSentence = `Translate ${text} to Hindi`;
  const translated = await translateText(contextSentence, 'hi');

  // Split the text "का हिंदी में अनुवाद करें" away from the translated text
  const translatedText = translated.split(' का हिंदी में अनुवाद करें')[0];

  return translatedText;

}

module.exports = {
  translateText
};