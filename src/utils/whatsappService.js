const axios = require('axios');

const sendWhatsAppMessage = async (recipients, template, data = null) => {
  try {
    const options = {
      method: 'POST',
      url: 'https://control.msg91.com/api/v5/whatsapp/whatsapp-outbound-message/bulk/',
      headers: {
        authkey: process.env.MSG_91_WHATSAPP_API_KEY,
        accept: 'application/json',
        'content-type': 'application/json'
      },
      data: JSON.stringify({
        integrated_number: process.env.MSG_91_WHATSAPP_NUMBER,
        content_type: 'template',
        payload: {
          type: 'template',
          template: {
            name: template,
            language: {
              code: 'en',
              policy: 'deterministic'
            },
            to_and_components: recipients.map(recipient => ({
              to: [ recipient ],
              components: data ?? {}
            }))
          },
          messaging_product: 'whatsapp'
        }
      }),
    };

    const response = await axios.request(options);

    if (response?.status !== 200) {
      throw new Error(`Failed to send SMS: ${response?.statusText}`);
    }

    return response?.data;

  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error sending whatsapp message:', error);
  }
};

module.exports = {
  sendWhatsAppMessage
};