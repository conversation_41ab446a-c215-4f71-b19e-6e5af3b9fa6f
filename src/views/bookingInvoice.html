<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Booking Confirmation</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      color: #333;
      line-height: 1.6;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding-top: 10px;
      padding-bottom: 0;
    }
    .header {
      display: flex;
      text-align: center;
      border-bottom: 2px solid black;
      padding: 0;
    }
    .logo-container {
      text-align: left;
      padding-left: 0;
    }
    .logo-container img {
      max-width: 200px;
      height: auto;
      display: block;
      padding: 0;
      margin: 0;
      position: relative;
      left: -50px;
      top: -30px;
    }
    .header-text {
      text-align: center;
      margin-left: 35px;
    }
    h1 {
      color: black;
      margin: 0;
      font-size: 24px;
    }
    .booking-details {
      margin-bottom: 30px;
    }
    .booking-details h2 {
      color: black;
      font-size: 18px;
      margin-top: -30px;
      padding-bottom: 5px;
    }
    .booking-details h3 {
      color: black;
      font-size: 18px;
      margin-bottom: 10px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    table, th, td {
      border: 1px solid #ddd;
    }
    th, td {
      padding: 5px;
      /* text-align: left; */
      page-break-inside: avoid !important;
      break-inside: avoid;
    }
    th {
      background-color: #f2f2f2;
    }
    .footer {
      margin-top: 50px;
      font-size: 12px;
      text-align: center;
      color: #666;
      border-top: 1px solid #ddd;
      padding-top: 20px;
    }
    .important-note {
      background-color: #f8f8f8;
      padding: 15px;
      /* border-left: 4px solid #0066cc; */
      border-left: 4px solid black;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo-container">
        {{#if logo}}
        <img src="{{logo}}" alt="Ek Ishwar Logo">
        {{/if}}
      </div>
      <div class="header-text">
        <h1>Ek Ishwar</h1>
        <h2>Booking Confirmation</h2>
        <p>Booking ID: {{bookingId}}</p>
      </div>
    </div>

    <div class="booking-details">
      <h2 style="text-align: center;">TAX INVOICE</h2>
      <table>
        <tr>
          <td ><strong>Invoice Date:</strong> {{invoiceDate}}</td>
          <td><strong>Invoice Number:</strong> {{invoiceNumber}}</td>
        </tr>
        <tr>
          <td style="width: 50%; vertical-align: top;">
            <strong>Bill From:</strong><br>
            {{templeName}}<br>
            {{#if templeCity}}
            {{templeCity}}<br>
            {{/if}}
            {{#if templeState}}
            {{templeState}} <br>
            {{/if}}
          </td>
          <td style="width: 50%; vertical-align: top;">
            <strong>Bill To:</strong><br>
            {{devoteeName}}<br>
            {{#if devoteeAddressLine1}}
            {{devoteeAddressLine1}}
            {{/if}}
            {{#if devoteeAddressLine2}}
            , {{devoteeAddressLine2}}
            {{/if}}
            {{#if devoteeCity}}
            , {{devoteeCity}}
            {{/if}}
            {{#if devoteeState}}
            , {{devoteeState}}
            {{/if}}
            {{#if devoteePostalCode}}
            , {{devoteePostalCode}} <br>
            {{/if}}
            <strong>Phone:</strong> {{devoteePhone}}<br>
          </td>
        </tr>
      </table>
    </div>    

    <div class="booking-details">
      <h3>{{bookingType}} Details</h3>
      <table>
        <tr style="text-align: left;">
          <th>Temple Name</th>
          <td>{{templeName}}</td>
        </tr>
        <tr style="text-align: left;">
          <th>{{bookingType}} Date</th>
          <td>{{bookingDates}}</td>
        </tr>
        <tr style="text-align: left;">
          <th>Time Slot</th>
          <td>{{timeSlot}}</td>
        </tr>
        <tr style="text-align: left;">
          <th>Number of Devotees</th>
          <td>{{numberOfPeople}}</td>
        </tr>
      </table>
    </div>

    {{#if pricingDetails}}
    <div class="booking-details">
      <h3>Pricing Breakdown by Date</h3>
      <table>
        <thead style="text-align: center;">
          <tr>
            <!-- <th>Date</th> -->
            <th>Service Name</th>
            <th>Service Code</th>
            <th>Booking Type</th>
            <th>Price Per Booking Type</th>
            <th>No. Of Devotee</th>
            {{#if isEvent}}
              <th>Taxable Value (INR)</th>
              {{#if isSameCountry}}
                {{#if isSameState}}
                  <th>CGST (%)</th>
                  <th>CGST Charges</th>
                  <th>SGST (%)</th>
                  <th>SGST Charges</th>
                {{else}}
                  <th>GST (%)</th>
                  <th>GST Charges</th>
                {{/if}}
              {{else}}
                <th>GST (%)</th>
                <th>GST Charges</th>
              {{/if}}
              <th>Total Amount (INR)</th>
            {{/if}}

            {{#unless isEvent}}
            <th>Taxable Value (INR)</th>
            {{/unless}}
          </tr>
        </thead>
        <tbody style="text-align: center;">
          {{#each pricingDetails}}
            {{#if this.individual.quantity}}
            <tr>
              <!-- <td rowspan="{{../rowSpan}}"><strong>{{this.date}}</strong></td> -->
              <td>{{../serviceName}}</td>
              <td>{{../serviceCode}}</td>
              <td>Individual</td>
              <td>₹{{this.individual.price}}</td>
              <td>{{this.individual.quantity}}</td>
              {{#if ../isEvent}}
                <td>₹{{this.individual.taxableValue}}</td>
                {{#if ../isSameCountry}}
                  {{#if ../isSameState}}
                    <td>{{this.individual.cgstPercentage}}%</td>
                    <td>₹{{this.individual.cgstCharges}}</td>
                    <td>{{this.individual.sgstPercentage}}%</td>
                    <td>₹{{this.individual.sgstCharges}}</td>
                  {{else}}
                    <td>{{this.individual.gstPercentage}}%</td>
                    <td>₹{{this.individual.gstCharges}}</td>
                  {{/if}}
                {{else}}
                  <td>{{this.individual.gstPercentage}}%</td>
                  <td>₹{{this.individual.gstCharges}}</td>
                {{/if}}
                <td>₹{{this.individual.totalAmount}}</td>
              {{/if}}

              {{#unless ../isEvent}}
                <td>₹{{this.individual.taxableValue}}</td>
              {{/unless}}
            </tr>
            {{/if}}

            {{#if this.couple.quantity}}
            <tr>
              <td>{{../serviceName}}</td>
              <td>{{../serviceCode}}</td>
              <td>Couple</td>
              <td>₹{{this.couple.price}}</td>
              <td>{{this.couple.quantity}}</td>

              {{#if ../isEvent}}
                <td>₹{{this.couple.taxableValue}}</td>
                {{#if ../isSameCountry}}
                  {{#if ../isSameState}}
                    <td>{{this.couple.cgstPercentage}}%</td>
                    <td>₹{{this.couple.cgstCharges}}</td>
                    <td>{{this.couple.sgstPercentage}}%</td>
                    <td>₹{{this.couple.sgstCharges}}</td>
                  {{else}}
                    <td>{{this.couple.gstPercentage}}%</td>
                    <td>₹{{this.couple.gstCharges}}</td>
                  {{/if}}
                {{else}}
                  <td>{{this.couple.gstPercentage}}%</td>
                  <td>₹{{this.couple.gstCharges}}</td>
                {{/if}}

                <td>₹{{this.couple.totalAmount}}</td>
              {{/if}}

              {{#unless ../isEvent}}
                <td>₹{{this.couple.taxableValue}}</td>
              {{/unless}}
            </tr>
            {{/if}}

            {{#if this.family.quantity}}
            <tr>
              <td>{{../serviceName}}</td>
              <td>{{../serviceCode}}</td>
              <td>Family</td>
              <td>₹{{this.family.price}}</td>
              <td>{{this.family.quantity}}</td>
              {{#if ../isEvent}}
                <td>₹{{this.family.taxableValue}}</td>
                {{#if ../isSameCountry}}
                  {{#if ../isSameState}}
                    <td>{{this.family.cgstPercentage}}%</td>
                    <td>₹{{this.family.cgstCharges}}</td>
                    <td>{{this.family.sgstPercentage}}%</td>
                    <td>₹{{this.family.sgstCharges}}</td>
                  {{else}}
                    <td>{{this.family.gstPercentage}}%</td>
                    <td>₹{{this.family.gstCharges}}</td>
                  {{/if}}
                {{else}}
                  <td>{{this.family.gstPercentage}}%</td>
                  <td>₹{{this.family.gstCharges}}</td>
                {{/if}}
                <td>₹{{this.family.totalAmount}}</td>
              {{/if}}

              {{#unless ../isEvent}}
                <td>₹{{this.family.taxableValue}}</td>
              {{/unless}}
            </tr>
            {{/if}}

            <!-- <tr>
              <td><strong>Day Total</strong></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td><strong>₹{{this.totalTaxableValues}}</strong></td>
              <td></td>
              <td><strong>₹{{this.totalGstCharges}}</strong></td>
              <td><strong>₹{{this.dayTotal}}</strong></td>
            </tr> -->
          {{/each}}
          <tr>
            <td><strong>Total</strong></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            {{#if isEvent}}
              <td><strong>₹{{totalTaxableAmount}}</strong></td>
              {{#if isSameCountry}}
                {{#if isSameState}}
                  <td></td> 
                  <td><strong>₹{{totalCgstCharges}}</strong></td>
                  <td></td> 
                  <td><strong>₹{{totalSgstCharges}}</strong></td>
                {{else}}
                  <td></td> 
                  <td><strong>₹{{totalIgstCharges}}</strong></td>
                {{/if}}
              {{else}}
                <td></td>
                <td><strong>₹{{totalIgstCharges}}</strong></td>
              {{/if}}
              <td><strong>₹{{totalAmount}}</strong></td>
            {{/if}}
            
            {{#unless isEvent}}
            <td><strong>₹{{totalTaxableAmount}}</strong></td>
            {{/unless}}
          </tr>
        </tbody>
      </table>
      <table>
        <thead style="text-align: center;">
          {{#if promotionalKitCount}}
          <tr>
            <th>Service Name</th>
            <th>Service Code</th>
            <th>Booking Type</th>
            <th>Price Per Puja Box</th>
            <th>No. Of Puja Box</th>
            <th>Taxable Value (INR)</th>
            <th>GST (%)</th>
            <th>GST Charges</th>
            <th>Total Amount (INR)</th>
          </tr>
          {{else}}
            {{#if shippingCharges}}
            <tr>
              <th>Service Name</th>
              <th>Service Code</th>
              <th>Booking Type</th>
              <th>Price Per Booking Type</th>
              <th>No. Of Puja Box</th>
              <th>Taxable Value (INR)</th>
              <th>GST (%)</th>
              <th>GST Charges</th>
              <th>Total Amount (INR)</th>
            </tr>
            {{/if}}
          {{/if}}
        </thead>
        <tbody style="text-align: center;">
          {{#if promotionalKitCount}}
          <tr>
            <td>Puja Box</td>
            <td>{{pujaBoxServiceCode}}</td>
            <td>-</td>
            <td>₹{{promotionalKitCost}}</td> 
            <td>{{promotionalKitCount}}</td>
            <td>₹{{promotionalKitTaxableValue}}</td>
            <td>{{promotionalKitGstPercentage}}%</td>
            <td>₹{{promotionalKitGstCharges}}</td>
            <td>₹{{promotionalKitTotalAmount}}</td>
          </tr>
          {{/if}}

          {{#if shippingCharges}}
          <tr>
            <td>Shipping Charges </td>
            <td>-</td>
            <td>-</td>
            <td>₹{{shippingTaxablePrice}}</td> 
            <td>-</td>
            <td>₹{{shippingTaxablePrice}}</td>
            <td>{{shippingGstPercentage}}%</td>
            <td>₹{{shippingGstCharges}}</td>
            <td>₹{{shippingTotalAmount}}</td>
          </tr>
          {{/if}}

          <!-- {{#if promotionalKitCount}}
          <tr>
            <td><strong>Total</strong></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td><strong>₹{{totalTaxableAmount}}</strong></td>
            <td></td>
            <td><strong>₹{{totalGstCharges}}</strong></td>
            <td><strong>₹{{totalAmount}}</strong></td>
          </tr>
          {{else}}
            {{#if shippingCharges}}
              <tr>
                <td><strong>Total</strong></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td><strong>₹{{totalTaxableAmount}}</strong></td>
                <td></td>
                <td><strong>₹{{totalGstCharges}}</strong></td>
                <td><strong>₹{{totalAmount}}</strong></td>
              </tr>
            {{/if}}
          {{/if}} -->

        </tbody>
      </table>
    </div>
    {{/if}}

    {{#if offerings}}
    <div class="booking-details">
      <h3>Offerings</h3>
      <table>
        <tr style="text-align: center;">
          <th>Offering Name</th>
          <th>Quantity</th>
          <th>Unit Cost</th>
          <th>Subtotal</th>
        </tr>
        {{#each offerings}}
        <tr style="text-align: center;">
          <td>{{this.offeringName}}</td>
          <td>{{this.quantity}}</td>
          <td>₹{{this.amount}}</td>
          <td>₹{{this.subtotal}}</td>
        </tr>
        {{/each}}
        <tr>
          <th colspan="3" style="text-align: center;">Offerings Total</th>
          <td style="text-align: center;"><strong>₹{{offeringsTotal}}</strong></td>
        </tr>
      </table>
    </div>
    {{/if}}

    {{#if discountAmount}}
    <div class="booking-details">
      <table style="page-break-inside: avoid !important; break-inside: avoid;">
        <tr>
          <th style="text-align: left; width: 80%">Discount Amount</th>
          <td style="text-align: left; width: 20%"><strong>₹{{discountAmount}}</strong></td>
        </tr>
        {{#unless isEvent}}
          {{#if isSameCountry}}
            {{#if isSameState}}
              <tr>
                <th style="text-align: left; width: 80%">Total CGST Charges</th>
                <td style="text-align: left; width: 20%"><strong>₹{{finalCgstCharges}}</strong></td>
              </tr>
              <tr>
                <th style="text-align: left; width: 80%">Total SGST Charges</th>
                <td style="text-align: left; width: 20%"><strong>₹{{finalSgstCharges}}</strong></td>
              </tr>
            {{else}}
              <tr>
                <th style="text-align: left; width: 80%">Total GST Charges</th>
                <td style="text-align: left; width: 20%"><strong>₹{{finalGstCharges}}</strong></td>
              </tr>
            {{/if}}
          {{else}}
            <tr>
              <th style="text-align: left; width: 80%">Total GST Charges</th>
              <td style="text-align: left; width: 20%"><strong>₹{{finalGstCharges}}</strong></td>
            </tr>
          {{/if}}  
        {{/unless}} 

        <tr>
          <th style="text-align: left; width: 80%">Total Amount</th>
          <td style="text-align: left; width: 20%"><strong>₹{{finalAmount}}</strong></td>
        </tr>
      </table>
    </div>
    {{/if}}

    {{#unless discountAmount}}
    <div class="booking-details">
      <table style="page-break-inside: avoid !important; break-inside: avoid;">
        {{#unless isEvent}}
          {{#if isSameCountry}}
            {{#if isSameState}}
              <tr>
                <th style="text-align: left; width: 80%">Total CGST Charges</th>
                <td style="text-align: left; width: 20%"><strong>₹{{finalCgstCharges}}</strong></td>
              </tr>
              <tr>
                <th style="text-align: left; width: 80%">Total SGST Charges</th>
                <td style="text-align: left; width: 20%"><strong>₹{{finalSgstCharges}}</strong></td>
              </tr>
            {{else}}
              <tr>
                <th style="text-align: left; width: 80%">Total GST Charges</th>
                <td style="text-align: left; width: 20%"><strong>₹{{finalGstCharges}}</strong></td>
              </tr>
            {{/if}}
          {{else}}
            <tr>
              <th style="text-align: left; width: 80%">Total GST Charges</th>
              <td style="text-align: left; width: 20%"><strong>₹{{finalGstCharges}}</strong></td>
            </tr>
          {{/if}}  
        {{/unless}}
        <tr>
          <th style="text-align: left; width: 80%">Total Amount</th>
          <td style="text-align: left; width: 20%"><strong>₹{{finalAmount}}</strong></td>
        </tr>
      </table>
    </div>
    {{/unless}} 

    <div class="booking-details">
      <h3>Primary Devotee Details</h3>
      <table style="page-break-inside: avoid !important; break-inside: avoid;">
        <tr>
          <th style="text-align: left;">Name</th>
          <td>{{devoteeName}}</td>
        </tr>
        <tr>
          <th style="text-align: left;">Phone</th>
          <td>{{devoteePhone}}</td>
        </tr>
      </table>
    </div>

    <div class="important-note">
      <p><strong>Important:</strong> Please carry a printed copy of this confirmation and your Aadhar Card during your visit for hassle-free entry. Please arrive 30 minutes before your scheduled time.</p>
    </div>

    <div class="footer">
      <p>For any queries, please contact us at {{supportContact}}</p>
      <p>Ek Ishwar LLP | {{contactDetails}}</p>
      <p>This is a computer-generated document. No signature is required.</p>
    </div>
  </div>
</body>
</html>
