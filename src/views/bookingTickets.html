<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Ek <PERSON> Booking Details</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #fff;
            color: #333;
        }

        .container {
            margin: auto;
            background: #fff;
            padding: 16px;
        }

        .header {
            position: relative;
            /* background-color: #aeecc2; */
            border-radius: 8px solid #aeecc2;
            text-align: center;
            padding: 12px;
            margin-bottom: 20px;
        }

        .logo-container {
            position: absolute;
            left: 0px;
            top: 28%;
            transform: translateY(-50%);
        }

        .header-text {
            display: inline-block;
        }

        .header h2 {
            color: #2e7d32;
            margin: 4px 0;
        }

        .booking-id {
            font-size: 14px;
            color: #555;
        }

        .section-title {
            font-weight: bold;
            margin: 20px 0 10px;
            border-left: 4px solid #e53935;
            padding-left: 8px;
            font-size: 16px;
        }

        .booking-details {
            display: flex;
            flex-direction: row;
            gap: 20px;
            margin-bottom: 16px;
        }

        .booking-details img {
            width: 100px;
            height: auto;
            border-radius: 8px;
            object-fit: cover;
        }

        .details-text {
            flex: 1;
        }

        .details-text h3 {
            color: #d32f2f;
            margin: 0;
        }

        .details-text p {
            margin: 4px 0;
            font-size: 14px;
        }

        .ticket {
            display: flex;
            background-color: #fff7f0;
            border-radius: 12px;
            padding: 22px;
            margin: 22px 0;
            align-items: center;
            gap: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
        }

        .ticket img {
            width: 80px;
            height: 80px;
            object-fit: contain;
        }

        .ticket-details {
            font-size: 14px;
        }

        .guidelines {
            margin-top: 20px;
        }

        .footer {
            text-align: center;
            font-size: 13px;
            color: #777;
            margin-top: 20px;
        }

        .contact {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .contact div {
            background-color: #fff3e0;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 13px;
        }

        @media (max-width: 450px) {
            .booking-details {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            .booking-details img {
                width: 100%;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <div class="logo-container">
                {{#if logo}}
                <img src="{{logo}}" alt="Ek Ishwar Logo" style="width: auto; height: 100px;">
                {{/if}}
            </div>
            <div class="header-text">
                <h2>Booking Confirmed</h2>
                <p>Congratulations! Your {{ entityType }} is successfully booked.</p>
                <div class="booking-id">Booking ID: {{bookingNumber}}</div>
            </div>
        </div>

        <div class="section-title">Booking Details</div>
        <div class="booking-details">
            <img src="{{entityImage}}" alt="Temple or Event" />
            <div class="details-text">
                <h3>{{entityName}}</h3>
                <p><strong>Date:</strong> {{bookingDate}}</p>
                <p><strong>Time:</strong> {{startTime}} - {{endTime}}</p>
                <p><strong>Temple:</strong> {{templeName}}</p>
                <p><strong>Amount Paid:</strong> ₹{{bookingAmount}}</p>
            </div>
        </div>

        <div class="section-title">E-Tickets</div>

        {{#each attendees}}
        <div class="ticket">
            <img src="{{this.qrCodeUrl}}" alt="QR Code" />
            <div class="ticket-details">
                <strong>{{this.name}}, {{this.gender}}</strong><br />
                {{../entityName}}<br />
                {{../bookingDate}}<br />
                {{../startTime}} - {{../endTime}}
            </div>
        </div>

        {{/each}}

        <div class="section-title">Guidelines</div>
        <div class="guidelines">
            {{guidelines}}
        </div>

        <div class="footer">
            You can access your booking from the 'My Bookings' tab.<br />
            Confirmation will be sent via email or SMS.
            <div class="contact">
                <div>📧 {{supportContact}}</div>
                <div>📞 {{contactDetails}}</div>
            </div>
        </div>
    </div>
</body>

</html>