<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Ek <PERSON>hwar Invoice</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #fff;
            line-height: 1.5;
        }
        .invoice-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            box-sizing: border-box;
        }
        .logo {
            text-align: center;
            margin-bottom: 10px;
            font-size: 32px;
            font-weight: 300;
            letter-spacing: 3px;
            text-transform: uppercase;
        }
        .invoice-label {
            text-align: center;
            color: #e67e22;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 40px;
        }
        .billing-container {
            background-color: #f9f9f9;
            padding: 30px;
            margin-bottom: 30px;
        }
        .billing-title {
            font-weight: bold;
            margin-bottom: 15px;
        }
        .billing-details {
            font-size: 14px;
        }
        .billing-grid {
            display: flex;
            justify-content: space-between;
        }
        .billing-left {
            width: 50%;
        }
        .billing-right {
            width: 40%;
        }
        .billing-row {
            display: flex;
            margin-bottom: 10px;
        }
        .billing-label {
            width: 80px;
            font-weight: bold;
            font-size: 14px;
        }
        .billing-value {
            font-size: 14px;
        }
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 40px 0;
        }
        .invoice-table th {
            border-bottom: 1px solid #ddd;
            padding: 10px;
            text-align: left;
            font-weight: normal;
            color: #666;
            font-size: 14px;
        }
        .invoice-table td {
            padding: 15px 10px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        .invoice-table .qty {
            text-align: center;
        }
        .invoice-table .price, .invoice-table .total {
            text-align: right;
        }
        .summary-container {
            margin-top: 20px;
        }
        .summary-row {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 5px;
        }
        .summary-label {
            width: 150px;
            text-align: right;
            padding-right: 20px;
            font-size: 14px;
        }
        .summary-value {
            width: 100px;
            text-align: right;
            font-size: 14px;
        }
        .summary-row.total {
            font-weight: bold;
            font-size: 16px;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
        }
        .summary-row.total .summary-label,
        .summary-row.total .summary-value {
            font-size: 16px;
        }
        .invoice-footer {
            margin-top: 80px;
            text-align: center;
            font-size: 13px;
            color: #666;
        }
        .footer-thanks {
            margin-bottom: 20px;
        }
        .footer-address {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="logo">EK ISHWAR</div>
        <div class="invoice-label">INVOICE</div>

        <div class="billing-container">
            <div class="billing-grid">
                <div class="billing-left">
                    <div class="billing-title">Billed To:</div>
                    <div class="billing-details">
                        {{vendorDetails.businessName}}<br>
                        {{vendorDetails.address}}<br>
                        <strong>GSTIN:</strong> {{vendorDetails.gstNumber}}
                    </div>
                </div>
                <div class="billing-right">
                    <div class="billing-row">
                        <div class="billing-label">Invoice #:</div>
                        <div class="billing-value">{{invoiceNumber}}</div>
                    </div>
                    <div class="billing-row">
                        <div class="billing-label">Order #:</div>
                        <div class="billing-value">{{orderNumber}}</div>
                    </div>
                    <div class="billing-row">
                        <div class="billing-label">Date:</div>
                        <div class="billing-value">{{invoiceDate}}</div>
                    </div>
                </div>
            </div>
        </div>

        <table class="invoice-table">
            <thead>
                <tr>
                    <th>Item</th>
                    <th>Product Code</th>
                    <th>HSN</th>
                    <th class="price">Unit Price</th>
                    <th class="qty">Qty</th>
                    <th class="price">Taxable Price</th>
                    <th class="price">GST %</th>
                    <th class="price">GST Amount</th>
                    <th class="total">Total</th>
                </tr>
            </thead>
            <tbody>
                {{#each orderItems}}
                <tr>
                    <td>{{this.name}}</td>
                    <td>{{this.productCode}}</td>
                    <td>{{this.hsn}}</td>
                    <td class="price">₹{{this.taxablePrice}}</td>
                    <td class="qty">{{this.quantity}}</td>
                    <td class="price">₹{{this.subTotalTaxablePrice}}</td>
                    <td class="price">{{this.gstPercentage}}%</td>
                    <td class="price">₹{{this.subTotalGstPrice}}</td>
                    <td class="total">₹{{this.subtotal}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>

        <div class="summary-container">
            <div class="summary-row">
                <div class="summary-label">Subtotal</div>
                <div class="summary-value">₹{{totalTaxablePrice}}</div>
            </div>
            <div class="summary-row">
                <div class="summary-label">GST</div>
                <div class="summary-value">₹{{totalGstPrice}}</div>
            </div>
            <div class="summary-row total">
                <div class="summary-label">Total</div>
                <div class="summary-value">₹{{totalAmount}}</div>
            </div>
        </div>
    </div>
    <div class="invoice-footer">
        <div class="footer-thanks">Thank you for your business with {{platformDetails.businessName}}</div>
        <div class="footer-address">
            Address: {{platformDetails.address}} | Email: <EMAIL> | GSTIN: {{platformDetails.gstNumber}}
        </div>
    </div>
</body>
</html>
