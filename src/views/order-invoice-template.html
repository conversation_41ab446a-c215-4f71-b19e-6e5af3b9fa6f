<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Ek <PERSON> Order Invoice</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #fff;
            line-height: 1.5;
        }
        .invoice-container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            box-sizing: border-box;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }
        .header-left {
            flex: 1;
            text-align: left;
        }
        .header-left img {
            height: 120px;
        }
        .header-center {
            flex: 1;
            text-align: center;
        }
        .header-right {
            flex: 1;
        }

        .logo {
            font-size: 32px;
            font-weight: 300;
            letter-spacing: 3px;
            text-transform: uppercase;
        }
        .invoice-label {
            color: #e67e22;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-top: 5px;
        }
        .billing-container {
            background-color: #f9f9f9;
            padding: 30px;
            margin-bottom: 30px;
        }
        .billing-title {
            font-weight: bold;
            margin-bottom: 15px;
        }
        .billing-details {
            font-size: 14px;
        }
        .billing-grid {
            display: flex;
            justify-content: space-between;
        }
        .billing-left {
            width: 50%;
        }
        .billing-right {
            width: 50%;
        }
        .billing-row {
            display: flex;
            margin-bottom: 10px;
        }
        .billing-label {
            width: 120px;
            font-weight: bold;
            font-size: 14px;
        }
        .billing-value {
            font-size: 14px;
        }
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin: 40px 0;
        }
        .invoice-table th {
            border-bottom: 1px solid #ddd;
            padding: 10px;
            text-align: left;
            font-weight: normal;
            color: #666;
            font-size: 14px;
        }
        .invoice-table td {
            padding: 15px 10px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        .invoice-table .qty {
            text-align: center;
        }
        .invoice-table .price, .invoice-table .total {
            text-align: right;
        }
        .summary-container {
            margin-left: auto;
            width: 300px;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .summary-row.total {
            font-weight: bold;
            border-bottom: 2px solid #333;
            border-top: 2px solid #333;
            padding: 15px 0;
            margin-top: 10px;
        }
        .summary-label {
            font-size: 14px;
        }
        .summary-value {
            font-size: 14px;
        }
        .invoice-footer {
            margin-top: 60px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .footer-thanks {
            font-size: 14px;
            margin-bottom: 10px;
        }
        .footer-address {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="header">
            <div class="header-left">
                {{#if logo}}
                <img src="{{logo}}" alt="Ek Ishwar Logo" style="width: auto;">
                {{/if}}
            </div>
            <div class="header-center">
                <div class="logo">EK ISHWAR</div>
                <div class="invoice-label">ORDER INVOICE</div>
            </div>
            <div class="header-right"></div>
        </div>
        <div class="billing-container">
            <div class="billing-grid">
                <div class="billing-left">
                    <div class="billing-title">Billed To:</div>
                    <div class="billing-details">
                        {{customerName}}<br>
                        {{billingAddress.addressLine1}}<br>
                        {{#if billingAddress.addressLine2}}{{billingAddress.addressLine2}}<br>{{/if}}
                        {{billingAddress.city}}, {{billingAddress.state}} {{billingAddress.postalCode}}<br>
                        {{billingAddress.country}}<br>
                        Phone: {{billingAddress.phone}}
                    </div>
                </div>
                <div class="billing-right">
                    <div class="billing-row">
                        <div class="billing-label">Invoice #:</div>
                        <div class="billing-value">{{orderNumber}}</div>
                    </div>
                    <div class="billing-row">
                        <div class="billing-label">Date:</div>
                        <div class="billing-value">{{orderDate}}</div>
                    </div>
                    <div class="billing-row">
                        <div class="billing-label">Payment Method:</div>
                        <div class="billing-value">{{paymentMethod}}</div>
                    </div>
                    <div class="billing-row">
                        <div class="billing-label">Payment Status:</div>
                        <div class="billing-value">{{paymentStatus}}</div>
                    </div>
                </div>
            </div>
        </div>

        <table class="invoice-table">
            <thead>
                <tr>
                    <th>Item</th>
                    <th class="qty">Qty</th>
                    <th class="price">Unit Price</th>
                    <th class="total">Total</th>
                </tr>
            </thead>
            <tbody>
                {{#each items}}
                <tr>
                    <td>{{this.name}}{{#if this.variantAttributes}} ({{this.variantAttributesText}}){{/if}}</td>
                    <td class="qty">{{this.quantity}}</td>
                    <td class="price">₹{{this.price}}</td>
                    <td class="total">₹{{this.subtotal}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>

        <div class="summary-container">
            <div class="summary-row">
                <div class="summary-label">Subtotal</div>
                <div class="summary-value">₹{{subtotal}}</div>
            </div>
            <div class="summary-row">
                <div class="summary-label">GST (18%)</div>
                <div class="summary-value">₹{{tax}}</div>
            </div>
            <div class="summary-row">
                <div class="summary-label">Shipping</div>
                <div class="summary-value">₹{{shippingCost}}</div>
            </div>
            {{#if discountAmount}}
            <div class="summary-row">
                <div class="summary-label">Discount</div>
                <div class="summary-value">-₹{{discountAmount}}</div>
            </div>
            {{/if}}
            <div class="summary-row total">
                <div class="summary-label">Total</div>
                <div class="summary-value">₹{{total}}</div>
            </div>
        </div>
    </div>
    <div class="invoice-footer">
        <div class="footer-thanks">Thank you for shopping with Ek Ishwar</div>
        <div class="footer-address">
            Address: Ek Ishwar Headquarters, India | Email: <EMAIL>
        </div>
    </div>
</body>
</html>
