<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>Ek <PERSON>r - Order Invoice</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #fff;
    }

    .container {
      width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      display: flex;
      align-items: center;
      border-bottom: 2px solid #000;
      padding-bottom: 15px;
      margin-bottom: 30px;
    }

    .logo-container {
      width: 33%;
    }

    .logo-container img {
      max-width: 150px;
      height: auto;
    }

    .header-center {
      width: 34%;
      text-align: center;
    }

    .header-center h1 {
      margin: 0;
      font-size: 24px;
      text-transform: uppercase;
      letter-spacing: 2px;
    }

    .header-center h2 {
      margin: 5px 0 0;
      font-size: 14px;
      color: #e67e22;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .invoice-meta {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px 40px;
      padding: 10px;
      border: 1px solid #ccc;
      margin-bottom: 30px;
      font-size: 14px;
    }

    .meta-row {
      display: flex;
      justify-content: flex-start;
      gap: 5px;
    }

    .meta-label {
      font-weight: bold;
      color: #000;
    }

    .meta-value {
      color: #555;
    }

    .bill-section {
      display: flex;
      border: 1px solid #ccc;
      margin-bottom: 30px;
      font-size: 14px;
    }

    .bill-box {
      width: 50%;
      padding: 10px;
      line-height: 1.2rem;
      box-sizing: border-box;
    }

    .bill-box + .bill-box {
      border-left: 1px solid #ccc;
    }

    .bill-box strong {
      display: block;
      margin-bottom: 5px;
      color: #000;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
      margin-bottom: 30px;
    }

    thead th {
      background-color: #f5f5f5;
      text-align: center;
      padding: 10px;
      border: 1px solid #ddd;
    }

    tbody td {
      padding: 10px;
      border: 1px solid #ddd;
      text-align: center;
    }

    .totals {
      width: 100%;
      max-width: 400px;
      margin-left: auto;
      font-size: 14px;
    }

    .shipping-details {
      display: flex;
      flex-direction: column;
      padding: 6px 0 10px;
      gap: 4px;
      margin-left: 10px;
      font-size: 13px;
    }

    .shipping-line {
      display: flex;
      justify-content: space-between;
      padding: 2px 0;
    }

    .total-shipping {
      font-weight: 600;
      border-top: 1px dashed #ccc;
      padding-top: 6px;
    }

    .totals-row {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      border-bottom: 1px solid #eee;
    }

    .totals-row.total {
      font-weight: bold;
      border-top: 2px solid #333;
      border-bottom: 2px solid #333;
      margin-top: 10px;
      padding: 12px 0;
    }

    .footer {
      clear: both;
      text-align: center;
      margin-top: 60px;
      font-size: 13px;
      color: #777;
    }

    .footer strong {
      color: #000;
    }
  </style>
</head>
<body>
  <div class="container">

    <!-- Header -->
    <div class="header">
      <div class="logo-container">
        {{#if logo}}
        <img src="{{logo}}" alt="Ek Ishwar Logo">
        {{/if}}
      </div>
      <div class="header-center">
        <h1>Ek Ishwar</h1>
        <h2>Order Invoice</h2>
        <p>Order ID: {{orderNumber}}</p>
      </div>
      <div style="width: 33%;"></div>
    </div>

    <!-- Invoice Metadata -->
    <div class="invoice-meta">
      <div class="meta-row">
        <span class="meta-label">Invoice Number:</span>
        <span class="meta-value">{{invoiceNumber}}</span>
      </div>
      <div class="meta-row">
        <span class="meta-label">Order Date:</span>
        <span class="meta-value">{{orderDate}}</span>
      </div>
      <div class="meta-row">
        <span class="meta-label">Payment Method:</span>
        <span class="meta-value">{{paymentMethod}}</span>
      </div>
      <div class="meta-row">
        <span class="meta-label">Payment Status:</span>
        <span class="meta-value">{{paymentStatus}}</span>
      </div>
    </div>

    <!-- Billing Section -->
    <div class="bill-section">
      <div class="bill-box">
        <strong>Bill From:</strong>
        {{platformDetails.businessName}}<br />
        Floor No.: {{platformDetails.floorNumber}}<br />
        Building/Flat No. : {{platformDetails.flatNumber}}, {{platformDetails.address}} <br />
        Road/Street: {{platformDetails.street}} <br />
        Locality/Sub  Locality: {{platformDetails.locality}} <br />
        City: {{platformDetails.city}} <br />
        District: {{platformDetails.district}} <br />
        State: {{platformDetails.state}} <br />
        GSTIN: {{platformDetails.gstNumber}}<br />
        {{#if platformDetails.phone}}Phone: {{platformDetails.phone}}<br />{{/if}}
        {{#if platformDetails.email}}Email: {{platformDetails.email}}{{/if}}
      </div>

      <div class="bill-box">
        <strong>Bill To:</strong>
        {{customerName}}<br />
        {{billingAddress.addressLine1}}<br />
        {{billingAddress.city}}, {{billingAddress.state}} {{billingAddress.postalCode}}<br />
        {{billingAddress.country}}<br />
        Phone: {{billingAddress.phone}}<br />
        {{#if billingAddress.email}}Email: {{billingAddress.email}}{{/if}}
      </div>
    </div>

    <!-- Order Table -->
    <table>
      <thead>
        <tr>
          <th>Item</th>
          <th>Qty</th>
          <th>Unit Price</th>
          {{#if isSameCountry}}
            {{#if isInterState}}
              <th>IGST %</th>
              <th>IGST Charges</th>
            {{else}}
              <th>CGST %</th>
              <th>CGST Charges</th>
              <th>SGST %</th>
              <th>SGST Charges</th>
            {{/if}}
          {{else}}
            <th>GST %</th>
            <th>GST Charges</th>
          {{/if}}
          <th>Total</th>
        </tr>
      </thead>
      <tbody>
        {{#each items}}
        <tr>
          <td>
            {{this.name}}{{#if this.variantAttributes}} ({{this.variantAttributesText}}){{/if}}
          </td>
          <td>{{this.quantity}}</td>
          <td>₹{{this.taxablePrice}}</td>
          {{#if ../isSameCountry}}
            {{#if ../isInterState}}
              <td>{{this.igstPercentage}}%</td>
              <td>₹{{this.igstAmount}}</td>
            {{else}}
              <td>{{this.cgstPercentage}}%</td>
              <td>₹{{this.cgstAmount}}</td>
              <td>{{this.sgstPercentage}}%</td>
              <td>₹{{this.sgstAmount}}</td>
            {{/if}}
          {{else}}
            <td>{{this.igstPercentage}}%</td>
            <td>₹{{this.igstAmount}}</td>
          {{/if}}
          <td>₹{{this.subtotal}}</td>
        </tr>
        {{/each}}
      </tbody>
    </table>

    <!-- Totals -->
    <div class="totals">
      <div class="totals-row">
        <span>Subtotal</span>
        <span>₹{{subTotal}}</span>
      </div>
      {{#if isSameCountry}}
        {{#if isInterState}}
          <div class="totals-row">
            <span>IGST Total</span>
            <span>₹{{igstTotal}}</span>
          </div>
        {{else}}
          <div class="totals-row">
            <span>CGST Total</span>
            <span>₹{{cgstTotal}}</span>
          </div>
          <div class="totals-row">
            <span>SGST Total</span>
            <span>₹{{sgstTotal}}</span>
          </div>
        {{/if}}
      {{else}}
        <div class="totals-row">
          <span>GST Total</span>
          <span>₹{{igstTotal}}</span>
        </div>
      {{/if}}
      <div class="totals-row">
        <span><strong>Shipping</strong></span>
        <span></span>
      </div>
      <div class="shipping-details">
        <div class="shipping-line">
          <span>Weight</span>
          <span>{{ shippingCost.totalWeight }}g</span>
        </div>
        <div class="shipping-line">
          <span>Base Shipping</span>
          <span>₹{{ shippingCost.baseShipping }}</span>
        </div>
        <div class="shipping-line">
          <span>GST</span>
          <span>₹{{ shippingCost.gst }}</span>
        </div>
        <div class="shipping-line total-shipping">
          <strong>Total Shipping</strong>
          <strong>₹{{ shippingCost.totalShipping }}</strong>
        </div>
      </div>
      {{#if discountAmount}}
      <div class="totals-row">
        <span>Discount</span>
        <span>-₹{{discountAmount}}</span>
      </div>
      {{/if}}
      <div class="totals-row total">
        <span>Total</span>
        <span>₹{{total}}</span>
      </div>
    </div>

    <!-- Footer -->
    <div class="footer">
      <div>Thank you for shopping with <strong>Ek Ishwar</strong></div>
      <div>Address: Ek Ishwar Headquarters, India | Email: <EMAIL></div>
    </div>

  </div>
</body>
</html>