<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{testTitle}}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .logo {
            max-width: 150px;
            height: auto;
            margin-bottom: 15px;
        }
        
        .title {
            color: #007bff;
            font-size: 28px;
            font-weight: bold;
            margin: 0;
        }
        
        .subtitle {
            color: #6c757d;
            font-size: 16px;
            margin: 5px 0 0 0;
        }
        
        .content {
            margin: 30px 0;
        }
        
        .message-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        
        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #007bff;
            font-size: 16px;
        }
        
        .sample-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
        }
        
        .sample-table th {
            background: #007bff;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
        }
        
        .sample-table td {
            padding: 12px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .sample-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .total-row {
            background: #e3f2fd !important;
            font-weight: bold;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 14px;
        }
        
        .success-badge {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            {{#if logo}}
            <img src="{{logo}}" alt="Logo" class="logo">
            {{/if}}
            <h1 class="title">{{testTitle}}</h1>
            <p class="subtitle">PDF Generation Test <span class="success-badge">SUCCESS</span></p>
        </div>

        <!-- Content Section -->
        <div class="content">
            <div class="message-box">
                <h3 style="margin-top: 0; color: #1976d2;">Test Message</h3>
                <p style="margin-bottom: 0; font-size: 16px; line-height: 1.6;">{{testMessage}}</p>
            </div>

            <!-- Info Grid -->
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Admin Email</div>
                    <div class="info-value">{{adminEmail}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Generated Date</div>
                    <div class="info-value">{{generatedAt}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Generated Time</div>
                    <div class="info-value">{{generatedTime}}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">PDF Engine</div>
                    <div class="info-value">html-pdf-node</div>
                </div>
            </div>

            <!-- Features Section -->
            <h3 style="color: #007bff; margin-top: 30px;">PDF Generation Features</h3>
            <ul class="feature-list">
                <li>HTML to PDF conversion using html-pdf-node</li>
                <li>Handlebars template engine integration</li>
                <li>Automatic S3 upload functionality</li>
                <li>Responsive design and styling</li>
                <li>Logo and image embedding support</li>
                <li>Dynamic data binding</li>
            </ul>

            <!-- Sample Table -->
            <h3 style="color: #007bff; margin-top: 30px;">Sample Data Table</h3>
            <table class="sample-table">
                <thead>
                    <tr>
                        <th>Item Name</th>
                        <th>Description</th>
                        <th>Price</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each sampleItems}}
                    <tr>
                        <td>{{name}}</td>
                        <td>{{description}}</td>
                        <td>₹{{price}}</td>
                    </tr>
                    {{/each}}
                    <tr class="total-row">
                        <td colspan="2"><strong>Total Amount</strong></td>
                        <td><strong>₹{{totalAmount}}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>This is a sample PDF generated for testing purposes.</p>
            <p>Generated on {{generatedAt}} at {{generatedTime}}</p>
            <p>&copy; {{currentYear}} Your Company Name. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
