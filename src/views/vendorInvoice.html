<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>Vendor Invoice</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #fff;
    }

    .container {
      width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      display: flex;
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid black;
      padding-bottom: 10px;
    }

    .logo-container {
      text-align: left;
      padding-left: 0;
    }
    .logo-container img {
      max-width: 200px;
      height: auto;
      display: block;
      padding: 0;
      margin: 0;
      position: relative;
      left: -30px;
      top: -5px;
    }
    .header-text {
      text-align: center;
      margin-top: -30px;
      margin-left: 30px;
    }

    .logo-left {
      max-height: 150px;
    }

    .title-center {
      flex: 1;
      text-align: center;
      font-size: 18px;
      text-transform: uppercase;
      color: #888;
      letter-spacing: 1px;
      margin-left: -60px;
    }

    .invoice-meta {
      display: flex;
      border: 1px solid #ccc;
      padding: 10px;
      font-size: 14px;
      margin-bottom: -1px;
    }

    .bill-section {
      display: flex;
      border: 1px solid #ccc;
      font-size: 14px;
      margin-bottom: 30px;
    }

    .bill-box {
      width: 50%;
      padding: 10px;
    }

    .bill-box + .bill-box {
      border-left: 1px solid #ccc;
    }

    .bill-box strong {
      display: block;
      margin-bottom: 5px;
      color: #000000;
      font-size: 14px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
      font-size: 14px;
    }

    thead th {
      background-color: #f5f5f5;
      text-align: center;
      padding: 10px;
      border: 1px solid #ddd;
    }

    tbody td {
      padding: 10px;
      border: 1px solid #ddd;
      text-align: center;
    }

    td.price, td.qty, td.total {
      text-align: center;
    }

    .footer {
      text-align: center;
      margin-top: 60px;
      font-size: 13px;
      color: #777;
    }

    .footer strong {
      color: #000000;
    }

    tr.total-row td {
      font-weight: bold;
      background-color: #f9f9f9;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header -->
    <div class="header">
      <div class="logo-container">
        {{#if logo}}
        <img src="{{logo}}" alt="Ek Ishwar Logo">
        {{/if}}
      </div>
      <div class="header-text">
        <h1>Ek Ishwar</h1>
        <h2>VENDOR INVOICE</h2>
        <p>Order Number: {{orderNumber}}</p>
      </div>
    </div>

    <table style="width: 100%; border-collapse: collapse; font-size: 14px; margin-bottom: 30px;">
      <!-- Invoice Meta Row -->
      <tr>
        <td style="padding: 10px; vertical-align: top; border: 1px solid #ccc; width: 50%;">
          <strong>Invoice Date:</strong> {{invoiceDate}}<br /><br>
          <strong>Invoice Number:</strong> {{invoiceNumber}}<br /><br>
          <strong>Order Number:</strong> {{orderNumber}}
        </td>
        <td style="padding: 10px; vertical-align: top; border: 1px solid #ccc; width: 50%;">
          <strong>Settlement Period:</strong><br /><br>
          From: {{settlementPeriodStart}}<br /><br>
          To: {{settlementPeriodEnd}}
        </td>
      </tr>

      <!-- Billing Row -->
      <tr>
        <td style="padding: 10px; vertical-align: top; border: 1px solid #ccc;">
          <strong>Bill From:</strong>
          {{platformDetails.businessName}}<br />
          Floor No.: {{platformDetails.floorNumber}}<br />
          Building/Flat No. : {{platformDetails.flatNumber}}, {{platformDetails.address}} <br />
          Road/Street: {{platformDetails.street}} <br />
          Locality/Sub  Locality: {{platformDetails.locality}} <br />
          City: {{platformDetails.city}} <br />
          District: {{platformDetails.district}} <br />
          State: {{platformDetails.state}} <br />
          GSTIN: {{platformDetails.gstNumber}}<br />
          {{#if platformDetails.phone}}Phone: {{platformDetails.phone}}<br />{{/if}}
          {{#if platformDetails.email}}Email: {{platformDetails.email}}{{/if}}
        </td>
        <td style="padding: 10px; vertical-align: top; border: 1px solid #ccc;">
          <strong>Bill To:</strong><br />
          {{vendorDetails.businessName}}<br />
          {{vendorDetails.address}}<br /><br>
          <strong>GSTIN:</strong> {{vendorDetails.gstNumber}}<br /><br>
          {{#if vendorDetails.phone}}<strong>Phone:</strong> {{vendorDetails.phone}}{{/if}}
        </td>
      </tr>
    </table>


    <!-- Table -->
    <table>
      <thead>
        <tr>
          <th>Item</th>
          <th>Product Code</th>
          <th>HSN</th>
          <th class="price">Unit Price</th>
          <th class="qty">Qty</th>
          <th class="price">Taxable Value (INR)</th>
          <th class="price">CGST %</th>
          <th class="price">CGST Charges</th>
          <th class="price">SGST %</th>
          <th class="price">SGST Charges</th>
          <th class="total">Total Amount (INR)</th>
        </tr>
      </thead>
      <tbody>
        {{#each orderItems}}
        <tr>
          <td>{{this.name}}</td>
          <td>{{this.productCode}}</td>
          <td>{{this.hsn}}</td>
          <td class="price">₹{{this.taxablePrice}}</td>
          <td class="qty">{{this.quantity}}</td>
          <td class="price">₹{{this.subTotalTaxablePrice}}</td>
          <td class="price">{{this.cgstPercentage}}%</td>
          <td class="price">₹{{this.cgstPrice}}</td>
          <td class="price">{{this.sgstPercentage}}%</td>
          <td class="price">₹{{this.sgstPrice}}</td>
          <td class="total">₹{{this.subtotal}}</td>
        </tr>
        {{/each}}

        <!-- Totals Row -->
        <tr class="total-row">
          <td>Total</td>
          <td></td>
          <td></td>
          <td></td>
          <td class="qty">{{totalQuantity}}</td>
          <td class="price">₹{{totalTaxablePrice}}</td>
          <td class="price">–</td>
          <td class="price">₹{{totalCgstAmount}}</td>
          <td class="price">–</td>
          <td class="price">₹{{totalSgstAmount}}</td>
          <td class="total">₹{{totalAmount}}</td>
        </tr>
      </tbody>
    </table>

    <!-- Footer -->
    <div class="footer">
      <div>Thank you for partnering with <strong>{{platformDetails.businessName}}</strong>.</div>
      <div>For queries, contact <EMAIL></div>
    </div>
  </div>
</body>
</html>
