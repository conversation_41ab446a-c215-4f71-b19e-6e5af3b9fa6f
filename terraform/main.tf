terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 2.0"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 4.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Create key pair
resource "tls_private_key" "key_pair" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

resource "aws_key_pair" "key_pair" {
  key_name   = "one-god-uat-ec2-key-pair"
  public_key = tls_private_key.key_pair.public_key_openssh
}

# Save private key locally
resource "local_file" "private_key" {
  content  = tls_private_key.key_pair.private_key_pem
  filename = "${path.module}/one-god-uat-ec2-key-pair.pem"

  provisioner "local-exec" {
    command = "chmod 400 ${path.module}/one-god-uat-ec2-key-pair.pem"
  }
}

# Security group
resource "aws_security_group" "one_god_uat_sg" {
  name        = "one-god-uat-security-group"
  description = "Security group for EC2 instance"

  # HTTP port
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # HTTPS port
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # SSH port
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Jenkins port
  ingress {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "one-god-uat-security-group"
  }
}

# EC2 instance
resource "aws_instance" "one_god_uat_server" {
  ami           = var.ubuntu_ami # Ubuntu 22.04 LTS AMI ID
  instance_type = "t2.micro"
  key_name      = aws_key_pair.key_pair.key_name
  iam_instance_profile = can(data.aws_iam_instance_profile.existing_codedeploy_profile.name) ? data.aws_iam_instance_profile.existing_codedeploy_profile.name : aws_iam_instance_profile.codedeploy_profile[0].name

  root_block_device {
    volume_size = 8
    volume_type = "gp2"
  }

  security_groups = [aws_security_group.one_god_uat_sg.name]

  user_data = <<-EOF
              #!/bin/bash

              # Update system
              apt-get update
              apt-get upgrade -y

              # Install necessary packages
              apt-get install -y nginx git curl ruby-full wget gnupg

              # Install AWS CodeDeploy Agent
              cd /home/<USER>
              wget https://aws-codedeploy-${var.aws_region}.s3.amazonaws.com/latest/install
              chmod +x ./install
              ./install auto
              systemctl enable codedeploy-agent
              systemctl start codedeploy-agent
              
              # Verify CodeDeploy agent installation
              if ! systemctl is-active --quiet codedeploy-agent; then
                echo "CodeDeploy agent failed to start. Retrying installation..."
                ./install auto
                systemctl enable codedeploy-agent
                systemctl start codedeploy-agent
              fi

              # Install Node.js 18.x
              curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
              apt-get install -y nodejs

              # Install PM2 globally
              npm install -g pm2

              # Install MongoDB 7.0
              curl -fsSL https://pgp.mongodb.com/server-7.0.asc | gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor
              echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list
              apt-get update
              apt-get install -y mongodb-org
              
              # Start and enable MongoDB
              systemctl start mongod
              systemctl enable mongod
              
              # Restart MongoDB to apply changes
              systemctl restart mongod
              
              # Verify MongoDB is running
              if ! systemctl is-active --quiet mongod; then
                echo "MongoDB failed to start. Checking logs..."
                journalctl -u mongod
              fi

              # Configure git to handle credentials
              git config --global credential.helper store
              echo "https://${var.bitbucket_username}:${var.bitbucket_token}@bitbucket.org" > /root/.git-credentials
              git config --global credential.helper 'store --file /root/.git-credentials'

              # Create development directory
              mkdir -p /home/<USER>/development/one-god-node
              git clone ${var.repo_url} /home/<USER>/development/one-god-node
              cd /home/<USER>/development/one-god-node
              git checkout uat

              # Secure git credentials
              rm /root/.git-credentials
              git config --global --unset credential.helper

              # Create .env file
              cat > /home/<USER>/development/one-god-node/.env <<EOL
              PORT=3000
              MONGODB_URI=mongodb://localhost:27017/${var.db_name}
              JWT_SECRET=${var.jwt_secret}
              EOL

              # Install dependencies and build
              cd /home/<USER>/development/one-god-node
              npm install

              # Configure Nginx
              cat > /etc/nginx/sites-available/default <<EOL
              server {
                  listen 80;
                  server_name _;

                  location / {
                      proxy_pass http://localhost:3000;
                      proxy_http_version 1.1;
                      proxy_set_header Upgrade \$http_upgrade;
                      proxy_set_header Connection 'upgrade';
                      proxy_set_header Host \$host;
                      proxy_cache_bypass \$http_upgrade;
                  }
              }
              EOL

              # Restart Nginx
              systemctl restart nginx

              # Start application with PM2
              cd /home/<USER>/development/one-god-node
              sleep 5  # Give PM2 time to initialize

              # Create PM2 home directory for ubuntu user
              mkdir -p /home/<USER>/.pm2
              chown -R ubuntu:ubuntu /home/<USER>/.pm2

              # Run PM2 commands as ubuntu user
              sudo -u ubuntu bash -c '
                cd /home/<USER>/development/one-god-node
                pm2 start src/index.js --name "one-god-dev"
                env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u ubuntu --hp /home/<USER>
                pm2 save
              '
              
              # Verify PM2 process
              pm2 list

              # Set proper permissions
              chown -R ubuntu:ubuntu /home/<USER>/development/one-god-node

              # Clean up any sensitive files
              history -c
              EOF
  tags = {
    Name = "one-god-uat-server"
  }
}

# Elastic IP
resource "aws_eip" "one_god_uat_eip" {
  instance = aws_instance.one_god_uat_server.id
  domain   = "vpc"

  tags = {
    Name = "one-god-uat-elastic-ip"
  }
}

# Data sources to check for existing resources
data "aws_iam_role" "existing_ec2_role" {
  name = "ec2-codedeploy-role"
}

data "aws_iam_role" "existing_codedeploy_role" {
  name = "codedeploy-service-role"
}

data "aws_iam_instance_profile" "existing_codedeploy_profile" {
  name = "codedeploy-profile"
}

# IAM role for EC2
resource "aws_iam_role" "ec2_role" {
  count = can(data.aws_iam_role.existing_ec2_role.arn) ? 0 : 1
  name  = "ec2-codedeploy-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })
}

# IAM instance profile for EC2
resource "aws_iam_instance_profile" "codedeploy_profile" {
  count = can(data.aws_iam_instance_profile.existing_codedeploy_profile.name) ? 0 : 1
  name  = "codedeploy-profile"
  role  = can(data.aws_iam_role.existing_ec2_role.name) ? data.aws_iam_role.existing_ec2_role.name : aws_iam_role.ec2_role[0].name
}

# IAM role for CodeDeploy
resource "aws_iam_role" "codedeploy_role" {
  count = can(data.aws_iam_role.existing_codedeploy_role.arn) ? 0 : 1
  name  = "codedeploy-service-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "codedeploy.amazonaws.com"
        }
      }
    ]
  })
}

# Attach AWS managed policy for CodeDeploy
resource "aws_iam_role_policy_attachment" "codedeploy_service_role_policy" {
  count      = can(data.aws_iam_role.existing_codedeploy_role.arn) ? 0 : 1
  role       = aws_iam_role.codedeploy_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSCodeDeployRole"
}

# Attach AWS managed policy for EC2
resource "aws_iam_role_policy_attachment" "ec2_service_role_policy" {
  count      = can(data.aws_iam_role.existing_ec2_role.arn) ? 0 : 1
  role       = aws_iam_role.ec2_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSCodeDeployRole"
}

# IAM user and access management
data "aws_iam_user" "existing_cicd_user" {
  user_name = "bitbucket-cicd"
}

data "aws_iam_access_keys" "existing_keys" {
  user = "bitbucket-cicd"
}

resource "aws_iam_user" "cicd_user" {
  count = can(data.aws_iam_user.existing_cicd_user.user_name) ? 0 : 1
  name  = "bitbucket-cicd"
}

resource "aws_iam_access_key" "cicd_user_key" {
  count = can(data.aws_iam_user.existing_cicd_user.user_name) ? 0 : 1
  user  = aws_iam_user.cicd_user[0].name
}

# CodeDeploy application
resource "aws_codedeploy_app" "one_god_uat_app" {
  name = "one-god-uat-app"
}

# CodeDeploy deployment group
resource "aws_codedeploy_deployment_group" "one_god_uat_deployment_group" {
  app_name              = aws_codedeploy_app.one_god_uat_app.name
  deployment_group_name = "one-god-uat-deployment-group"
  service_role_arn      = can(data.aws_iam_role.existing_codedeploy_role.arn) ? data.aws_iam_role.existing_codedeploy_role.arn : aws_iam_role.codedeploy_role[0].arn

  ec2_tag_set {
    ec2_tag_filter {
      key   = "Name"
      type  = "KEY_AND_VALUE"
      value = "one-god-uat-server"
    }
  }

  auto_rollback_configuration {
    enabled = true
    events  = ["DEPLOYMENT_FAILURE"]
  }
}

# S3 bucket for CodeDeploy artifacts
resource "aws_s3_bucket" "codedeploy_bucket" {
  bucket        = var.aws_s3_bucket
  force_destroy = true
}

# IAM user policy attachments
resource "aws_iam_user_policy_attachment" "s3_access" {
  user       = "bitbucket-cicd"
  policy_arn = "arn:aws:iam::aws:policy/AmazonS3FullAccess"
}

resource "aws_iam_user_policy_attachment" "codedeploy_access" {
  user       = "bitbucket-cicd"
  policy_arn = "arn:aws:iam::aws:policy/AWSCodeDeployFullAccess"
}

# Outputs
output "cicd_access_key_id" {
  description = "Access Key ID for the CI/CD IAM user"
  value       = can(data.aws_iam_user.existing_cicd_user.user_name) ? "Using existing access key" : aws_iam_access_key.cicd_user_key[0].id
  sensitive   = true
}

output "cicd_secret_access_key" {
  description = "Secret Access Key for the CI/CD IAM user"
  value       = can(data.aws_iam_user.existing_cicd_user.user_name) ? "Using existing access key" : aws_iam_access_key.cicd_user_key[0].secret
  sensitive   = true
}
