output "instance_public_ip" {
  description = "Public IP address of the EC2 instance"
  value       = aws_eip.one_god_uat_eip.public_ip
}

output "ssh_command" {
  description = "SSH command to connect to the instance"
  value       = "ssh -i ${path.module}/one-god-uat-ec2-key-pair.pem ubuntu@${aws_eip.one_god_uat_eip.public_ip}"
}

# CI/CD user credentials
# output "cicd_access_key_id" {
#   description = "Access Key ID for the CI/CD IAM user"
#   value       = aws_iam_access_key.cicd_user_key.id
#   sensitive   = true
# }

# output "cicd_secret_access_key" {
#   description = "Secret Access Key for the CI/CD IAM user"
#   value       = aws_iam_access_key.cicd_user_key.secret
#   sensitive   = true
# }

# output "aws_region" {
#   description = "AWS region used for deployment"
#   value       = var.aws_region
# }

output "s3_bucket_name" {
  description = "S3 bucket name for CodeDeploy artifacts"
  value       = aws_s3_bucket.codedeploy_bucket.bucket
}
