variable "aws_region" {
  description = "AWS region"
  default     = "ap-south-1"
}

variable "ubuntu_ami" {
  description = "Ubuntu 22.04 LTS AMI ID for ap-south-1"
  default     = "ami-03f4878755434977f"  # Ubuntu 22.04 LTS AMI ID for ap-south-1
}

variable "db_name" {
  description = "MongoDb database name"
  type        = string
  default     = "one_god_uat"  # Changed hyphen to underscore for better compatibility
}

variable "db_user" {
  description = "MongoDb database user"
  type        = string
  default     = "one_god_user"
}

variable "db_password" {
  description = "MongoDb database password"
  type        = string
  sensitive   = true
}

variable "jwt_secret" {
  description = "JWT secret key"
  type        = string
  sensitive   = true
}

# Remove/replace these variables
variable "bitbucket_username" {
  description = "Bitbucket username"
  type        = string
  sensitive   = true
}

variable "bitbucket_token" {
  description = "Bitbucket app password"
  type        = string
  sensitive   = true
}

variable "repo_url" {
  description = "Bitbucket repository URL"
  type        = string
}

variable "aws_s3_bucket" {
  description = "S3 bucket for CodeDeploy artifacts"
  type        = string
  default     = "one-god-uat-ci-codedeploy-artifacts"
}
