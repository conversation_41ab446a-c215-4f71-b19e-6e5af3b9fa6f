const joi = require('joi');
const mongoose = require('mongoose');

const createPujariSchema = joi.object({
  name: joi.string().required().messages({
    'string.empty': 'Name is required',
    'any.required': 'Name is required'
  }),
  email: joi.string().email().lowercase().optional(),
  phoneNumber: joi.string().pattern(/^\+[1-9]\d{1,14}$/),
  temple: joi.string().regex(/^[0-9a-fA-F]{24}$/).required().messages({
    'string.pattern.base': 'Invalid temple ID format',
    'string.empty': 'Temple ID is required',
    'any.required': 'Temple ID is required'
  }),
  status: joi.string().valid('ACTIVE', 'INACTIVE').default('ACTIVE'),
  approvalStatus: joi.string().valid('PENDING_APPROVAL', 'APPROVED', 'REJECTED').default('PENDING_APPROVAL')
});

const uploadPujarisSchema = joi.object({
  temple: joi.string().regex(/^[0-9a-fA-F]{24}$/).required().messages({
    'string.pattern.base': 'Invalid temple ID format',
    'string.empty': 'Temple ID is required',
    'any.required': 'Temple ID is required'
  })
});

const listPujarisSchema = joi.object({
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(10),
  temple: joi.string().regex(/^[0-9a-fA-F]{24}$/),
  status: joi.string().valid('ACTIVE', 'INACTIVE'),
  approvalStatus: joi.string().valid('PENDING_APPROVAL', 'APPROVED', 'REJECTED'),
  search: joi.string().min(1).max(100),
  sortBy: joi.string().valid('name', 'email', 'phoneNumber', 'status', 'createdAt', 'updatedAt').default('createdAt'),
  sortOrder: joi.number().valid(1, -1).default(-1)
});

const templeAdminCreatePujariSchema = joi.object({
  name: joi.string().required().messages({
    'string.empty': 'Name is required',
    'any.required': 'Name is required'
  }),
  phoneNumber: joi.string().pattern(/^\+[1-9]\d{1,14}$/),
});

const approvePujariSchema = joi.object({
  approvalStatus: joi.string().valid('APPROVED', 'REJECTED').required().messages({
    'string.empty': 'Approval status is required',
    'any.required': 'Approval status is required',
    'any.only': 'Approval status must be either APPROVED or REJECTED'
  }),
  rejectionReason: joi.when('approvalStatus', {
    is: 'REJECTED',
    then: joi.string().required().messages({
      'string.empty': 'Rejection reason is required when rejecting a pujari',
      'any.required': 'Rejection reason is required when rejecting a pujari'
    }),
    otherwise: joi.forbidden()
  })
});

const editPujariSchema = joi.object({
  name: joi.string().messages({
    'string.empty': 'Name is required',
    'any.required': 'Name is required'
  }),
  phoneNumber: joi.string().pattern(/^\+[1-9]\d{1,14}$/),
  email: joi.string().email().lowercase().optional().allow(''),
});

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateId = joi.string()
  .custom(validateObjectId)
  .required()
  .messages({
    'any.invalid': 'Invalid ID format',
    'any.required': 'ID is required'
  });

module.exports = {
  createPujariSchema,
  uploadPujarisSchema,
  listPujarisSchema,
  templeAdminCreatePujariSchema,
  approvePujariSchema,
  editPujariSchema,
  validateId
};
