const joi = require('joi');
const mongoose = require('mongoose');
const moment = require('moment');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const validateDateString = (value, helpers) => {
  if (!moment(value, 'YYYY-MM-DD', true).isValid()) {
    return helpers.error('string.dateFormat');
  }
  return value;
};

// Schema for verifying a devotee
const verifyDevoteeSchema = joi.object({
  bookingId: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Booking ID is required',
      'any.required': 'Booking ID is required'
    }),
  devoteeId: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Devotee ID is required',
      'any.required': 'Devotee ID is required'
    }),
  date: joi.string().custom(validateDateString)
    .messages({
      'string.dateFormat': 'Date must be in YYYY-MM-DD format and be a valid date'
    }),
  notes: joi.string().allow('').optional()
});

// Schema for checking verification status
const checkVerificationStatusSchema = joi.object({
  bookingId: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Booking ID is required',
      'any.required': 'Booking ID is required'
    }),
  devoteeId: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Devotee ID is required',
      'any.required': 'Devotee ID is required'
    }),
  date: joi.string().custom(validateDateString)
    .messages({
      'string.dateFormat': 'Date must be in YYYY-MM-DD format and be a valid date'
    })
});

// Schema for getting booking verification details
const getBookingVerificationSchema = joi.object({
  bookingId: joi.string().custom(validateObjectId).required()
    .messages({
      'string.empty': 'Booking ID is required',
      'any.required': 'Booking ID is required'
    }),
  date: joi.string().custom(validateDateString)
    .messages({
      'string.dateFormat': 'Date must be in YYYY-MM-DD format and be a valid date'
    })
});

module.exports = {
  verifyDevoteeSchema,
  checkVerificationStatusSchema,
  getBookingVerificationSchema
};
