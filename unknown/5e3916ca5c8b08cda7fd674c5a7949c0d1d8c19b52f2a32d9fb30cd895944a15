const mongoose = require('mongoose');

const subCategorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxLength: 50
  },
  description: {
    type: String,
    trim: true,
    maxLength: 200
  },
  image: {
    type: String,
    trim: true
  },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  // Variant types associated with this subcategory
  variantTypes: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AttributeOption'
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  }
}, { timestamps: true });

// Add indexes for better query performance
subCategorySchema.index({ name: 1 });
subCategorySchema.index({ category: 1 });
subCategorySchema.index({ isActive: 1 });

const SubCategory = mongoose.model('SubCategory', subCategorySchema);

module.exports = SubCategory;
