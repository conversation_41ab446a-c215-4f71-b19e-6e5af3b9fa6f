const Offering = require('../../../models/Offering');
const User = require('../../../models/User');
const Language = require('../../../models/Language');
const { transformTranslatedFields } = require('../../../utils/localizer');

const listOfferings = async (queryParams, userId) => {
  const { page = 1, limit = 10, search, temple, isActive, sortBy, sortOrder } = queryParams;
  const skip = (page - 1) * limit;
  const sortOptions = {};

  sortOptions[sortBy] = parseInt(sortOrder);

  const query = {};

  let language = { code: 'en' };

  if (userId) {
    const loggedInUser = await User.findById(userId);

    if (loggedInUser && loggedInUser.preferredLanguage) {
      const userLanguage = await Language.findOne({ name: loggedInUser.preferredLanguage });

      if (userLanguage) {
        language = userLanguage;
      }
    }
  }

  if (search) {
    query.$or = [
      { [`name.${language.code}`]: { $regex: search, $options: 'i' } },
      { [`description.${language.code}`]: { $regex: search, $options: 'i' } }
    ];
  }

  if (temple) {
    query.temple = temple;
  }

  if (isActive) {
    query.isActive = isActive;
  }

  const offerings = await Offering.find(query)
    .populate('temple')
    .sort(sortOptions).collation({ locale: 'en', strength: 1 })
    .skip(skip)
    .limit(limit)
    .lean();

  const total = await Offering.countDocuments(query);

  const localizedOfferings = await transformTranslatedFields(offerings, language.code);

  return {
    offerings: localizedOfferings,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / parseInt(limit))
    }
  };
};

module.exports = {
  listOfferings
};