const mongoose = require('mongoose');
const { shipmentStatusValue, settlementStatusValue } = require('../constants/dbEnums');

const productShipmentSchema = new mongoose.Schema({
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: true
  },
  orderItems: [{
    orderItem: {
      type: mongoose.Schema.Types.ObjectId,
      required: true
    },
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true
    },
    variant: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ProductVariant'
    },
    quantity: {
      type: Number,
      required: true,
      min: 1
    },
    price: {
      type: Number,
      required: true,
      min: 0
    },
    subtotal: {
      type: Number,
      required: true,
      min: 0
    },
    name: {
      type: String,
      required: true
    },
    variantAttributes: {
      type: Map,
      of: String
    }
  }],
  vendor: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'vendorModel',
    required: true
  },
  vendorModel: {
    type: String,
    required: true,
    enum: [ 'Admin', 'Vendor' ]
  },
  trackingId: {
    type: String,
    trim: true
  },
  shiprocketOrderId: {
    type: String,
    trim: true
  },
  shiprocketShipmentId: {
    type: String,
    trim: true
  },
  courier: {
    courierId: {
      type: String,
      trim: true
    },
    courierName: {
      type: String,
      trim: true
    }
  },
  pickupDate: {
    type: Date
  },
  deliveryDate: {
    type: Date
  },
  estimatedDeliveryDate: {
    type: Date
  },
  trackingUrl: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: Object.values(shipmentStatusValue),
    default: shipmentStatusValue.PENDING
  },
  weight: {
    type: Number,
    min: 0
  },
  dimensions: {
    length: {
      type: Number,
      min: 0
    },
    width: {
      type: Number,
      min: 0
    },
    height: {
      type: Number,
      min: 0
    }
  },
  pickupAddress: {
    addressLine1: {
      type: String,
      trim: true
    },
    addressLine2: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      trim: true
    },
    state: {
      type: String,
      trim: true
    },
    postalCode: {
      type: String,
      trim: true
    },
    country: {
      type: String,
      trim: true,
      default: 'India'
    },
    phone: {
      type: String,
      trim: true
    }
  },
  deliveryAddress: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    addressLine1: {
      type: String,
      trim: true
    },
    addressLine2: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      trim: true
    },
    state: {
      type: String,
      trim: true
    },
    postalCode: {
      type: String,
      trim: true
    },
    country: {
      type: String,
      trim: true,
      default: 'India'
    },
    phone: {
      type: String,
      trim: true
    }
  },
  notes: {
    type: String,
    trim: true
  },
  apiResponses: [{
    endpoint: String,
    response: Object,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  trackingHistory: [{
    status: String,
    location: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    description: String
  }],
  scans: [{
    type: Object
  }],
  settlementStatus: {
    type: String,
    enum: Object.values(settlementStatusValue),
    default: settlementStatusValue.PENDING
  },
  settledAt: {
    type: Date
  },
  settlementNotes: {
    type: String,
    trim: true
  },
  settledBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Add virtual field for invoice
productShipmentSchema.virtual('invoice', {
  ref: 'Invoice',
  localField: '_id',
  foreignField: 'shipment'
});

// Add indexes for better query performance
productShipmentSchema.index({ order: 1 });
productShipmentSchema.index({ vendor: 1 });
productShipmentSchema.index({ trackingId: 1 });
productShipmentSchema.index({ shiprocketOrderId: 1 });
productShipmentSchema.index({ shiprocketShipmentId: 1 });
productShipmentSchema.index({ status: 1 });
productShipmentSchema.index({ 'orderItems.product': 1 });
productShipmentSchema.index({ settlementStatus: 1 });
productShipmentSchema.index({ settledAt: 1 });
productShipmentSchema.index({ settledBy: 1 });

const ProductShipment = mongoose.model('ProductShipment', productShipmentSchema);

module.exports = ProductShipment;
