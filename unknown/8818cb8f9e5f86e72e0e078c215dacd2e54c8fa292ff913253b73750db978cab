const mongoose = require('mongoose');
const { transactionTypeValue, transactionStatusValue } = require('../constants/dbEnums');

const payPhiTransactionSchema = new mongoose.Schema({
  tranCtx: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  txnDate: {
    type: String,
    required: true
  },
  secureHash: {
    type: String,
    required: true
  },
  merchantTxnNo: {
    type: String,
    required: true
  },
  redirectUrl: {
    type: String,
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    required: false,
  },
  order: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order',
    required: false,
  },
  amount: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    required: true,
    default: '356',
    enum: [ '356' ]
  },
  type: {
    type: String,
    required: true,
    enum: Object.values(transactionTypeValue),
    default: transactionTypeValue.PAYMENT
  },
  status: {
    type: String,
    required: true,
    enum: Object.values(transactionStatusValue),
    default: transactionStatusValue.PENDING
  },
  paymentMode: {
    type: String,
    required: false
  },
  paymentSource: {
    type: String,
    required: false
  },
  webhookResponsePayload: {
    type: Object,
    required: false
  },
}, { timestamps: true });

// Add indexes for better query performance
payPhiTransactionSchema.index({ merchantTxnNo: 1 });
payPhiTransactionSchema.index({ tranCtx: 1 });
payPhiTransactionSchema.index({ user: 1 });
payPhiTransactionSchema.index({ type: 1 });
payPhiTransactionSchema.index({ status: 1 });
payPhiTransactionSchema.index({ createdAt: -1 });

const PayPhiTransaction = mongoose.model('PayPhiTransaction', payPhiTransactionSchema);

module.exports = PayPhiTransaction;
