const express = require('express');
const router = express.Router();
const productController = require('./controller');
const auth = require('../../../middleware/auth');

// Public routes
router.get('/', productController.getAllProducts);
router.get('/featured', productController.getFeaturedProducts);
router.get('/categories/:categoryId', productController.getProductsByCategory);
router.get('/subcategories/:subCategoryId', productController.getProductsBySubCategory);
router.get('/collections/:collectionId', productController.getProductsByCollection);
router.get('/temples/:templeId', productController.getProductsByTemple);
router.get('/search', productController.searchProducts);
router.get('/:id', productController.getProductById);

// Authenticated user routes
router.post('/review/:id', auth, productController.addProductReview);

module.exports = router;
