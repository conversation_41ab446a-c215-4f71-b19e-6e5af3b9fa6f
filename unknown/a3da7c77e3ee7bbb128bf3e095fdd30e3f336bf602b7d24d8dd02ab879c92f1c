const joi = require('joi');

/**
 * Validation schema for ID parameter
 */
const validateIdSchema = joi.object({
  id: joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .required()
    .messages({
      'string.pattern.base': 'Invalid ID format',
      'any.required': 'ID is required'
    })
});

/**
 * Validation schema for getting pooja schedules
 */
const getPoojaSchedulesSchema = joi.object({
  page: joi.number().integer().min(1).default(1),
  limit: joi.number().integer().min(1).max(100).default(10),
  search: joi.string().trim().allow('').optional().description('Search term for pooja name or description'),
  type: joi.string().valid('PHYSICAL', 'VIRTUAL').optional().description('Filter by pooja type'),
  dateFilter: joi.string().valid('TODAY', 'PAST', 'FUTURE', 'CUSTOM'),
  startDate: joi.date().iso().when('dateFilter', {
    is: 'CUSTOM',
    then: joi.required(),
    otherwise: joi.optional()
  }).messages({
    'date.base': 'Start date must be a valid date',
    'date.format': 'Start date must be in ISO format (YYYY-MM-DD)',
    'any.required': 'Start date is required when using custom date filter'
  }),
  endDate: joi.date().iso().when('dateFilter', {
    is: 'CUSTOM',
    then: joi.required(),
    otherwise: joi.optional()
  }).when('startDate', {
    is: joi.exist(),
    then: joi.date().greater(joi.ref('startDate'))
  }).messages({
    'date.base': 'End date must be a valid date',
    'date.format': 'End date must be in ISO format (YYYY-MM-DD)',
    'date.greater': 'End date must be greater than start date',
    'any.required': 'End date is required when using custom date filter'
  }),
  specificDate: joi.date().iso().messages({
    'date.base': 'Specific date must be a valid date',
    'date.format': 'Specific date must be in ISO format (YYYY-MM-DD)'
  }),
  sortBy: joi.string().valid('createdAt', 'name', 'type').default('createdAt'),
  sortOrder: joi.number().valid(1, -1).default(-1)
});

module.exports = {
  getPoojaSchedulesSchema,
  validateIdSchema
};
