const bcrypt = require('bcryptjs');
const KioskUser = require('../../../models/KioskUser');
const Temple = require('../../../models/Temple');
const Kiosk = require('../../../models/Kiosk');
const Token = require('../../../models/Token');
const { throwBadRequestError, throwNotFoundError } = require('../../../errors');
const { messages } = require('../../../messages');
const { userTypeValue } = require('../../../constants/dbEnums');
const { generateToken } = require('../../../utils/jwt');
const { createToken } = require('../../../database/queries/accessToken.query');

/**
 * Login a kiosk user
 * @param {Object} loginData - Login credentials
 * @returns {Object} User data and token
 */
const login = async (loginData) => {
  // Find user by email
  const user = await KioskUser.findOne({ email: loginData.email.toLowerCase() });
  
  if (!user) {
    throwBadRequestError(messages.INVALID_CREDENTIALS);
  }

  // Check if user is active
  if (user.status !== 'ACTIVE') {
    throwBadRequestError(messages.USER_INACTIVE);
  }

  // Verify password
  const isPasswordValid = await bcrypt.compare(loginData.password, user.password);
  
  if (!isPasswordValid) {
    throwBadRequestError(messages.INVALID_CREDENTIALS);
  }

  // Get kiosk and temple details
  const kiosk = await Kiosk.findById(user.kiosk);

  if (!kiosk) {
    throwNotFoundError('Associated kiosk not found');
  }

  const temple = await Temple.findById(kiosk.temple);

  if (!temple) {
    throwNotFoundError(messages.TEMPLE_NOT_FOUND);
  }

  // Generate token
  const token = await generateToken({ 
    _id: user._id,
    email: user.email,
    name: user.name,
    userType: userTypeValue.KIOSK
  });

  // Save token
  const expireTime = process.env.JWT_EXPIRY;
  const days = expireTime.toLowerCase().replace('d', '');

  await createToken({
    token: token,
    userType: userTypeValue.KIOSK,
    userId: user._id,
    expiresAt: new Date().getTime() + (parseInt(days) * 24 * 60 * 60 * 1000),
  });

  return {
    user: {
      id: user._id,
      name: user.name,
      email: user.email,
      phoneNumber: user.phoneNumber,
      countryCode: user.countryCode,
      passwordChangeRequired: user.passwordChangeRequired,
      kiosk: {
        id: kiosk._id,
        location: kiosk.location,
        temple: {
          id: temple._id,
          name: temple.name,
          city: temple.city,
          state: temple.state
        }
      }
    },
    token
  };
};

/**
 * Change password for a kiosk user
 * @param {string} userId - User ID
 * @param {Object} passwordData - Password data
 * @returns {Object} Success message
 */
const changePassword = async (userId, passwordData) => {
  // Find user
  const user = await KioskUser.findById(userId);
  
  if (!user) {
    throwNotFoundError('User not found');
  }

  // Verify current password
  const isPasswordValid = await bcrypt.compare(passwordData.currentPassword, user.password);
  
  if (!isPasswordValid) {
    throwBadRequestError('Current password is incorrect');
  }

  // Hash new password
  const hashedPassword = await bcrypt.hash(passwordData.newPassword, 10);

  // Update password
  user.password = hashedPassword;
  user.passwordChangeRequired = false;
  user.updatedBy = userId;
  
  await user.save();

  return { success: true, message: 'Password changed successfully' };
};

/**
 * Logout a kiosk user
 * @param {string} userId - User ID
 * @param {string} token - JWT token
 * @returns {Object} Success message
 */
const logout = async (userId, token) => {
  await Token.deleteOne({ 
    userId,
    token,
    userType: userTypeValue.KIOSK
  });

  return { success: true, message: 'Logged out successfully' };
};

module.exports = {
  login,
  changePassword,
  logout
};
