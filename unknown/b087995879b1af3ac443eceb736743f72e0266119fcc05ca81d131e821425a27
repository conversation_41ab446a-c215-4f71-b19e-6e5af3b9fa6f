const joi = require('joi');

const initiateLoginSchema = joi.object({
  email: joi.string().email().lowercase().required(),
  password: joi.string().required(),
});

const verifyOtpSchema = joi.object({
  email: joi.string().email().lowercase().required(),
  otp: joi.string().length(6).pattern(/^[0-9]+$/).required().messages({
    'string.pattern.base': 'OTP must contain only numbers',
    'string.length': 'OTP must be exactly 6 digits',
    'any.required': 'OTP is required'
  })
});

module.exports = {
  initiateLoginSchema,
  verifyOtpSchema
};
