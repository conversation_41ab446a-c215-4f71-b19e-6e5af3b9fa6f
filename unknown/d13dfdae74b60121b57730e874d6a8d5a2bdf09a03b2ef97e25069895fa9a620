const { apiResponse, errorApiResponse } = require('../../../config/responseHandler');
const { commonConstants } = require('../../../constants/common');
const poojaService = require('./service');
const { createPoojaScheduleSchema, updatePoojaScheduleSchema } = require('./validation');
const { SUCCESS } = commonConstants;

const createPoojaSchedule = async (req, res) => {
  try {
    const { error } = createPoojaScheduleSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await poojaService.createPoojaSchedule(req.body, req.user.id);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: 'Pooja schedule created successfully', 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const updatePoojaSchedule = async (req, res) => {
  try {
    const { error } = updatePoojaScheduleSchema.validate(req.body);

    if (error) {
      return res.status(400).json({ 
        message: error.details[0].message,
        status: false 
      });
    }

    const data = await poojaService.updatePoojaSchedule(
      req.params.id,
      req.body,
      req.user.id
    );
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: 'Pooja schedule updated successfully', 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const deletePoojaSchedule = async (req, res) => {
  try {
    await poojaService.deletePoojaSchedule(req.params.id);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: 'Pooja schedule deleted successfully', 
      status: true 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const listPoojaSchedules = async (req, res) => {
  try {
    const { schedules, pagination } = await poojaService.listPoojaSchedules(req.query);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: 'Pooja schedules retrieved successfully', 
      status: true, 
      data: { schedules, pagination }
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

const getPoojaScheduleById = async (req, res) => {
  try {
    const data = await poojaService.getPoojaScheduleById(req.params.id);
    
    return apiResponse({ 
      res, 
      code: SUCCESS.CODE, 
      message: 'Pooja schedule retrieved successfully', 
      status: true, 
      data 
    });
  } catch (error) {
    return errorApiResponse(res, error);
  }
};

module.exports = {
  createPoojaSchedule,
  updatePoojaSchedule,
  deletePoojaSchedule,
  listPoojaSchedules,
  getPoojaScheduleById
};
