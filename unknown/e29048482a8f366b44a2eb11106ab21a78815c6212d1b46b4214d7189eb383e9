const joi = require('joi');
const mongoose = require('mongoose');

const validateObjectId = (value, helpers) => {
  if (!mongoose.Types.ObjectId.isValid(value)) {
    return helpers.error('any.invalid');
  }
  return value;
};

const listOfferingsSchema = joi.object({
  page: joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be greater than or equal to 1'
    }),
  limit: joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be greater than or equal to 1',
      'number.max': 'Limit cannot exceed 100 records per page'
    }),
  search: joi.string()
    .trim()
    .allow('')
    .optional()
    .messages({
      'string.base': 'Search term must be a string'
    }),
  temple: joi.string()
    .custom(validateObjectId)
    .allow('')
    .optional()
    .messages({
      'any.invalid': 'Invalid temple ID format'
    }),
  isActive: joi.boolean()
    .allow('')
    .optional()
    .messages({
      'boolean.base': 'Status must be a boolean'
    }),
  sortBy: joi.string()
    .valid('name', 'amount', 'createdAt', 'updatedAt')
    .default('createdAt')
    .messages({
      'string.base': 'Sort field must be a string',
      'any.only': 'Sort field must be one of: name, amount, createdAt, or updatedAt'
    }),
  sortOrder: joi.number()
    .valid(1, -1)
    .default(-1)
    .messages({
      'number.base': 'Sort order must be a number',
      'any.only': 'Sort order must be either 1 (ascending) or -1 (descending)'
    })
});

module.exports = {
  listOfferingsSchema
};